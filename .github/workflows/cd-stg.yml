name: Build and Deploy Next.js to Server Staging

on:
  push:
    branches:
      - staging
      - test-ci2
  workflow_dispatch:

jobs:
  build:
    name: Build
    # if: github.ref == 'refs/heads/deploy'
    runs-on: [self-hosted, stg]

    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      - name: Setup Node.js 20
        uses: actions/setup-node@v3
        with:
          node-version: 20
      - name: Install dependencies
        run: |
          if [ -f .yarn.hash ] && sha256sum -c .yarn.hash; then
            echo "✅ No dependency change, skipping yarn install"
          else
            echo "📦 Dependencies changed, running yarn install..."
            yarn install
            sha256sum package.json yarn.lock > .yarn.hash
          fi
          echo "timestamp=$(date '+%d/%m/%Y %H:%M:%S')" >> $GITHUB_ENV
          yarn build:staging
          cp -r build/. /var/www/admin-partner/build

      - name: Send noti
        run: |
          curl --location 'https://api.telegram.org/bot${{ secrets.TELEGRAM_BOT_TOKEN }}/sendMessage' \
          --header 'Content-Type: application/x-www-form-urlencoded' \
          --data-urlencode 'chat_id=-4520692772' \
          --data-urlencode 'text=[${{ env.timestamp }}] Build AdminPartner Dev success - ${{ github.event.head_commit.message }}'
