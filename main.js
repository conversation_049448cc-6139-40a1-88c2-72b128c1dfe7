const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');
const { PosPrinter } = require('@plick/electron-pos-printer');

let mainWindow;

function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      preload: path.join(__dirname, 'preload.js'), // Đảm bảo đường dẫn đúng
      contextIsolation: true,
      enableRemoteModule: false,
    },
  });
  mainWindow.loadURL('http://localhost:8010'); // URL của ứng dụng React
}

app.whenReady().then(() => {
  createWindow();
  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) createWindow();
  });
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') app.quit();
});

// // Handler in hóa đơn (invoice)
ipcMain.handle('print-invoice', async (event, htmlContent) => {
  try {
    const options = {
      preview: false, // In trực tiếp, không xem trước
      margin: '0 0 0 0',
      copies: 1,
      printerName: 'POS-80C', // Tên máy in hóa đơn của bạn
      timeOutPerLine: 400,
      pageSize: '80mm',
    };

    const invoiceTemplate = [
      {
        type: 'text',
        value: htmlContent,
        style: { fontSize: '14px', textAlign: 'left', fontFamily: 'Arial, sans-serif' },
      },
    ];

    await PosPrinter.print(invoiceTemplate, options);
    return 'Invoice printed successfully!';
  } catch (error) {
    console.error('Error printing invoice:', error);
    throw error;
  }
});

ipcMain.handle('print-label', async (event, htmlContent) => {
  try {
    const options = {
      preview: false,
      margin: '0 0 0 0',
      copies: 1,
      printerName: 'Xprinter XP-350B', // Tên máy in tem
      timeOutPerLine: 400,
      pageSize: '80mm',
    };

    const labelTemplate = htmlContent.map((content) => ({
      type: 'text',
      value: content,
      style: {
        fontSize: '10px',
        textAlign: 'left',
        fontFamily: 'Arial, sans-serif',
        pageBreakBefore: 'always',
      },
    }));

    await PosPrinter.print(labelTemplate, options);
    return 'Label printed successfully!';
  } catch (error) {
    console.error('Error printing label:', error);
    throw error;
  }
});
