<!DOCTYPE html>
<html lang="vi">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Evotech - Offline</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #333;
      }

      .offline-container {
        text-align: center;
        background: white;
        padding: 3rem;
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        max-width: 500px;
        margin: 20px;
      }

      .offline-icon {
        width: 80px;
        height: 80px;
        margin: 0 auto 2rem;
        background: #2654fe;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 2rem;
      }

      .offline-title {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 1rem;
        color: #2654fe;
      }

      .offline-message {
        font-size: 1.1rem;
        color: #666;
        margin-bottom: 2rem;
        line-height: 1.6;
      }

      .offline-button {
        background: #2654fe;
        color: white;
        border: none;
        padding: 12px 30px;
        border-radius: 8px;
        font-size: 1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-block;
      }

      .offline-button:hover {
        background: #1a3fd1;
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(38, 84, 254, 0.3);
      }

      .offline-tips {
        margin-top: 2rem;
        padding: 1.5rem;
        background: #f8f9fa;
        border-radius: 12px;
        text-align: left;
      }

      .offline-tips h3 {
        color: #2654fe;
        margin-bottom: 1rem;
        font-size: 1.2rem;
      }

      .offline-tips ul {
        list-style: none;
      }

      .offline-tips li {
        margin-bottom: 0.5rem;
        padding-left: 1.5rem;
        position: relative;
        color: #666;
      }

      .offline-tips li:before {
        content: "•";
        color: #2654fe;
        font-weight: bold;
        position: absolute;
        left: 0;
      }

      @media (max-width: 768px) {
        .offline-container {
          padding: 2rem;
          margin: 10px;
        }

        .offline-title {
          font-size: 1.5rem;
        }

        .offline-message {
          font-size: 1rem;
        }
      }
    </style>
  </head>
  <body>
    <div class="offline-container">
      <div class="offline-icon">📡</div>

      <h1 class="offline-title">Bạn đang offline</h1>

      <p class="offline-message">
        Không thể kết nối internet. Vui lòng kiểm tra kết nối mạng và thử lại.
      </p>

      <button class="offline-button" onclick="window.location.reload()">Thử lại</button>

      <div class="offline-tips">
        <h3>💡 Gợi ý:</h3>
        <ul>
          <li>Kiểm tra kết nối WiFi hoặc 4G</li>
          <li>Thử tắt và bật lại mạng</li>
          <li>Khởi động lại ứng dụng</li>
          <li>Liên hệ IT nếu vấn đề vẫn tiếp tục</li>
        </ul>
      </div>
    </div>

    <script>
      // Auto retry khi có kết nối
      window.addEventListener("online", () => {
        window.location.reload();
      });

      // Check connection every 30 seconds
      setInterval(() => {
        if (navigator.onLine) {
          window.location.reload();
        }
      }, 30000);
    </script>
  </body>
</html>
