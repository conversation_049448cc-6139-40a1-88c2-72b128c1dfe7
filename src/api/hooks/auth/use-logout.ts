import { useState } from "react";
import { authService } from "../../services/auth/auth.service";
import { ErrorHandlerService } from "../../services/error-handler.service";

export const useLogout = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const logout = async (refreshToken: string, tokenFCM: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await authService.logout(refreshToken, tokenFCM);
      localStorage.removeItem("token"); // Xóa token khi logout
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  return { logout, loading, error };
};
