import { useState } from "react";
import { ErrorHandlerService } from "../../services/error-handler.service";
import { InvoiceQueryParams, invoiceService } from "../../services/invoice/invoice.service";
import { InvoiceConfigData, InvoiceProvider } from "../../types/invoice.types";
import {
  BalanceLogRequestDto,
  balanceLogService,
} from "../../services/balance-log/balance-log.service";

export const useBalanceLog = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const getBalanceHistory = async (data: BalanceLogRequestDto) => {
    try {
      setLoading(true);
      setError(null);
      const response = await balanceLogService.getBalanceHistory(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  return {
    getBalanceHistory,
    loading,
    error,
  };
};
