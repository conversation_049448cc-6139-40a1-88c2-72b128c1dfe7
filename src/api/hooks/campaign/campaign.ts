import { useState } from "react";
import { ErrorHandlerService } from "../../services/error-handler.service";
import {
  IBodyTemplateZns,
  zaloAutomationService,
} from "../../services/zalo-automation/zalo-automation.service";
import {
  CampaignQueryParams,
  campaignService,
  CampaignUserQueryParams,
  IBodyCreateCampaign,
  IBodyImportExcelListUserCampaign,
} from "../../services/campaign/campaign.service";

export const useCampaign = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const importFileExcelListUserCampaign = async (data: IBodyImportExcelListUserCampaign) => {
    try {
      setLoading(true);
      setError(null);
      const response = await campaignService.importFileExcelListUserCampaign(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };
  const getListCampaign = async (params: CampaignQueryParams) => {
    try {
      setLoading(true);
      setError(null);
      if (params.shopId) {
        const response = await campaignService.getListCampaign(params);
        return response;
      }
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };
  const createCampaign = async (data: IBodyCreateCampaign) => {
    try {
      setLoading(true);
      setError(null);
      const response = await campaignService.createCampaign(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const updateCampaign = async (data: IBodyCreateCampaign) => {
    try {
      setLoading(true);
      setError(null);
      const response = await campaignService.updateCampaign(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const getDetailCampaign = async (shopId: string, campaignId: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await campaignService.getDetailCampaign(shopId, campaignId);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };
  const deleteCampaign = async (shopId: string, campaignId: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await campaignService.deleteCampaign(shopId, campaignId);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };
  const getListUserOfCampaign = async (data: CampaignUserQueryParams) => {
    try {
      setLoading(true);
      setError(null);
      const response = await campaignService.getListUserOfCampaign(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };
  const changeStatusOfCampaign = async (shopId: string, campaignId: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await campaignService.changeStatusOfCampaign(shopId, campaignId);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const rerunCampaign = async (shopId: string, campaignId: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await campaignService.rerunCampaign(shopId, campaignId);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };


  return {
    importFileExcelListUserCampaign,
    getListCampaign,
    createCampaign,
    updateCampaign,
    getDetailCampaign,
    deleteCampaign,
    getListUserOfCampaign,
    changeStatusOfCampaign,
    rerunCampaign,
    loading,
    error,
  };
};
