import { useState } from 'react';
import { useStoreId } from '@/src/hooks/use-store-id';
import { GetWarehouseRequest } from '@/src/api/types/warehouse.types';
import { warehouseService } from '@/src/api/services/dashboard/product/warehouse.service';
import { ErrorHandlerService } from '@/src/api/services/error-handler.service';
import { StorageService } from 'nextjs-api-lib';

export const useWarehouse = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const storeId = useStoreId();
  const partnerId = StorageService.get('partnerId') as string | null;

  const getWarehouse = async (skip: number, limit: number, search: string = '') => {
    try {
      setLoading(true);
      setError(null);
      const params: GetWarehouseRequest = {
        partnerId: partnerId,
        shopId: storeId,
        search,
        skip,
        limit
      };
      const response = await warehouseService.getWarehouse(params);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  return {
    getWarehouse,
    loading,
    error
  };
};
