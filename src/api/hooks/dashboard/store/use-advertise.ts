import { useState } from 'react';
import { useStoreId } from '@/src/hooks/use-store-id';
import { GetAdvertiseRequest } from '@/src/api/types/advertise.types';
import { advertiseService } from '@/src/api/services/dashboard/store/advertise.service';
import { ErrorHandlerService } from '@/src/api/services/error-handler.service';

export const useAdvertise = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const storeId = useStoreId();

  const getAdvertise = async (skip: number, limit: number, search: string = '') => {
    try {
      setLoading(true);
      setError(null);
      const params: GetAdvertiseRequest = {
        shopId: storeId,
        search,
        skip,
        limit,
      };
      const response = await advertiseService.getAdvertises(params);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  const createAdvertise = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await advertiseService.createAdvertise(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  const updateAdvertise = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await advertiseService.updateAdvertise(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  const updateImage = async (advertiseId: string, imageFile: File) => {
    try {
      setLoading(true);
      setError(null);
      const response = await advertiseService.updateImageAdvertise(advertiseId, imageFile);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  const deleteAdvertise = async (advertiseId: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await advertiseService.deleteAdvertise(advertiseId);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  const getAdvertiseDetail = async (advertiseId: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await advertiseService.getAdvertiseDetail(advertiseId);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  return {
    getAdvertise,
    createAdvertise,
    getAdvertiseDetail,
    updateAdvertise,
    updateImage,
    deleteAdvertise,
    loading,
    error,
  };
};
