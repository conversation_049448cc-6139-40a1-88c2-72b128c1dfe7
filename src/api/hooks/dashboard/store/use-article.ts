import { useState } from "react";
import { useStoreId } from "@/src/hooks/use-store-id";
import { GetArticleRequest } from "@/src/api/types/article.types";
import { ArticleDto, articleService } from "@/src/api/services/dashboard/store/article.service";
import { ErrorHandlerService } from "@/src/api/services/error-handler.service";

export const useArticle = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const storeId = useStoreId();

  const getArticle = async (skip: number, limit: number, search: string = "") => {
    try {
      setLoading(true);
      setError(null);
      const params: GetArticleRequest = {
        shopId: storeId,
        search,
        skip,
        limit,
      };
      const response = await articleService.getArticles(params);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  const createArticle = async (data: ArticleDto) => {
    try {
      setLoading(true);
      setError(null);
      const response = await articleService.createArticle(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  const updateArticle = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await articleService.updateArticle(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  const updateImage = async (articleId: string, imageFile: File) => {
    try {
      setLoading(true);
      setError(null);
      const response = await articleService.updateImageArticle(articleId, imageFile);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  const deleteArticle = async (articleId: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await articleService.deleteArticle(articleId);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  const getArticleDetail = async (articleId: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await articleService.getArticleDetail(articleId);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  return {
    getArticle,
    createArticle,
    getArticleDetail,
    updateArticle,
    updateImage,
    deleteArticle,
    loading,
    error,
  };
};
