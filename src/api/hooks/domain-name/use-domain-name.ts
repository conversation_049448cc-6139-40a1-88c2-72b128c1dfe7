import { useState } from 'react';
import { ErrorHandlerService } from '../../services/error-handler.service';
import { domainNameService } from '../../services/domain-name/domain-name.service';

export const useDomainName = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const createDomainName = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await domainNameService.createDomainName(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const listShopDomainName = async (shopId: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await domainNameService.listDomainName(shopId);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const updateDomainName = async (data: any) => {
    try {
      setLoading(true);
      setError(null);

      const response = await domainNameService.updateDomainName(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const deleteDomainNames = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await domainNameService.deleteDomainNames(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const checkDomainNameExist = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await domainNameService.checkDomainNameExist(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  return {
    createDomainName,
    listShopDomainName,
    updateDomainName,
    deleteDomainNames,
    checkDomainNameExist,
    loading,
    error
  };
};
