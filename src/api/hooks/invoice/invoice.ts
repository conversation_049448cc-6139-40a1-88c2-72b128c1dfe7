import { useState } from "react";
import { ErrorHandlerService } from "../../services/error-handler.service";
import { InvoiceQueryParams, invoiceService } from "../../services/invoice/invoice.service";
import { InvoiceConfigData, InvoiceProvider } from "../../types/invoice.types";

export const useInvoice = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const getInvoiceConfig = async (shopId: string, provider: InvoiceProvider) => {
    try {
      setLoading(true);
      setError(null);
      const response = await invoiceService.getInvoiceConfig(shopId, provider);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  const activeInvoiceConfig = async (id: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await invoiceService.activeInvoiceConfig(id);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  const createInvoiceConfig = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await invoiceService.createInvoiceConfig(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const updateInvoiceConfig = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await invoiceService.updateInvoiceConfig(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const getListHistoryInvoice = async (data: InvoiceQueryParams) => {
    try {
      setLoading(true);
      setError(null);
      const response = await invoiceService.getListHistoryInvoice(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const createManualInvoice = async (orderId: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await invoiceService.createManualInvoice(orderId);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const downloadFilePdfInvoice = async (invoiceNo: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await invoiceService.downloadFilePdfInvoice(invoiceNo);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return {
    getInvoiceConfig,
    activeInvoiceConfig,
    createInvoiceConfig,
    updateInvoiceConfig,
    getListHistoryInvoice,
    createManualInvoice,
    downloadFilePdfInvoice,
    loading,
    error,
  };
};
