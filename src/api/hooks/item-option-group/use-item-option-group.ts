import { useEffect, useState } from 'react';

import { ErrorHandlerService } from '@/src/api/services/error-handler.service';

import { itemOptionGroupService } from '../../services/item-option-group/item-option-group.service';
import { CreateItemGroupRequestBodyRequest } from '../../types/item-option-group.type';

export const useItemOptionGroup = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const listItemOptionGroup = async (skip: number, limit: number, data: any) => {
    try {
      setLoading(true);
      setError(null);
      const params = {
        data,
        skip,
        limit,
      };
      const response = await itemOptionGroupService.listItemOptionGroup(params);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };
  const detailItemOptionGroup = async (id: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await itemOptionGroupService.detailItemOptionGroup(id);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const createItemOptionGroup = async (body: CreateItemGroupRequestBodyRequest) => {
    try {
      setLoading(true);
      setError(null);
      const response = await itemOptionGroupService.createItemOptionGroup(body);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const updateItemOptionGroup = async (id: string, body: CreateItemGroupRequestBodyRequest) => {
    try {
      setLoading(true);
      setError(null);
      const response = await itemOptionGroupService.updateItemOptionGroup(id, body);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  return {
    createItemOptionGroup,
    listItemOptionGroup,
    detailItemOptionGroup,
    updateItemOptionGroup,
    loading,
    error,
  };
};
