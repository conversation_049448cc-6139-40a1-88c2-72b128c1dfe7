import { useEffect, useState } from "react";

import { ErrorHandlerService } from "@/src/api/services/error-handler.service";

import {
  ItemOptionCreateDto,
  itemOptionService,
} from "../../services/item-option/item-option.service";

export const useItemOption = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const listItemOptionByGroupId = async (skip: number, limit: number, data: any) => {
    try {
      setLoading(true);
      setError(null);
      const params = {
        data,
        skip,
        limit,
      };
      const response = await itemOptionService.listItemOptionByGroupId(params);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const itemOptionUser = async (data: any) => {
    try {
      setLoading(true);
      setError(null);

      const response = await itemOptionService.itemOptionUser(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const listItemOptionByIds = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await itemOptionService.listItemOptionByIds(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const createItemOption = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await itemOptionService.createItemOption(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const updateItemOption = async (id: string, data: any) => {
    try {
      setLoading(true);
      setError(null);

      const response = await itemOptionService.updateItemOption(id, data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };
  const createAndUpdateItemOption = async (shopId: string, data: ItemOptionCreateDto[]) => {
    try {
      setLoading(true);
      setError(null);

      const response = await itemOptionService.createAndUpdateItemOption(shopId, data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };
  const deleteItemOption = async (id: string) => {
    try {
      setLoading(true);
      setError(null);

      const response = await itemOptionService.deleteItemOption(id);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  return {
    itemOptionUser,
    listItemOptionByGroupId,
    listItemOptionByIds,
    createItemOption,
    updateItemOption,
    deleteItemOption,
    createAndUpdateItemOption,
    loading,
    error,
  };
};
