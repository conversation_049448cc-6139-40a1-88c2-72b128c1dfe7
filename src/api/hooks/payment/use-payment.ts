import { useState } from "react";
import { DataActiveStatePayment, paymentService } from "@/src/api/services/payment/payment.service";
import { ErrorHandlerService } from "@/src/api/services/error-handler.service";

export const usePayment = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string>("");

  const getListPayment = async (shopId: string, skip: number, limit: number) => {
    try {
      setLoading(true);
      setError(null);

      const params = {
        skip,
        limit,
      };

      console.log("Calling getListPayment with params:", params);
      const response = await paymentService.getListPayment(shopId, params);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      console.error("getListPayment error:", errorResponse);
    } finally {
      setLoading(false);
    }
  };

  const getListBankPartner = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await paymentService.getListBankPartner();
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  return {
    getListPayment,
    getListBankPartner,
    loading,
    error,
  };
};
