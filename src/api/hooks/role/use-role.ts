import { useState } from "react";
import { ErrorHandlerService } from "../../services/error-handler.service";
import {
  RoleBodyCreateApi,
  RoleBodyUpdateApi,
  roleService,
} from "../../services/role/role.service";

export const useRole = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const createRole = async (data: RoleBodyCreateApi) => {
    try {
      setLoading(true);
      setError(null);
      const response = await roleService.createRole(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const getRoleById = async (roleId: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await roleService.getRoleById(roleId);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const updateRole = async (data: RoleBodyUpdateApi) => {
    try {
      setLoading(true);
      setError(null);
      const response = await roleService.updateRole(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const listRole = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await roleService.listRole();
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const deleteRole = async (roleId: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await roleService.deleteRole(roleId);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  return {
    createRole,
    getRoleById,
    updateRole,
    listRole,
    deleteRole,
    loading,
    error,
  };
};
