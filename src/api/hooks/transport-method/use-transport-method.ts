import { useState } from "react";
import { ErrorHandlerService } from "../../services/error-handler.service";
import { transportMethodService } from "../../services/transport-method/transport-method.service";
import { UpdateTransportSettingRequest } from "../../types/transport-method.types";

export const useTransportMethod = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const getListTransportMethod = async (data) => {
    try {
      setLoading(true);
      setError(null);
      const response = await transportMethodService.getListTransportMethod(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  const updateTransportMethod = async (data: UpdateTransportSettingRequest) => {
    try {
      setLoading(true);
      setError(null);
      const response = await transportMethodService.updateTransportMethod(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  const signUpAhamove = async (data) => {
    try {
      setLoading(true);
      setError(null);
      const response = await transportMethodService.signUpAhamove(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  return { updateTransportMethod, getListTransportMethod, signUpAhamove, loading, error };
};
