import { useState } from "react";
import { ErrorHandlerService } from "../../services/error-handler.service";
import { userService } from "../../services/user/user.service";
import {
  BodyExportExcelListUser,
  FilterRequestDto,
  ParamImportExcelUserGroup,
  SearchUserOfUserGroupDto,
  UserGroupAdvancedSearchDto,
  UserGroupQueryParams,
  userGroupService,
} from "../../services/user-group/user-group.service";

export const useUserGroup = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const getListUserGroup = async (params: UserGroupQueryParams) => {
    try {
      setLoading(true);
      setError(null);
      const response = await userGroupService.getListUserGroup(params);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const deleteUserGroup = async (shopId: string, groupId: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await userGroupService.deleteUserGroup(shopId, groupId);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };
  const exportTemplateExcelUser = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await userGroupService.exportTemplateExcelUser();
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const getListFilterUserGroup = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await userGroupService.getListFilterUserGroup();
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const importExcelListUser = async (data: ParamImportExcelUserGroup) => {
    try {
      setLoading(true);
      setError(null);
      const response = await userGroupService.importExcelListUser(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const getListUserByGroupId = async (data: SearchUserOfUserGroupDto) => {
    try {
      setLoading(true);
      setError(null);
      const response = await userGroupService.getListUserByGroupId(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };
  const exportExcelListUserByGroupId = async (data: BodyExportExcelListUser) => {
    try {
      setLoading(true);
      setError(null);
      const response = await userGroupService.exportExcelListUserByGroupId(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const filterListUserByAdvancedFilter = async (data: FilterRequestDto) => {
    try {
      setLoading(true);
      setError(null);
      const response = await userGroupService.filterListUserByAdvancedFilter(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };
  const createUserGroupWithAdvancedSearch = async (data: UserGroupAdvancedSearchDto) => {
    try {
      setLoading(true);
      setError(null);
      const response = await userGroupService.createUserGroupWithAdvancedSearch(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const updateUserGroupWithAdvancedSearch = async (data: UserGroupAdvancedSearchDto) => {
    try {
      setLoading(true);
      setError(null);
      const response = await userGroupService.updateUserGroupWithAdvancedSearch(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const getListUserGroupById = async (shopId: string, groupId: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await userGroupService.getListUserGroupById(shopId, groupId);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  return {
    getListUserGroup,
    deleteUserGroup,
    exportTemplateExcelUser,
    getListFilterUserGroup,
    importExcelListUser,
    getListUserByGroupId,
    exportExcelListUserByGroupId,
    filterListUserByAdvancedFilter,
    createUserGroupWithAdvancedSearch,
    updateUserGroupWithAdvancedSearch,
    getListUserGroupById,
    loading,
    error,
  };
};
