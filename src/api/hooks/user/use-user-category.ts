import { useState } from 'react';
import { userCategoryService } from '../../services/user/user-category.service';

export const useUserCategory = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const getUserCategory = async (skip: number, limit: number, shopId: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await userCategoryService.getUserCategory({ skip, limit, shopId });
      return response;
    } catch (err: any) {
      setError(err.message || 'Lỗi lấy danh mục');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return { getUserCategory, loading, error };
};
