import { useState } from "react";
import { ErrorHandlerService } from "../../services/error-handler.service";
import { zaloTemplateService } from "../../services/zalo-template/zalo-template.service";
import { IZaloTemplate } from "../../types/zalo-template.types";

export interface ZaloTemplateRequest {
  skip: number;
  limit: number;
  search?: string;
  type: string;
  shopId: string;
}
export const useZaloTemplate = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const getZaloTemplate = async (model: ZaloTemplateRequest) => {
    try {
      setLoading(true);
      setError(null);
      const response = await zaloTemplateService.getZaloTemplate(model);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  const getDetailTemplateById = async (shopId: string, templateId: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await zaloTemplateService.getDetailTemplateById(shopId, templateId);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  const updateTemplate = async (data: IZaloTemplate) => {
    try {
      setLoading(true);
      setError(null);
      const response = await zaloTemplateService.updateTemplate(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  const getTriggerEventsByType = async (type: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await zaloTemplateService.getTriggerEventsByType(type);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };
  const getTriggerPrameter = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await zaloTemplateService.getTriggerPrameter();
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };
  const getSendTypes = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await zaloTemplateService.getSendTypes();
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  const getButtonActions = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await zaloTemplateService.getButtonActions();
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };
  const getMessageTypesByTemplateType = async (type: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await zaloTemplateService.getMessageTypesByTemplateType(type);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  const createZaloTemplate = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await zaloTemplateService.createZaloTemplate(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const updateZaloTemplate = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await zaloTemplateService.updateZaloTemplate(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };
  const deleteZaloTemplate = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await zaloTemplateService.deleteZaloTemplate(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const changeTemplateStatus = async (id: string, status: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await zaloTemplateService.changeTemplateStatus(id, status);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  return {
    getZaloTemplate,
    getDetailTemplateById,
    updateTemplate,
    getTriggerEventsByType,
    getTriggerPrameter,
    getSendTypes,
    getButtonActions,
    getMessageTypesByTemplateType,
    createZaloTemplate,
    updateZaloTemplate,
    deleteZaloTemplate,
    changeTemplateStatus,
    loading,
    error,
  };
};
