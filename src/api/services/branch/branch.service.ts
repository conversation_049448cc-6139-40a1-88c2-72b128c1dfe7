import { apiClient } from '@/src/api/config/api-client';
import { API_PATHS } from '@/src/api/constants/api-paths';
import type { ApiError, ErrorConfig } from '@/src/api/types/error.types';
import type { ExtendedRequestConfig } from '@/src/api/types/api.types';
import {
  GetServiceRequest,
  GetServiceRequest2,
  GetServiceRequestBody,
} from '@/src/api/types/service.types';

function handleApiError(error: any): never {
  const status = error.response?.status || 500;
  const errorData = error.response?.data as ApiError;
  const errorMessage = errorData?.detail || errorData?.title || error.message;

  if (error.response) {
    throw {
      ...error,
      message: errorMessage,
    };
  }
  throw error;
}

export const branchService = {
  getBranchs: async <T = any>(
    shopId: string,
    params: GetServiceRequest2,
    errorConfig?: ErrorConfig
  ) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const queryParams = [];
      if (params.skip !== undefined) queryParams.push(`skip=${params.skip}`);
      if (params.limit !== undefined) queryParams.push(`limit=${params.limit}`);

      const queryString = queryParams.join('&');
      const response = await apiClient.get<GetServiceRequestBody, T>(
        `${API_PATHS.BRANCH.GET_BRANCHS}?shopId=${shopId}&${queryString}`,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  createBranch: async <T = any>(data: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.post<any, T>(API_PATHS.BRANCH.CREATE_BRANCH, data, config);
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  updateBranch: async <T = any>(data: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.put<any, T>(API_PATHS.BRANCH.UPDATE_BRANCH, data, config);
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  deleteBranch: async <T = any>(branchId: string, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.delete<T>(
        `${API_PATHS.BRANCH.DELETE_BRANCH}?branchId=${branchId}`,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  getBranchDetail: async <T = any>(branchId: string, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.get<any, T>(
        `${API_PATHS.BRANCH.GET_BRANCH_DETAIL}/${branchId}`,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
};
