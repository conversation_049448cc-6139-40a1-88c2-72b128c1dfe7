import { apiClient } from "@/src/api/config/api-client";
import { API_PATHS } from "@/src/api/constants/api-paths";
import type { ApiError, ErrorConfig } from "@/src/api/types/error.types";
import type { ExtendedRequestConfig } from "@/src/api/types/api.types";
import { GetProductRequest, GetProductRequestBody } from "@/src/api/types/product.types";

function handleApiError(error: any): never {
  const status = error.response?.status || 500;
  const errorData = error.response?.data as ApiError;
  const errorMessage = errorData?.detail || errorData?.title || error.message;

  if (error.response) {
    throw {
      ...error,
      message: errorMessage,
    };
  }
  throw error;
}

export const cartService = {
  searchItems: async <T = any>(params, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.get(
        `${API_PATHS.CART.SEARCH_ITEMS}?skip=${params.skip}&limit=${params.limit}&shopId=${params.shopId}&search=${params.search}`,
        {
          shopId: params.shopId,
          search: params.search,
        },
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
  searchUsers: async <T = any>(params, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.get(
        `${API_PATHS.CART.SEARCH_USERS}?skip=${params.skip}&limit=${params.limit}&shopId=${params.shopId}&search=${params.search}`,
        {
          shopId: params.shopId,
          search: params.search,
        },
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
  deleteCart: async <T = any>(cartId: string, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.delete<any, T>(
        `${API_PATHS.CART.DELETE_CART}?cartId=${cartId}`,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
  createOrUpdateCart: async <T = any>(data: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.post<any, T>(
        API_PATHS.CART.CREATE_OR_UPDATE_CART,
        data,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  getCart: async <T = any>(data: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.get<any, T>(
        `${API_PATHS.CART.GET_CART}?cartId=${data}`,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  listCart: async <T = any>(params, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      let url = `${API_PATHS.CART.LIST_CART}?skip=${params.skip}&limit=${params.limit}&shopId=${params.shopId}&partnerId=${params.partnerId}`;

      if (params.origins) {
        params.origins.forEach((origin) => {
          url += `&origins=${origin}`;
        });
      }
      if (params.branchId) {
        url += `&branchId=${params.branchId}`;
      }

      const response = await apiClient.get(url, config);
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  estimatePoint: async <T = any>(params, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      let url = `${API_PATHS.CART.ESTIMATE_POINT_CART}?cartId=${params.cartId}`;

      const response = await apiClient.get(url, config);
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
};
