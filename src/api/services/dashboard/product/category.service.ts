import { apiClient } from "@/src/api/config/api-client";
import { API_PATHS } from "@/src/api/constants/api-paths";
import type { ApiError, ErrorConfig } from "@/src/api/types/error.types";
import type { ExtendedRequestConfig } from "@/src/api/types/api.types";
import {
  GetProductCategoryRequest,
  GetProductCategoryRequestBody,
} from "@/src/api/types/product-category.types";
import { MediaFileDto } from "../store/article-category.service";

function handleApiError(error: any): never {
  const status = error.response?.status || 500;
  const errorData = error.response?.data as ApiError;
  const errorMessage = errorData?.detail || errorData?.title || error.message;

  if (error.response) {
    throw {
      ...error,
      message: errorMessage,
    };
  }
  throw error;
}
export interface CategoryDto {
  categoryId?: string;
  parentId?: string;
  parentName?: string;
  quantityItems?: number;
  partnerId?: string;
  shopId: string;
  categoryType: string;
  categoryName: string;
  categoryDisplayName?: string;
  categoryLevel?: string;
  image: MediaFileDto;
  categoryDesc?: string;
  categoryPosition?: number;
  isHome?: boolean;
  publish?: string;
  ruleActive?: string;
  created?: string;
  updated?: string;
}

export interface ExportProductCategoryParams {
  shopId: string;
  partnerId?: string;
  categoryType?: string;
  search?: string;
}
export const productCategoryService = {
  getProductCategoryTree: async <T = any>(
    params: GetProductCategoryRequest,
    errorConfig?: ErrorConfig
  ) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.post<GetProductCategoryRequestBody, T>(
        `${API_PATHS.PRODUCT_CATEGORY.GET_PRODUCT_CATEGORY_TREE}`,
        params,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  getProductCategory: async <T = any>(
    params: GetProductCategoryRequest,
    errorConfig?: ErrorConfig
  ) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const { partnerId, shopId, parentId, isHome, level, categoryType, paging } = params;

      const queryParams = new URLSearchParams({
        PartnerId: partnerId,
        ShopId: shopId,
        IsHome: isHome?.toString() || "",
        CategoryType: categoryType,
        "Paging.NameType": paging?.nameType || "",
        "Paging.SortType": paging?.sortType || "",
        "Paging.PageSize": paging?.pageSize?.toString() || "",
        "Paging.PageIndex": paging?.pageIndex?.toString() || "",
        "Paging.Search": paging?.search || "",
        "Paging.Name": paging?.name || "",
        "Paging.Sort": paging?.sort || "",
      });
      if (parentId) {
        queryParams.append("ParentId", parentId);
      }
      if (level) {
        queryParams.append("Level", level);
      }

      const response = await apiClient.get<GetProductCategoryRequestBody, T>(
        `${API_PATHS.PRODUCT_CATEGORY.GET_PRODUCT_CATEGORY}?${queryParams.toString()}`,
        undefined,
        config
      );

      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  createProductCategory: async <T = any>(data: CategoryDto, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.post<any, T>(
        API_PATHS.PRODUCT_CATEGORY.CREATE_PRODUCT_CATEGORY,
        data,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  updateProductCategory: async <T = any>(data: CategoryDto, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.put<any, T>(
        API_PATHS.PRODUCT_CATEGORY.UPDATE_PRODUCT_CATEGORY,
        data,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  updateImageProductCategory: async <T = any>(
    productCategoryId: string,
    imageFile: File,
    errorConfig?: ErrorConfig
  ) => {
    const formData = new FormData();
    formData.append("CategoryId", productCategoryId);
    formData.append("FileUpload", imageFile);

    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
      headers: {
        "Content-Type": "multipart/form-data",
      },
    };

    try {
      const response = await apiClient.post<FormData, T>(
        API_PATHS.PRODUCT_CATEGORY.UPDATE_IMAGE_PRODUCT_CATEGORY,
        formData,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  deleteProductCategory: async <T = any>(productCategoryId: string, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.delete<T>(
        `${API_PATHS.PRODUCT_CATEGORY.DELETE_PRODUCT_CATEGORY}?categoryId=${productCategoryId}`,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  getProductCategoryDetail: async <T = any>(
    productCategoryId: string,
    errorConfig?: ErrorConfig
  ) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.get<any, T>(
        `${API_PATHS.PRODUCT_CATEGORY.GET_PRODUCT_CATEGORY_DETAIL}?categoryId=${productCategoryId}`,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
  exportTemplateProductCategory: async <T = any>(
    shopId: string,
    categoryType: string,
    errorConfig?: ErrorConfig
  ) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.get<any, T>(
        `${API_PATHS.PRODUCT_CATEGORY.EXPORT_TEMPLATE_PRODUCT_CATEGORY}?shopId=${shopId}&categoryType=${categoryType}`,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
  importProductCategory: async <T = any>(
    shopId: string,
    categoryType: string,
    file: File,
    errorConfig?: ErrorConfig
  ) => {
    const formData = new FormData();
    formData.append("file", file);
    formData.append("shopId", shopId);
    formData.append("categoryType", categoryType);

    const config: any = {
      errorHandling: errorConfig,
      headers: {
        "Content-Type": "multipart/form-data",
      },
    };

    try {
      const response = await apiClient.post<any, T>(
        `${API_PATHS.PRODUCT_CATEGORY.IMPORT_PRODUCT_CATEGORY}`,
        formData,
        config
      );
      return response;
    } catch (error: any) {
      // handleApiError(error);
      return error;
    }
  },

  exportListProductCategory: async <T = any>(
    data: ExportProductCategoryParams,
    errorConfig?: ErrorConfig
  ) => {
    const config: any = {
      errorHandling: errorConfig,
      responseType: "blob",
    };

    const params = [];

    if (!data.shopId) {
      throw new Error("ShopId is required");
    }
    params.push(`ShopId=${encodeURIComponent(data.shopId)}`);

    if (data.categoryType) {
      params.push(`categoryType=${encodeURIComponent(data.categoryType)}`);
    }

    if (data.partnerId) {
      params.push(`partnerId=${encodeURIComponent(data.partnerId)}`);
    }

    if (data.search) {
      params.push(`search=${encodeURIComponent(data.search)}`);
    }

    if (data.shopId) {
      params.push(`shopId=${encodeURIComponent(data.shopId)}`);
    }

    const url = `${API_PATHS.PRODUCT_CATEGORY.EXPORT_LIST_PRODUCT_CATEGORY}?${params.join("&")}`;

    try {
      const response = await apiClient.get<any, T>(url, config);
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
};
