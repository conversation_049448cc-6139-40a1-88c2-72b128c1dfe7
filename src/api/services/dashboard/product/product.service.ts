import { apiClient } from "@/src/api/config/api-client";
import { API_PATHS } from "@/src/api/constants/api-paths";
import type { ApiError, ErrorConfig } from "@/src/api/types/error.types";
import type { ExtendedRequestConfig } from "@/src/api/types/api.types";
import { GetProductRequest, GetProductRequestBody } from "@/src/api/types/product.types";

function handleApiError(error: any): never {
  const status = error.response?.status || 500;
  const errorData = error.response?.data as ApiError;
  const errorMessage = errorData?.detail || errorData?.title || error.message;

  if (error.response) {
    throw {
      ...error,
      message: errorMessage,
    };
  }
  throw error;
}

export interface ExportProductParams {
  shopId: string;
  type?: string;
  categoryId?: string;
  subCategoryId?: string;
  search?: string;
}
export interface IBodyDeleteListItem {
  shopId: string;
  itemsType: string;
  data: string[];
}

export const productService = {
  getProduct: async <T = any>(params: GetProductRequest, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const queryParams = [];
      if (params.skip !== undefined) queryParams.push(`skip=${params.skip}`);
      if (params.limit !== undefined) queryParams.push(`limit=${params.limit}`);
      if (params.partnerId !== undefined) queryParams.push(`partnerId=${params.partnerId}`);
      if (params.shopId !== undefined) queryParams.push(`shopId=${params.shopId}`);
      if (params.itemsType !== undefined) queryParams.push(`itemsType=${params.itemsType}`);
      if (params.categoryId !== null) queryParams.push(`categoryId=${params.categoryId}`);
      if (params.subCategoryId !== null) queryParams.push(`subCategoryId=${params.subCategoryId}`);
      if (params.search !== undefined && params.search !== "")
        queryParams.push(`search=${params.search}`);
      // Kết hợp các tham số thành query string
      const queryString = queryParams.join("&");
      const response = await apiClient.get<GetProductRequestBody, T>(
        `${API_PATHS.PRODUCT.GET_PRODUCT}?${queryString}`,
        {
          partnerId: params.partnerId,
          shopId: params.shopId,
          itemsType: params.itemsType,
          categoryId: params.categoryId,
          subCategoryId: params.subCategoryId,
          search: params.search,
        },
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  createProduct: async <T = any>(data: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.post<any, T>(API_PATHS.PRODUCT.CREATE_PRODUCT, data, config);
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  updateProduct: async <T = any>(data: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.put<any, T>(API_PATHS.PRODUCT.UPDATE_PRODUCT, data, config);
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  updateImageProduct: async <T = any>(
    productId: string,
    imageFile: File,
    errorConfig?: ErrorConfig
  ) => {
    const formData = new FormData();
    formData.append("ProductId", productId);
    formData.append("FileUpload", imageFile);

    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
      headers: {
        "Content-Type": "multipart/form-data",
      },
    };

    try {
      const response = await apiClient.post<FormData, T>(
        API_PATHS.PRODUCT.UPDATE_IMAGE_PRODUCT,
        formData,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  deleteProduct: async <T = any>(itemsCode: string, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.delete<T>(
        `${API_PATHS.PRODUCT.DELETE_PRODUCT}?itemsCode=${itemsCode}`,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  getProductDetail: async <T = any>(
    shopId: string,
    productId: string,
    errorConfig?: ErrorConfig
  ) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.get<any, T>(
        `${API_PATHS.PRODUCT.GET_PRODUCT_DETAIL}/${shopId}?itemsCode=${productId}`,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
  getListProductByProductId: async <T = any>(data: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.post<any, T>(
        `${API_PATHS.PRODUCT.LIST_ITEMS_BY_ITEM_ID}`,
        data,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
  listItemsByItemsCode: async <T = any>(data: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.post<any, T>(
        API_PATHS.PRODUCT.LIST_ITEMS_BY_ITEMS_CODE,
        { shopId: data.shopId, itemsCodes: data.itemsCodes },
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
  exportProductTemplate: async <T = any>(data: any, errorConfig?: ErrorConfig) => {
    const config: any = {
      errorHandling: errorConfig,
      // responseType: "arraybuffer",
      responseType: "blob",
    };
    try {
      const response = await apiClient.get<any, T>(
        `${API_PATHS.PRODUCT.EXPORT_PRODUCT_TEMPLATE}?shopId=${data.shopId}&type=${data.type}`,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
  importProduct: async <T = any>(
    file: File,
    shopId: string,
    type: string,
    errorConfig?: ErrorConfig
  ) => {
    const formData = new FormData();
    formData.append("file", file);
    formData.append("shopId", shopId);
    formData.append("type", type);

    const config: any = {
      errorHandling: errorConfig,
      headers: {
        "Content-Type": "multipart/form-data",
      },
    };

    try {
      const response = await apiClient.post<any, T>(
        `${API_PATHS.PRODUCT.IMPORT_PRODUCT}`,
        formData, // Send the FormData object
        config
      );
      return response;
    } catch (error: any) {
      return error;
      // handleApiError(error);
    }
  },

  exportListProduct: async <T = any>(data: ExportProductParams, errorConfig?: ErrorConfig) => {
    const config: any = {
      errorHandling: errorConfig,
      responseType: "blob",
    };

    const params = [];

    if (!data.shopId) {
      throw new Error("ShopId is required");
    }
    params.push(`ShopId=${encodeURIComponent(data.shopId)}`);

    if (data.type) {
      params.push(`ItemsType=${encodeURIComponent(data.type)}`);
    }

    if (data.categoryId) {
      params.push(`CategoryId=${encodeURIComponent(data.categoryId)}`);
    }

    if (data.subCategoryId) {
      params.push(`SubCategoryId=${encodeURIComponent(data.subCategoryId)}`);
    }

    if (data.search) {
      params.push(`Search=${encodeURIComponent(data.search)}`);
    }

    const url = `${API_PATHS.PRODUCT.EXPORT_LIST_PRODUCT}?${params.join("&")}`;

    try {
      const response = await apiClient.get<any, T>(url, config);
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  deleteListItem: async <T = any>(body: IBodyDeleteListItem, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };
    if (body?.shopId) {
      const url = `${API_PATHS.PRODUCT.DELETE_LIST_ITEM}/${body?.shopId}?itemsType=${body?.itemsType}`;

      try {
        const response = await apiClient.delete<any, T>(url, {
          ...config,
          data: body?.data,
        });
        return response;
      } catch (error: any) {
        handleApiError(error);
      }
    }
  },
};
