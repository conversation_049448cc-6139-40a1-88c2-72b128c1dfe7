import { apiClient } from '@/src/api/config/api-client';
import { API_PATHS } from '@/src/api/constants/api-paths';
import type { ApiError, ErrorConfig } from '@/src/api/types/error.types';
import type { ExtendedRequestConfig } from '@/src/api/types/api.types';
import { GetAdvertiseRequest, GetAdvertiseRequestBody } from '@/src/api/types/advertise.types';

function handleApiError(error: any): never {
  const status = error.response?.status || 500;
  const errorData = error.response?.data as ApiError;
  const errorMessage = errorData?.detail || errorData?.title || error.message;

  if (error.response) {
    throw {
      ...error,
      message: errorMessage,
    };
  }
  throw error;
}

export const advertiseService = {
  getAdvertises: async <T = any>(params: GetAdvertiseRequest, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.get<GetAdvertiseRequestBody, T>(
        `${API_PATHS.ADVERTISE.GET_ADVERTISE}?skip=${params.skip}&limit=${params.limit}&shopId=${params.shopId}&search=${params.search}`,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  createAdvertise: async <T = any>(data: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.put<any, T>(
        API_PATHS.ADVERTISE.CREATE_ADVERTISE,
        data,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  updateAdvertise: async <T = any>(data: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.post<any, T>(
        API_PATHS.ADVERTISE.UPDATE_ADVERTISE,
        data,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  updateImageAdvertise: async <T = any>(
    advertiseId: string,
    imageFile: File,
    errorConfig?: ErrorConfig
  ) => {
    const formData = new FormData();
    formData.append('AdvertiseId', advertiseId);
    formData.append('FileUpload', imageFile);

    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    };

    try {
      const response = await apiClient.post<FormData, T>(
        API_PATHS.ADVERTISE.UPDATE_IMAGE_ADVERTISE,
        formData,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  deleteAdvertise: async <T = any>(advertiseId: string, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.delete<T>(
        `${API_PATHS.ADVERTISE.DELETE_ADVERTISE}?advertiseId=${advertiseId}`,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  getAdvertiseDetail: async <T = any>(advertiseId: string, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.get<any, T>(
        `${API_PATHS.ADVERTISE.GET_ADVERTISE_DETAIL}?advertiseId=${advertiseId}`,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
};
