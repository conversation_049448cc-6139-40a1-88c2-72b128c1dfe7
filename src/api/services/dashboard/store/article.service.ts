import { apiClient } from "@/src/api/config/api-client";
import { API_PATHS } from "@/src/api/constants/api-paths";
import type { ApiError, ErrorConfig } from "@/src/api/types/error.types";
import type { ExtendedRequestConfig } from "@/src/api/types/api.types";
import { GetArticleRequest, GetArticleRequestBody } from "@/src/api/types/article.types";
import { MediaFileDto } from "./article-category.service";

function handleApiError(error: any): never {
  const status = error.response?.status || 500;
  const errorData = error.response?.data as ApiError;
  const errorMessage = errorData?.detail || errorData?.title || error.message;

  if (error.response) {
    throw {
      ...error,
      message: errorMessage,
    };
  }
  throw error;
}

export interface ArticleDto {
  articleId?: string;
  shopId: string;
  articleCategoryId: string;
  articleSubCategoryId?: string;
  title: string;
  desc: string;
  images: MediaFileDto[];
  content: string;
  typePosition: string;
  typePublish: string;
  status: string;
  created?: string;
  updated?: string;
}

export const articleService = {
  getArticles: async <T = any>(params: GetArticleRequest, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.get<GetArticleRequestBody, T>(
        `${API_PATHS.ARTICLE.GET_ARTICLE}?skip=${params.skip}&limit=${params.limit}&shopId=${params.shopId}&search=${params.search}`,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  createArticle: async <T = any>(data: ArticleDto, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.post<any, T>(API_PATHS.ARTICLE.CREATE_ARTICLE, data, config);
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  updateArticle: async <T = any>(data: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.put<any, T>(API_PATHS.ARTICLE.UPDATE_ARTICLE, data, config);
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  updateImageArticle: async <T = any>(
    articleId: string,
    imageFile: File,
    errorConfig?: ErrorConfig
  ) => {
    const formData = new FormData();
    formData.append("ArticleId", articleId);
    formData.append("FileUpload", imageFile);

    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
      headers: {
        "Content-Type": "multipart/form-data",
      },
    };

    try {
      const response = await apiClient.post<FormData, T>(
        API_PATHS.ARTICLE.UPDATE_IMAGE_ARTICLE,
        formData,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  deleteArticle: async <T = any>(articleId: string, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.delete<T>(
        `${API_PATHS.ARTICLE.DELETE_ARTICLE_CATEGORY}?articleId=${articleId}`,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  getArticleDetail: async <T = any>(articleId: string, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.get<any, T>(
        `${API_PATHS.ARTICLE.GET_ARTICLE_DETAIL}?articleId=${articleId}`,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
};
