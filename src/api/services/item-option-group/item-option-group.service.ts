import { apiClient } from "@/src/api/config/api-client";
import { API_PATHS } from "@/src/api/constants/api-paths";
import type { ApiError, ErrorConfig } from "@/src/api/types/error.types";
import type { ExtendedRequestConfig } from "@/src/api/types/api.types";
import { GetProductRequest, GetProductRequestBody } from "@/src/api/types/product.types";
import { CreateItemGroupRequestBodyRequest } from "../../types/item-option-group.type";

function handleApiError(error: any): never {
  const status = error.response?.status || 500;
  const errorData = error.response?.data as ApiError;
  const errorMessage = errorData?.detail || errorData?.title || error.message;

  if (error.response) {
    throw {
      ...error,
      message: errorMessage,
    };
  }
  throw error;
}

export const itemOptionGroupService = {
  listItemOptionGroup: async <T = any>(params, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };
    const queryParams = new URLSearchParams({
      skip: params.skip,
      limit: params.limit,
      shopId: params?.data?.shopId,
    });

    try {
      const response = await apiClient.get(
        `${API_PATHS.ITEM_OPTION_GROUP.URL_ITEM_OPTION_GROUP}?${queryParams.toString()}`,
        params,
        config
      );

      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
  detailItemOptionGroup: async (id: string, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };
    try {
      const response = await apiClient.get(
        `${API_PATHS.ITEM_OPTION_GROUP.URL_ITEM_OPTION_GROUP}/${id}`,
        config
      );

      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
  createItemOptionGroup: async <T = any>(
    body: CreateItemGroupRequestBodyRequest,
    errorConfig?: ErrorConfig
  ) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.post(
        `${API_PATHS.ITEM_OPTION_GROUP.URL_ITEM_OPTION_GROUP}`,
        body,
        config
      );

      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  deleteItemOptionGroup: async <T = any>(id: string, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.delete(
        `${API_PATHS.ITEM_OPTION_GROUP.URL_ITEM_OPTION_GROUP}/${id}`,
        config
      );

      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  updateItemOptionGroup: async <T = any>(
    id: string,
    body: CreateItemGroupRequestBodyRequest,
    errorConfig?: ErrorConfig
  ) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.put(
        `${API_PATHS.ITEM_OPTION_GROUP.URL_ITEM_OPTION_GROUP}/${id}`,
        body,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
};
