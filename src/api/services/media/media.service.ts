import { apiClient } from "@/src/api/config/api-client";
import { API_PATHS } from "@/src/api/constants/api-paths";
import type { ApiError, ErrorConfig } from "@/src/api/types/error.types";
import type { ExtendedRequestConfig } from "@/src/api/types/api.types";
import {
  GetGroupFileRequest,
  GetFileGroupRequest,
  CreateFileGroupRequest,
  CreateGroupFileRequest,
} from "@/src/api/types/media.types";

function handleApiError(error: any): never {
  const status = error.response?.status || 500;
  const errorData = error.response?.data as ApiError;
  const errorMessage = errorData?.detail || errorData?.title || error.message;

  if (error.response) {
    throw {
      ...error,
      message: errorMessage,
    };
  }
  throw error;
}

export const mediaService = {
  getGroups: async <T = any>(params: GetGroupFileRequest, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      if (params.ShopId) {
        const queryParams = new URLSearchParams({ ShopId: params.ShopId });

        if (params.Skip != null) queryParams.append("Skip", String(params.Skip));
        if (params.Limit != null) queryParams.append("Limit", String(params.Limit));
        if (params.Search != null && params.Search !== "")
          queryParams.append("Search", params.Search);

        const url = `${API_PATHS.FILE_MANAGE.GET_GROUP_FILE}?${queryParams.toString()}`;

        const response = await apiClient.get<any, T>(url, config);
        return response;
      }
    } catch (error: any) {
      handleApiError(error);
    }
  },

  createGroup: async <T = any>(data: CreateGroupFileRequest, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.post<any, T>(
        API_PATHS.FILE_MANAGE.CREATE_GROUP_FILE,
        data,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  updateGroup: async <T = any>(data: CreateGroupFileRequest, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.put<any, T>(
        API_PATHS.FILE_MANAGE.UPDATE_GROUP_FILE,
        data,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  deleteGroup: async <T = any>(groupFileId: string, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      if (groupFileId) {
        const response = await apiClient.delete<T>(
          `${API_PATHS.FILE_MANAGE.DELETE_GROUP_FILE}?groupFileId=${groupFileId}`,
          config
        );
        return response;
      }
    } catch (error: any) {
      handleApiError(error);
    }
  },

  getFiles: async <T = any>(params: GetFileGroupRequest, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      if (params.ShopId) {
        const queryParams = new URLSearchParams({
          ShopId: params.ShopId,
        });
        if (params.GroupFileId != null)
          queryParams.append("GroupFileId", String(params.GroupFileId));
        if (params.Skip != null) queryParams.append("Skip", String(params.Skip));
        if (params.Limit != null) queryParams.append("Limit", String(params.Limit));
        if (params.Search) queryParams.append("Search", params.Search);
        if (params.RefType != null) queryParams.append("RefType", String(params.RefType));

        const url = `${API_PATHS.FILE_MANAGE.GET_FILE_GROUP}?${queryParams.toString()}`;

        const response = await apiClient.get<any, T>(url, config);
        return response;
      }
    } catch (error: any) {
      handleApiError(error);
    }
  },

  uploadFile: async <T = any>(data: CreateFileGroupRequest, errorConfig?: ErrorConfig) => {
    const formData = new FormData();
    formData.append("FileUpload", data.FileUpload);

    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
      headers: {
        "Content-Type": "multipart/form-data",
      },
    };

    try {
      const params = new URLSearchParams();
      if (data.ShopId) params.append("ShopId", data.ShopId);
      if (data.GroupFileId) params.append("GroupFileId", data.GroupFileId);
      if (data.RefType) params.append("RefType", data.RefType);
      if (data.RefId) params.append("RefId", data.RefId);

      const url = `${API_PATHS.FILE_MANAGE.CREATE_FILE_GROUP}?${params.toString()}`;

      const response = await apiClient.post<FormData, T>(url, formData, config);
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  deleteFile: async <T = any>(mediaFileId: string, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      if (mediaFileId) {
        const response = await apiClient.delete<T>(
          `${API_PATHS.FILE_MANAGE.DELETE_FILE_GROUP}?mediaFileId=${mediaFileId}`,
          config
        );
        return response;
      }
    } catch (error: any) {
      handleApiError(error);
    }
  },
};
