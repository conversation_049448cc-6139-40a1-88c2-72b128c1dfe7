import { Link } from "@mui/material";
import { apiClient } from "../../config/api-client";
import { API_PATHS } from "../../constants/api-paths";
import type { ApiError, ErrorConfig } from "../../types/error.types";
import type { ExtendedRequestConfig } from "../../types/api.types";
import { logger } from "@/src/utils/logger";
import { UpdateUserInfoBody } from "../../types/user.types";

function handleApiError(error: any): never {
  const status = error.response?.status || 500;
  const errorData = error.response?.data as ApiError;
  const errorMessage = errorData?.detail || errorData?.title || error.message;

  if (error.response) {
    throw {
      ...error,
      message: errorMessage,
    };
  }

  throw error;
}

export interface VariantImage {
  mediaFileId?: string;
  type?: string;
  link?: string;
}
export interface PagingParams {
  NameType?: string;
  SortType?: string;
  PageSize?: number;
  PageIndex?: number;
  Search?: string;
  Name?: string;
  Sort?: string;
}

export interface PriceListItemDto {
  adjustedPrice: number;
  categoryIds: string[];
  images: VariantImage[];
  isVariant: boolean;
  itemsCode: string;
  itemsId: string;
  itemsName: string;
  price: number;
  priceListId: string;
  variantImage: VariantImage;
  variantNameOne: string;
  variantNameTwo: string;
  variantNameThree: string;
  variantValueOne: string;
  variantValueTwo: string;
  variantValueThree: string;
}

export interface GetPriceListParams {
  shopId: string;
  priceListId?: string;
  fromDate?: string;
  toDate?: string;
  paging?: PagingParams;
}

export interface GetListProductParam {
  shopId: string;
  priceListId?: string;
  categoryId?: string;
  itemsType?: string;
  paging?: PagingParams;
}

export interface CreatePriceListRequest {
  shopId: string;
  priceListId?: string;
  name?: string;
  type?: string;
  adjustmentType?: string;
  adjustmentValue?: number;
  adjustmentUnit?: string;
  appliedBranchIds?: string[];
  appliedRankIds?: string[];
  appliedUserIds?: string[];
  appliedCustomerGroupIds?: string[];
  appliedProductIds?: string[];
  appliedCategoryIds?: string[];
}

export interface PriceListDto {
  id: string;
  name: string;
  type: "Price" | string;
  adjustmentType: "Increase" | "Decrease" | string;
  adjustmentUnit: "Percentage" | "Amount" | string;
  adjustmentValue: number;
  appliedBranchIds: string[];
  appliedCategoryIds: string[];
  appliedCustomerGroupIds: string[];
  appliedProductIds: string[];
  appliedRankIds: string[];
  appliedUserIds: string[];
  shopId: string;
  priceListId: string;
  isActive: boolean;
  isDeleted: boolean;
  createdBy: string;
  createdDate: string;
  modifiedBy: string | null;
  modifiedDate: string | null;
  deletedAt: string | null;
}

export interface ProductPriceListDto {
  id: string;
  createdDate: string;
  modifiedDate: string;
  createdBy: string;
  modifiedBy: string;
  isDeleted: boolean;
  deletedAt: string;
  shopId: string;
  priceListId: string;
  name: string;
  type: string;
  adjustmentType: "Increase" | "Decrease" | string;
  adjustmentValue: number;
  adjustmentUnit: "Percentage" | "Fixed" | string;
  isActive: boolean;
  appliedBranchIds: string[];
  appliedRankIds: string[];
  appliedUserIds: string[];
  appliedCustomerGroupIds: string[];
  appliedProductIds: string[];
  appliedCategoryIds: string[];
}

export const priceListService = {
  getListPriceList: async <T = any>(params: GetPriceListParams, errorConfig?: ErrorConfig) => {
    const { shopId, priceListId, fromDate, toDate, paging } = params;

    if (!shopId) throw new Error("shopId is required");

    const queryParams: any = {
      ShopId: shopId,
    };

    if (priceListId) queryParams.PriceListId = priceListId;
    if (fromDate) queryParams.FromDate = fromDate;
    if (toDate) queryParams.ToDate = toDate;

    if (paging) {
      if (paging.NameType) queryParams["Paging.NameType"] = paging.NameType;
      if (paging.SortType) queryParams["Paging.SortType"] = paging.SortType;
      if (paging.PageSize !== undefined) queryParams["Paging.PageSize"] = paging.PageSize;
      if (paging.PageIndex !== undefined) queryParams["Paging.PageIndex"] = paging.PageIndex;
      if (paging.Search) queryParams["Paging.Search"] = paging.Search;
      if (paging.Name) queryParams["Paging.Name"] = paging.Name;
      if (paging.Sort) queryParams["Paging.Sort"] = paging.Sort;
    }

    const config: any = {
      params: queryParams,
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.get<T>(API_PATHS.PRICE_LIST.PRICE_LIST_API, config);
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
  createPriceList: async <T = any>(data: CreatePriceListRequest, errorConfig?: ErrorConfig) => {
    const config: any = {
      errorHandling: errorConfig,
    };

    try {
      if (!data.shopId) {
        throw new Error("shopId is required");
      }

      const response = await apiClient.post<T>(API_PATHS.PRICE_LIST.PRICE_LIST_API, data, config);

      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  updatePriceList: async <T = any>(data: CreatePriceListRequest, errorConfig?: ErrorConfig) => {
    const config: any = {
      errorHandling: errorConfig,
    };
    try {
      if (data.shopId) {
        const response = await apiClient.put<T>(
          `${API_PATHS.PRICE_LIST.PRICE_LIST_API}`,
          data,
          config
        );

        return response;
      }
    } catch (error: any) {
      handleApiError(error);
    }
  },
  getDetailPriceList: async <T = any>(
    shopId: string,
    priceListId: string,
    errorConfig?: ErrorConfig
  ) => {
    const config: any = {
      errorHandling: errorConfig,
    };
    try {
      if (shopId && priceListId) {
        const response = await apiClient.get<T>(
          `${API_PATHS.PRICE_LIST.PRICE_LIST_API}/${shopId}/${priceListId}`,
          config
        );

        return response;
      }
    } catch (error: any) {
      handleApiError(error);
    }
  },
  deletePriceListById: async <T = any>(
    shopId: string,
    priceListId: string,
    errorConfig?: ErrorConfig
  ) => {
    const config: any = {
      errorHandling: errorConfig,
    };
    try {
      if (shopId && priceListId) {
        const response = await apiClient.delete<T>(
          `${API_PATHS.PRICE_LIST.PRICE_LIST_API}/${shopId}/${priceListId}`,
          config
        );

        return response;
      }
    } catch (error: any) {
      handleApiError(error);
    }
  },
  deleteManyPriceList: async <T = any>(
    shopId: string,
    listId: string[],
    errorConfig?: ErrorConfig
  ) => {
    const config: any = {
      errorHandling: errorConfig,
    };
    try {
      if (shopId && listId?.length > 0) {
        const response = await apiClient.delete<T>(
          `${API_PATHS.PRICE_LIST.PRICE_LIST_API}/deletemany/${shopId}`,
          listId,
          config
        );

        return response;
      }
    } catch (error: any) {
      handleApiError(error);
    }
  },
  addProductsForPriceList: async <T = any>(
    shopId: string,
    priceListId: string,
    ProductPriceListDto: string[],
    errorConfig?: ErrorConfig
  ) => {
    const config: any = {
      errorHandling: errorConfig,
    };
    try {
      if (priceListId) {
        const response = await apiClient.post<T>(
          `${API_PATHS.PRICE_LIST.UPDATE_PRODUCTS_FOR_PRICE_LIST}/${shopId}/${priceListId}`,
          ProductPriceListDto,
          config
        );

        return response;
      }
    } catch (error: any) {
      handleApiError(error);
    }
  },
  deleteProductsForPriceList: async <T = any>(
    shopId: string,
    priceListId: string,
    ProductPriceListDto: string[],
    errorConfig?: ErrorConfig
  ) => {
    const config: any = {
      data: ProductPriceListDto,
      errorHandling: errorConfig,
    };
    try {
      if (priceListId && shopId) {
        const response = await apiClient.delete<T>(
          `${API_PATHS.PRICE_LIST.UPDATE_PRODUCTS_FOR_PRICE_LIST}/${shopId}/${priceListId}`,
          config
        );

        return response;
      }
    } catch (error: any) {
      handleApiError(error);
    }
  },
  updateBranchsForPriceList: async <T = any>(
    shopId: string,
    priceListId: string,
    listBranchId: string[],
    errorConfig?: ErrorConfig
  ) => {
    const config: any = {
      errorHandling: errorConfig,
    };
    try {
      if (priceListId && shopId) {
        const response = await apiClient.put<T>(
          `${API_PATHS.PRICE_LIST.UPDATE_BRANCHS_FOR_PRICE_LIST}/${shopId}/${priceListId}`,
          listBranchId,
          config
        );

        return response;
      }
    } catch (error: any) {
      handleApiError(error);
    }
  },
  updateRanksForPriceList: async <T = any>(
    shopId: string,
    priceListId: string,
    listRankId: string[],
    errorConfig?: ErrorConfig
  ) => {
    const config: any = {
      errorHandling: errorConfig,
    };
    try {
      if (priceListId && shopId) {
        const response = await apiClient.put<T>(
          `${API_PATHS.PRICE_LIST.UPDATE_RANKS_FOR_PRICE_LIST}/${shopId}/${priceListId}`,
          listRankId,
          config
        );

        return response;
      }
    } catch (error: any) {
      handleApiError(error);
    }
  },
  setActiveStatusForPriceList: async <T = any>(
    shopId: string,
    priceListId: string,
    isActive: boolean,
    errorConfig?: ErrorConfig
  ) => {
    const config: any = {
      errorHandling: errorConfig,
    };
    try {
      if (priceListId && shopId) {
        const response = await apiClient.put<T>(
          `${API_PATHS.PRICE_LIST.ACTIVE_STATUS_FOR_PRICE_LIST}/${shopId}/${priceListId}?isActive=${isActive}`,
          config
        );

        return response;
      }
    } catch (error: any) {
      handleApiError(error);
    }
  },

  getListProductByPriceListId: async <T = any>(
    params: GetListProductParam,
    errorConfig?: ErrorConfig
  ) => {
    const { shopId, priceListId, paging, itemsType, categoryId } = params;

    if (!shopId || !priceListId) throw new Error("shopId and priceListId is required");

    const queryParams: any = {
      ShopId: shopId,
    };

    if (priceListId) queryParams.PriceListId = priceListId;
    if (itemsType) queryParams.ItemsType = itemsType;
    if (categoryId) queryParams.CategoryId = categoryId;

    if (paging) {
      if (paging.NameType) queryParams["Paging.NameType"] = paging.NameType;
      if (paging.SortType) queryParams["Paging.SortType"] = paging.SortType;
      if (paging.PageSize !== undefined) queryParams["Paging.PageSize"] = paging.PageSize;
      if (paging.PageIndex !== undefined) queryParams["Paging.PageIndex"] = paging.PageIndex;
      if (paging.Search) queryParams["Paging.Search"] = paging.Search;
      if (paging.Name) queryParams["Paging.Name"] = paging.Name;
      if (paging.Sort) queryParams["Paging.Sort"] = paging.Sort;
    }

    const config: any = {
      params: queryParams,
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.get<T>(
        API_PATHS.PRICE_LIST.GET_LIST_PRODUCT_BY_PRICE_LIST_ID,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  getListProductNotInPriceList: async <T = any>(
    params: GetListProductParam,
    errorConfig?: ErrorConfig
  ) => {
    const { shopId, priceListId, paging, itemsType, categoryId } = params;

    if (!shopId || !priceListId) throw new Error("shopId and priceListId is required");

    const queryParams: any = {
      ShopId: shopId,
    };

    if (priceListId) queryParams.PriceListId = priceListId;
    if (itemsType) queryParams.ItemsType = itemsType;
    if (categoryId) queryParams.CategoryId = categoryId;

    if (paging) {
      if (paging.NameType) queryParams["Paging.NameType"] = paging.NameType;
      if (paging.SortType) queryParams["Paging.SortType"] = paging.SortType;
      if (paging.PageSize !== undefined) queryParams["Paging.PageSize"] = paging.PageSize;
      if (paging.PageIndex !== undefined) queryParams["Paging.PageIndex"] = paging.PageIndex;
      if (paging.Search) queryParams["Paging.Search"] = paging.Search;
      if (paging.Name) queryParams["Paging.Name"] = paging.Name;
      if (paging.Sort) queryParams["Paging.Sort"] = paging.Sort;
    }

    const config: any = {
      params: queryParams,
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.get<T>(
        API_PATHS.PRICE_LIST.GET_LIST_PRODUCT_NOT_IN_PRICE_LIST,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  getListProductByTiers: async <T = any>(
    params: GetListProductParam,
    errorConfig?: ErrorConfig
  ) => {
    const { shopId, priceListId, paging } = params;

    if (!shopId || !priceListId) throw new Error("shopId and priceListId is required");

    const queryParams: any = {
      ShopId: shopId,
    };

    if (priceListId) queryParams.PriceListId = priceListId;

    if (paging) {
      if (paging.NameType) queryParams["Paging.NameType"] = paging.NameType;
      if (paging.SortType) queryParams["Paging.SortType"] = paging.SortType;
      if (paging.PageSize !== undefined) queryParams["Paging.PageSize"] = paging.PageSize;
      if (paging.PageIndex !== undefined) queryParams["Paging.PageIndex"] = paging.PageIndex;
      if (paging.Search) queryParams["Paging.Search"] = paging.Search;
      if (paging.Name) queryParams["Paging.Name"] = paging.Name;
      if (paging.Sort) queryParams["Paging.Sort"] = paging.Sort;
    }

    const config: any = {
      params: queryParams,
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.get<T>(
        API_PATHS.PRICE_LIST.GET_LIST_PRODUCT_BY_TIERS,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
};
