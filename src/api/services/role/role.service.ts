import { apiClient } from "../../config/api-client";
import { API_PATHS } from "../../constants/api-paths";
import type { ApiError, ErrorConfig } from "../../types/error.types";
import type { ExtendedRequestConfig } from "../../types/api.types";
import { logger } from "@/src/utils/logger";
import { UpdateUserInfoBody } from "../../types/user.types";
import { Interface } from "readline";

function handleApiError(error: any): never {
  const status = error.response?.status || 500;
  const errorData = error.response?.data as ApiError;
  const errorMessage = errorData?.detail || errorData?.title || error.message;

  if (error.response) {
    throw {
      ...error,
      message: errorMessage,
    };
  }

  throw error;
}

export interface FunctionObject {
  functionId: string;
  permissions: string[];
}
export interface RoleBodyCreateApi {
  roleName: string;
  roleDescription: string;
  roleIcon: string;
  functions: FunctionObject[];
}
export interface RoleBodyUpdateApi {
  roleId: string;
  body: {
    roleName: string;
    roleDescription: string;
    roleIcon: string;
    functions: FunctionObject[];
  };
}

export const roleService = {
  createRole: async <T = any>(data: RoleBodyCreateApi, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.post<T>(API_PATHS.ROLE.PARTNER_ROLE_API, data, config);

      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  updateRole: async <T = any>(data: RoleBodyUpdateApi, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.put<T>(
        `${API_PATHS.ROLE.PARTNER_ROLE_API}/${data.roleId}`,
        data.body,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  getRoleById: async <T = any>(roleId: string, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.get<T>(
        `${API_PATHS.ROLE.PARTNER_ROLE_API}/${roleId}`,
        config
      );

      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  listRole: async <T = any>(errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.get<T>(`${API_PATHS.ROLE.PARTNER_ROLE_API}`, config);
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  deleteRole: async <T = any>(roleId: string, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.delete<T>(`${API_PATHS.ROLE.PARTNER_ROLE_API}/${roleId}`, {
        ...config,
      });
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
};
