import { apiClient } from "../../config/api-client";
import { API_PATHS } from "../../constants/api-paths";
import type { ApiError, ErrorConfig } from "../../types/error.types";
import type { ExtendedRequestConfig } from "../../types/api.types";
import { logger } from "@/src/utils/logger";
import { TokenService } from "nextjs-api-lib";

function handleApiError(error: any): never {
  const status = error.response?.status || 500;
  const errorData = error.response?.data as ApiError;
  const errorMessage = errorData?.detail || errorData?.title || error.message;

  if (error.response) {
    throw {
      ...error,
      message: errorMessage,
    };
  }
  throw error;
}

export const shopSettingService = {
  getDetailShopSetting: async <T = any>(shopId: string, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };
    try {
      if (shopId) {
        const response = await apiClient.get<any, T>(
          `${API_PATHS.SHOP_SETTING.URL_SHOP_SETTING}/${shopId}`,
          config
        );
        return response;
      }
    } catch (error: any) {
      handleApiError(error);
    }
  },

  createAndUpdateShopSetting: async <T = any>(data: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };
    try {
      const response = await apiClient.post<any, T>(
        API_PATHS.SHOP_SETTING.URL_SHOP_SETTING,
        data,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  createGithubTag: async <T = any>(data: any, errorConfig?: ErrorConfig) => {
    const accessToken = TokenService.getAuthToken("partner");
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
      headers: {
        ...(accessToken ? { Authorization: `Bearer ${accessToken}` } : {}),
        "Content-Type": "application/json",
      },
    };
    try {
      const response = await apiClient.post<any, T>(
        API_PATHS.SHOP_SETTING.BUILD_MINIAPP,
        data,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
};
