import { apiClient } from "../../config/api-client";
import { API_PATHS } from "../../constants/api-paths";
import type { ApiError, ErrorConfig } from "../../types/error.types";
import type { ExtendedRequestConfig } from "../../types/api.types";
import { logger } from "@/src/utils/logger";
import { MediaFile } from "@/src/constants/file-types";

function handleApiError(error: any): never {
  const status = error.response?.status || 500;
  const errorData = error.response?.data as ApiError;
  const errorMessage = errorData?.detail || errorData?.title || error.message;

  if (error.response) {
    throw {
      ...error,
      message: errorMessage,
    };
  }

  throw error;
}
export interface IBodyUpdateShopTaxRate {
  shopId: string;
  taxRate: number;
}

export interface ShopThemeDto {
  primaryColor: string;
  secondaryColor: string;
  accentColor: string;
}

export interface ShopDto {
  shopId?: string;
  partnerId?: string;
  oaId?: string;
  businessType?: string;
  shopName?: string;
  shopSlogan?: string;
  shopDesc?: string;
  shopInfo?: string;
  shopLogo?: MediaFile;
  shopDeeplink?: string;
  referralCode?: string;
  prefixCode?: string;
  startDate?: string;
  endDate?: string;
  openTime?: string;
  closeTime?: string;
  shopTheme?: ShopThemeDto;
  shipCost?: number;
  provinceId?: string;
  provinceName?: string;
  districtId?: string;
  districtName?: string;
  wardsId?: string;
  wardsName?: string;
  address?: string;
  transportPrice?: number;
  longitude?: number;
  latitude?: number;
  active?: string;
  status?: string;
  created?: string;
  updated?: string;
  template?: string;
  enableExpressDelivery?: boolean;
  enableInShop?: boolean;
  defaultTaxRate?: number;
}

export const shopService = {
  getShop: async <T = any>(
    skip: number,
    limit: number,
    search: string = null,
    errorConfig?: ErrorConfig
  ) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.get<{ skip: number; limit: number; search: string }, T>(
        `${API_PATHS.SHOP.GET_SHOP}?skip=${skip}&limit=${limit}`,
        { skip, limit, search: search || null },
        config
      );

      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  createShop: async <T = any>(data: ShopDto, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.post<any, T>(API_PATHS.SHOP.CREATE_SHOP, data, config);

      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  updateShop: async <T = any>(data: ShopDto, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
      headers: {
        "Content-Type": "application/json",
      },
    };

    try {
      const response = await apiClient.post<any, T>(API_PATHS.SHOP.UPDATE_SHOP, data, config);

      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  detailShop: async <T = any>(shopId?: string, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.get<any, T>(
        `${API_PATHS.SHOP.DETAIL_SHOP}?shopId=${shopId}`,
        config
      );

      return response;
    } catch (error: any) {
      // handleApiError(error);
    }
  },

  deleteShop: async <T = any>(shopId: string, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.delete<T>(
        `${API_PATHS.SHOP.DELETE_SHOP}?shopId=${shopId}`,
        config
      );

      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  updateLogoShop: async <T = any>(shopId: string, logoFile: File, errorConfig?: ErrorConfig) => {
    const formData = new FormData();
    formData.append("ShopId", shopId);
    formData.append("FileUpload", logoFile);

    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
      headers: {
        "Content-Type": "multipart/form-data",
      },
    };

    try {
      const response = await apiClient.post<FormData, T>(
        `${API_PATHS.SHOP.UPDATE_LOGO_SHOP}`,
        formData,
        config
      );

      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  updateShopDelivery: async <T = any>(data, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.post<any, T>(
        API_PATHS.SHOP.UPDATE_SHOP_DELIVERY,
        data,
        config
      );

      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
  updateShopTaxRate: async <T = any>(data: IBodyUpdateShopTaxRate, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      if (data.shopId) {
        const response = await apiClient.post<any, T>(
          `${API_PATHS.SHOP.UPDATE_SHOP_TAX_RATE}/${data.shopId}`,
          data.taxRate,
          config
        );
        return response;
      }
    } catch (error: any) {
      handleApiError(error);
    }
  },
};
