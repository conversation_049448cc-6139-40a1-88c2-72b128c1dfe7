import { apiClient } from "../../config/api-client";
import { API_PATHS } from "../../constants/api-paths";
import type { ApiError, ErrorConfig } from "../../types/error.types";
import type { ExtendedRequestConfig } from "../../types/api.types";

function handleApiError(error: any): never {
  const status = error.response?.status || 500;
  const errorData = error.response?.data as ApiError;
  const errorMessage = errorData?.detail || errorData?.title || error.message;

  if (error.response) {
    throw {
      ...error,
      message: errorMessage,
    };
  }

  throw error;
}

export const syncService = {
  configSyncService: async (data: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.post(API_PATHS.SYNC_SERVICE.CONNFIG, data, config);
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
  getSyncServiceConfig: async (data: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.get(
        `${API_PATHS.SYNC_SERVICE.CONNFIG}/${data.shopId}?syncService=${data.syncService}`,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
  updateAccessCode: async (data: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.put(API_PATHS.SYNC_SERVICE.CONNFIG, data, config);
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
  deleteSyncServiceConfig: async (data: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.delete(
        `${API_PATHS.SYNC_SERVICE.CONNFIG}/${data.shopId}?syncService=${data.syncService}`,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
};
