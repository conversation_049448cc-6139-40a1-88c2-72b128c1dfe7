import { apiClient } from "../../config/api-client";
import { API_PATHS } from "../../constants/api-paths";
import type { ApiError, ErrorConfig } from "../../types/error.types";
import type { ExtendedRequestConfig } from "../../types/api.types";
import { logger } from "@/src/utils/logger";
import { UpdateUserInfoBody } from "../../types/user.types";

function handleApiError(error: any): never {
  const status = error.response?.status || 500;
  const errorData = error.response?.data as ApiError;
  const errorMessage = errorData?.detail || errorData?.title || error.message;

  if (error.response) {
    throw {
      ...error,
      message: errorMessage,
    };
  }

  throw error;
}

export interface BodyTaxInvoiceDto {
  shopId: string;
  taxInvoiceId: string;
  userId: string;
  taxPayerType: string;
  taxCode: string;
  name: string;
  address: string;
  email: string;
  phoneNumber: string;
  isDefault: boolean;
}

export const userTaxInvoiceService = {
  getListConfigTaxInvoice: async <T = any>(shopId: string, errorConfig?: ErrorConfig) => {
    const config: any = {
      errorHandling: errorConfig,
    };

    try {
      if (shopId) {
        const response = await apiClient.get<T>(
          `${API_PATHS.TAX_INVOICE.GET_LIST_CONFIG_TAX_INVOICE}/${shopId}`,
          config
        );

        return response;
      }
    } catch (error: any) {
      handleApiError(error);
    }
  },
  createConfigTaxInvoice: async <T = any>(data: BodyTaxInvoiceDto, errorConfig?: ErrorConfig) => {
    const config: any = {
      errorHandling: errorConfig,
    };
    try {
      if (data.shopId) {
        const response = await apiClient.post<T>(
          `${API_PATHS.TAX_INVOICE.CREATE_CONFIG_TAX_INVOICE}`,
          data,
          config
        );

        return response;
      }
    } catch (error: any) {
      handleApiError(error);
    }
  },

  updateConfigTaxInvoice: async <T = any>(data: BodyTaxInvoiceDto, errorConfig?: ErrorConfig) => {
    const config: any = {
      errorHandling: errorConfig,
    };
    try {
      if (data.shopId) {
        const response = await apiClient.put<T>(
          `${API_PATHS.TAX_INVOICE.UPDATE_CONFIG_TAX_INVOICE}`,
          data,
          config
        );

        return response;
      }
    } catch (error: any) {
      handleApiError(error);
    }
  },
  deleteConfigTaxInvoice: async <T = any>(
    shopId: string,
    id: string,
    errorConfig?: ErrorConfig
  ) => {
    const config: any = {
      errorHandling: errorConfig,
    };
    try {
      if (shopId && id) {
        const response = await apiClient.delete<T>(
          `${API_PATHS.TAX_INVOICE.UPDATE_CONFIG_TAX_INVOICE}/${shopId}/${id}`,
          config
        );

        return response;
      }
    } catch (error: any) {
      handleApiError(error);
    }
  },
  setDefaultConfigTaxInvoice: async <T = any>(
    shopId: string,
    userId: string,
    id: string,
    errorConfig?: ErrorConfig
  ) => {
    const config: any = {
      errorHandling: errorConfig,
    };
    try {
      if (shopId && userId && id) {
        const response = await apiClient.put<T>(
          `${API_PATHS.TAX_INVOICE.SET_DEFAULT_CONFIG_TAX_INVOICE}/${shopId}/${userId}/${id}`,
          config
        );

        return response;
      }
    } catch (error: any) {
      handleApiError(error);
    }
  },
  getInforBusinessConfigTaxInvoice: async <T = any>(taxCode: string, errorConfig?: ErrorConfig) => {
    const config: any = {
      errorHandling: errorConfig,
    };
    try {
      if (taxCode) {
        const response = await apiClient.get<T>(
          `${API_PATHS.TAX_INVOICE.GET_INFOR_BUSINESS_CONFIG_TAX_INVOICE}/${taxCode}`,
          config
        );

        return response;
      }
    } catch (error: any) {
      handleApiError(error);
    }
  },
  getInforIndividualConfigTaxInvoice: async <T = any>(
    taxCode: string,
    name: string,
    errorConfig?: ErrorConfig
  ) => {
    const config: any = {
      errorHandling: errorConfig,
    };
    try {
      if (taxCode) {
        const response = await apiClient.get<T>(
          `${API_PATHS.TAX_INVOICE.GET_INFOR_INDIVIDUAL_CONFIG_TAX_INVOICE}/${taxCode}?name=${name}`,
          config
        );

        return response;
      }
    } catch (error: any) {
      handleApiError(error);
    }
  },
};
