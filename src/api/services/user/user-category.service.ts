import { apiClient } from '@/src/api/config/api-client';
import { API_PATHS } from '@/src/api/constants/api-paths';

export const userCategoryService = {
  getUserCategory: async (params: { skip: number; limit: number; shopId: string }) => {
    const { skip, limit, shopId } = params;
    const url = `${API_PATHS.USER_CATEGORY.GET_USER_CATEGORY}?skip=${skip}&limit=${limit}&shopId=${shopId}`;
    const response = await apiClient.get(url);
    return response;
  },
};
