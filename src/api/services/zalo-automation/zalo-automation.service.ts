import { apiClient } from "../../config/api-client";
import { API_PATHS } from "../../constants/api-paths";
import type { ApiError, ErrorConfig } from "../../types/error.types";
import type { ExtendedRequestConfig } from "../../types/api.types";

function handleApiError(error: any): never {
  const status = error.response?.status || 500;
  const errorData = error.response?.data as ApiError;
  const errorMessage = errorData?.detail || errorData?.title || error.message;

  if (error.response) {
    throw {
      ...error,
      message: errorMessage,
    };
  }

  throw error;
}

export interface IBodyTemplateZns {
  shopId: string;
  templateId: string;
  templateType: string;
}

export interface IBodyTemplateZnsUpdate {
  shopId: string;
  znsId: string;
  templateType: string;
}

export interface ZnsTemplateDto {
  shopId: string;
  znsId: string;
  templateId: string;
  templateType: string;
  templateName: string;
  templateCreatedAt: string;
  status: string;
  templateQuality: string | null;
  timeout: number;
  previewUrl: string;
  templateTag: string;
  price: number;
}

export interface ZnsTemplateParam {
  name: string;
  require: boolean;
  type: string;
  maxLength: number;
  minLength: number;
  acceptNull: boolean;
}

export interface ZnsTemplateButton {
  type: number;
  title: string;
  content: string;
}

export interface DetailZnsTemplateDto {
  partnerId: string;
  shopId: string;
  znsId: string;
  templateId: string;
  templateType: string;
  templateName: string;
  templateCreatedAt: string;
  status: string;
  templateQuality: string | null;
  timeout: number;
  previewUrl: string;
  templateTag: string;
  price: number;
  listParams: ZnsTemplateParam[];
  listButtons: ZnsTemplateButton[];
  createdDate: string;
  modifiedDate: string | null;
  createdBy: string;
  modifiedBy: string | null;
  isDeleted: boolean;
  deletedAt: string | null;
  id: string;
}

export const zaloAutomationService = {
  getMessageReport: async <T = any>(filter: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      if (filter?.FromDate !== "Invalid Date" || filter?.ToDate !== "Invalid Date") {
        const response = await apiClient.post<any, T>(
          API_PATHS.ZALO_AUTOMATION.MESSAGE_REPORT,
          filter,
          config
        );
        return response;
      }
    } catch (error: any) {
      handleApiError(error);
    }
  },

  exportSentMessageReport: async <T = any>(filter: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.post<any, T>(
        API_PATHS.ZALO_AUTOMATION.EXPORT_MESSAGE_REPORT,
        filter,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  // ZNS

  getListTemplateZns: async <T = any>(
    shopId: string,
    templateName?: string,
    templateId?: string,
    errorConfig?: ErrorConfig
  ) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      if (shopId) {
        const params = new URLSearchParams();

        if (templateName) params.append("templateName", templateName);
        if (templateId) params.append("templateId", templateId);

        const queryString = params.toString();
        const url = `${API_PATHS.ZALO_AUTOMATION.API_ZALO_TEMPLATE_ZNS}/${shopId}${
          queryString ? `?${queryString}` : ""
        }`;

        const response = await apiClient.get<any, T>(url, config);
        return response;
      }
    } catch (error: any) {
      handleApiError(error);
    }
  },

  getDetailTemplateZns: async <T = any>(shopId: string, id: string, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      if (shopId && id) {
        const response = await apiClient.get<any, T>(
          `${API_PATHS.ZALO_AUTOMATION.API_ZALO_TEMPLATE_ZNS}/${shopId}/info/${id}`,
          config
        );
        return response;
      }
    } catch (error: any) {
      handleApiError(error);
    }
  },

  exportFileTemplateExcelZns: async <T = any>(
    shopId: string,
    id: string,
    errorConfig?: ErrorConfig
  ) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.get<any, T>(
        `${API_PATHS.ZALO_AUTOMATION.API_ZALO_TEMPLATE_ZNS}/${shopId}/template/${id}`,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  createTemplateZns: async <T = any>(data: IBodyTemplateZns, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.post<any, T>(
        `${API_PATHS.ZALO_AUTOMATION.API_ZALO_TEMPLATE_ZNS}`,
        data,
        config
      );
      return response;
    } catch (error: any) {
      // handleApiError(error);
    }
  },
  updateTemplateZns: async <T = any>(data: IBodyTemplateZnsUpdate, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      if (data?.shopId && data?.znsId) {
        const response = await apiClient.put<any, T>(
          `${API_PATHS.ZALO_AUTOMATION.API_ZALO_TEMPLATE_ZNS}/${data.shopId}/${data?.znsId}?templateType=${data?.templateType}`,
          data,
          config
        );
        return response;
      }
    } catch (error: any) {
      // handleApiError(error);
    }
  },

  deleteTemplateZns: async <T = any>(shopId: string, id: string, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.delete<any, T>(
        `${API_PATHS.ZALO_AUTOMATION.API_ZALO_TEMPLATE_ZNS}/${shopId}/${id}`,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
  getTemplateZnsFromZalo: async <T = any>(shopId: string, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      if (shopId) {
        const response = await apiClient.get<any, T>(
          `${API_PATHS.ZALO_AUTOMATION.API_ZALO_TEMPLATE_ZNS}/load?shopId=${shopId}`,
          config
        );
        return response;
      }
    } catch (error: any) {
      handleApiError(error);
    }
  },
};
