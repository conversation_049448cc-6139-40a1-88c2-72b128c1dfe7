import { apiClient } from "../../config/api-client";
import { API_PATHS } from "../../constants/api-paths";
import type { ApiError, ErrorConfig } from "../../types/error.types";
import type { ExtendedRequestConfig } from "../../types/api.types";
import { logger } from "@/src/utils/logger";
import { IZaloTemplate } from "../../types/zalo-template.types";
import { ZaloTemplateRequest } from "../../hooks/zalo-template/zalo-template";

function handleApiError(error: any): never {
  const status = error.response?.status || 500;
  const errorData = error.response?.data as ApiError;
  const errorMessage = errorData?.detail || errorData?.title || error.message;

  if (error.response) {
    throw {
      ...error,
      message: errorMessage,
    };
  }

  throw error;
}

export const zaloTemplateService = {
  getZaloTemplate: async <T = any>(model: ZaloTemplateRequest, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.get<{ skip: number; limit: number; search: string }, T>(
        `${API_PATHS.ZALO_TEMPLATE.LIST_TEMPLATE}?skip=${model.skip}&limit=${model.limit}&type=${model.type}&Search=${model.search}&shopId=${model.shopId}`,
        config
      );

      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
  getDetailTemplateById: async <T = any>(
    shopId: string,
    templateId: string,
    errorConfig?: ErrorConfig
  ) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      if (shopId && templateId) {
        const response = await apiClient.get<{}, T>(
          `${API_PATHS.ZALO_TEMPLATE.GET_TEMPLATE_BY_TEMPLATE_ID}/${shopId}/${templateId}`,
          config
        );

        return response;
      }
    } catch (error: any) {
      handleApiError(error);
    }
  },

  updateTemplate: async <T = any>(data: IZaloTemplate, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      if (data.shopId && data.templateId) {
        const response = await apiClient.put<{}, T>(
          `${API_PATHS.ZALO_TEMPLATE.UPDATE_TEMPLATE}`,
          data,
          config
        );

        return response;
      }
    } catch (error: any) {
      handleApiError(error);
    }
  },
  getTriggerEventsByType: async <T = any>(type: string, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.get<{ type: string }, T>(
        `${API_PATHS.ZALO_TEMPLATE.ZALO_TRIGGER}/by-type/${type}`,
        config
      );

      return response;
    } catch (error: any) {
      // handleApiError(error);
    }
  },
  getTriggerPrameter: async <T = any>(errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.get<{ type: string }, T>(
        `${API_PATHS.ZALO_TEMPLATE.ZALO_TRIGGER_PARAMETER}`,
        config
      );

      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
  getSendTypes: async <T = any>(errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.get<{ type: string }, T>(
        `${API_PATHS.ZALO_TEMPLATE.LIST_SEND_TYPE}`,
        config
      );

      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
  getButtonActions: async <T = any>(errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.get<{ type: string }, T>(
        `${API_PATHS.ZALO_TEMPLATE.LIST_BUTTON_ACTON}`,
        config
      );

      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
  getMessageTypesByTemplateType: async <T = any>(type: string, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.get<{ type: string }, T>(
        `${API_PATHS.ZALO_TEMPLATE.LIST_MESSAGE_TYPE_BY_TEMPLATE_TYPE}/${type}`,
        config
      );

      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
  createZaloTemplate: async <T = any>(data: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.post<any, T>(
        API_PATHS.ZALO_TEMPLATE.CREATE_TEMPLATE,
        data,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
  updateZaloTemplate: async <T = any>(data: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.put<any, T>(
        `${API_PATHS.ZALO_TEMPLATE.ZALO_TEMPLATE}/${data.id}`,
        data,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  deleteZaloTemplate: async <T = any>(id: string, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };
    try {
      const response = await apiClient.delete<any, T>(
        `${API_PATHS.ZALO_TEMPLATE.ZALO_TEMPLATE}/${id}`,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  changeTemplateStatus: async <T = any>(id: string, status: string, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.put<any, T>(
        `${API_PATHS.ZALO_TEMPLATE.ZALO_TEMPLATE}/${id}/changeStatus?status=${status}`,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  getZaloAuthUrl: async <T = { url: string }>(shopId: string, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.get<null, T>(
        `${API_PATHS.ZALO_TEMPLATE.GET_OAUTH_URL}/${shopId}`,
        config
      );
      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
};
