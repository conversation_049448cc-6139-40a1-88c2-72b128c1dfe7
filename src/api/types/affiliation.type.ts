export interface ReportOverviewInterface {
  accessByLink: number;
  approvedCommission: number;
  pendingCommission: number;
  revenue: number;
  successOrders: number;
  totalAccess: number;
  totalOrderByPartners: number;
  totalPartner: number;
}

export interface UserGroupCommissionConfig {
  id?: string;
  name: string;
  levelOneCommissionPercentage: number;
  levelTwoCommissionPercentage: number;
  userIds: string[];
}

export interface ItemGroupCommissionConfig {
  itemsCode: string;
  levelOneCommissionPercentage: number;
  levelTwoCommissionPercentage: number;
}

export interface BasicCommissionsConfig {
  levelOneCommissionPercentage: number;
  levelTwoCommissionPercentage: number;
  isActiveLevelTwo: boolean;
  userGroupCommissionsConfigs: UserGroupCommissionConfig[];
  itemGroupCommissionsConfigs: ItemGroupCommissionConfig[];
}

export interface AdvancedCommissionsConfig {
  isSelfReferralCommission: boolean;
  selfReferralCommissionPercentage: string;
  partnerCommExpiry: number; // ISO date string
  isAutoApproved: boolean;
  minSpendToApproved: string;
  paymentDue: number;
}

export interface CommissionsConfigData {
  basicCommissionsConfig: BasicCommissionsConfig;
  advancedCommissionsConfig: AdvancedCommissionsConfig;
  isActive: boolean;
}

export interface CommissionsConfigResponse {
  data: CommissionsConfigData;
}
export enum StatusAffiliationPartner {
  Pending,
  Actived,
  InActived,
}

export interface GetAffiliationPartnerRequest {
  ShopId: string;
  AffiliationStatus: string;
  FromDate?: string;
  ToDate?: string;
  PageSize?: number;
  PageIndex?: number;
  Search?: string;
  Name?: string;
  Sort?: string;
}

export interface UpdateAffiliationPartnerRequest {
  affiliationStatus: string;
  fullname: string;
  email?: string;
  phoneNumber?: string;
  affiliationFullName: string;
  affiliationEmail: string;
  affiliationPhoneNumber: string;
  identityCardNumber?: string;
  taxCode?: string;
  paymentType?: string;
  bankName: string;
  bankAccountNumber: string;
  referrerCode: string;
}

export interface GetCommissionOrderRequest {
  shopId: string;
  PageNumber?: number;
  PageSize?: number;
  SortBy?: string;
  SortOrder?: string;
  SearchTerm?: string;
  StartDate?: string;
  EndDate?: string;
}

export type AffiliationConversionRateType = {
  rateAddToCart: number;
  rateConversion: number;
  newCusViaLink: number;
  numbersAccessViaLink: number[]; // Mảng số nguyên
  numbersOrder: number[]; // Mảng số nguyên
};

export interface GetOverviewPartnerRequest {
  shopId: string;
  fromDate?: string;
  toDate?: string;
  searchTerm?: string;
  skip?: number;
  limit?: number;
}

export interface RecruitmentBody {
  shopId: string; // UUID
  bannerFileImage?: File; // hoặc có thể là string nếu chỉ gửi tên file hoặc đường dẫn
  content?: string;
  navBarContent?: string;
  title?: string;
}
