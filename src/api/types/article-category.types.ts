// Request Types
export interface GetArticleCategoryRequest {
  shopId: string;
  search: string;
  skip: number;
  limit: number;
}

export interface GetArticleCategoryRequestBody {
  shopId: string;
  search: string;
}

export interface CreateArticleCategoryRequest {
  shopId: string;
  name: string;
  desc: string;
  image: ImageDetail;
  typePublish: 'Publish' | 'UnPublish';
  status: 'Actived' | 'InActived';
}

export interface UpdateArticleCategoryRequest {
  articleCategoryId: string;
  shopId: string;
  name: string;
  desc: string;
  image: ImageDetail;
  typePublish: 'Publish' | 'UnPublish';
  status: 'Actived' | 'InActived';
}

// Response Types
export interface ArticleCategory {
  articleCategoryId: string;
  name: string;
  desc: string;
  image: ImageDetail;
  typePublish: 'Publish' | 'UnPublish';
  status: 'Actived' | 'InActived';
  created: string;
  updated: string;
}

export interface ImageDetail {
  type: string;
  link: string;
}
