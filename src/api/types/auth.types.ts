export type LoginProvider = 'Phone' | 'Email';

export interface LoginRequest {
  provider: LoginProvider;
  phoneNumber: string;
  email: string;
  username: string;
  password: string;
  code2FA: string;
}

export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
  user: {
    id: string;
    email: string;
    name: string;
  }
}

export interface MessageResponse {
  message: string;
}

export interface ResetPasswordRequest {
  phoneNumber: string;
  newPassword: string;
  codeOtp: string;
}

export interface VerifyOtpRequest {
  typeOtp: string;
  phoneNumber: string;
  email: string;
  codeOtp: string;
}

export interface ChangePasswordRequest {
  oldPassword: string;
  newPassword: string;
}
