export enum InvoiceProvider {
  Viettel = "Viettel",
  VNPT = "VNPT",
  Misa = "Misa",
}

export class InvoiceConfigData {
  id: string;
  invoiceConfigId: string;
  shopId: string;
  provider: InvoiceProvider;
  username: string;
  password: string;
  accessToken: string;
  tokenExpired: string;
  sellerTaxCode: string;
  sellerLegalName: string;
  templateCode: string;
  invoiceSeries: string;
  invoiceType: string;
  reservationCode: string;
  isDraft: boolean;
  isActive: boolean;
  sellerAddressLine: string;
  sellerBankAccount: string;
  sellerBankName: string;
  sellerEmail: string;
  sellerPhoneNumber: string;
}
