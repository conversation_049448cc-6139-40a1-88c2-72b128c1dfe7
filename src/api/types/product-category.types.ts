// Request Types
export type CategoryType = "Product" | "Service";

export interface GetProductCategoryRequest {
  partnerId?: string;
  shopId: string;
  parentId?: string | null;
  isHome?: boolean;
  level?: string;
  categoryType: CategoryType;
  paging?: {
    nameType: string;
    sortType: string;
    pageSize: number;
    pageIndex: number;
    search?: string;
    name: string;
    sort: string;
  };
}

export interface GetProductCategoryRequestBody {
  shopId: string;
  partnerId: string | null;
  parentId: string | null;
  categoryType: CategoryType;
  search: string;
}

export interface CreateProductCategoryRequest {
  shopId: string;
  name: string;
  desc: string;
  image: ImageDetail;
  typePublish: "Publish" | "UnPublish";
  status: "Actived" | "InActived";
}

export interface UpdateProductCategoryRequest {
  categoryId: string;
  shopId: string;
  name: string;
  desc: string;
  image: ImageDetail;
  typePublish: "Publish" | "UnPublish";
  status: "Actived" | "InActived";
}

// Response Types
export interface ProductCategory {
  categoryId: string;
  name: string;
  desc: string;
  image: ImageDetail;
  typePublish: "Publish" | "UnPublish";
  status: "Actived" | "InActived";
  created: string;
  updated: string;
}

export interface ImageDetail {
  type: string;
  link: string;
}

export interface TreeCategory {
  categoryId: string;
  categoryName: string;
  listSubCategory: TreeCategory[];
}
