export interface AhamoveConfig {
  phoneNumber: string;
  authToken: string;
}

export interface JTExpressConfig {
  customerCode: string;
  password: string;
}

export interface TransportConfig {
  ahamove: AhamoveConfig | null;
  jtExpress: JTExpressConfig | null;
  additionalSettings: Record<string, any>;
}

export interface ServiceOption {
  serviceType: TransportServiceType;
  isEnabled: boolean;
}

export interface TransportMethod {
  transportMethodId: string;
  shopId: string;
  transportCode:
    | "Ahamove"
    | "JTExpress"
    | "GHTK"
    | "GHN"
    | "ViettelPost"
    | "ViettelPay"
    | "GHTKExpress"
    | "ViettelPostExpress";
  transportName: string;
  isEnabled: boolean;
  transportConfig: TransportConfig;
  serviceOptions: ServiceOption[];
  created: string;
  updated: string;
}

export interface UpdateTransportSettingRequest {
  transportMethodId: string; // Giữ nguyên id để xác định phương thức vận chuyển
  transportConfig?: TransportConfig; // Cập nhật config của phương thức vận chuyển
  serviceOptions?: ServiceOption[]; // Cập nhật các lựa chọn dịch vụ
}

export type TransportServiceType = "Standard" | "Express";
