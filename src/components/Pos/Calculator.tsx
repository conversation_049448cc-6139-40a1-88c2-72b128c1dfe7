import { useState, useEffect } from "react"
import { Typo<PERSON>, Button, Box, TextField, styled } from "@mui/material"
import { Grid } from "@mui/material"
import BackspaceIcon from '@mui/icons-material/Backspace';
const KeypadButton = styled(Button)(({ theme }) => ({
  width: "100%",
  height: 35,
  fontSize: "1rem",
  fontWeight: "normal",
  borderRadius: "6px",
  border: "1px solid #ddd",
  backgroundColor: "#fff",
  color: "#000",
  boxShadow: "none",
  "&:hover": {
    backgroundColor: "#f5f5f5",
    boxShadow: "none",
  },
}))

const AmountButton = styled(KeypadButton)({
  color: "#000",
  justifyContent: "center",
})

const EmptyButton = styled(Button)(({ theme }) => ({
  width: "100%",
  height: 35,
  fontSize: "0.5rem",
  borderRadius: "6px",
  backgroundColor: "#f0f0f0",
  color: "#000",
  boxShadow: "none",
  "&:hover": {
    backgroundColor: "#f0f0f0",
    boxShadow: "none",
  },
}))

interface RightPanelProps {
  onClose: () => void
  initialTotalAmount?: number
  handleChangeMoney: (money: number) => void
}

declare global {
  interface Window {
    electron: {
      invoke: (channel: string, data: any) => Promise<any>
    }
  }
}

const RightPanel = ({ initialTotalAmount, handleChangeMoney }: RightPanelProps) => {
  const [totalAmount, setTotalAmount] = useState(initialTotalAmount?.toString() || "")
  const [customerPaid, setCustomerPaid] = useState("")
  const [changeMoney, setChangeMoney] = useState(0)

  useEffect(() => {
    if (initialTotalAmount !== undefined && initialTotalAmount !== null) {
      const formattedAmount = formatNumber(initialTotalAmount.toString())
      setTotalAmount(formattedAmount)
    }
  }, [initialTotalAmount])

  useEffect(() => {
    const change = getChange()
    setChangeMoney(change)
    handleChangeMoney(change)
  }, [customerPaid, totalAmount])

  const formatNumber = (num: string) => {
    const cleanedNum = num.replace(/[.,]/g, "")
    return new Intl.NumberFormat("vi-VN").format(Number(cleanedNum))
  }

  const getChange = () => {
    const paid = customerPaid || "0"
    const total = totalAmount || "0"
    const change = Number(paid.replace(/\./g, "")) - Number(total.replace(/\./g, ""))
    return change > 0 ? change : 0
  }

  const handleNumberClick = (num: string) => {
    setCustomerPaid((prev) => {
      const newValue = formatNumber((prev || "0").replace(/\./g, "") + num)
      return newValue
    })
  }

  const handleBackspace = () => {
    setCustomerPaid((prev) => {
      if (!prev || prev === "0") return ""
      const newValue = prev.replace(/\./g, "").slice(0, -1)
      return newValue ? formatNumber(newValue) : ""
    })
  }

  const keypadValues = [
    { label: "1", onClick: () => handleNumberClick("1") },
    { label: "2", onClick: () => handleNumberClick("2") },
    { label: "3", onClick: () => handleNumberClick("3") },
    { label: "100.000", onClick: () => handleNumberClick("100000"), isAmount: true },
    { label: "4", onClick: () => handleNumberClick("4") },
    { label: "5", onClick: () => handleNumberClick("5") },
    { label: "6", onClick: () => handleNumberClick("6") },
    { label: "200.000", onClick: () => handleNumberClick("200000"), isAmount: true },
    { label: "7", onClick: () => handleNumberClick("7") },
    { label: "8", onClick: () => handleNumberClick("8") },
    { label: "9", onClick: () => handleNumberClick("9") },
    { label: "500.000", onClick: () => handleNumberClick("500000"), isAmount: true },
    { label: "", onClick: () => { }, isEmpty: true },
    { label: "0", onClick: () => handleNumberClick("0") },
    { label: "", onClick: () => { }, isEmpty: true },
    { label: <BackspaceIcon />, onClick: handleBackspace, isBackspace: true },
  ]

  return (
    <Box
      sx={{
        flexGrow: 1,
        display: "flex",
        flexDirection: "column",
        height: "100%",
        maxWidth: 400,
        mx: "auto",
        px: 1.5,
      }}
    >
      <Typography
        variant="body1"
        sx={{
          fontWeight: "bold",
          textAlign: "center",
          mb: 2,
          fontSize: "1.3rem",
        }}
      >
        Xác nhận thanh toán
      </Typography>

      <Box sx={{ mb: 2 }}>
        <Box
          sx={{
            display: "flex",
            border: "1px solid #ddd",
            borderRadius: "6px",
            mb: 1,
            overflow: "hidden",
          }}
        >
          <Box
            sx={{
              width: "50%",
              p: 1,
              borderRight: "1px solid #ddd",
              display: "flex",
              alignItems: "center",
            }}
          >
            <Typography sx={{ color: "#666", fontWeight: "normal", fontSize: "0.8rem" }}>Tổng đơn</Typography>
          </Box>
          <Box
            sx={{
              width: "50%",
              p: 1,
              display: "flex",
              alignItems: "center",
              justifyContent: "flex-end",
            }}
          >
            <Typography sx={{ fontWeight: "normal" }}>{totalAmount}</Typography>
          </Box>
        </Box>

        <Box
          sx={{
            display: "flex",
            border: "1px solid #ddd",
            borderRadius: "6px",
            mb: 1,
            overflow: "hidden",
          }}
        >
          <Box
            sx={{
              width: "50%",
              p: 1,
              borderRight: "1px solid #ddd",
              display: "flex",
              alignItems: "center",
            }}
          >
            <Typography sx={{ color: "#666", fontWeight: "normal", fontSize: "0.8rem" }}>Tiền khách đưa</Typography>
          </Box>
          <Box
            sx={{
              width: "50%",
              p: 1,
              display: "flex",
              alignItems: "center",
              justifyContent: "flex-end",
            }}
          >
            <TextField
              fullWidth
              value={customerPaid}
              onChange={(e) => {
                const newValue = e.target.value.replace(/\./g, "")
                if (/^\d*$/.test(newValue)) {
                  setCustomerPaid(newValue ? formatNumber(newValue) : "")
                }
              }}
              variant="standard"
              InputProps={{
                disableUnderline: true,
              }}
              inputProps={{
                style: {
                  textAlign: "right",
                  padding: 0,
                },
              }}
              sx={{
                "& .MuiInputBase-root": {
                  height: "auto",
                },
              }}
            />
          </Box>
        </Box>

        <Box
          sx={{
            display: "flex",
            border: "1px solid #ddd",
            borderRadius: "6px",
            overflow: "hidden",
          }}
        >
          <Box
            sx={{
              width: "50%",
              p: 1,
              borderRight: "1px solid #ddd",
              display: "flex",
              alignItems: "center",
            }}
          >
            <Typography sx={{ color: "#666", fontWeight: "normal", fontSize: "0.8rem" }}>Trả lại</Typography>
          </Box>
          <Box
            sx={{
              width: "50%",
              p: 1,
              display: "flex",
              alignItems: "center",
              justifyContent: "flex-end",
            }}
          >
            <Typography sx={{ fontWeight: "normal" }}>
              {changeMoney > 0 ? formatNumber(changeMoney.toString()) : ""}
            </Typography>
          </Box>
        </Box>
      </Box>

      <Grid container spacing={1.5} >
        {keypadValues.map((keypad, index) => (
          <Grid item xs={3} key={index}>
            {keypad.isEmpty ? (
              <EmptyButton disabled />
            ) : keypad.isBackspace ? (
              <KeypadButton
                onClick={keypad.onClick}
                sx={{
                  fontSize: keypad.isAmount || !keypad.isBackspace ? "1rem" : "0.5rem",
                  backgroundColor: "#ff6e6e",
                  color: "white",
                  border: "none",
                  "&:hover": {
                    backgroundColor: "#ff5f5f",
                  }
                }}
              >
                {keypad.label}
              </KeypadButton>
            ) : keypad.isAmount ? (
              <AmountButton onClick={keypad.onClick}>{keypad.label}</AmountButton>
            ) : (
              <KeypadButton onClick={keypad.onClick}>{keypad.label}</KeypadButton>
            )}
          </Grid>
        ))}
      </Grid>
    </Box>
  )
}

export default RightPanel

