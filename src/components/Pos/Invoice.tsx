import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Divider,
  Dialog,
  DialogActions,
  DialogContent,
  List,
  ListItem,
  ListItemText,
  Stack,
  CircularProgress,
  InputAdornment,
  IconButton,
} from "@mui/material";
import { useState, useEffect, useCallback } from "react";
import { Grid } from "@mui/system";
import useSnackbar from "@/src/hooks/use-snackbar";
import { useRouter } from "next/router";
import { useUser } from "@/src/api/hooks/user/use-user";
import { useStoreId } from "@/src/hooks/use-store-id";
import { userService } from "@/src/api/services/user/user.service";
import _ from "lodash";
import { formatCurrency } from "@/src/utils/format-number";
import { CartItem } from "@/src/pages/pos";
import { useItemOption } from "@/src/api/hooks/item-option/use-item-option";
import OptionName from "./invoiceData";
import { useCart } from "@/src/api/hooks/cart/use-cart";
import { CreateOrUpdateCartData } from "@/src/api/types/cart.types";
// import OptionName from '@/src/components/Pos/invoiceData';
import { StorageService } from "nextjs-api-lib";
import InfiniteScroll from "../infinite-scroll";
import ClearIcon from "@mui/icons-material/Clear";
interface Customer {
  fullname: string;
  userId: string;
  phoneNumber: string;
  orders: CartItem[];
  point: number;
}

const buttonStyles = {
  textTransform: "none",
  bgcolor: "#f0f0f0",
  border: "1px solid #0040FF",
  color: "black",
  width: "100%",
  mb: 2,
};

interface InvoiceProps {
  selectedCustomer: Customer | null;
  setSelectedCustomer: (customer: Customer | null) => void;
  cart: any;
  setSelectedCartItemIndex: (index: number | null) => void;
  cartTotalPrice: number;
  selectedCartItemIndex: number | null;
  setCart: (cart: any) => void;
  carts: any[];
  handleClickAddNew: () => void;
  setCarts: (carts) => void;
  searchText: string;
  setSearchText: (text: string) => void;
  hasMore: boolean;
  skip: number;
  loadMoreData: (reset?: boolean) => void;
  userFound: boolean;
  setUserFound: (value: boolean) => void;
}

export default function Invoice({
  setSelectedCustomer,
  cart,
  setSelectedCartItemIndex,
  selectedCartItemIndex,
  setCart,
  carts,
  handleClickAddNew,
  setCarts,
  searchText,
  setSearchText,
  hasMore,
  skip,
  loadMoreData,
  userFound,
  setUserFound,
}: InvoiceProps) {
  const [customerName, setCustomerName] = useState("");
  const [openAddCustomer, setOpenAddCustomer] = useState(false);
  const [newCustomerName, setNewCustomerName] = useState("");
  const [newCustomerPhone, setNewCustomerPhone] = useState("");
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchAttempted, setSearchAttempted] = useState(false);
  const [searchResults, setSearchResults] = useState<Customer[]>([]);
  const { listItemOptionByIds } = useItemOption();
  const snackbar = useSnackbar();
  const router = useRouter();
  const { listUser, detailUser } = useUser();
  const storeId = useStoreId();
  const { listCart } = useCart();
  const [maxPointCanUse, setMaxPointCanUse] = useState(0);

  const { createOrUpdateCart, estimatePoint } = useCart();
  if (cart && !Array.isArray(cart.listItems)) {
    cart.listItems = [];
  }

  const handleAddCustomer = () => {
    setOpenAddCustomer(true);
  };

  const handleCloseAddCustomer = () => {
    setOpenAddCustomer(false);
    setNewCustomerName("");
    setNewCustomerPhone("");
  };

  const handleConfirmAddCustomer = async () => {
    const phoneNumberPattern = /^[0-9]{10}$/;
    if (!newCustomerName.trim()) {
      snackbar.error("Tên khách hàng không được để trống");
      return;
    }
    if (!phoneNumberPattern.test(newCustomerPhone)) {
      snackbar.error("Số điện thoại phải có 10 chữ số và chỉ chứa số");
      return;
    }

    try {
      const userData = {
        fullName: newCustomerName,
        phoneNumber: newCustomerPhone,
        shopId: storeId,
      };
      const response = await userService.createUser(userData);
      if (response && response.data) {
        const updatedCustomer = {
          ...response.data,
          fullname: newCustomerName,
          phoneNumber: newCustomerPhone,
        };
        // setCustomers((prev) => [...prev, updatedCustomer]);
        snackbar.success("Thêm khách hàng thành công");

        // Automatically fill the new customer into the input field
        setSelectedCustomer(updatedCustomer);
        setSearchText(`${updatedCustomer.fullname}(${updatedCustomer.phoneNumber})`);
        setUserFound(true);
        setSearchAttempted(false);
      }
    } catch (error) {
      console.error("Lỗi khi tạo khách hàng:", error);
      if (error.response && error.response.status === 400) {
        const errorMessage = error.response.data.detail || "Có lỗi xảy ra khi tạo khách hàng";
        snackbar.error(errorMessage);
      } else {
        snackbar.error("Số điện thoại đã được sử dụng bởi người dùng khác");
      }
    }
    setOpenAddCustomer(false);
    setNewCustomerName("");
    setNewCustomerPhone("");
  };
  const debouncedFetchCustomer = useCallback(
    _.debounce(async (searchText: string) => {
      let searchQuery = searchText;
      if (searchText.startsWith("0")) {
        searchQuery = `84${searchText.slice(1)}`;
      }
      try {
        const response = await listUser(`?search=${searchQuery}`, { shopId: storeId });
        if (response && response.data && response.data.data.length > 0) {
          setSearchResults(response.data.data);
          setUserFound(true);
        } else {
          setSearchResults([]);
          setUserFound(false);
          setSearchAttempted(true);
        }
      } catch (error) {
        console.error("Lỗi khi tìm kiếm khách hàng:", error);
        setSearchResults([]);
        setUserFound(false);
        setSearchAttempted(true);
      }
    }, 400),
    [listUser, storeId]
  );

  useEffect(() => {
    if (searchText && !userFound && !searchAttempted) {
      debouncedFetchCustomer(searchText);
    } else if (!searchText) {
      setSelectedCustomer(null);
      setSearchResults([]);
      setUserFound(false);
      setSearchAttempted(false);
    }
    return () => {
      debouncedFetchCustomer.cancel();
    };
  }, [searchText, userFound, searchAttempted, setSelectedCustomer]);

  const handleCustomerSelect = (customer: Customer) => {
    setSelectedCustomer(customer);
    setSearchResults([]);
    setSearchText(`${customer.fullname} (${customer.point} điểm) \n${customer.phoneNumber}`);
    setUserFound(true);
    setSearchAttempted(false);
  };

  const handleClearUser = () => {
    if (cart.cartId) {
      const updatedCart = {
        ...cart,
        userId: "",
      };

      createOrUpdateCart(updatedCart)
        .then((response) => {
          if (response && response.data) {
            snackbar.success("Cập nhật giỏ hàng thành công");
            const responseCart = { ...response.data };
            setCart(responseCart);

            setCarts((prevCarts) => {
              // Cập nhật phần tử nếu tồn tại
              return prevCarts.map((cartData) =>
                cartData.cartId === responseCart.cartId ? responseCart : cartData
              );
            });
          } else {
            snackbar.error("Lỗi khi cập nhật giỏ hàng");
          }
        })
        .catch((error) => {
          console.error("Lỗi API:", error);
          snackbar.error("Lỗi khi cập nhật giỏ hàng");
        });
    }

    setSelectedCustomer(null);
    setSearchText("");
    setUserFound(false);
    setSearchAttempted(false);
  };

  useEffect(() => {
    if (storeId) {
      loadMoreData(true);
    }
  }, [storeId]);

  const estimateCartPoint = async (cartId) => {
    try {
      const response = await estimatePoint({ cartId });
      if (response?.data) setMaxPointCanUse(response?.data?.pointAfterCompleteOrder);
    } catch (error) {
      console.error("Error estimating cart point:", error);
    }
  };
  useEffect(() => {
    if (cart && cart.cartId && cart.userId) {
      estimateCartPoint(cart?.cartId);
    }
  }, [cart]);


  return (
    <Grid container sx={{ height: "100vh" }} spacing={1}>
      <Grid
        size={{ xs: 12, md: 4 }}
        sx={{
          p: 1,
          display: "flex",
          flexDirection: "column",
          justifyContent: "flex-start",
          background: "white",
          boxShadow: 1,
          mt: 1,
        }}
      >
        <Stack spacing={2} sx={{ height: "calc(100vh - 60px)", overflow: "auto", padding: 1 }}>
          <Button
            variant="contained"
            sx={{ mb: 2, backgroundColor: "#2654FE" }}
            onClick={handleClickAddNew}
          >
            Thêm mới
          </Button>
          {carts?.length > 0 && (
            <InfiniteScroll
              loader={
                <Stack justifyContent={"center"} alignItems={"center"} padding={1}>
                  <CircularProgress />
                </Stack>
              }
              className=""
              fetchMore={() => loadMoreData()}
              hasMore={hasMore}
              endMessage={null}
            >
              {carts.map((cartData) => {
                return (
                  <Button
                    key={cartData.cartId}
                    fullWidth
                    variant={cartData.cartId === cart.cartId ? "contained" : "outlined"}
                    sx={{
                      mb: 2,
                      border: cartData.cartId === cart.cartId ? "none" : "1px solid #000000",
                      color: cartData.cartId === cart.cartId ? "#fff" : "#000000",
                      backgroundColor: cartData.cartId === cart.cartId ? "#2654FE" : "#fff",
                      wordBreak: "break-word",
                    }}
                    onClick={() => {
                      if (cartData.userInfo) {
                        setUserFound(true);
                        setSearchText(cartData?.userInfo);
                      } else {
                        setSearchText("");
                      }

                      setCart(cartData);
                    }}
                  >
                    <Box display="flex" flexDirection="column">
                      <Typography sx={{ fontSize: "0.6rem", ordBreak: "break-word" }}>
                        Đơn {cartData.cartNo}
                      </Typography>
                      {cartData.userId && (
                        <>
                          <Typography sx={{ fontSize: "0.6rem", ordBreak: "break-word" }}>
                            {cartData?.userFullData?.fullname}
                          </Typography>
                        </>
                      )}
                      <></>
                    </Box>
                  </Button>
                );
              })}
            </InfiniteScroll>
          )}
        </Stack>
      </Grid>
      <Grid
        size={{ xs: 12, md: 8 }}
        sx={{ display: "flex", flexDirection: "column", height: { md: "90%", xs: "auto" }, mt: 1 }}
      >
        <Box sx={{ p: 2, display: "flex", gap: 1, background: "white", boxShadow: 1 }}>
          <TextField
            fullWidth
            placeholder="Nhập tên hoặc số điện thoại khách hàng"
            variant="outlined"
            size="small"
            value={searchText}
            onChange={(e) => {
              setSearchText(e.target.value);
              setUserFound(false);
              setSearchAttempted(false);
            }}
            multiline
            minRows={1} // Số dòng tối thiểu
            maxRows={5} // Số dòng tối đa trước khi xuất hiện thanh cuộn
            InputProps={{
              endAdornment: searchText && (
                <InputAdornment position="end">
                  <IconButton
                    onClick={() => {
                      handleClearUser();
                    }}
                    edge="end"
                    sx={{ paddingRight: 1 }}
                  >
                    <ClearIcon />
                  </IconButton>
                </InputAdornment>
              ),
            }}
          />
        </Box>
        {searchResults.length > 0 && (
          <List sx={{ maxHeight: 200, overflow: "auto", background: "white", boxShadow: 1 }}>
            {searchResults.map((customer) => (
              <ListItem key={customer.userId} onClick={() => handleCustomerSelect(customer)}>
                <ListItemText
                  primary={`${customer.fullname} (${customer.point} điểm)`}
                  secondary={customer.phoneNumber}
                />
              </ListItem>
            ))}
          </List>
        )}
        {searchAttempted && searchResults.length === 0 && (
          <Box
            sx={{
              mb: 2,
              p: 2,
              border: "1px solid black",
              borderRadius: 1,
              textAlign: "center",
            }}
          >
            <Typography>Khách hàng chưa tồn tại</Typography>
            <Button
              onClick={handleAddCustomer}
              fullWidth
              sx={{
                mt: 2,
                backgroundColor: "#0040FF",
                color: "white",
                "&:hover": { backgroundColor: "#0030CC" },
              }}
            >
              Thêm
            </Button>
          </Box>
        )}
        <Box
          sx={{
            flexGrow: 1,
            overflow: "auto",
            px: 2,
            background: "white",
            boxShadow: 1,
            maxHeight: { md: "calc(100% - 200px)", xs: "auto" },
          }}
        >
          {cart.listItems && cart.listItems.length > 0 ? (
            cart.listItems.map((item: any, index: number) => (
              <Box
                key={index}
                onClick={() => setSelectedCartItemIndex(index)}
                sx={{
                  py: 2,
                  display: "flex",
                  flexDirection: "column",
                  border:
                    selectedCartItemIndex === index ? "2px solid blue" : "1px solid lightgray",
                  borderRadius: 1,
                  p: 2,
                  mb: 2,
                  cursor: "pointer",
                }}
              >
                <Box
                  sx={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}
                >
                  <Typography sx={{ fontSize: "0.75rem", flex: 1 }}>{index + 1}</Typography>
                  <Box sx={{ flex: 2 }}>
                    <Typography sx={{ fontSize: "0.75rem", fontWeight: "bold" }}>
                      {item.itemsName}
                    </Typography>
                    <Typography variant="body2" sx={{ fontSize: "0.75rem" }}>
                      {Array.from(
                        new Set(
                          [
                            item.variantValueOne,
                            item.variantValueTwo,
                            item.variantValueThree,
                          ].filter(Boolean)
                        )
                      ).join(", ")}
                    </Typography>
                    <Typography component="span">
                      <OptionName optionIds={item.extraOptions} />
                    </Typography>
                    <Typography variant="body2" sx={{ fontSize: "0.75rem" }}>
                      {item.note}
                    </Typography>
                  </Box>
                  <Typography sx={{ fontSize: "0.75rem", flex: 1, ml: 2 }}>
                    x{item.quantity}
                  </Typography>
                  <Box
                    sx={{
                      display: "flex",
                      flexDirection: "column",
                      alignItems: "flex-end",
                      flex: 2,
                    }}
                  >
                    <Typography sx={{ fontSize: "0.75rem", fontWeight: "bold" }}>
                      {formatCurrency(item.price)} đ
                    </Typography>
                    {item.priceReal > item.price && (
                      <Typography
                        variant="body2"
                        sx={{ color: "#999", textDecoration: "line-through", fontSize: "0.75rem" }}
                      >
                        {formatCurrency(item.priceReal)} đ
                      </Typography>
                    )}
                  </Box>
                </Box>
              </Box>
            ))
          ) : (
            <Box sx={{ p: 2, border: "1px solid black", borderRadius: 1, textAlign: "center" }}>
              <Typography>Giỏ hàng hiện không có sản phẩm nào</Typography>
            </Box>
          )}
        </Box>
        <Box
          sx={{ p: 2, bgcolor: "#f8f8f8", flexShrink: 0, background: "white", boxShadow: 1, mt: 1 }}
        >
          <Box sx={{ display: "flex", justifyContent: "space-between", mb: 2 }}>
            <Typography sx={{ fontSize: "0.875rem" }}>
              Tổng số lượng: {cart.listItems ? cart.listItems.length : 0} sản phẩm
            </Typography>
            <Typography sx={{ fontSize: "0.875rem", textAlign: "right" }}>
              {formatCurrency(cart.originPrice)} đ
            </Typography>
          </Box>
          <Box sx={{ display: "flex", justifyContent: "space-between", mb: 2 }}>
            <Typography sx={{ fontSize: "0.875rem" }}>
              Khuyến mãi: <span style={{ color: "#0040FF" }}>Tổng giảm giá voucher</span>
            </Typography>
            <Typography color="error" sx={{ fontSize: '0.875rem' }}>
              {formatCurrency(cart.voucherPromotionPrice || 0)} đ
            </Typography>
          </Box>
          <Box sx={{ display: "flex", justifyContent: "space-between", mb: 2 }}>
            <Typography sx={{ fontSize: "0.875rem" }}>
              Thanh toán điểm: {cart.exchangePoints > 0 ? -cart.exchangePoints : 0} Điểm
            </Typography>
            <Typography sx={{ fontSize: "0.875rem" }}>
              {cart.pointPrice > 0 ? `-${formatCurrency(cart.pointPrice)}` : 0} đ
            </Typography>
          </Box>
          <Box sx={{ display: "flex", justifyContent: "space-between", mb: 2 }}>
            <Typography sx={{ fontSize: "0.875rem" }}>
              Điểm thưởng: {maxPointCanUse} Điểm
            </Typography>
          </Box>
          <Divider sx={{ bgcolor: "black", mb: 2 }} />
          <Box sx={{ display: "flex", justifyContent: "space-between", mb: 2 }}>
            <Typography sx={{ fontSize: "0.875rem" }}>Tổng tiền:</Typography>
            <Typography color="error" variant="h6" sx={{ fontWeight: "bold", fontSize: "1rem" }}>
              {formatCurrency(cart.price)} đ
            </Typography>
          </Box>
        </Box>
        <Dialog open={openAddCustomer} onClose={handleCloseAddCustomer} maxWidth="xs" fullWidth>
          <DialogContent>
            <Box sx={{ borderRadius: 1, p: 2 }}>
              <Typography variant="subtitle1" sx={{ mb: 1 }}>
                Tên khách hàng
              </Typography>
              <TextField
                autoFocus
                margin="dense"
                type="text"
                fullWidth
                variant="outlined"
                value={newCustomerName}
                onChange={(e) => setNewCustomerName(e.target.value)}
                sx={{ mb: 2 }}
              />
              <Typography variant="subtitle1" sx={{ mb: 1 }}>
                Số điện thoại
              </Typography>
              <TextField
                margin="dense"
                type="text"
                fullWidth
                variant="outlined"
                value={newCustomerPhone}
                onChange={(e) => setNewCustomerPhone(e.target.value)}
              />
            </Box>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseAddCustomer} color="primary">
              Đóng
            </Button>
            <Button onClick={handleConfirmAddCustomer} color="primary">
              Xác nhận
            </Button>
          </DialogActions>
        </Dialog>
      </Grid>
    </Grid>
  );
}
