import {
  Box,
  Tabs,
  Tab,
  Typography,
  Paper,
  Button,
  Badge,
  Grid,
  TablePagination,
  Dialog,
  DialogContent,
  DialogActions,
  DialogTitle,
  DialogContentText,
  styled,
} from "@mui/material";
import { LocalShipping, Inventory2 } from "@mui/icons-material";
import { useState, useEffect } from "react";
import LeftPanel from "./Bill";
import TitleDialog from "src/components/dialog/TitleDialog";
import { orderService } from "@/src/api/services/order/order.service";
import { useOrder } from "@/src/api/hooks/order/use-order";
import useSnackbar from "@/src/hooks/use-snackbar";
import { useStoreId } from "@/src/hooks/use-store-id";
import { ListOrderStatus } from "@/src/api/types/order.type";
import dayjs from "dayjs";

export const enum OrderTab {
  ALL = 0,
  COUNTER = 1,
  DELIVERY = 2,
  SUCCESS = 3,
  CANCEL = 4,
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: OrderTab;
  value: OrderTab;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

interface OrderManagementProps {
  initialTab: OrderTab;
  onBadgeUpdate?: (counter: boolean, delivery: boolean, all: boolean) => void;
}

export default function OrderManagement({ initialTab, onBadgeUpdate }: OrderManagementProps) {
  const [value, setValue] = useState<OrderTab>(initialTab);
  const [orders, setOrders] = useState<any[]>([]);
  const [open, setOpen] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<any>(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const [isLeftPanelOpen, setIsLeftPanelOpen] = useState(false);
  const [openPopup, setOpenPopup] = useState(false);

  // State cho popup xác nhận hoàn thành (Dialog thường)
  const [openCompletePopup, setOpenCompletePopup] = useState(false);
  const [orderToComplete, setOrderToComplete] = useState<any>(null);

  const [hasNewOrdersAll, setHasNewOrdersAll] = useState(false);
  const [hasNewOrdersCounter, setHasNewOrdersCounter] = useState(false);
  const [hasNewOrdersDelivery, setHasNewOrdersDelivery] = useState(false);
  const [hasNewOrdersSuccess, setHasNewOrdersSuccess] = useState(false);
  const [hasNewOrdersCancel, setHasNewOrdersCancel] = useState(false);

  const { updatePaidOrder, completeShippingItems, getOrder } = useOrder();
  const snackbar = useSnackbar();
  const storeId = useStoreId();
  const [tabOrders, setTabOrders] = useState<{ [key in OrderTab]?: any[] }>({});

  const handleOpenPopup = () => {
    setOpenPopup(true);
  };
  const LargeBadge = styled(Badge)(({ theme }) => ({
    "& .MuiBadge-dot": {
      height: "10px",
      minWidth: "10px",
      borderRadius: "6px",
    },
  }));
  const handleClosePopup = () => {
    setOpenPopup(false);
  };

  useEffect(() => {
    setValue(initialTab);
  }, [initialTab]);

  const fetchOrders = async (currentTab: OrderTab) => {
    try {
      const branch = localStorage.getItem("selectedBranch");
      if (!branch) {
        console.error("No branch selected");
        return;
      }
      const selectedBranch = JSON.parse(branch);

      // Lấy tất cả các đơn để tính badge
      const paramsBadge = {
        skip: 0,
        limit: 1000,
        data: { shopId: selectedBranch.shopId, branchId: selectedBranch.branchId },
      };
      const responseBadge = await orderService.listOrder(paramsBadge);
      if (responseBadge && responseBadge.data) {
        const allOrders = Array.isArray(responseBadge.data.data) ? responseBadge.data.data : [];
        const today = dayjs().format("YYYY-MM-DD");

        const newOrdersAll = allOrders.some(
          (order) => order.statusOrder === "Pending" && order.created.startsWith(today)
        );
        const newOrdersCounter = allOrders.some(
          (order) =>
            order.statusOrder === "Pending" && order.created.startsWith(today) && order.partnerId
        );
        const newOrdersDelivery = allOrders.some(
          (order) =>
            order.statusOrder === "Pending" && order.created.startsWith(today) && !order.partnerId
        );
        const newOrdersSuccess = allOrders.some(
          (order) => order.statusOrder === "Success" && order.created.startsWith(today)
        );
        const newOrdersCancel = allOrders.some(
          (order) => order.statusOrder === "Failed" && order.created.startsWith(today)
        );

        setHasNewOrdersAll(newOrdersAll);
        setHasNewOrdersDelivery(newOrdersDelivery);
        setHasNewOrdersCounter(newOrdersCounter);
        setHasNewOrdersSuccess(newOrdersSuccess);
        setHasNewOrdersCancel(newOrdersCancel);
      }

      const paramsDisplay = {
        skip: page * rowsPerPage,
        limit: rowsPerPage,
        data: {
          shopId: selectedBranch.shopId,
          branchId: selectedBranch.branchId,
          orderSource: "",
          statusOrder: "",
        },
      };
      if (currentTab === OrderTab.ALL) {
        paramsDisplay.data.statusOrder = "Pending";
      } else if (currentTab === OrderTab.COUNTER) {
        paramsDisplay.data.orderSource = "Partner";
        paramsDisplay.data.statusOrder = "Pending";
      } else if (currentTab === OrderTab.DELIVERY) {
        paramsDisplay.data.orderSource = "User";
        paramsDisplay.data.statusOrder = "Pending";
      } else if (currentTab === OrderTab.SUCCESS) {
        paramsDisplay.data.statusOrder = "Success";
      } else if (currentTab === OrderTab.CANCEL) {
        paramsDisplay.data.statusOrder = "Failed";
      }
      const response = await orderService.listOrder(paramsDisplay);
      if (response && response.data) {
        const fetchedOrders = Array.isArray(response.data.data) ? response.data.data : [];
        setOrders(fetchedOrders);
        setTabOrders((prevTabOrders) => ({ ...prevTabOrders, [currentTab]: fetchedOrders }));
        setTotalCount(response.data.total || 0);
      } else {
        setOrders([]);
        setTabOrders((prevTabOrders) => ({ ...prevTabOrders, [currentTab]: [] }));
      }
    } catch (error) {
      console.error("Failed to fetch orders:", error);
      setOrders([]);
      setTabOrders((prevTabOrders) => ({ ...prevTabOrders, [currentTab]: [] }));
    }
  };

  useEffect(() => {
    fetchOrders(value);
    const interval = setInterval(() => {
      fetchOrders(value);
    }, 5000);
    return () => clearInterval(interval);
  }, [page, rowsPerPage, value]);

  const handleChange = (event: React.SyntheticEvent, newValue: OrderTab) => {
    setPage(0);
    setValue(newValue);
    if (tabOrders[newValue]) {
      setOrders(tabOrders[newValue]!);
    } else {
      fetchOrders(newValue);
    }
  };

  const handleOpenDialog = async (order: any) => {
    try {
      const response = await orderService.getOrder({
        orderId: order.orderId,
        shopId: order.shopId,
      });
      setSelectedOrder(response.data);
      setOpen(true);
    } catch (error) {
      console.error("Failed to fetch order details:", error);
    }
  };

  // Hàm hoàn thành đơn hàng
  const handleClickUpdateStatusOrder = async (order: any) => {
    try {
      const branch = localStorage.getItem("selectedBranch");
      if (!branch) {
        console.error("No branch selected");
        return;
      }
      const selectedBranch = JSON.parse(branch);

      // Nếu đơn chưa thanh toán
      if (order.statusPay !== "Paid") {
        const dataPay = {
          shopId: storeId,
          orderId: order.orderId,
          branchId: selectedBranch.branchId,
          updateAction: "StatusPay",
        };
        const responsePay = await updatePaidOrder(dataPay);
        if (!responsePay?.data) {
          snackbar.error("Thanh toán đơn hàng thất bại");
          return;
        }
      }

      // Gọi API cập nhật trạng thái đơn hàng
      const data = {
        shopId: storeId,
        orderId: order.orderId,
        branchId: selectedBranch.branchId,
        updateAction: "StatusDelivery",
      };

      const response1 = await completeShippingItems(data);
      if (response1?.data) {
        const response2 = await updatePaidOrder(data);
        if (response2?.data) {
          const responseOrder = await getOrder({ orderId: order.orderId, shopId: order.shopId });
          if (responseOrder?.data) {
            // Cập nhật lại danh sách orders
            setOrders((prevOrders) =>
              prevOrders.map((o) =>
                o.orderId === order.orderId ? { ...o, statusOrder: "Success" } : o
              )
            );
            setSelectedOrder(responseOrder.data);
            snackbar.success("Cập nhật thông tin đơn hàng thành công");
            // Gọi lại fetchOrders để tính lại badge
            fetchOrders(value);
          }
        }
      }
    } catch (error) {
      console.error("Failed to update order status:", error);
      snackbar.error("Cập nhật trạng thái đơn hàng thất bại");
    }
  };

  const handleCloseDialog = () => {
    setOpen(false);
    setSelectedOrder(null);
  };

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };
  const translatePaymentMethod = (method: string) => {
    switch (method) {
      case "Transfer":
        return "Chuyển khoản";
      case "COD":
        return "Thanh toán khi nhận hàng";
      case "Vnpay":
        return "VNPAY";
      case "Momo":
        return "MOMO";
      case "Zalo":
        return "Zalo";
      case "Other":
        return "Khác";
      default:
        return "Không xác định";
    }
  };
  const translateStatus = (status: string) => {
    switch (status) {
      case "Pending":
        return "Đang chờ";
      case "InProgress":
        return "Đang xử lý";
      case "WaitingForDelivery":
        return "Chờ giao hàng";
      case "Delivering":
        return "Đang giao hàng";
      case "Success":
        return "Hoàn thành";
      case "Failed":
        return "Hủy";
      case "Refund":
        return "Hoàn tiền";
      default:
        return status;
    }
  };

  const renderOrderIcon = (order: any) => (
    <Box
      sx={{
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        width: 70,
        height: "100%",
        borderRadius: 1,
        bgcolor: order.partnerId ? "#FF9800" : "#20C997",
        position: "absolute",
        left: 0,
        top: 0,
        bottom: 0,
      }}
    >
      {order.partnerId ? (
        <Inventory2 sx={{ color: "#fff", fontSize: 24 }} />
      ) : (
        <LocalShipping sx={{ color: "#fff", fontSize: 24 }} />
      )}
    </Box>
  );
  const printBill = (printContentId: string) => {
    const printContent = document.getElementById(printContentId)?.innerHTML;
    if (printContent) {
      const WindowPrt = window.open("", "", "width=900,height=650");
      if (WindowPrt) {
        WindowPrt.document.write(`
          <html>
            <head>
              <title>In Bill</title>
              <style>
                @media print {
                  .detachable-label {
                    display: none;
                  }
                }
              </style>
            </head>
            <body>
              ${printContent}
            </body>
          </html>
        `);
        WindowPrt.document.close();
        WindowPrt.focus();
        WindowPrt.print();
        WindowPrt.close();
      }
    }
  };

  const printLabels = (selectedOrder: any) => {
    const labelContents = selectedOrder?.listItems
      .flatMap((item: any, index: number) => {
        return Array.from({ length: item.quantity || 1 }).map((_, quantityIndex) => {
          const labelContent = document.getElementById(
            `print-label-content-${index}-${quantityIndex}`
          )?.innerHTML;
          if (!labelContent) {
            console.warn(
              `Không tìm thấy nội dung in cho ID: print-label-content-${index}-${quantityIndex}`
            );
          }
          return labelContent ? `<div class="page">${labelContent}</div>` : null;
        });
      })
      .filter(Boolean);

    if (labelContents?.length === 0) {
      console.error("Không tìm thấy nội dung in");
      return;
    }

    if (window.electron && typeof window.electron.invoke === "function") {
      window.electron
        .invoke("print-label", labelContents)
        .catch((error) => console.error("Lỗi khi in tem qua Electron:", error));
    } else {
      console.warn("Electron API không khả dụng, chuyển sang in truyền thống.");
      const printWindow = window.open("", "", "width=600,height=400");
      if (!printWindow) {
        console.error("Không thể mở cửa sổ in. Có thể bị chặn bởi trình duyệt.");
        return;
      }
      printWindow.document.write(`
        <html>
          <head>
            <title>In Tem</title>
            <style>
              @page {
                margin: 25mm 20mm 20mm 20mm;
              }
              body {
                font-family: Arial, sans-serif;
                font-size: 8px;
                margin: 0;
                padding: 10px;
              }
              .page {
                page-break-after: always; 
                margin: 0;
                padding: 10px;
              }
            </style>
          </head>
          <body>
            ${labelContents.join("")}
          </body>
        </html>
      `);
      printWindow.document.close();
      printWindow.focus();
      printWindow.print();
      printWindow.close();
    }
  };
  const orderList = Array.isArray(orders)
    ? orders.map((order, index) => (
      <Paper
        key={index}
        elevation={0}
        sx={{
          p: 2,
          mb: 2,
          display: "flex",
          alignItems: "center",
          gap: 2,
          position: "relative",
          pl: 10,
          border: "1px solid #D3D3D3",
          borderRadius: 1,
        }}
      >
        {renderOrderIcon(order)}
        <Grid container alignItems="center" spacing={2}>
          <Grid item xs={12} sm={3} md={2}>
            <Box sx={{ display: "flex", flexDirection: "column", alignItems: "start" }}>
              <Typography fontWeight="bold">{order.orderNo}</Typography>
              <Typography>{order.creator?.fullName || "Khách hàng vãng lai"}</Typography>
              <Typography>{order.creator?.phoneNumber}</Typography>
            </Box>
          </Grid>
          <Grid item xs={12} sm={3} md={3}>
            <Typography variant="body1" color="text.secondary" sx={{ ml: 8 }}>
              {translateStatus(order.statusOrder)}
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ ml: 7 }}>
              {order.statusPay === "Paid" ? "Đã thanh toán" : "Chưa thanh toán"}
            </Typography>
          </Grid>
          <Grid item xs={12} sm={3} md={3}>
            <Box sx={{ display: "flex", flexDirection: "column", alignItems: "center" }}>
              <Typography variant="body1" color="text.secondary">
                {new Date(order.created).toLocaleString()}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {translatePaymentMethod(order.typePay)}
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12} sm={3} md={4}>
            <Grid container spacing={1}>
              {order.statusOrder !== "Failed" && (
                <Grid item xs={12} sm={4}>
                  {order.statusOrder !== "Success" ? (
                    <Button
                      sx={{
                        height: 70,
                        width: "100%",
                        bgcolor: "#4A89FF",
                        color: "#fff",
                        fontWeight: "bold",
                        boxShadow: "none",
                        "&:hover": { boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.2)" },
                      }}
                      onClick={() => {
                        setOrderToComplete(order);
                        setOpenCompletePopup(true);
                      }}
                    >
                      Hoàn thành
                    </Button>
                  ) : (
                    <Box sx={{ height: 70, width: "100%" }} />
                  )}
                </Grid>

              )}
              {/* <Grid item xs={12} sm={4}>
                <Button
                  sx={{
                    height: 70,
                    width: '100%',
                    bgcolor: (value === OrderTab.DELIVERY || (value === OrderTab.ALL && !order.partnerId))
                      ? '#4A89FF'
                      : '#DBEF28',
                    color: (value === OrderTab.DELIVERY || (value === OrderTab.ALL && !order.partnerId))
                      ? '#fff'
                      : '#2E2E2E',
                    fontWeight: 'bold',
                    boxShadow: 'none',
                    '&:hover': { boxShadow: '0px 4px 10px rgba(0, 0, 0, 0.2)' },
                  }}
                  onClick={handleOpenPopup}
                >
                  {(value === OrderTab.DELIVERY || (value === OrderTab.ALL && !order.partnerId))
                    ? 'Gọi ship'
                    : 'Thông báo lần 1'}
                </Button>
              </Grid> */}
              {order.statusOrder !== "Failed" && order.statusOrder !== "Success" && (
                <Grid item xs={12} sm={4}>
                  <Button
                    sx={{
                      height: 70,
                      width: "100%",
                      bgcolor: "#FF4A4A",
                      color: "#fff",
                      fontWeight: "bold",
                      boxShadow: "none",
                      "&:hover": { boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.2)" },
                    }}
                    onClick={async () => {
                      try {
                        const data = {
                          shopId: storeId,
                          orderId: order.orderId,
                          updateAction: "CancelOrder",
                        };
                        const response = await orderService.cancelOrder(data);
                        if (response?.data) {
                          snackbar.success("Đơn hàng đã được hủy thành công");
                          fetchOrders(value);
                        } else {
                          snackbar.error("Hủy đơn hàng thất bại");
                        }
                      } catch (error) {
                        console.error("Failed to cancel order:", error);
                        snackbar.error("Hủy đơn hàng thất bại");
                      }
                    }}
                  >
                    Hủy đơn hàng
                  </Button>
                </Grid>
              )}

              {order.statusOrder !== "Failed" && (
                <Grid item xs={12} sm={4}>
                  <Button
                    sx={{
                      height: 70,
                      width: "100%",
                      bgcolor: "#D1D1D1",
                      color: "#2E2E2E",
                      fontWeight: "bold",
                      boxShadow: "none",
                      "&:hover": { boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.2)" },
                    }}
                    onClick={() => {
                      setSelectedOrder(order);
                      setIsLeftPanelOpen(true);
                    }}
                  >
                    In đơn hàng
                  </Button>
                </Grid>
              )}
            </Grid>
          </Grid>
        </Grid>
      </Paper>
    ))
    : null;

  return (
    <Box sx={{ margin: "auto", mt: 4, px: 2 }}>
      <Box sx={{ borderBottom: 1, borderColor: "divider", width: "100%", margin: "auto" }}>
        <Tabs
          value={value}
          onChange={handleChange}
          aria-label="order tabs"
          variant="scrollable"
          scrollButtons="auto"
        >
          <Tab
            label={
              <LargeBadge color="error" variant={hasNewOrdersAll ? "dot" : "standard"}>
                Chờ xác nhận (ALL)
              </LargeBadge>
            }
            sx={{
              color: value === OrderTab.ALL ? "primary.main" : "inherit",
              fontWeight: value === OrderTab.ALL ? "bold" : "normal",
              fontSize: "1.2rem",
            }}
          />
          <Tab
            label={
              <LargeBadge color="error" variant={hasNewOrdersCounter ? "dot" : "standard"}>
                Order tại quầy
              </LargeBadge>
            }
            sx={{ fontSize: "1.2rem" }}
          />
          <Tab
            label={
              <LargeBadge color="error" variant={hasNewOrdersDelivery ? "dot" : "standard"}>
                Giao đi
              </LargeBadge>
            }
            sx={{ fontSize: "1.2rem" }}
          />
          <Tab
            label={
              <LargeBadge color="error" variant={hasNewOrdersSuccess ? "dot" : "standard"}>
                Hoàn thành
              </LargeBadge>
            }
            sx={{ fontSize: "1.2rem" }}
          />
          <Tab
            label={
              <LargeBadge color="error" variant={hasNewOrdersCancel ? "dot" : "standard"}>
                Đã huỷ
              </LargeBadge>
            }
            sx={{ fontSize: "1.2rem" }}
          />
        </Tabs>
      </Box>

      <Box sx={{ height: 400, overflow: "auto" }}>
        <TabPanel value={value} index={OrderTab.ALL}>
          {orderList}
        </TabPanel>
        <TabPanel value={value} index={OrderTab.COUNTER}>
          {orderList}
        </TabPanel>
        <TabPanel value={value} index={OrderTab.DELIVERY}>
          {orderList}
        </TabPanel>
        <TabPanel value={value} index={OrderTab.SUCCESS}>
          {orderList}
        </TabPanel>
        <TabPanel value={value} index={OrderTab.CANCEL}>
          {orderList}
        </TabPanel>
      </Box>

      <TablePagination
        component="div"
        count={totalCount}
        page={page}
        onPageChange={handleChangePage}
        rowsPerPage={rowsPerPage}
        onRowsPerPageChange={handleChangeRowsPerPage}
        rowsPerPageOptions={[5, 10, 20]}
        labelRowsPerPage="Số dòng mỗi trang"
      />

      {isLeftPanelOpen && (
        <Dialog
          open={isLeftPanelOpen}
          onClose={() => setIsLeftPanelOpen(false)}
          maxWidth="md"
          fullWidth
        >
          <DialogContent>
            <LeftPanel changeAmount={0} showLabels={true} cart={null} order={selectedOrder} />
          </DialogContent>
          <DialogActions
            className="no-print"
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <Button onClick={() => setIsLeftPanelOpen(false)} color="primary" variant="contained">
              Đóng
            </Button>
            <Box sx={{ display: "flex", gap: 2 }}>
              <Button
                onClick={() => printBill("print-invoice")}
                color="secondary"
                variant="contained"
              >
                In Bill
              </Button>
              <Button
                onClick={() => printLabels(selectedOrder)}
                sx={{ backgroundColor: "#FF0F0F" }}
                variant="contained"
              >
                In Tem
              </Button>
            </Box>
          </DialogActions>
        </Dialog>
      )}

      <TitleDialog
        open={openPopup}
        handleClose={handleClosePopup}
        title="Chức năng đang phát triển"
        closeBtnTitle="Đóng"
        showActionDialog={false}
      >
        <DialogActions sx={{ justifyContent: "flex-end", p: 0 }}>
          <Button onClick={handleClosePopup} variant="contained" color="primary">
            Đóng
          </Button>
        </DialogActions>
      </TitleDialog>

      <Dialog
        open={openCompletePopup}
        onClose={() => setOpenCompletePopup(false)}
        maxWidth="xs"
        fullWidth
      >
        <DialogTitle>Bạn có chắc muốn hoàn thành đơn hàng không?</DialogTitle>
        <DialogContent></DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenCompletePopup(false)} color="secondary" variant="outlined">
            Hủy
          </Button>
          <Button
            onClick={async () => {
              if (orderToComplete) {
                await handleClickUpdateStatusOrder(orderToComplete);
              }
              setOpenCompletePopup(false);
            }}
            color="primary"
            variant="contained"
          >
            Xác nhận
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}
