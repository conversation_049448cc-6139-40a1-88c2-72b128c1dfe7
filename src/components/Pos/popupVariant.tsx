import {
  Dialog,
  DialogTitle,
  <PERSON>alog<PERSON>ontent,
  <PERSON><PERSON>,
  TextField,
  ToggleButton,
  ToggleButtonGroup,
  Typography,
  Box,
  Divider,
  Grid,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import { useEffect, useState } from 'react';
import useSnackbar from '@/src/hooks/use-snackbar';
import ItemOptionsComponent from '@/src/pages/pos/item_option';
import { formatCurrency } from '@/src/utils/format-number';

const StyledDialog = styled(Dialog)(({ theme }) => ({
  '& .MuiDialog-paper': {
    borderRadius: '8px',
    maxWidth: 700,
  },
}));

const StyledToggleButton = styled(ToggleButton)(({ theme }) => ({
  backgroundColor: '#E8E8E8',
  border: 'none',
  borderRadius: '4px !important',
  margin: '0 4px',
  flex: 100,
  maxWidth: 100,
  '&.Mui-selected': {
    backgroundColor: '#BECCFF !important',
    color: '#000 !important',
  },
  '&:hover': {
    backgroundColor: '#E0E0E0',
  },
}));

const StyledToggleButtonGroup = styled(ToggleButtonGroup)(({ theme }) => ({
  display: 'flex',
  flexWrap: 'wrap',
  gap: '8px',
  justifyContent: 'flex-start',
  '& .MuiToggleButton-root': {
    flex: 100,
    maxWidth: 100,
  },
}));

const dialogTitleStyles = {
  p: 2,
  color: '#4255FF',
  fontSize: '16px',
  fontWeight: 'normal',
};

const productBoxStyles = {
  display: 'flex',
  justifyContent: 'space-between',
  mb: 2,
  backgroundColor: '#BECCFF',
  p: 2,
};

const textFieldStyles = {
  '& .MuiOutlinedInput-root': {
    backgroundColor: '#F5F5F5',
  },
};

const cancelButtonStyles = {
  bgcolor: '#9E9E9E',
  '&:hover': {
    bgcolor: '#7E7E7E',
  },
  height: '100%',
};

const submitButtonStyles = {
  bgcolor: '#4255FF',
  '&:hover': {
    bgcolor: '#2654FE',
  },
  height: '100%',
};

interface ProductOptionsDialogProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (itemToAdd: any) => void;
  product: {
    listVariant: any;
    price: number;
    itemsId: string;
    itemsName: string;
    itemsCode: string;
    priceReal: number;
    partnerId: string;
    shopId: string;
    categoryId: string;
    image: string;
    variants: {
      price: any;
      priceReal: any;
      itemsId: any;
      variantNameOne: string;
      variantValueOne: string;
      variantNameTwo: string;
      variantValueTwo: string;
      variantNameThree: string;
      variantValueThree: string;
      quantity: number;
    }[];
    extraItemOptionGroups: string[];
  };
}

export default function ProductOptionsDialog({
  open,
  onClose,
  onSubmit,
  product,
}: ProductOptionsDialogProps) {
  const [propertiesOne, setPropertiesOne] = useState('');
  const [propertiesTwo, setPropertiesTwo] = useState('');
  const [propertiesThree, setPropertiesThree] = useState('');
  const [note, setNote] = useState('');
  const snackbar = useSnackbar();
  const [selectedItemOptions, setSelectedItemOptions] = useState([]);
  const [selectedVariant, setSelectedVariant] = useState(null);
  const [validationErrors, setValidationErrors] = useState({
    variant: '',
    options: '',
  });

  const [quantity, setQuantity] = useState<number>(0);

  useEffect(() => {
    setPropertiesOne(product?.variants[0]?.variantValueOne || '');
    setPropertiesTwo(product?.variants[0]?.variantValueTwo || '');
    setPropertiesThree(product?.variants[0]?.variantValueThree || '');
    setSelectedVariant(product?.variants[0] || null);
    setQuantity(product?.variants[0]?.quantity || 0);
  }, [product]);

  const calculateSalePrice = () => {
    const extraOptionPrice = selectedItemOptions.reduce(
      (acc, group) =>
        acc +
        group.itemOptions
          .filter((opt) => opt.isSelected)
          .reduce((sum, opt) => sum + (opt.price || 0), 0),
      0
    );
    return (
      (selectedVariant
        ? selectedVariant.price
        : product.listVariant && product.listVariant[0]
          ? product.listVariant[0].price
          : product.price) + extraOptionPrice
    );
  };

  const salePrice = calculateSalePrice();

  const handleItemOptionsChange = (updatedOptions) => {
    setSelectedItemOptions(updatedOptions);
  };

  const onChooseProperty = (propertyOne, propertyTwo, propertyThree) => {
    const listVariant = product?.variants;

    if (!listVariant) {
      console.warn('⚠️ Không có danh sách biến thể!');
      return;
    }

    const matchedVariant = listVariant.find(
      (variant) =>
        variant.variantValueOne === propertyOne &&
        (propertyTwo === undefined || variant.variantValueTwo === propertyTwo) &&
        (propertyThree === undefined || variant.variantValueThree === propertyThree)
    );
    if (matchedVariant) {
      setQuantity(matchedVariant.quantity);
    }

    setSelectedVariant(matchedVariant || null);
  };

  const uniquePropertiesOne = Array.from(
    new Set(product?.variants.map((variant) => variant.variantValueOne))
  );
  const uniquePropertiesTwo = Array.from(
    new Set(product?.variants.map((variant) => variant.variantValueTwo))
  );
  const uniquePropertiesThree = Array.from(
    new Set(product?.variants.map((variant) => variant.variantValueThree))
  );

  const handleSubmit = () => {
    if (quantity <= 0) {
      setValidationErrors((prevErrors) => ({
        ...prevErrors,
        options: `Sản phẩm đã hết hàng!`,
      }));

      return;
    }
    const unselectedRequiredGroups = selectedItemOptions
      .filter((group) => group.require)
      .filter((group) => {
        const hasSelection = group.itemOptions.some((opt) => opt.isSelected);
        return !hasSelection;
      })
      .map((group) => group.name);

    if (unselectedRequiredGroups.length > 0) {
      setValidationErrors((prevErrors) => ({
        ...prevErrors,
        options: `Vui lòng chọn ${unselectedRequiredGroups.join(
          ', '
        )} trước khi thêm vào giỏ hàng!`,
      }));
      return;
    } else {
      setValidationErrors((prevErrors) => ({ ...prevErrors, options: '' }));
    }

    if (!selectedVariant) {
      setValidationErrors((prevErrors) => ({
        ...prevErrors,
        variant: 'Vui lòng chọn biến thể trước khi thêm sản phẩm vào giỏ hàng!',
      }));
      return;
    } else {
      setValidationErrors((prevErrors) => ({ ...prevErrors, variant: '' }));
    }

    const extraOptions = selectedItemOptions.reduce((acc, group) => {
      const selectedIds = group.itemOptions
        .filter((opt) => opt.isSelected)
        .map((opt) => opt.itemOptionId);
      return [...acc, ...selectedIds];
    }, []);

    const itemToAdd = {
      itemsId: selectedVariant.itemsId,
      variantImage: { Type: 'IMAGE', Link: product.image },
      variantNameOne: selectedVariant.variantNameOne || '',
      variantValueOne: propertiesOne,
      variantNameTwo: selectedVariant.variantNameTwo || '',
      variantValueTwo: propertiesTwo,
      variantNameThree: selectedVariant.variantNameThree || '',
      variantValueThree: propertiesThree,
      priceCapital: 0,
      priceReal: selectedVariant.priceReal,
      price: calculateSalePrice(),
      quantity: 1,
      quantityPurchase: 1,
      sellOver: false,
      itemsCode: product.itemsCode,
      partnerId: product.partnerId,
      shopId: product.shopId,
      itemsType: 'Product',
      categoryId: product.categoryId,
      subCategoryId: null,
      itemsName: product.itemsName,
      itemsNameOrigin: product.itemsName,
      isTop: false,
      itemsPosition: 0,
      itemsInfo: null,
      images: [{ Type: 'IMAGE', Link: product.image }],
      sold: '0',
      isVariant: true,
      warehouseId: '',
      itemsWeight: 0,
      itemsLength: 0,
      itemsWidth: 0,
      itemsHeight: 0,
      note: note || '',
      extraOptions: extraOptions || [],
    };

    onSubmit(itemToAdd);
    onClose();
  };

  return (
    <StyledDialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      {/* <DialogTitle sx={dialogTitleStyles}>Tùy chọn thuộc tính</DialogTitle> */}

      <DialogContent sx={{ p: 2 }}>
        <Box sx={{ mb: 3 }}>
          <Box sx={productBoxStyles}>
            <Typography sx={{ color: '#000', fontWeight: 'bold' }}>{product.itemsName}</Typography>
            <Typography sx={{ color: '#000' }}>{formatCurrency(salePrice)}</Typography>
          </Box>
          <Box>
            <Typography fontWeight="bold">Tồn kho: {quantity}</Typography>
          </Box>

          {product.variants[0]?.variantNameOne &&
            uniquePropertiesOne.length > 0 &&
            uniquePropertiesOne[0] && (
              <>
                <Typography sx={{ mb: 1, color: '#000', fontSize: '14px' }}>
                  {product.variants[0].variantNameOne}
                </Typography>
                <StyledToggleButtonGroup
                  value={propertiesOne}
                  exclusive
                  onChange={(e, newPropertiesOne) => {
                    if (newPropertiesOne) {
                      setPropertiesOne(newPropertiesOne);
                      onChooseProperty(newPropertiesOne, propertiesTwo, propertiesThree);
                    }
                  }}
                  sx={{ mb: 1 }}
                >
                  {uniquePropertiesOne.map((propertiesOne, index) => (
                    <StyledToggleButton key={index} value={propertiesOne}>
                      {propertiesOne}
                    </StyledToggleButton>
                  ))}
                </StyledToggleButtonGroup>
                {validationErrors.variant && (
                  <Typography sx={{ color: 'red', fontSize: 15, mt: 1 }}>
                    {validationErrors.variant}
                  </Typography>
                )}
              </>
            )}

          {product.variants[0]?.variantNameTwo &&
            uniquePropertiesTwo.length > 0 &&
            uniquePropertiesTwo[0] && (
              <>
                <Typography sx={{ mb: 1, color: '#000', fontSize: '14px' }}>
                  {product.variants[0].variantNameTwo}
                </Typography>
                <StyledToggleButtonGroup
                  value={propertiesTwo}
                  exclusive
                  onChange={(e, newPropertiesTwo) => {
                    if (newPropertiesTwo) {
                      setPropertiesTwo(newPropertiesTwo);
                      onChooseProperty(propertiesOne, newPropertiesTwo, propertiesThree);
                    }
                  }}
                  sx={{ mb: 1 }}
                >
                  {uniquePropertiesTwo.map((propertiesTwo, index) => (
                    <StyledToggleButton key={index} value={propertiesTwo}>
                      {propertiesTwo}
                    </StyledToggleButton>
                  ))}
                </StyledToggleButtonGroup>
              </>
            )}

          {product.variants[0]?.variantNameThree &&
            uniquePropertiesThree.length > 0 &&
            uniquePropertiesThree[0] && (
              <>
                <Typography sx={{ mb: 1, color: '#000', fontSize: '14px' }}>
                  {product.variants[0].variantNameThree}
                </Typography>
                <StyledToggleButtonGroup
                  value={propertiesThree}
                  exclusive
                  onChange={(e, newPropertiesThree) => {
                    if (newPropertiesThree) {
                      setPropertiesThree(newPropertiesThree);
                      onChooseProperty(propertiesOne, propertiesTwo, newPropertiesThree);
                    }
                  }}
                  sx={{ mb: 1 }}
                >
                  {uniquePropertiesThree.map((propertiesThree, index) => (
                    <StyledToggleButton key={index} value={propertiesThree}>
                      {propertiesThree}
                    </StyledToggleButton>
                  ))}
                </StyledToggleButtonGroup>
              </>
            )}
          <ItemOptionsComponent
            itemOptionIds={product.extraItemOptionGroups}
            onOptionsChange={handleItemOptionsChange}
          />
          {validationErrors.options && (
            <Typography sx={{ color: 'red', fontSize: 15, mt: 1 }}>
              {validationErrors.options}
            </Typography>
          )}
          <Divider sx={{ mb: 2 }} />
          <Grid container spacing={1} alignItems="center">
            <Grid item xs={6}>
              <TextField
                fullWidth
                placeholder="Thêm ghi chú sản phẩm"
                value={note}
                onChange={(e) => setNote(e.target.value)}
                size="small"
                sx={textFieldStyles}
              />
            </Grid>
            <Grid item xs={3}>
              <Button onClick={onClose} variant="contained" fullWidth sx={cancelButtonStyles}>
                Huỷ
              </Button>
            </Grid>
            <Grid item xs={3}>
              <Button onClick={handleSubmit} variant="contained" fullWidth sx={submitButtonStyles}>
                Thêm
              </Button>
            </Grid>
          </Grid>
        </Box>
      </DialogContent>
    </StyledDialog>
  );
}
