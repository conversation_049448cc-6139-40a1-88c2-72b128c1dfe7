import { useState, useEffect, useCallback } from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  List,
  ListItem,
  Stack,
  Box,
  Divider,
  TablePagination,
  Modal,
  IconButton,
  Tooltip,
} from "@mui/material";
import Inventory2Icon from "@mui/icons-material/Inventory2";
import LocalShippingIcon from "@mui/icons-material/LocalShipping";
import { useVoucher } from "@/src/api/hooks/voucher/use-voucher";
import { useCart } from "@/src/api/hooks/cart/use-cart";
import useSnackbar from "@/src/hooks/use-snackbar";
import { useStoreId } from "@/src/hooks/use-store-id";
import { formatCurrency } from "@/src/utils/format-number";
import { Grid } from "@mui/system";
import CloseIcon from "@mui/icons-material/Close";
import { useProductCategory } from "@/src/api/hooks/dashboard/product/use-category";
import { CategoryType, GetProductCategoryRequest } from "@/src/api/types/product-category.types";
import { useProduct } from "@/src/api/hooks/dashboard/product/use-product";
import { productService } from "@/src/api/services/dashboard/product/product.service";
import { GetProductRequest } from "@/src/api/types/product.types";
import { set } from "nprogress";
import dayjs, { Dayjs } from "dayjs";
import { CopyAllOutlined } from "@mui/icons-material";
import toast from "react-hot-toast";
import { VOUCHER_TYPE } from "@/src/api/types/voucher.type";
import { StorageService } from "nextjs-api-lib";

interface Voucher {
  endDate: string | number | Date;
  voucherId: string;
  voucherCode: string;
  voucherName: string;
  voucherInfo: string;
  voucherType: string;
  initialSelectedVoucher?: Voucher[];
  minOrder: number;
  isLongTerm: boolean;
}

export interface VoucherSelectionProps {
  onClose: () => void;
  partnerId: string;
  shopId: any;
  userId: any;
  listItems: any;
  cartId: string;
  cart: any;
  onVoucherApplied: (updatedCart: any) => void;
  open: boolean;
  initialSelectedVoucher?: Voucher | [];
}

export default function VoucherSelection({
  onClose,
  partnerId,
  shopId,
  userId,
  listItems,
  cartId,
  cart,
  open,
  onVoucherApplied,
  initialSelectedVoucher,
}: VoucherSelectionProps) {
  const [selectedVouchers, setSelectedVouchers] = useState<Voucher[]>(
    Array.isArray(initialSelectedVoucher) ? initialSelectedVoucher : []
  );
  const [vouchers, setVouchers] = useState<Voucher[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(5);
  const { listVoucher } = useVoucher();
  const { createOrUpdateCart } = useCart();
  const snackbar = useSnackbar();
  const storeId = useStoreId();
  const [disabledVoucher, setDisabledVoucher] = useState();
  const [isShowDetailVoucher, setIsShowDetailVoucher] = useState(false);
  const [voucherDetail, setVoucherDetail] = useState<any>(null);
  const { getProduct, deleteProduct, loading: productLoading } = useProduct();
  const fetchVouchers = useCallback(async () => {
    setError(null);
    const data = { partnerId, shopId, userId, listItems, skip: 0, limit: 99 };
    const response = await listVoucher(data);
    if (response && response.data && response.data.data) {
      const currentDate = new Date();
      const filteredVouchers = response.data.data.filter((voucher: Voucher) => {
        const voucherEndDate = new Date(voucher.endDate);
        return (
          voucher.voucherType !== VOUCHER_TYPE.TRANSPORT &&
          (voucherEndDate >= currentDate || voucher.isLongTerm === true)
        );
      });
      setVouchers(filteredVouchers);
    } else {
      setError("Không tìm thấy voucher nào.");
    }
  }, [partnerId, shopId, userId, listItems]);

  useEffect(() => {
    fetchVouchers();
  }, [open]);

  const fetchVoucherStatus = async () => {
    if (Array.isArray(vouchers) && vouchers.length > 0) {
      const voucherIds = vouchers.map((x) => x.voucherId);
      const data = {
        voucherIds,
        cartId,
        shopId,
      };
      const res = await listVoucher(data);
      if (res && res?.data) {
        setDisabledVoucher(res?.data);
      }
    }
  };
  useEffect(() => {
    fetchVoucherStatus();
  }, [open, vouchers]);

  const handleApply = async () => {
    if (selectedVouchers.length > 0) {
      const updatedCartPayload = {
        branchId: storeId,
        partnerId,
        shopId,
        userId,
        listItems,
        statusDelivery: "InShop",
        transportService: "LCOD",
        transportPrice: 0,
        typePay: "COD",
        voucherPromotion: selectedVouchers,
        cartId,
      };

      try {
        const response = await createOrUpdateCart(updatedCartPayload);
        if (response && response.data) {
          snackbar.success("Áp dụng voucher thành công");
          onVoucherApplied(response.data);
        } else {
          snackbar.error("Lỗi khi áp dụng voucher");
        }
      } catch (error) {
        console.error("Lỗi API:", error);
        snackbar.error("Lỗi khi áp dụng voucher");
      } finally {
        onClose();
      }
    } else {
      snackbar.error("Vui lòng chọn voucher");
    }
  };

  const handleVoucherSelect = (voucher: Voucher) => {
    setSelectedVouchers((prevSelectedVouchers) => {
      const existingVoucherIndex = prevSelectedVouchers.findIndex(
        (v) => v.voucherType === voucher.voucherType
      );
      if (existingVoucherIndex !== -1) {
        const updatedVouchers = [...prevSelectedVouchers];
        updatedVouchers[existingVoucherIndex] = voucher;
        return updatedVouchers;
      } else {
        return [...prevSelectedVouchers, voucher];
      }
    });
  };

  const renderVoucherIcon = (voucher: Voucher) => {
    return (
      <>
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            width: 60,
            height: 60,
            borderRadius: 1,
            bgcolor: voucher.voucherType === VOUCHER_TYPE.TRANSPORT ? "#20C997" : "#FF9800",
          }}
          onClick={() => {
            setIsShowDetailVoucher(true);
            setVoucherDetail(voucher);
          }}
        >
          {voucher.voucherType === VOUCHER_TYPE.TRANSPORT ? (
            <LocalShippingIcon sx={{ color: "#fff", fontSize: 20 }} />
          ) : (
            <Inventory2Icon sx={{ color: "#fff", fontSize: 20 }} />
          )}
        </Box>
      </>
    );
  };

  const SelectedVoucher = (item: any) => {
    const {
      voucherType,
      discountType,
      percentDiscount,
      minOrder,
      maxDiscount,
      releaseType,
      exchangePoints,
    } = item;
    if (voucherType === VOUCHER_TYPE.TRANSPORT) {
      if (minOrder > 0) {
        return `Miễn phí giao hàng với đơn tối thiểu ${formatCurrency(minOrder)}`;
      } else {
        return `Miễn phí giao hàng`;
      }
    } else {
      if (discountType === "Percent") {
        return `Giảm ${percentDiscount}% tối đa ${formatCurrency(
          maxDiscount
        )} với đơn tối thiểu ${formatCurrency(minOrder)}`;
      } else {
        if (releaseType === "Free") {
          return `Giảm tối đa ${formatCurrency(maxDiscount)} với đơn tối thiểu ${formatCurrency(
            minOrder
          )}`;
        } else {
          return `Đổi ${exchangePoints} điểm để được giảm tối đa ${formatCurrency(
            maxDiscount
          )} với đơn tối thiểu ${formatCurrency(minOrder)}`;
        }
      }
    }
  };

  const renderVoucherItem = (voucher: Voucher) => {
    const isSelected = selectedVouchers.some((v) => v.voucherId === voucher.voucherId);
    return (
      <Box key={voucher.voucherId} sx={{ display: "flex", alignItems: "center", mb: 1 }}>
        {renderVoucherIcon(voucher)}
        <ListItem
          sx={{
            border: 1,
            borderColor: "divider",
            borderRadius: 1,
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            flexGrow: 1,
            py: 2,
          }}
        >
          <Grid container alignItems="center" spacing={2} sx={{ flexGrow: 1 }}>
            <Grid size={{ xs: 12, md: 6 }}>
              <Typography sx={{ fontWeight: 500 }}>{voucher.voucherCode}</Typography>
            </Grid>
            <Grid
              size={{ xs: 12, md: 6 }}
              sx={{ paddingRight: { sm: 4 }, marginBottom: { xs: 2, sm: 0 } }}
            >
              <Typography>{SelectedVoucher(voucher)}</Typography>
            </Grid>
          </Grid>
          <Button
            variant="contained"
            color="primary"
            disabled={disabledVoucher && !disabledVoucher[voucher?.voucherId]}
            onClick={() => handleVoucherSelect(voucher)}
            sx={{
              minWidth: 100,
              bgcolor: isSelected ? "#D9D9D9" : "#4A89FF",
            }}
          >
            {isSelected ? "Đã chọn" : "Chọn"}
          </Button>
        </ListItem>
      </Box>
    );
  };

  const renderVoucherList = () => {
    const indexOfLastVoucher = page * rowsPerPage + rowsPerPage;
    const indexOfFirstVoucher = page * rowsPerPage;
    const currentVouchers = vouchers
      .sort((a, b) => {
        const isADisabled = disabledVoucher && disabledVoucher?.[a.voucherId];
        const isBDisabled = disabledVoucher && disabledVoucher?.[b.voucherId];
        if (isADisabled !== isBDisabled) {
          return !isADisabled ? 1 : -1;
        }
        const minOrderA = a?.minOrder || 0;
        const minOrderB = b?.minOrder || 0;
        return minOrderA - minOrderB;
      })
      .slice(indexOfFirstVoucher, indexOfLastVoucher);

    return (
      <List sx={{ mb: 2, width: "100%" }}>
        {currentVouchers.length > 0 ? (
          currentVouchers.map(renderVoucherItem)
        ) : (
          <Typography sx={{ textAlign: "center", mt: 2, color: "red" }}>
            Không có voucher nào khả dụng.
          </Typography>
        )}
      </List>
    );
  };

  const [listCategory, setListCategory] = useState([]);
  const [listProduct, setListProduct] = useState([]);
  const { getProductCategory } = useProductCategory();

  const fetchCategory = async () => {
    const paramsProduct: GetProductCategoryRequest = {
      categoryType: "Product",
      shopId: storeId,
      partnerId: StorageService.get("partnerId") as string,
      paging: {
        pageIndex: page,
        pageSize: rowsPerPage,
        search: "",
        nameType: "Created",
        sortType: "Desc",
        name: "Created",
        sort: "Desc",
      },
    };
    const paramsService: GetProductCategoryRequest = {
      categoryType: "Service",
      shopId: storeId,
      partnerId: StorageService.get("partnerId") as string,
      paging: {
        pageIndex: page,
        pageSize: rowsPerPage,
        search: "",
        nameType: "Created",
        sortType: "Desc",
        name: "Created",
        sort: "Desc",
      },
    };
    const response = await getProductCategory(paramsProduct);
    if (response && response.data && response.data.data) {
      setListCategory(response.data.data);
    }
    const response2 = await getProductCategory(paramsService);
    if (response2 && response2.data && response2.data.data) {
      setListCategory((prev) => [...prev, ...response2.data.data]);
    }
  };

  const fetchItem = async () => {
    if (voucherDetail?.productIds.length > 0) {
      const data = {
        shopId: storeId,
        itemsIds: voucherDetail.productIds,
      };
      const response = await productService.getListProductByProductId(data);
      if (response && response.data && response.data.data) {
        setListProduct(response.data.data);
      }
    }
  };
  useEffect(() => {
    fetchCategory();
    fetchItem();
  }, [storeId, isShowDetailVoucher]);

  const handleCopy = () => {
    if (voucherDetail?.voucherCode) {
      navigator.clipboard.writeText(voucherDetail.voucherCode);
      toast.success("Copy thành công mã giảm giá");
    }
  };

  const renderModalDetailVoucher = () => {
    const cate = listCategory.filter((item) => item.categoryId === voucherDetail?.categoryId);
    return (
      <Modal
        sx={{ maxWidth: 1000, margin: "auto" }}
        open={isShowDetailVoucher}
        onClose={() => {
          setIsShowDetailVoucher(false);
          setVoucherDetail(null);
        }}
      >
        <Box
          sx={{
            position: "absolute",
            top: "50%",
            left: "50%",
            transform: "translate(-50%, -50%)",
            width: "80%",
            bgcolor: "background.paper",
            borderRadius: "8px",
            boxShadow: 24,
            p: 2,
          }}
        >
          <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
            <Typography sx={{ fontSize: "22px", fontWeight: "600", color: "#000" }}>
              Chi tiết voucher
            </Typography>
            <IconButton onClick={() => setIsShowDetailVoucher(false)} sx={{ color: "black" }}>
              <CloseIcon />
            </IconButton>
          </Box>

          <Box sx={{ display: "flex", alignItems: "start", paddingTop: 2 }}>
            {renderVoucherIcon(voucherDetail)}
            <Box
              sx={{
                display: "flex",
                alignItems: "start",
                flexDirection: "column",
                marginLeft: 3,
                "@media (max-width: 600px)": { flexDirection: "column" },
              }}
            >
              <Typography sx={{ fontWeight: 500 }}>{SelectedVoucher(voucherDetail)}</Typography>
              <Typography sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                Mã voucher: #{voucherDetail?.voucherCode}
                {voucherDetail?.voucherCode && (
                  <Tooltip title="Sao chép mã">
                    <IconButton onClick={handleCopy} size="small">
                      <CopyAllOutlined fontSize="small" />
                    </IconButton>
                  </Tooltip>
                )}
              </Typography>
              <Typography>
                HSD:{" "}
                {voucherDetail?.endDate
                  ? `${dayjs(voucherDetail?.startDate).format("DD/MM/YYYY")} - ${dayjs(
                      voucherDetail?.endDate
                    ).format("DD/MM/YYYY")}`
                  : voucherDetail.isLongTerm && "Không giới hạn"}
              </Typography>
              {voucherDetail.categoryId && (
                <Stack sx={{ paddingTop: 2 }} marginBlock={0}>
                  <Typography sx={{ fontWeight: 600, display: "inline" }}>
                    Áp dụng cho danh mục sản phẩm
                  </Typography>
                  <Typography sx={{ marginLeft: 1.6 }}>
                    Chỉ áp dụng cho loại sản phẩm:{" "}
                    <Typography sx={{ fontWeight: 600, display: "inline" }}>
                      {cate[0]?.categoryName}
                    </Typography>
                  </Typography>
                </Stack>
              )}
              {voucherDetail.productIds.length > 0 && (
                <Stack sx={{ paddingTop: 2 }} marginBlock={0}>
                  <Typography sx={{ fontWeight: 600, display: "inline" }}>
                    Áp dụng cho sản phẩm
                  </Typography>
                  <Typography sx={{ marginLeft: 1.6 }}>
                    Chỉ áp dụng cho sản phẩm:{" "}
                    <Typography sx={{ fontWeight: 600, display: "inline" }}>
                      {listProduct.map((item: any) => item.itemsName).join(", ")}
                    </Typography>
                  </Typography>
                </Stack>
              )}
            </Box>
          </Box>
        </Box>
      </Modal>
    );
  };

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const renderHeader = () => (
    <Grid size={12}>
      <Typography variant="h5" sx={{ mb: 2, fontWeight: 500, color: "blue" }}>
        Chọn khuyến mại
      </Typography>
      <Divider sx={{ mb: 2, borderWidth: 1 }} />
    </Grid>
  );

  const renderActions = () => (
    <Stack
      direction="row"
      spacing={2}
      sx={{ mt: 2, justifyContent: "space-between", width: "100%" }}
    >
      <Button
        variant="contained"
        color="inherit"
        onClick={onClose}
        sx={{
          width: "150px",
          bgcolor: "grey.700",
          color: "white",
          "&:hover": {
            bgcolor: "#454545",
          },
        }}
      >
        Hủy
      </Button>
      <Button
        variant="contained"
        color="primary"
        onClick={handleApply}
        sx={{
          width: "150px",
          bgcolor: "#1E90FF",
          "&:hover": {
            bgcolor: "#2654FE",
          },
        }}
      >
        Áp dụng
      </Button>
    </Stack>
  );

  return (
    <Grid
      container
      sx={{ maxWidth: 1000, mx: "auto", p: 2, mt: 4, bgcolor: "white", borderRadius: 1 }}
    >
      {renderHeader()}
      {error ? (
        <Typography color="error" sx={{ mb: 2 }}>
          {error}
        </Typography>
      ) : (
        <>
          {renderVoucherList()}
          <Box sx={{ display: "flex", justifyContent: "flex-end", width: "100%" }}>
            <TablePagination
              component="div"
              count={vouchers.length}
              page={page}
              onPageChange={handleChangePage}
              rowsPerPage={rowsPerPage}
              onRowsPerPageChange={handleChangeRowsPerPage}
              rowsPerPageOptions={[5, 10, 20]}
              labelRowsPerPage="Số dòng mỗi trang"
            />
          </Box>
        </>
      )}
      {renderActions()}

      {isShowDetailVoucher && renderModalDetailVoucher()}
    </Grid>
  );
}
