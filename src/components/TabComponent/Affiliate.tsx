import React, { useEffect, useState } from 'react';
import { <PERSON>, Tabs, Tab, Typography, Card, Grid, Button, Stack } from '@mui/material';
import Daily from './ComponentRank/Daily';
import Monthly from './ComponentRank/Monthly';
import Weekly from './ComponentRank/Weekly';
import { LocalizationProvider, DateRangePicker, DatePicker } from '@mui/x-date-pickers-pro';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';
import { ReportOverviewInterface } from '@/src/api/types/affiliation.type';
import { useAffiliation } from '@/src/api/hooks/affiliation/use-affiliation';
import { formatCurrency } from '@/src/utils/format-number';
import { useStoreId } from '@/src/hooks/use-store-id';
const stats = [
  { label: "Tổng lượt truy cập", value: "0" },
  { label: "<PERSON>h<PERSON>ch hàng qua liên kết", value: "0" },
  { label: "Tổng số đối tác", value: "0" },
  { label: "Tổng đơn hàng đối tác", value: "0" },
  { label: "Đơn hàng thành công", value: "0" },
  { label: "Doanh thu", value: "0 đ" },
  { label: "Hoa hồng đã duyệt", value: "0 đ" },
  { label: "Hoa hồng chờ duyệt", value: "0 đ" },
];
const Ranking = ({ selectedDay,
  setSelectedDay,
  startDate,
  setStartDate,
  endDate,
  setEndDate,
  tabRank,
  setTabRank,
  dataOrder, topRank }) => {
  // const [tabValue, setTabValue] = useState();
  const [open, setOpen] = useState(false);
  const storeId = useStoreId();

  const {
    getReportOverview,
    getDashboardStatistic,
    getPartnerOverview,
    exportExcelPartnerOverview,
  } = useAffiliation();
  const [reportOverviews, setReportOverviews] = useState(stats);
  const [chartDateRange, setChartDateRange] = useState([null, null]);

  const handleTabChange = (event, newValue) => {
    setTabRank(newValue)
  };
  const statisticsData = [
    { title: 'Doanh thu', value: '100,000,000 đ' },
    { title: 'Tổng đơn hàng đối tác', value: '1245' },
    { title: 'Hoa hồng đã duyệt', value: '20,000,000 đ' },
    { title: 'Đơn hàng thành công', value: '10000' },
    { title: 'Hoa hồng chờ duyệt', value: '10,000,000 đ' },
    { title: 'Tổng số đối tác', value: '2000' },
  ];


  const fetchOverview = async (shopId, fromDate, toDate) => {
    const response = await getReportOverview({ shopId, fromDate, toDate });
    console.log({ response })
    if (response?.data) {
      const { data }: { data: ReportOverviewInterface } = response?.data;
      const newStats = [
        { label: "Doanh thu", value: `${formatCurrency(data.revenue)} đ` },
        { label: "Tổng đơn hàng đối tác", value: `${formatCurrency(data.totalOrderByPartners)}` },
        { label: "Hoa hồng đã duyệt", value: `${formatCurrency(data.approvedCommission)} đ` },
        { label: "Đơn hàng thành công", value: `${formatCurrency(data.successOrders)}` },
        { label: "Hoa hồng chờ duyệt", value: `${formatCurrency(data.pendingCommission)} đ` },
        { label: "Tổng số đối tác", value: `${formatCurrency(data.totalPartner)}` },
        // { label: "Tổng lượt truy cập", value: `${formatCurrency(data.totalAccess)}` },
        // { label: "Khách hàng qua liên kết", value: `${formatCurrency(data.accessByLink)}` },
      ];
      setReportOverviews(newStats);
    }
  };

  useEffect(() => {
    fetchOverview(storeId, startDate, endDate)
  }, [storeId, startDate, endDate])


  const handleChartStartDateChange = (newValue) => {
    const endDate = chartDateRange[1];
    const startD = newValue.startOf("day").format("YYYY-MM-DD HH:mm:ss")
    let updatedRange;

    if (!endDate || newValue.isAfter(endDate)) {
      updatedRange = [newValue, newValue];
      setEndDate(newValue?.endOf("day").format("YYYY-MM-DD HH:mm:ss"))
      setStartDate(startD)
    } else {
      const endD = endDate.endOf("day").format("YYYY-MM-DD HH:mm:ss")
      updatedRange = [newValue, endDate];
      setEndDate(endD)
      setStartDate(startD)
    }
    setSelectedDay("")
    setChartDateRange(updatedRange);
  };

  const handleChartEndDateChange = (newValue) => {
    const startDate = chartDateRange[0];
    const endD = newValue.endOf("day").format("YYYY-MM-DD HH:mm:ss")
    let updatedRange;

    if (!startDate || newValue.isBefore(startDate, "day")) {
      updatedRange = [newValue, newValue];
      setEndDate(endD)
      setStartDate(newValue.startOf("day").format("YYYY-MM-DD HH:mm:ss"))
    } else {
      const startD = startDate.startOf("day").format("YYYY-MM-DD HH:mm:ss")
      updatedRange = [startDate, newValue];
      setEndDate(endD)
      setStartDate(startD)
    }
    setSelectedDay("")
    setChartDateRange(updatedRange);
  };
  return (
    <Box sx={{ p: 3, borderRadius: '20px', marginTop: '-15px', background: '#fff' }}>
      <Stack
        flexDirection={'row'}
        alignItems={'stretch'}
        gap={'20px'}
        sx={{
          '@media(max-width: 1024px)': {
            flexDirection: 'column',
          },
        }}
      >
        <Box>
          <Typography
            sx={{
              color: '#000000',
              fontWeight: '700',
              fontSize: '20px',
              flexDirection: 'column',
              justifyContent: 'space-between',
            }}
          >
            Bảng xếp hạng
          </Typography>
          <Box
            sx={{
              background: '#F4F5F9',
              borderRadius: '20px',
              padding: '15px',
              marginTop: '25px',
              minHeight: '670px',
              display: 'flex',
              flexDirection: 'column',
              // justifyContent: 'space-between',
            }}
          >
            <Tabs
              value={tabRank}
              onChange={handleTabChange}
              sx={{
                mb: 3,
                width: '100%',
                display: 'flex',
                alignItems: 'center',
                '& .MuiTabs-indicator': {
                  backgroundColor: '#1976d2',
                },
              }}
            >
              <Tab
                label="Hằng ngày"
                value='Day'
                disableRipple
                sx={{
                  textTransform: 'none',
                  fontSize: '20px',
                  // fontWeight: '700',
                  // color: '#fff',
                  fontWeight: tabRank === 'Day' ? 'bold' : 'bold',
                  color: tabRank === 'Day' ? '#fff !important' : '#000 !important',
                  background: tabRank === 'Day' ? '#2654FE' : '#fff',
                  borderRadius: tabRank === 'Day' ? '10px 10px 0 0' : '0',
                  width: '33.333%',
                  '&.Mui-selected': {
                    borderBottom: 'none',
                  },
                  '@media(max-width: 600px)': {
                    fontSize: '16px',
                    width: '30%',
                  },
                  // '@media(max-width: 600px)': {
                  //   fontSize: '14px',
                  //   width: '30%',
                  // },
                }}
              />
              <Tab
                label="Hằng tuần"
                disableRipple
                value='Week'
                sx={{
                  textTransform: 'none',
                  fontSize: '20px',
                  // fontWeight: '700',
                  // color: '#fff',
                  fontWeight: tabRank === 'Week' ? 'bold' : 'bold',
                  color: tabRank === 'Week' ? '#fff !important' : '#000 !important',
                  background: tabRank === 'Week' ? '#2654FE' : '#fff',
                  borderRadius: tabRank === 'Week' ? '10px 10px 0 0' : '0',
                  width: '33.333%',
                  '&.Mui-selected': {
                    borderBottom: 'none',
                  },
                  '@media(max-width: 600px)': {
                    fontSize: '16px',
                    width: '30%',
                  },
                  '@media(max-width: 480px)': {
                    fontSize: '14px',
                    width: '30%',
                  },
                }}
              />
              <Tab
                label="Hằng tháng"
                disableRipple
                value='Month'
                sx={{
                  textTransform: 'none',
                  fontSize: '20px',
                  // fontWeight: '700',
                  // color: '#fff',
                  fontWeight: tabRank === 'Month' ? 'bold' : 'bold',
                  color: tabRank === 'Month' ? '#fff !important' : '#000 !important',
                  background: tabRank === 'Month' ? '#2654FE' : '#fff',
                  borderRadius: tabRank === 'Month' ? '10px 10px 0 0' : '0',
                  width: '33.333%',
                  '&.Mui-selected': {
                    borderBottom: 'none',
                  },
                  '@media(max-width: 600px)': {
                    fontSize: '16px',
                    width: '30%',
                  },
                  '@media(max-width: 480px)': {
                    fontSize: '14px',
                    width: '30%',
                  },
                }}
              />
            </Tabs>
            {tabRank === 'Day' && <Daily topRank={topRank} />}
            {tabRank === 'Week' && <Weekly topRank={topRank} />}
            {tabRank === 'Month' && <Monthly topRank={topRank} />}
          </Box>
          <Box
            sx={{ background: '#F4F5F9', borderRadius: '20px', padding: '15px', marginTop: '25px' }}
          >
            <Typography
              sx={{
                color: '#000000',
                fontSize: '20px',
                fontWeight: '700',
                marginBottom: '8px',
              }}
            >
              Thêm đối tác mới thủ công
            </Typography>
            <Typography
              sx={{ color: '#000000', fontSize: '20', fontWeight: '400', marginBottom: '25px' }}
            >
              Nhạp dữ liệu đối tác thủ công và cài đặt hoa hồng cho đối tác của bạn
            </Typography>
            <Button
              sx={{
                textTransform: 'none',
                color: '#2654FE',
                fontSize: '20px',
                fontWeight: '400',
                padding: 0,
              }}
            >
              Thêm đối tác
            </Button>
          </Box>
        </Box>
        <Box display={'flex'} flexDirection={'column'} justifyContent={'space-between'}>
          <Stack
            flexDirection={'row'}
            alignItems={'start'}
            justifyContent={'space-between'}
            sx={{
              '@media(max-width: 1580px)': {
                flexDirection: 'column',
              },
            }}
          >
            <Stack flexDirection={'row'} alignItems={'center'} gap={'5px'}>
              <Typography color="#000000" fontSize={'20px'} fontWeight={'700'}>
                Dữ liệu tổng quan
              </Typography>
              <Button
                sx={{
                  textTransform: 'none',
                  color: '#2654FE',
                  fontSize: '16px',
                  fontWeight: '400',
                  padding: 0,
                }}
              >
                Chi tiết
              </Button>
            </Stack>
            <LocalizationProvider dateAdapter={AdapterDayjs} adapterLocale="vi">
              <Box sx={{ display: "flex" }}>
                <DatePicker
                  value={chartDateRange[0]}
                  format="DD/MM/YYYY"
                  onChange={handleChartStartDateChange}
                  slotProps={{
                    textField: {
                      sx: { height: 36, paddingLeft: 0, width: 180 },
                      InputProps: { sx: { height: 36 } },
                    },
                  }}
                />
                <Typography sx={{ paddingLeft: 1, paddingRight: 1, marginTop: 0.5 }}>-</Typography>
                <DatePicker
                  value={chartDateRange[1]}
                  format="DD/MM/YYYY"
                  onChange={handleChartEndDateChange}
                  shouldDisableDate={(date) => date.isBefore(chartDateRange[1], "day")}
                  slotProps={{
                    textField: {
                      sx: { height: 36, paddingLeft: 0, width: 180 },
                      InputProps: { sx: { height: 36 } },
                    },
                  }}
                />
              </Box>
            </LocalizationProvider>
          </Stack>
          <Grid
            container
            spacing={2}
            sx={{ display: 'flex', marginTop: '-3px', minHeight: '670px' }}
          >
            {reportOverviews.map((stat, index) => (
              <Grid
                item
                key={index}
                sx={{
                  width: '50%',
                  maxWidth: 'unset',
                  '& .MuiPaper-root': {
                    height: '100% !important',
                  },
                }}
              >

                <Card sx={{ p: 2, textAlign: 'left', borderRadius: '20px', height: '100%', background: '#F4F5F9' }}>
                  <Typography color="#202224" fontSize={'16px'} fontWeight={'600'}>
                    {stat.label}
                  </Typography>
                  <Typography
                    color="#202224"
                    fontSize={'28px'}
                    mb={'50px'}
                    fontWeight={'700'}
                    sx={{ mt: 1 }}
                  >
                    {stat.value}
                  </Typography>
                </Card>
              </Grid>
            ))}
          </Grid>
          <Box
            sx={{ background: '#F4F5F9', borderRadius: '20px', padding: '15px', marginTop: '25px' }}
          >
            <Typography
              sx={{
                color: '#000000',
                fontSize: '20px',
                fontWeight: '700',
                marginBottom: '8px',
              }}
            >
              Sử dụng quảng cáo hoặc gửi link để tuyển dụng
            </Typography>
            <Typography
              sx={{ color: '#000000', fontSize: '20', fontWeight: '400', marginBottom: '25px' }}
            >
              Sử dụng quảng cáo hoặc gửi link đăng ký để đối tác của bạn tự nhập thông tin đăng ký{' '}
            </Typography>
            <Button
              sx={{
                textTransform: 'none',
                color: '#2654FE',
                fontSize: '20px',
                fontWeight: '400',
                padding: 0,
              }}
            >
              Chia sẻ
            </Button>
          </Box>
        </Box>
      </Stack>
    </Box>
  );
};

export default Ranking;
