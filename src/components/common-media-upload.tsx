import React, { use<PERSON><PERSON>back, useState, useEffect } from "react";
import { <PERSON>, Typography, IconButton, Stack, Avatar, SvgIcon, Button, Card } from "@mui/material";
import CloudUploadIcon from "@mui/icons-material/CloudUpload";
import CloseIcon from "@mui/icons-material/Close";
import { useDropzone } from "react-dropzone";
import { ExistingMediaFile } from "../pages/dashboard/product/product-management/create/create";
import { FileType, MediaFile } from "../constants/file-types";
import { Info, LibraryAdd, Link } from "@mui/icons-material";
import { tokens } from "../locales/tokens";
import { useTranslation } from "react-i18next";
import DialogAddMediaUrl from "../pages/dashboard/product/product-management/media/dialog-add-media-url";
import DialogAddMediaLibrary from "../pages/dashboard/product/product-management/media/dialog-add-media-library";
import { FILE_SIZE_2MB, MAX_FILE_IMAGE } from "../constants/constant";

interface CommonMediaUploadProps {
  caption?: string;
  maxFiles?: number;
  accept?: Record<string, string[]>;
  maxSize?: number;
  disabled?: boolean;
  sx?: any;
  existingFiles?: ExistingMediaFile[];
  setExistingFiles?: any;
  localFiles?: File[];
  setLocalFiles?: React.Dispatch<React.SetStateAction<File[]>>;
  onFilesChange?: (files: File[]) => void;
  onRemove?: (index: number, isExisting: boolean) => void;
  defaultGroupId?: string;
  isShowPreviewImage?: boolean;
  errorMsg?: string;
  setErrorMsg?: (msg: string | null) => void;
}

export interface LocalPreview {
  link?: string;
  file: File;
  previewUrl: string;
  type: FileType;
  mediaFileId?: string;
}

export const CommonMediaUpload: React.FC<CommonMediaUploadProps> = ({
  caption = "Chọn ảnh hoặc video",
  maxFiles = MAX_FILE_IMAGE,
  accept = {
    "image/*": [".png", ".jpg", ".jpeg", ".jfif"],
    "video/*": [".mp4", ".mov", ".avi"],
  },
  maxSize = FILE_SIZE_2MB, // 2MB
  disabled = false,
  sx,
  existingFiles = [],
  setExistingFiles,
  localFiles,
  setLocalFiles,
  onFilesChange,
  onRemove,
  defaultGroupId,
  isShowPreviewImage = true,
  errorMsg,
  setErrorMsg,
}) => {
  const [openUrlDialog, setOpenUrlDialog] = useState(false);
  const [openLibraryDialog, setOpenLibraryDialog] = useState(false);
  const [localPreviews, setLocalPreviews] = useState<LocalPreview[]>([]);
  const { t } = useTranslation();

  const isFileAccepted = (file: File, accept: Record<string, string[]>): boolean => {
    const mimeType = file.type;
    const baseMimeType = mimeType.replace(/\/.*$/, "");

    for (const [type, extensions] of Object.entries(accept)) {
      const acceptBaseType = type.replace(/\/\*$/, "");

      if (acceptBaseType === baseMimeType) {
        if (extensions.length === 0) return true;
        const fileExtension = "." + file.name.split(".").pop()?.toLowerCase();
        if (extensions.map((ext) => ext.toLowerCase()).includes(fileExtension)) {
          return true;
        }
      }
    }
    return false;
  };

  const [allPreviews, setAllPreviews] = useState<
    Array<ExistingMediaFile | (LocalPreview & { isLocal?: boolean })>
  >([]);

  useEffect(() => {
    setAllPreviews([
      ...existingFiles.map((f) => ({ ...f, isLocal: false })),
      ...(Array.isArray(localPreviews) ? localPreviews.map((f) => ({ ...f, isLocal: true })) : []),
    ]);
  }, [existingFiles, localPreviews]);

  useEffect(() => {
    Array.isArray(localPreviews) &&
      localPreviews.forEach(({ previewUrl }) => {
        if (previewUrl.startsWith("blob:")) {
          URL.revokeObjectURL(previewUrl);
        }
      });

    setLocalPreviews(
      Array.isArray(localFiles) &&
        localFiles.map((file) => ({
          file,
          previewUrl: URL.createObjectURL(file),
          type: file.type.startsWith("video/") ? FileType.VIDEO : FileType.IMAGE,
          isLocal: true,
        }))
    );
    // eslint-disable-next-line
  }, [localFiles]);

  const onDropRejected = useCallback(
    (fileRejections) => {
      let msg = "";
      fileRejections.forEach((rej) => {
        rej.errors.forEach((err) => {
          if (err.code === "file-too-large") {
            msg = `Có file vượt quá kích thước tối đa ${(maxSize / 1024 / 1024).toFixed(1)}MB`;
          } else if (err.code === "file-invalid-type") {
            msg = "Có file không đúng định dạng cho phép";
          }
        });
      });
      setErrorMsg(msg);
    },
    [maxSize]
  );

  const onDrop = useCallback(
    (acceptedFiles: File[]) => {
      setErrorMsg(null);

      const validFiles = acceptedFiles.filter((file) => isFileAccepted(file, accept));
      const invalidFiles = acceptedFiles.filter((file) => !isFileAccepted(file, accept));

      if (invalidFiles.length > 0) {
        const invalidFileNames = invalidFiles.map((file) => file.name).join(", ");
        setErrorMsg(`File không đúng định dạng cho phép: ${invalidFileNames}`);
        if (validFiles.length === 0) return;
      }

      let nextLocalPreviews: LocalPreview[] = [];

      if (maxFiles === 1) {
        setLocalFiles([]);
        setExistingFiles([]);
        const file = acceptedFiles[acceptedFiles.length - 1];
        nextLocalPreviews = [
          {
            file,
            previewUrl: URL.createObjectURL(file),
            type: file.type.startsWith("video/") ? FileType.VIDEO : FileType.IMAGE,
          },
        ];
      } else {
        const totalFiles = existingFiles.length + localPreviews.length + acceptedFiles.length;
        if (totalFiles > maxFiles) {
          setErrorMsg(`Chỉ được chọn tối đa ${maxFiles} file`);
          return;
        }
        const newPreviews: LocalPreview[] = acceptedFiles.map((file) => ({
          file,
          previewUrl: URL.createObjectURL(file),
          type: file.type.startsWith("video/") ? FileType.VIDEO : FileType.IMAGE,
        }));
        nextLocalPreviews = [...localPreviews, ...newPreviews];
      }

      setLocalPreviews(nextLocalPreviews);
      if (onFilesChange) {
        onFilesChange(nextLocalPreviews.map((p) => p.file));
      }
    },
    [existingFiles.length, localPreviews, maxFiles, onFilesChange]
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    onDropRejected,
    accept,
    maxSize,
    disabled,
    multiple: maxFiles > 1,
  });

  const handleRemove = (index: number) => {
    if (index < existingFiles.length) {
      if (onRemove) onRemove(index, true);
    } else {
      const localIdx = index - existingFiles.length;
      const updated = [...localPreviews];
      const removed = updated.splice(localIdx, 1)[0];
      if (removed.previewUrl.startsWith("blob:")) {
        URL.revokeObjectURL(removed.previewUrl);
      }
      setLocalPreviews(updated);
      if (onFilesChange) {
        onFilesChange(updated.map((p) => p.file));
      }
    }
  };

  return (
    <Card sx={{ padding: 3, borderRadius: 1, backgroundColor: "white", width: "100%", ...sx }}>
      <Box sx={{ display: "flex", alignItems: "center", justifyContent: "space-between", mb: 2.5 }}>
        <Box sx={{ display: "flex", alignItems: "center" }}>
          <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
            Hình ảnh ({allPreviews.length}/{maxFiles})
          </Typography>
          <IconButton size="small" sx={{ ml: 1 }}>
            <Info fontSize="small" />
          </IconButton>
        </Box>

        <Stack
          direction={{ xs: "column", sm: "row" }}
          spacing={1.5}
          sx={{
            width: { xs: "100%", sm: "auto" },
            alignItems: { xs: "stretch", sm: "center" },
          }}
        >
          <Button
            variant="outlined"
            size="small"
            startIcon={<Link />}
            onClick={() => setOpenUrlDialog(true)}
            sx={{
              minWidth: 130,
              height: 36,
              fontSize: "0.875rem",
              color: "#2654FE",
              borderColor: "#2654FE",
              width: { xs: "100%", sm: "auto" },
            }}
          >
            {t(tokens.contentManagement.product.create.media.addFromUrl)}
          </Button>
          <Button
            variant="outlined"
            size="small"
            startIcon={<LibraryAdd />}
            onClick={() => setOpenLibraryDialog(true)}
            sx={{
              minWidth: 130,
              height: 36,
              fontSize: "0.875rem",
              color: "#2654FE",
              borderColor: "#2654FE",
              width: { xs: "100%", sm: "auto" },
            }}
          >
            {t(tokens.contentManagement.product.create.media.addFromLibrary)}
          </Button>
        </Stack>
      </Box>
      <Box
        {...getRootProps()}
        sx={{
          border: "2px dashed",
          borderColor: disabled ? "grey.400" : "primary.main",
          borderRadius: 1,
          p: 3,
          textAlign: "center",
          cursor: disabled ? "not-allowed" : "pointer",
          bgcolor: isDragActive ? "action.hover" : "background.paper",
          opacity: disabled ? 0.6 : 1,
          transition: "background-color 0.2s ease",
        }}
      >
        <input {...getInputProps()} />
        <Stack alignItems="center" spacing={1}>
          <CloudUploadIcon color={disabled ? "disabled" : "primary"} sx={{ fontSize: 40 }} />
          <Typography variant="h6" color={disabled ? "text.disabled" : "text.primary"}>
            {isDragActive ? "Thả file vào đây..." : caption}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Định dạng:&nbsp;
            {Object.entries(accept).map(([type, exts], idx, arr) => {
              let label = type.replace("/*", "");
              if (label === "image") label = "ảnh";
              if (label === "video") label = "video";
              return (
                <span key={type}>
                  <b>{label}</b>
                  {": "}
                  {exts.join(", ")}
                  {idx < arr.length - 1 && " | "}
                </span>
              );
            })}
            &nbsp;| Tối đa: {(maxSize / 1024 / 1024).toFixed(1)}MB
          </Typography>
        </Stack>
      </Box>
      {errorMsg && (
        <Typography color="error" variant="body2" sx={{ mt: 1 }}>
          {errorMsg}
        </Typography>
      )}
      {allPreviews.length > 0 && isShowPreviewImage && (
        <Stack direction="row" flexWrap="wrap" gap={2} mt={2}>
          {allPreviews.map((item, idx) => (
            <Box
              key={idx}
              sx={{
                position: "relative",
                width: 100,
                height: 100,
                borderRadius: 1,
                overflow: "hidden",
                border: "1px solid",
                borderColor: "divider",
                cursor: item.type === FileType.VIDEO ? "pointer" : "default",
              }}
            >
              {item.type === FileType.IMAGE ? (
                <Avatar
                  variant="rounded"
                  src={"url" in item ? item.url : item.previewUrl ? item.previewUrl : item.link}
                  alt={`preview-${idx}`}
                  sx={{ width: "100%", height: "100%", objectFit: "cover" }}
                />
              ) : (
                <video
                  src={"url" in item ? item.url : item.previewUrl}
                  style={{ width: "100%", height: "100%", objectFit: "cover" }}
                  controls
                />
              )}
              <IconButton
                size="small"
                onClick={() => handleRemove(idx)}
                sx={{
                  position: "absolute",
                  top: 2,
                  right: 2,
                  bgcolor: "background.paper",
                  "&:hover": { bgcolor: "error.main", color: "common.white" },
                }}
                disabled={disabled}
                aria-label="Remove file"
              >
                <SvgIcon component={CloseIcon} fontSize="small" />
              </IconButton>
            </Box>
          ))}
        </Stack>
      )}

      <DialogAddMediaUrl
        open={openUrlDialog}
        onClose={() => setOpenUrlDialog(false)}
        onSave={onDrop}
        defaultGroupId={defaultGroupId}
        maxSize={maxSize}
        acceptFile={accept}
      />
      <DialogAddMediaLibrary
        open={openLibraryDialog}
        onClose={() => setOpenLibraryDialog(false)}
        onSave={onDrop}
        maxSelect={20 - allPreviews.length}
        maxFile={maxFiles}
        existingFiles={existingFiles}
        setExistingFiles={setExistingFiles}
        localFiles={localFiles}
        setLocalFiles={setLocalFiles}
        errorMsg={errorMsg}
        setErrorMsg={setErrorMsg}
        localPreviews={localPreviews}
        setLocalPreviews={setLocalPreviews}
        allPreviews={allPreviews}
        setAllPreviews={setAllPreviews}
      />
    </Card>
  );
};
