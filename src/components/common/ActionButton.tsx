import { But<PERSON>, Toolt<PERSON> } from "@mui/material";
import type { ButtonProps, SxProps, Theme } from "@mui/material";
import {
  generateActionButtonStyles,
  type ActionButtonStyleConfig,
} from "@/src/styles/ActionButtonStyles";

interface ActionButtonProps extends ActionButtonStyleConfig {
  permission?: string;
  tooltip?: string;
  icon?: React.ReactNode;
  children: React.ReactNode;
  onClick?: () => void;
  disabled?: boolean;
  isGranted?: (pathname: string, permission: string) => boolean;
  pathname?: string;
  startIcon?: React.ReactNode;
  endIcon?: React.ReactNode;
  sx?: SxProps<Theme>;
  loading?: boolean;
}

const ActionButton = ({
  permission,
  tooltip = "",
  variant = "outlined",
  size = "medium",
  color = "primary",
  fullWidth = false,
  icon,
  startIcon,
  endIcon,
  children,
  onClick,
  disabled = false,
  isGranted,
  pathname = "",
  customSx = {},
  sx = {},
  loading = false,
  ...otherProps
}: ActionButtonProps) => {
  const hasPermission = permission && isGranted ? isGranted(pathname, permission) : true;
  const isDisabled = disabled || !hasPermission || loading;
 const buttonStyles = {
    ...generateActionButtonStyles({
      variant,
      size,
      color,
      fullWidth,
      customSx,
    }),
    ...(Array.isArray(sx) ? Object.assign({}, ...sx) : sx),
  };
  const iconToUse = startIcon || icon;

  const renderButton = () => (
    <Button
      variant={variant}
      sx={buttonStyles}
      startIcon={iconToUse}
      endIcon={endIcon}
      onClick={onClick}
      disabled={isDisabled}
      {...otherProps}
    >
      {loading ? "Đang xử lý..." : children}
    </Button>
  );

  return isDisabled && tooltip ? (
    <Tooltip title={tooltip}>
      <span style={{ width: fullWidth ? "100%" : "auto", display: "inline-block" }}>
        {renderButton()}
      </span>
    </Tooltip>
  ) : (
    renderButton()
  );
};

export default ActionButton;
