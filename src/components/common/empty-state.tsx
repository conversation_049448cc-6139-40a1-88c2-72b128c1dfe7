import { <PERSON>, Button, Tooltip, Typography } from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import { PERMISSION_TYPE_ENUM } from "@/src/constants/constant";
import { usePathname } from "next/navigation";

interface EmptyStateProps {
  title: string;
  subtitle: string;
  buttonText: string;
  imageSrc?: string;
  onAddClick: () => void;
  isGranted: any;
}

export const EmptyState = ({
  title,
  subtitle,
  buttonText,
  imageSrc = "/assets/image_empty.png",
  onAddClick,
  isGranted,
}: EmptyStateProps) => {
  const pathname = usePathname();
  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        height: "70vh",
        textAlign: "center",
        backgroundColor: "background.paper",
        borderRadius: 2,
        padding: 4,
        boxShadow: "0 0 20px rgba(0,0,0,0.05)",
      }}
    >
      <Box
        sx={{
          position: "relative",
          mb: 3,
          "&:hover": {
            transform: "translateY(-5px)",
            transition: "transform 0.3s ease-in-out",
          },
        }}
      >
        <img
          src={imageSrc}
          alt={title}
          style={{
            width: "100%",
            maxWidth: "120px",
            height: "auto",
            margin: "0 auto",
          }}
        />
        <Box
          sx={{
            position: "absolute",
            top: "50%",
            left: "50%",
            transform: "translate(-50%, -50%)",
            width: "220px",
            height: "220px",
            background: "radial-gradient(circle, rgba(63,94,251,0.1) 0%, rgba(255,255,255,0) 70%)",
            borderRadius: "50%",
            zIndex: -1,
          }}
        />
      </Box>

      <Typography
        variant="h6"
        color="text.primary"
        sx={{ mb: 2, fontWeight: 600, fontSize: "1.2rem" }}
      >
        {title}
      </Typography>

      <Typography
        variant="body2"
        color="text.secondary"
        sx={{ mb: 2, maxWidth: "400px", lineHeight: 1.6 }}
      >
        {subtitle}
      </Typography>
      <Tooltip
        title={!isGranted(pathname, PERMISSION_TYPE_ENUM.Add) ? "Bạn không có quyền thêm" : ""}
      >
        <span>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={onAddClick}
            disabled={!isGranted(pathname, PERMISSION_TYPE_ENUM.Add)}
            sx={{
              py: 1,
              px: 2,
              borderRadius: 2,
              textTransform: "none",
              fontSize: "0.8rem",
              fontWeight: 400,
              boxShadow: 1,
              background: "#2654FE",
              "&:hover": {
                transform: "translateY(-2px)",
                boxShadow: 4,
                transition: "all 0.2s ease-in-out",
              },
            }}
          >
            {buttonText}
          </Button>
        </span>
      </Tooltip>
    </Box>
  );
};
