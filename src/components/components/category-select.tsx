import React, { useState, useEffect } from "react";
import {
  FormControl,
  Select,
  MenuItem,
  FormHelperText,
  SelectChangeEvent,
  styled,
} from "@mui/material";
import type { SxProps } from "@mui/material";

interface Option {
  value: string;
  label: string;
  disabled?: boolean;
}

interface CategorySelectProps {
  label: string;
  value: string;
  onChange: (event: SelectChangeEvent<string>) => void;
  onBlur?: () => void;
  options: Option[];
  error?: boolean;
  helperText?: string;
  variant?: "standard" | "outlined" | "filled";
  size?: "small" | "medium";
  sx?: SxProps;
  placeholder?: string;
  disabled?: boolean;
  emptyMessage?: string;
}

const StyledFormControl = styled(FormControl)(({ theme }) => ({
  "& .MuiOutlinedInput-root": {
    borderRadius: "8px",
    backgroundColor: theme.palette.background.paper,
    height: "38px",

    "& .MuiSelect-select": {
      paddingTop: "8px",
      paddingBottom: "8px",
      paddingLeft: "12px",
      color: theme.palette.text.secondary,
      fontSize: "0.875rem",
      display: "flex",
      alignItems: "center",
    },

    "& .MuiOutlinedInput-notchedOutline": {
      borderColor: theme.palette.divider,
      borderWidth: "1px",
    },

    "&:hover .MuiOutlinedInput-notchedOutline": {
      borderColor: theme.palette.action.hover,
    },

    "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
      borderColor: theme.palette.primary.main,
      borderWidth: "1px",
    },
  },

  // Custom styles cho menu items
  "& .MuiMenu-paper": {
    marginTop: "4px",
    borderRadius: "8px",
    boxShadow: theme.shadows[3],
  },

  "& .MuiMenuItem-root": {
    fontSize: "0.875rem",
    minHeight: "38px",
    padding: "8px 12px",

    "&:hover": {
      backgroundColor: theme.palette.action.hover,
    },

    "&.Mui-selected": {
      backgroundColor: theme.palette.primary.light,
      "&:hover": {
        backgroundColor: theme.palette.primary.light,
      },
    },
  },
}));

export const CategorySelect: React.FC<CategorySelectProps> = ({
  label,
  value,
  onChange,
  onBlur,
  options,
  error = false,
  helperText,
  variant = "outlined",
  size = "small",
  sx,
  placeholder,
  disabled,
  emptyMessage,
}) => {
  const [open, setOpen] = useState(false);
  const displayLabel = placeholder || label;
  const hasOptions = options && options.length > 0;

  const handleClose = () => {
    setOpen(false);
  };

  const handleOpen = () => {
    setOpen(true);
  };

  const handleChange = (event: SelectChangeEvent<string>) => {
    onChange(event);
    handleClose();
  };

  return (
    <StyledFormControl error={error} size={size} variant={variant} sx={sx}>
      <Select
        open={open}
        onClose={handleClose}
        onOpen={handleOpen}
        value={value || ""}
        onChange={handleChange}
        onBlur={onBlur}
        displayEmpty
        disabled={disabled || !hasOptions}
        renderValue={(selected) => {
          if (!selected) {
            return <span style={{ color: "inherit" }}>{displayLabel}</span>;
          }
          const selectedOption = options.find((opt) => opt.value === selected);
          return selectedOption ? selectedOption.label : displayLabel;
        }}
        MenuProps={{
          PaperProps: {
            sx: {
              maxHeight: 300,
            },
          },
        }}
      >
        {!hasOptions ? (
          <MenuItem disabled>
            <em>{emptyMessage || "No options available"}</em>
          </MenuItem>
        ) : (
          <>
            <MenuItem value="" sx={{ color: "text.secondary" }}>
              <em>{displayLabel}</em>
            </MenuItem>
            {options.map((option) => (
              <MenuItem key={option.value} value={option.value} disabled={option.disabled}>
                {option.label}
              </MenuItem>
            ))}
          </>
        )}
      </Select>
      {helperText && <FormHelperText>{helperText}</FormHelperText>}
    </StyledFormControl>
  );
};

export default CategorySelect;
