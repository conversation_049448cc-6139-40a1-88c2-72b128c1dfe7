import { Chip } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { tokens } from '@/src/locales/tokens';

interface HomeDisplayChipProps {
  isHome: boolean;
}

export const HomeDisplayChip = ({ isHome }: HomeDisplayChipProps) => {
  const { t } = useTranslation();

  return (
    <Chip
      label={
        isHome
          ? t(tokens.contentManagement.category.status.published)
          : t(tokens.contentManagement.category.status.unpublished)
      }
      color={isHome ? 'success' : 'default'}
      sx={{
        backgroundColor: isHome ? 'rgba(84, 214, 44, 0.16)' : 'rgba(145, 158, 171, 0.16)',
        color: isHome ? 'rgb(34, 154, 22)' : 'rgb(99, 115, 129)'
      }}
    />
  );
};
