import React, { useCallback, useRef, useState } from "react";
import {
  Add,
  CheckBox,
  Delete,
  Edit,
  Search,
  Upload,
  UploadFile,
  NavigateBefore as NavigateBeforeIcon,
  NavigateNext as NavigateNextIcon,
  IosShare,
  FileDownload,
  FileUpload,
  Close,
  PanoramaFishEye,
  Download,
  CloudUpload,
} from "@mui/icons-material";
import {
  Box,
  Button,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Paper,
  Typography,
} from "@mui/material";
import { useProduct } from "@/src/api/hooks/dashboard/product/use-product";
import { useStoreId } from "@/src/hooks/use-store-id";
import useSnackbar from "@/src/hooks/use-snackbar";
import { EXCEL_MAX_SIZE } from "@/src/constants/constant";

const ModalImportExcelItems = ({ open, setOpen, tabValue, fetchData }) => {
  const [file, setFile] = useState(null);
  const [loading, setLoading] = useState(false);
  const [loadingDownloadTemplate, setLoadingDownloadTemplate] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState(false);
  const fileInputRef = useRef(null);
  const { exportProductTemplate, importProduct } = useProduct();
  const storeId = useStoreId();
  const snackbar = useSnackbar();
  const handleOpen = () => {
    setOpen(true);
    setFile(null);
    setError("");
    setSuccess(false);
  };

  const handleClose = () => {
    setFile(null);
    setOpen(false);
  };

  const handleDownloadTemplate = async () => {
    try {
      const data = {
        shopId: storeId,
        type: tabValue === 0 ? "Product" : "Service",
      };
      setLoadingDownloadTemplate(true);

      const response = await exportProductTemplate(data);

      const reader = new FileReader();

      reader.onload = async function () {
        try {
          if (typeof reader.result === "string") {
            const jsonResponse = JSON.parse(reader.result);

            if (jsonResponse.data && jsonResponse.data.link) {
              const fileName = jsonResponse.data.link.split("/").pop();

              const fileResponse = await fetch(jsonResponse.data.link);
              const fileBlob = await fileResponse.blob();

              const url = window.URL.createObjectURL(fileBlob);
              const link = document.createElement("a");
              link.href = url;
              link.setAttribute("download", fileName);
              document.body.appendChild(link);
              link.click();

              document.body.removeChild(link);
              window.URL.revokeObjectURL(url);
              setFile(null);
            } else {
              console.error("Không tìm thấy link file trong response JSON");
            }
          } else {
            console.error("reader.result không phải là string");
          }
        } catch (error) {
          console.error("Lỗi khi xử lý JSON:", error);
        }
      };

      reader.readAsText(response.data);
    } catch (error) {
      console.error("Error downloading template:", error);
      snackbar.error("Tải file mẫu thất bại");
    } finally {
      setLoadingDownloadTemplate(false);
      snackbar.success("Tải file mẫu thành công");
    }
  };

  const handleFileChange = (event) => {
    const selectedFile = event.target.files[0];
    validateAndSetFile(selectedFile);
  };

  const validateAndSetFile = (selectedFile) => {
    setError("");

    if (!selectedFile) return;

    const fileExt = selectedFile.name.split(".").pop().toLowerCase();
    if (fileExt !== "xlsx" && fileExt !== "xls") {
      setError("Chỉ chấp nhận file Excel (.xlsx, .xls)");
      return;
    }

    if (selectedFile.size > EXCEL_MAX_SIZE) {
      setError("Kích thước file không được vượt quá 10MB");
      return;
    }

    setFile(selectedFile);
  };

  const handleDragOver = (event) => {
    event.preventDefault();
    event.stopPropagation();
  };

  const handleDrop = (event) => {
    event.preventDefault();
    event.stopPropagation();

    if (event.dataTransfer.files && event.dataTransfer.files.length > 0) {
      validateAndSetFile(event.dataTransfer.files[0]);
    }
  };

  const handleUpload = async () => {
    if (!file) return;
    setLoading(true);
    const data = {
      shopId: storeId,
      file: file,
      type: tabValue === 0 ? "Product" : "Service",
    };
    const response = await importProduct(data);
    if (response && response.status === 200) {
      setSuccess(true);
      snackbar.success("Import dữ liệu thành công");
      setFile(null);
      fetchData();
      setOpen(false);
    } else {
      setSuccess(false);
      snackbar.error(response.detail);
    }
    setLoading(false);
  };

  const handleDragAreaClick = () => {
    fileInputRef.current.click();
  };

  return (
    <>
      <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
        <DialogTitle>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Typography variant="h6">Tải lên danh sách</Typography>
            <Box
              sx={{
                padding: 0,
                display: "flex",
                justifyContent: "end",
                cursor: "pointer",
                "&:hover": {
                  backgroundColor: "#f0f0f0",
                  borderRadius: "4px",
                },
              }}
            >
              <Close onClick={handleClose} sx={{ color: "#aaa" }} />
            </Box>
          </Box>
        </DialogTitle>
        <DialogContent>
          <Box mb={2}>
            <Typography variant="subtitle1" gutterBottom>
              Tải file mẫu
            </Typography>
            <Button
              variant="outlined"
              color="primary"
              startIcon={
                loadingDownloadTemplate ? (
                  <CircularProgress size={16} color="inherit" />
                ) : (
                  <Download />
                )
              }
              onClick={handleDownloadTemplate}
              size="small"
              disabled={loadingDownloadTemplate}
            >
              {loadingDownloadTemplate ? "ĐANG TẢI..." : "TẢI FILE MẪU"}
            </Button>
          </Box>

          <Typography variant="subtitle1" gutterBottom>
            Tải lên danh sách
          </Typography>

          <Paper
            variant="outlined"
            sx={{
              border: "1px dashed #ccc",
              borderRadius: 1,
              p: 3,
              textAlign: "center",
              cursor: "pointer",
              backgroundColor: file ? "#f5f5f5" : "transparent",
              "&:hover": {
                backgroundColor: "#f0f7ff",
              },
            }}
            onDragOver={handleDragOver}
            onDrop={handleDrop}
            onClick={handleDragAreaClick}
          >
            <input
              type="file"
              accept=".xlsx,.xls"
              onChange={handleFileChange}
              style={{ display: "none" }}
              ref={fileInputRef}
            />

            <CloudUpload color="primary" sx={{ fontSize: 48, mb: 1 }} />

            <Typography variant="body1" gutterBottom>
              Kéo và thả file vào đây, hoặc click để chọn file
            </Typography>

            <Typography variant="body2" color="textSecondary" gutterBottom>
              Định dạng tệp tải lên: XLSX
            </Typography>

            {file && (
              <Box mt={2} p={1} bgcolor="background.paper" borderRadius={1} position="relative">
                <Typography variant="body2">Đã chọn: {file.name}</Typography>
                <Close
                  sx={{
                    position: "absolute",
                    top: "23%",
                    right: 10,
                    fontSize: 18,
                    cursor: "pointer",
                    color: "#666",
                    "&:hover": {
                      color: "#000",
                    },
                  }}
                  onClick={(e) => {
                    e.stopPropagation();
                    setFile(null);
                    setLoading(false);
                    if (fileInputRef.current) {
                      fileInputRef.current.value = "";
                    }
                  }}
                />
              </Box>
            )}
          </Paper>
        </DialogContent>
        <DialogActions sx={{ padding: "0 24px 20px" }}>
          <Button variant="outlined" onClick={handleClose}>
            Đóng
          </Button>
          <Button
            onClick={handleUpload}
            color="primary"
            variant="contained"
            disabled={!file || loading}
            startIcon={loading ? <CircularProgress size={20} /> : null}
          >
            {loading ? "Đang xử lý..." : "Tải lên"}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default ModalImportExcelItems;
