import React, { Change<PERSON><PERSON><PERSON><PERSON><PERSON>, useEffect, useRef, useState } from "react";
import { MenuItem, TextField } from "@mui/material";
import type { SxProps } from "@mui/material";
import TruncatedText from "./truncated-text/truncated-text";

interface Option {
  value: string;
  label: string;
}

interface CustomSelectProps {
  label?: string;
  value: string;
  onChange: ChangeEventHandler<HTMLInputElement | HTMLTextAreaElement>;
  onBlur?: () => void;
  options: Option[];
  error?: boolean;
  helperText?: string;
  variant?: "standard" | "outlined" | "filled";
  sx?: SxProps;
  disabled?: boolean;
}

export const CustomSelect: React.FC<CustomSelectProps> = ({
  label,
  value,
  onChange,
  onBlur,
  options,
  error = false,
  helperText,
  variant = "outlined",
  sx,
  disabled = false,
}) => {
  const textFieldRef = useRef(null);
  const [menuWidth, setMenuWidth] = useState<number | undefined>(undefined);

  useEffect(() => {
    if (textFieldRef.current) {
      const width = (textFieldRef.current as HTMLElement).offsetWidth;
      setMenuWidth(width);
    }
  }, []);

  return (
    <TextField
      select
      ref={textFieldRef}
      fullWidth
      label={label}
      value={value}
      disabled={disabled}
      onChange={onChange}
      onBlur={onBlur}
      variant={variant}
      error={error}
      helperText={helperText}
      sx={sx}
      slotProps={{
        select: {
          MenuProps: {
            PaperProps: {
              style: {
                width: menuWidth || "auto",
              },
            },
          },
        },
      }}
    >
      {options.map((option) => (
        <MenuItem key={option.value} value={option.value}>
          <TruncatedText text={option.label} />
        </MenuItem>
      ))}
    </TextField>
  );
};

export default CustomSelect;
