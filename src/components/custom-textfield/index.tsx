import React from 'react';
import { TextField, TextFieldProps } from '@mui/material';
import { styled } from '@mui/material/styles';

const StyledTextField = styled(TextField)(({ theme }) => ({
  '& .MuiOutlinedInput-root': {
    borderRadius: theme.shape.borderRadius,
    backgroundColor: theme.palette.background.paper,
    transition: theme.transitions.create(['border-color', 'background-color', 'box-shadow']),
    '&:hover': {
      backgroundColor: theme.palette.action.hover
    },
    '&.Mui-focused': {
      backgroundColor: theme.palette.background.paper,
      boxShadow: `${theme.palette.primary.main} 0 0 0 1px`
    },
    '&.Mui-error': {
      boxShadow: `${theme.palette.error.main} 0 0 0 1px`
    }
  },
  '& .MuiOutlinedInput-notchedOutline': {
    borderColor: theme.palette.divider
  },
  '& .MuiInputLabel-root': {
    '&.Mui-focused': {
      color: theme.palette.text.primary
    }
  },
  '& .MuiInputBase-input': {
    fontSize: '0.875rem'
  },
  // Khi disabled
  '& .Mui-disabled': {
    backgroundColor: theme.palette.action.disabledBackground,
    cursor: 'not-allowed'
  }
}));

const CustomTextField: React.FC<TextFieldProps> = (props) => {
  return <StyledTextField size="small" {...props} />;
};

export default CustomTextField;
