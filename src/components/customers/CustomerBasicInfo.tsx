import React from "react";
import { Card, TextField, Typography } from "@mui/material";
import Grid from "@mui/material/Grid2";
import { Controller, useFormContext } from "react-hook-form";
export default function CustomerBasicInfo() {
  const {
    control,
    formState: { errors },
  } = useFormContext();
  return (
    <Card sx={{ p: 2 }}>
      <Typography variant="h6" sx={{ mb: 4, mt: 2 }}>
        Thông tin cơ bản
      </Typography>
      <Grid size={{ xs: 12 }} sx={{ mb: 3 }}>
        <Typography variant="subtitle1" gutterBottom sx={{ display: "flex" }}>
          Họ tên <Typography sx={{ color: "red", marginLeft: 0.5 }}>*</Typography>
        </Typography>
        <Controller
          name="name"
          control={control}
          render={({ field }) => (
            <TextField
              {...field}
              fullWidth
              error={!!errors.name}
              helperText={errors.name?.message as string}
              variant="outlined"
            />
          )}
        />
      </Grid>
      <Grid sx={{ mb: 3 }}>
        <Typography variant="subtitle1" gutterBottom>
          Email
        </Typography>
        <Controller
          name="email"
          control={control}
          render={({ field }) => (
            <TextField
              {...field}
              fullWidth
              error={!!errors.email}
              helperText={errors.email?.message as string}
              variant="outlined"
            />
          )}
        />
      </Grid>
      <Grid sx={{ mb: 3 }}>
        <Typography variant="subtitle1" gutterBottom sx={{ display: "flex" }}>
          Số điện thoại <Typography sx={{ color: "red", marginLeft: 0.5 }}>*</Typography>
        </Typography>
        <Controller
          name="phone"
          control={control}
          defaultValue=""
          render={({ field }) => (
            <TextField
              {...field}
              fullWidth
              error={!!errors.phone}
              helperText={errors.phone?.message as string}
              variant="outlined"
            />
          )}
        />
      </Grid>
    </Card>
  );
}
