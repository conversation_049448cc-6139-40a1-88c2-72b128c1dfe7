import {
  <PERSON><PERSON>,
  Card,
  Checkbox,
  Chip,
  FormControlLabel,
  FormGroup,
  InputAdornment,
  TextField,
  Tooltip,
  Typography,
} from "@mui/material";
import Grid from "@mui/material/Grid2";
import { Box } from "@mui/system";
import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { fetchTags } from "@/src/slices/listTagSlice";
import { AppDispatch, RootState } from "@/src/store";
import TitleDialog from "@/src/components/dialog/TitleDialog";
import { useStoreId } from "@/src/hooks/use-store-id";

export default function CustomerTag({ submitTag, dataTags = [], isGranted }) {
  const storeId = useStoreId();

  const dispatch = useDispatch<AppDispatch>();
  const { tags } = useSelector((state: RootState) => state.listTag);
  const [currentTags, setCurrentTags] = useState([]);
  const [finalTags, setFinalTags] = useState(dataTags);
  const [open, setOpen] = useState(false);
  const [inputTagText, setInputTagText] = useState("");

  const handleInputTag = (e) => {
    const { value } = e.target;
    setInputTagText(value);
  };
  const handleClose = () => {
    setCurrentTags(finalTags);
    setInputTagText("");
    setOpen(false);
  };
  const onClickManage = () => {
    setOpen(true);
  };
  const handleSubmit = () => {
    setFinalTags(currentTags);
    submitTag(currentTags);
    setOpen(false);
  };

  const handleClickAddTag = () => {
    if (inputTagText.trim() === "") return; // Không cho phép tag trống
    if (!currentTags.includes(inputTagText.trim())) {
      setCurrentTags([...currentTags, inputTagText.trim()]); // Thêm tag vào danh sách
      setInputTagText("");
    }
  };

  useEffect(() => {
    if (storeId) {
      dispatch(fetchTags({ bodyData: { search: "", shopId: storeId } }));
    }
  }, [storeId]);

  // useEffect(() => {
  //   setCurrentTags(finalTags);
  //   setValue(`tags`, finalTags);
  // }, [finalTags]);

  // Cập nhật state khi giá trị props thay đổi (nếu cần)
  useEffect(() => {
    setFinalTags(dataTags);
  }, [dataTags.length]);

  const handleDelete = (tagToDelete) => {
    const updatedTags = currentTags.filter((tag) => tag !== tagToDelete);
    setCurrentTags(updatedTags);
  };

  const handleDeleteFinalTags = (tagToDelete) => {
    const updatedTags = finalTags.filter((tag) => tag !== tagToDelete);
    setFinalTags(updatedTags);
    setCurrentTags(updatedTags);
    submitTag(updatedTags);
  };

  const onClickAvailableTag = (tagToAdd) => {
    const tagName = tagToAdd.name;
    if (tagName.trim() === "") return; // Không cho phép tag trống
    if (!currentTags.includes(tagName.trim())) {
      setCurrentTags([...currentTags, tagName.trim()]); // Thêm tag vào danh sách
    }
  };
  return (
    <Grid>
      <Box className="flex justify-between">
        <Typography variant="subtitle1" gutterBottom>
          Nhãn khách hàng
        </Typography>
        <Tooltip title={!isGranted ? "Bạn không có quyền" : ""}>
          <span>
            <Button
              variant="text"
              sx={{ padding: 0 }}
              disabled={!isGranted}
              onClick={onClickManage}
            >
              Quản lý
            </Button>
          </span>
        </Tooltip>
      </Box>
      <Box>
        {finalTags.map((tag, index) => {
          return (
            <>
              {isGranted ? (
                <Chip
                  key={index}
                  onDelete={() => handleDeleteFinalTags(tag)}
                  label={tag}
                  sx={{ mr: 2, mb: 1, mt: 1 }}
                />
              ) : (
                <Chip key={index} label={tag} sx={{ mr: 2, mb: 1, mt: 1 }} />
              )}
            </>
          );
        })}
      </Box>

      <TitleDialog
        open={open}
        handleClose={handleClose}
        handleSubmit={handleSubmit}
        title="Thêm thẻ"
      >
        <>
          <Box display="flex" flexDirection="row">
            <TextField
              fullWidth
              size="small"
              variant="outlined"
              onChange={handleInputTag}
              placeholder="VIP, đã mua, chưa mua hàng"
              value={inputTagText}
              sx={{
                "& .MuiOutlinedInput-root": {
                  borderTopRightRadius: 0,
                  borderBottomRightRadius: 0, // Loại bỏ bo cong
                },
              }}
            />
            <Button
              variant="contained"
              disabled={!inputTagText.trim()}
              onClick={handleClickAddTag}
              sx={{ marginLeft: 1 }}
              size="small"
            >
              Thêm
            </Button>
          </Box>
          <Box>
            {currentTags && currentTags.length > 0 ? (
              <>
                <Typography variant="subtitle1" sx={{ mt: 2 }}>
                  Thẻ đã chọn
                </Typography>
                {currentTags.map((tag, index) => {
                  return (
                    <Chip
                      key={index}
                      onDelete={() => handleDelete(tag)}
                      label={tag}
                      sx={{ mr: 2, mb: 1, mt: 1 }}
                    />
                  );
                })}
              </>
            ) : null}
          </Box>
          <Box>
            <Typography variant="subtitle1" sx={{ mt: 2 }}>
              Tất cả thẻ
            </Typography>
            {tags.map((tag, index) => {
              return (
                <Chip
                  key={index}
                  onClick={() => onClickAvailableTag(tag)}
                  label={tag.name}
                  sx={{ mr: 2, mb: 1, mt: 1 }}
                />
              );
            })}
          </Box>
        </>
      </TitleDialog>
    </Grid>
  );
}
