import { <PERSON>, <PERSON><PERSON>, Card, Chip, Typography } from "@mui/material";
import React, { useEffect, useState } from "react";
import TitleDialog from "@/src/components/dialog/TitleDialog";
import FormShippingAddress from "./FormShippingAddress";
import FormDialog from "@/src/components/dialog/FormDialog";
import ListAddressDialog from "./ListAddressDialog";

import { defaultShippingValues } from "./EditCustomerForm";
import { FormProvider, useForm } from "react-hook-form";
import { shippingAddressSchema } from "@/src/utils/validations/validationSchema";
import { yupResolver } from "@hookform/resolvers/yup";
import { useUser } from "@/src/api/hooks/user/use-user";
import { useTranslation } from "react-i18next";
import useSnackbar from "@/src/hooks/use-snackbar";
import { useAllPermissions } from "@/src/api/hooks/auth/use-all-permissions";
import { Add, LocationCity } from "@mui/icons-material";
export default function ListAddress({ customer, isGranted }) {
  const { t } = useTranslation();
  const [userAddresses, setUserAddresses] = useState([]);
  const snackbar = useSnackbar();
  const { listAddress, createUserAddress, updateUserAddress, deleteUserAddress } = useUser();
  interface Address {
    fullName: string;
    phoneNumber: string;
    address: string;
    provinceId: string;
    provinceName?: string;
    districtId: string;
    districtName?: string;
    wardId: string;
    wardName?: string;
    shippingAddressId?: string;
    userId: string;
  }

  const [openManageDialog, setOpenManageDialog] = useState<boolean>(false);
  const [openAddAddress, setOpenAddAddress] = useState<boolean>(false);
  const [defaultAddress, setDefaultAddress] = useState<Address | undefined>(undefined);
  const [currentAddress, setCurrentAddress] = useState();
  const methods = useForm({
    defaultValues: defaultShippingValues,
    resolver: yupResolver(shippingAddressSchema(t)),
  });

  const fetchData = async () => {
    const response = await listAddress("?skip=0&limit=99", { userId: customer.userId });
    if (response && response.data) {
      const listFetchAddress = response.data.data;
      const sortedAddresses = listFetchAddress.sort((a, b) => {
        // First sort by isDefault, with true first (b isDefault - a isDefault)
        if (b.isDefault !== a.isDefault) {
          return b.isDefault - a.isDefault;
        }

        // If isDefault is the same, sort by created in descending order
        return new Date(b.created).getTime() - new Date(a.created).getTime();
      });
      setUserAddresses(sortedAddresses);
      setDefaultAddress(listFetchAddress.filter((a) => a.isDefault)[0]);
    }
  };
  useEffect(() => {
    if (customer) {
      fetchData();
    }
  }, [customer]);

  const handleClickManage = () => {
    setOpenManageDialog(true);
  };
  const handleCloseDialog = () => {
    setOpenManageDialog(false);
  };

  const handleClickAddAddress = () => {
    setOpenAddAddress(true);
  };
  const handleCloseAddress = () => {
    setOpenAddAddress(false);
    setCurrentAddress(null);
    methods.reset();
  };

  const handleSubmitSaveAddress = async (data) => {
    const addressData: Address = {
      fullName: data.shippingAddressFullname,
      address: data.shippingAddressAddress,
      phoneNumber: data.shippingAddressPhone,
      wardId: data.shippingAddressWard,
      districtId: data.shippingAddressDistrict,
      provinceId: data.shippingAddressProvince,
      userId: customer.userId,
    };

    if (data.shippingAddressId) {
      // If there is a shippingAddressId, update the address
      addressData.shippingAddressId = data.shippingAddressId;
      const response = await updateUserAddress(addressData);
      snackbar.success("Cập nhật địa chỉ giao hàng thành công");
    } else {
      // If there is no shippingAddressId, create a new address
      const response = await createUserAddress(addressData);
      snackbar.success("Tạo địa chỉ giao hàng thành công");
    }
    fetchData();
  };

  const handleEditAddress = (address) => {
    setCurrentAddress(address);
    setOpenManageDialog(false);
    setOpenAddAddress(true);
  };

  const handleClickSetDefaultAddress = async (address) => {
    const addressData = {
      shippingAddressId: address.shippingAddressId,
      isDefault: true,
      fullName: "",
      phoneNumber: "",
    };

    const response = await updateUserAddress(addressData);
    fetchData();
  };

  const handleDeleteAddress = async (address) => {
    const response = await deleteUserAddress(address.shippingAddressId);
    if (response && response.data) {
      fetchData();
    }
  };

  return (
    <>
      {/* <Box sx={{ p: 2 }}>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            gap: 2,
          }}
        >
          <Typography variant="h6">Địa chỉ giao hàng</Typography>
          <Tooltip title={!isGranted ? "Bạn không có quyền" : ""}>
            <span>
              <Button
                variant="text"
                sx={{ padding: 0 }}
                disabled={!isGranted}
                onClick={handleClickManage}
              >
                Quản lý
              </Button>
            </span>
          </Tooltip>
        </Box>
        <Box>{defaultAddress?.fullName}</Box>
        <Box>{defaultAddress?.phoneNumber}</Box>
        <Box>{defaultAddress?.address}</Box>
        <Box>
          {defaultAddress ? (
            [
              defaultAddress?.wardName,
              defaultAddress?.districtName,
              defaultAddress?.provinceName,
            ].join(", ")
          ) : (
            <Typography color="text.secondary" variant="subtitle2">
              Chưa có địa chỉ giao hàng
            </Typography>
          )}
        </Box>
        <Box
          sx={{
            display: "flex",
            gap: 2,
          }}
        >
          <Tooltip title={!isGranted ? "Bạn không có quyền" : ""}>
            <span>
              <Button
                variant="text"
                sx={{ p: 0 }}
                disabled={!isGranted}
                onClick={handleClickAddAddress}
              >
                Thêm địa chỉ
              </Button>
            </span>
          </Tooltip>
        </Box>
      </Box> */}

      <Box
        sx={{
          marginTop: 2,
          p: 2,
          borderRadius: 2,
          // boxShadow: "0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24)",
          // bgcolor: "background.paper",
          transition: "all 0.3s cubic-bezier(.25,.8,.25,1)",
          // "&:hover": {
          //   boxShadow: "0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23)",
          // },
        }}
      >
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            mb: 1.5,
          }}
        >
          <Typography
            variant="h6"
            sx={{
              fontWeight: 600,
              fontSize: "1.1rem",
              color: "text.primary",
            }}
          >
            Địa chỉ giao hàng
          </Typography>
          <Button
            variant="text"
            sx={{
              p: 0,
              color: "primary.main",
              fontWeight: 500,
              textTransform: "uppercase",
              fontSize: "0.875rem",
              "&:hover": {
                backgroundColor: "transparent",
                textDecoration: "underline",
              },
            }}
            onClick={handleClickManage}
          >
            Quản lý
          </Button>
        </Box>

        {defaultAddress ? (
          <>
            <Chip
              label="Địa chỉ mặc định"
              size="small"
              icon={<LocationCity />}
              sx={{
                background: "rgba(71,108,240,.08)",
                color: "#476cf0",
                mb: 1.5,
                fontWeight: 500,
                fontSize: "0.75rem",
                height: 24,
                "& .MuiChip-icon": {
                  color: "#476cf0",
                },
              }}
            />
            <Box sx={{ display: "flex" }} gap={2} alignItems="center" mb={0.5}>
              <Typography
                sx={{
                  fontWeight: 500,
                  mb: 0.5,
                  color: "text.primary",
                  fontSize: "0.95rem",
                }}
              >
                {defaultAddress.fullName}
              </Typography>
              <Typography
                sx={{
                  mb: 0.5,
                  color: "text.secondary",
                  fontSize: "0.9rem",
                }}
              >
                {defaultAddress.phoneNumber}
              </Typography>
            </Box>
            <Typography
              fontWeight="bold"
              sx={{
                mb: 0.5,
                color: "text.secondary",
                fontSize: "0.9rem",
              }}
            >
              {defaultAddress.address}
            </Typography>
            <Typography
              sx={{
                mb: 2,
                color: "text.secondary",
                fontSize: "0.9rem",
              }}
            >
              {[
                defaultAddress.wardName,
                defaultAddress.districtName,
                defaultAddress.provinceName,
              ].join(", ")}
            </Typography>
          </>
        ) : (
          <Typography
            color="text.secondary"
            variant="subtitle2"
            sx={{
              mb: 2,
              fontStyle: "italic",
              fontSize: "0.9rem",
            }}
          >
            Chưa có địa chỉ giao hàng
          </Typography>
        )}

        <Box
          sx={{
            display: "flex",
            gap: 2,
            mt: 1,
          }}
        >
          <Button
            variant="text"
            startIcon={<Add fontSize="small" />}
            sx={{
              p: 0,
              color: "primary.main",
              fontWeight: 500,
              textTransform: "uppercase",
              fontSize: "0.875rem",
              "&:hover": {
                backgroundColor: "transparent",
                textDecoration: "underline",
              },
            }}
            onClick={handleClickAddAddress}
          >
            Thêm địa chỉ
          </Button>
        </Box>
      </Box>

      <TitleDialog
        open={openManageDialog}
        title="Quản lý địa chỉ giao hàng"
        handleClose={handleCloseDialog}
        showActionDialog={false}
      >
        {userAddresses.length > 0 ? (
          <ListAddressDialog
            addresses={userAddresses}
            handleEditAddress={handleEditAddress}
            handleClickSetDefaultAddress={handleClickSetDefaultAddress}
            handleClickDelete={handleDeleteAddress}
          />
        ) : (
          <Typography sx={{ textAlign: "center" }}>Không có dữ liệu</Typography>
        )}
      </TitleDialog>
      <FormProvider {...methods}>
        <FormDialog
          open={openAddAddress}
          title={currentAddress ? "Sửa địa chỉ giao hàng" : "Thêm địa chỉ giao hàng"}
          onClose={handleCloseAddress}
          onSubmit={handleSubmitSaveAddress}
        >
          <FormShippingAddress shippingAddress={currentAddress} />
        </FormDialog>
      </FormProvider>
    </>
  );
}
