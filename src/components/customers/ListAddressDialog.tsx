import {
  Box,
  <PERSON>ton,
  Chip,
  Dialog,
  <PERSON>alogA<PERSON>,
  DialogContent,
  DialogTitle,
  Divider,
  Paper,
  Tooltip,
  Typography,
} from "@mui/material";
import React, { useState } from "react";
import ModeEditOutlinedIcon from "@mui/icons-material/ModeEditOutlined";
import TitleDialog from "../dialog/TitleDialog";
import DeleteOutlineOutlinedIcon from "@mui/icons-material/DeleteOutlineOutlined";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import { LocationCity } from "@mui/icons-material";
export default function ListAddressDialog({
  addresses,
  handleEditAddress,
  handleClickSetDefaultAddress,
  handleClickDelete,
}) {
  const sortedAddresses = addresses?.sort((a, b) => (a.isDefaultAddress ? -1 : 1));
  const [confirmDialog, setConfirmDialog] = useState<{ open: boolean; address: any }>({
    open: false,
    address: null,
  });

  const handleOpenConfirmDialog = (address) => {
    setConfirmDialog({ open: true, address });
  };

  const handleCloseConfirmDialog = () => {
    setConfirmDialog({ open: false, address: null });
  };

  const handleConfirmDelete = () => {
    if (confirmDialog.address) {
      handleClickDelete?.(confirmDialog.address);
    }
    handleCloseConfirmDialog();
  };

  return (
    <>
      <Box>
        {sortedAddresses.map((address, index) => {
          const isLast = index === addresses.length - 1;
          return (
            <Paper
              key={index}
              elevation={0}
              sx={{
                mb: isLast ? 0 : 3,
                p: 2.5,
                borderRadius: 2,
                border: "1px solid",
                borderColor: address.isDefault ? "primary.light" : "divider",
                backgroundColor: address.isDefault ? "rgba(71,108,240,0.02)" : "background.paper",
                position: "relative",
                transition: "all 0.2s ease-in-out",
                "&:hover": {
                  boxShadow: "0 4px 8px rgba(0,0,0,0.08)",
                },
              }}
            >
              <Box
                display="flex"
                flexDirection={{
                  xs: "column",
                  sm: "row",
                }}
                justifyContent="space-between"
              >
                <Box flex={1}>
                  {address.isDefault ? (
                    <Chip
                      label="Địa chỉ mặc định"
                      size="small"
                      icon={<LocationCity />}
                      sx={{
                        background: "rgba(71,108,240,.08)",
                        color: "#476cf0",
                        mb: 1.5,
                        fontWeight: 500,
                        fontSize: "0.75rem",
                        height: 24,
                        "& .MuiChip-icon": {
                          color: "#476cf0",
                        },
                      }}
                    />
                  ) : null}

                  <Box display="flex" gap={2} alignItems="center" mb={0.5}>
                    <Typography
                      sx={{
                        fontWeight: 500,
                        fontSize: "0.95rem",
                      }}
                    >
                      {address.fullName}
                    </Typography>
                    <Typography
                      color="text.secondary"
                      sx={{
                        fontSize: "0.9rem",
                      }}
                    >
                      {address.phoneNumber}
                    </Typography>
                  </Box>

                  <Box mb={0.5}>
                    <Typography
                      fontWeight="bold"
                      sx={{
                        fontSize: "0.95rem",
                      }}
                    >
                      {address.address}
                    </Typography>
                  </Box>

                  <Box mb={1.5}>
                    <Typography
                      color="text.secondary"
                      sx={{
                        fontSize: "0.9rem",
                      }}
                    >
                      {[address?.wardName, address?.districtName, address?.provinceName].join(", ")}
                    </Typography>
                  </Box>

                  {!address.isDefault && (
                    <Button
                      variant="outlined"
                      size="small"
                      onClick={() => handleClickSetDefaultAddress(address)}
                      sx={{
                        mt: 1,
                        textTransform: "none",
                        borderColor: "primary.main",
                        color: "primary.main",
                        fontWeight: 500,
                        fontSize: "0.8rem",
                        borderRadius: 6,
                        px: 2,
                        py: 0.5,
                        "&:hover": {
                          backgroundColor: "rgba(71,108,240,0.04)",
                          borderColor: "primary.dark",
                        },
                      }}
                    >
                      Đặt làm địa chỉ mặc định
                    </Button>
                  )}
                </Box>

                <Box
                  sx={{
                    display: "flex",
                    alignItems: "flex-start",
                    marginTop: {
                      xs: 2,
                      sm: 0,
                    },
                    ml: {
                      xs: 0,
                      sm: 2,
                    },
                  }}
                >
                  <Tooltip title="Sửa địa chỉ">
                    <Button
                      color="primary"
                      onClick={() => handleEditAddress(address)}
                      sx={{
                        minWidth: 40,
                        width: 40,
                        height: 40,
                        borderRadius: "50%",
                        p: 0,
                        mr: 1,
                        color: "primary.main",
                        "&:hover": {
                          backgroundColor: "rgba(71,108,240,0.08)",
                        },
                      }}
                    >
                      <EditOutlinedIcon fontSize="small" />
                    </Button>
                  </Tooltip>

                  {!address.isDefault && (
                    <Tooltip title="Xoá địa chỉ">
                      <Button
                        color="error"
                        onClick={() => handleOpenConfirmDialog(address)}
                        sx={{
                          minWidth: 40,
                          width: 40,
                          height: 40,
                          borderRadius: "50%",
                          p: 0,
                          color: "error.main",
                          "&:hover": {
                            backgroundColor: "rgba(211,47,47,0.08)",
                          },
                        }}
                      >
                        <DeleteOutlineOutlinedIcon fontSize="small" />
                      </Button>
                    </Tooltip>
                  )}
                </Box>
              </Box>
            </Paper>
          );
        })}

        <Dialog
          open={confirmDialog.open}
          onClose={handleCloseConfirmDialog}
          PaperProps={{
            sx: {
              borderRadius: 2,
              width: "100%",
              maxWidth: 400,
            },
          }}
        >
          <DialogTitle sx={{ pb: 1, fontWeight: 600 }}>Xóa địa chỉ</DialogTitle>
          <DialogContent>
            <Typography variant="body2">Bạn có chắc muốn xóa địa chỉ này?</Typography>
          </DialogContent>
          <DialogActions sx={{ px: 3, pb: 2 }}>
            <Button
              onClick={handleCloseConfirmDialog}
              variant="outlined"
              sx={{
                textTransform: "none",
                borderRadius: 1.5,
              }}
            >
              Hủy
            </Button>
            <Button
              onClick={handleConfirmDelete}
              variant="contained"
              color="error"
              sx={{
                textTransform: "none",
                borderRadius: 1.5,
                boxShadow: "none",
                "&:hover": {
                  boxShadow: "none",
                },
              }}
            >
              Xác nhận
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </>
  );
}
