import React from 'react';
import { Alert, FormHelperText } from '@mui/material';

interface ErrorAlertProps {
  error: string | null;
  isShowError: boolean;
}

const ErrorAlert: React.FC<ErrorAlertProps> = ({ error, isShowError }) => {
  if (!isShowError || !error) return null;

  return (
    <Alert
      severity="error"
      sx={{
        mt: 2,
        borderRadius: 1,
        backgroundColor: 'rgba(255, 0, 0, 0.1)',
        display: 'flex',
        alignItems: 'center'
      }}
    >
      <FormHelperText
        error
        sx={{
          mt: 0,
          fontSize: '0.8rem',
          textAlign: 'start',
          flex: 1
        }}
      >
        {error}
      </FormHelperText>
    </Alert>
  );
};

export default ErrorAlert;
