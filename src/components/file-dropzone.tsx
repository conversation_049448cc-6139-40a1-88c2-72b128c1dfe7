import React, { useState, useEffect } from "react";
import { useDropzone } from "react-dropzone";
import Upload01Icon from "@untitled-ui/icons-react/build/esm/Upload01";
import XIcon from "@untitled-ui/icons-react/build/esm/X";
import { Avatar, Box, IconButton, Stack, SvgIcon, Typography, SxProps, Theme } from "@mui/material";
import { ImageType } from "nextjs-api-lib";
import { logger } from "@/src/utils/logger";
import { ImageProcessor } from "@/src/utils/image-processor";
import { FILE_SIZE_2MB, IMAGE_TYPE } from "../constants/constant";

interface FileDropzoneProps {
  caption?: string;
  onRemove?: (index: number) => void;
  onDrop?: (files: File[]) => void;
  maxFiles?: number;
  accept?: Record<string, string[]>;
  maxSize?: number;
  disabled?: boolean;
  showError?: boolean;
  sx?: SxProps<Theme>;
  existingFiles?: string[];
  previewUrlsImg?: any;
  setPreviewUrlsImg?: any;
}

export const FileDropzone: React.FC<FileDropzoneProps> = ({
  caption,
  onRemove,
  onDrop,
  maxFiles,
  accept = { "image/*": IMAGE_TYPE },
  maxSize = FILE_SIZE_2MB,
  disabled = false,
  showError = false,
  sx,
  existingFiles = [],
  previewUrlsImg,
  setPreviewUrlsImg,
}) => {
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);

  useEffect(() => {
    if (Array.isArray(existingFiles)) {
      setPreviewUrlsImg(existingFiles);
    }
    return () => {
      previewUrlsImg?.forEach((url) => {
        if (url?.startsWith("blob:")) {
          URL.revokeObjectURL(url);
        }
      });
    };
  }, [existingFiles]);

  const handleDrop = async (acceptedFiles: File[]) => {
    try {
      const processedFiles: File[] = [];
      const newPreviewUrls: string[] = [];
      for (const file of acceptedFiles) {
        if (file.type.startsWith("image/")) {
          logger.debug("Processing image:", { fileName: file.name, fileSize: file.size });
          const image = new Image();
          const previewUrlClone = URL.createObjectURL(file);
          image.onload = () => {
            URL.revokeObjectURL(previewUrlClone);
          };
          const processedFile = await ImageProcessor.processImage(file, {
            maxSizeMB: 1,
            maxWidth: image.width,
            maxHeight: image.height,
            maintainAspectRatio: true,
          });
          const previewUrl = URL.createObjectURL(processedFile);
          newPreviewUrls.push(previewUrl);
          processedFiles.push(processedFile);
        }
      }

      setSelectedFiles((prevFiles) => [...prevFiles, ...processedFiles]);
      setPreviewUrlsImg((prevUrls) => [...prevUrls, ...newPreviewUrls]);

      if (onDrop) {
        onDrop(processedFiles);
      }
    } catch (error) {
      logger.error("Error processing dropped files:", error);
    }
  };

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    accept: {
      "image/png": [".png"],
      "image/jpeg": [".jpg", ".jpeg", ".jfif"],
    },
    maxFiles,
    maxSize,
    disabled,
    onDrop: handleDrop,
  });

  return (
    <Box sx={{ width: "100%", ...sx }}>
      <Box
        {...getRootProps()}
        sx={{
          border: 1,
          borderRadius: 1,
          borderStyle: "dashed",
          borderColor: "divider",
          p: 3,
          outline: "none",
          ...(isDragActive && {
            backgroundColor: "action.active",
            opacity: 0.5,
          }),
          ...(disabled && {
            backgroundColor: "action.disabledBackground",
            cursor: "not-allowed",
          }),
        }}
      >
        <input {...getInputProps()} />
        <Stack alignItems="center" spacing={2}>
          <SvgIcon>
            <Upload01Icon />
          </SvgIcon>
          <Typography variant="h6">{caption || "Select file"}</Typography>
        </Stack>
      </Box>

      {previewUrlsImg?.length > 0 && (
        <Box sx={{ mt: 2 }}>
          <Stack direction="row" flexWrap="wrap" gap={2}>
            {previewUrlsImg?.map((url, index) => (
              <Box
                key={index}
                sx={{
                  position: "relative",
                  width: 100,
                  height: 100,
                }}
              >
                <Avatar
                  src={url}
                  variant="rounded"
                  sx={{
                    width: "auto",
                    height: "100%",
                    objectFit: "cover",
                  }}
                />
                <IconButton
                  size="small"
                  sx={{
                    position: "absolute",
                    top: 4,
                    right: 4,
                    backgroundColor: "background.paper",
                    "&:hover": {
                      backgroundColor: "action.hover",
                    },
                  }}
                  onClick={() => onRemove(index)}
                >
                  <SvgIcon fontSize="small">
                    <XIcon />
                  </SvgIcon>
                </IconButton>
              </Box>
            ))}
          </Stack>
        </Box>
      )}
    </Box>
  );
};
