import React, { useState, useEffect } from "react";
import { Box, Avatar, Typography } from "@mui/material";
import { useTheme } from "@mui/material/styles";
import { useTranslation } from "react-i18next";
import { tokens } from "src/locales/tokens";
import { logger } from "@/src/utils/logger";
import { isValidImageFile } from "../pages/dashboard/product/category-management/create";
import useSnackbar from "../hooks/use-snackbar";
import { FILE_SIZE_2MB } from "../constants/constant";

interface LogoUploadProps {
  onLogoChange: (file: File | null) => void;
  showError: boolean;
  currentLogo?: string;
}

const LogoUploadV2: React.FC<LogoUploadProps> = ({ onLogoChange, showError, currentLogo }) => {
  const { t } = useTranslation();
  const [logo, setLogo] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | undefined>(currentLogo);
  const theme = useTheme();
  const snackbar = useSnackbar();
  useEffect(() => {
    setPreviewUrl(currentLogo);
  }, [currentLogo]);

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!isValidImageFile(file)) {
      snackbar.error(
        "Định dạng file không hợp lệ. Hãy chọn file định dạng PNG, JPG, JPEG hoặc JFIF."
      );
    }

    if (file && file.size > FILE_SIZE_2MB) {
      snackbar.error(
        `Kích thước ảnh không được vượt quá ${(FILE_SIZE_2MB / 1024 / 1024).toFixed(1)}MB.`
      );
      return;
    } else {
      if (file && file.type.startsWith("image/")) {
        try {
          logger.debug("Processing logo image:", { fileName: file.name, fileSize: file.size });

          const img = new Image();
          img.src = URL.createObjectURL(file);
          img.onload = async () => {
            const canvas = document.createElement("canvas");
            const ctx = canvas.getContext("2d");

            if (ctx) {
              // Tính toán kích thước mới
              const maxWidth = 512;
              const maxHeight = 512;
              let { width, height } = img;

              // Giữ tỷ lệ khung hình
              if (width > maxWidth) {
                height *= maxWidth / width;
                width = maxWidth;
              }
              if (height > maxHeight) {
                width *= maxHeight / height;
                height = maxHeight;
              }

              // Thiết lập kích thước canvas
              canvas.width = width;
              canvas.height = height;

              // Vẽ hình ảnh lên canvas
              ctx.drawImage(img, 0, 0, width, height);

              // Lưu lại hình ảnh với nền trong suốt
              const processedFile = await new Promise<File>((resolve) => {
                canvas.toBlob(
                  (blob) => {
                    if (blob) {
                      resolve(new File([blob], file.name, { type: file.type }));
                    } else {
                      resolve(file);
                    }
                  },
                  file.type,
                  0.9
                ); // Chất lượng 0.9
              });

              const newPreviewUrl = URL.createObjectURL(processedFile);

              logger.debug("Logo processed successfully", {
                originalSize: file.size,
                processedSize: processedFile.size,
                fileName: processedFile.name,
              });

              if (previewUrl && previewUrl !== currentLogo) {
                URL.revokeObjectURL(previewUrl);
              }

              setLogo(processedFile);
              setPreviewUrl(newPreviewUrl);
              onLogoChange(processedFile);
            }
          };
        } catch (error) {
          logger.error("Error processing logo:", error);
          setLogo(null);
          setPreviewUrl(currentLogo);
          onLogoChange(null);
        }
      } else {
        logger.warn("Invalid file type selected for logo");
        setLogo(null);
        setPreviewUrl(currentLogo);
        onLogoChange(null);
      }
    }
  };
  useEffect(() => {
    return () => {
      if (previewUrl && previewUrl !== currentLogo) {
        URL.revokeObjectURL(previewUrl);
        logger.debug("Cleaned up logo preview URL");
      }
    };
  }, [previewUrl, currentLogo]);

  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        cursor: "pointer",
      }}
      onClick={() => document.getElementById("logo-input")?.click()}
    >
      <input
        id="logo-input"
        type="file"
        accept="image/*"
        size={FILE_SIZE_2MB}
        style={{ display: "none" }}
        onChange={handleFileChange}
      />
      <Avatar
        src={previewUrl}
        sx={{
          width: 128,
          height: 128,
          borderRadius: "50%",
          border: `2px dashed ${showError ? "red" : theme.palette.primary.main}`,
          mb: 1,
          backgroundColor: logo || currentLogo ? "transparent" : theme.palette.primary.light,
          transition: "border-color 0.3s ease",
        }}
      >
        {!logo && !currentLogo && t(tokens.logoUpload.uploadLogo)}
      </Avatar>
      {showError && (
        <Typography variant="caption" color="error">
          {t(tokens.store.logoRequired)}
        </Typography>
      )}
      <Typography variant="caption" color="textSecondary">
        {t(tokens.logoUpload.description)}
      </Typography>
    </Box>
  );
};

export default LogoUploadV2;
