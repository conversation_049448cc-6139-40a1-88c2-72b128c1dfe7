import React, { useState, useEffect } from "react";
import { Box, Avatar, Typography } from "@mui/material";
import { useTheme } from "@mui/material/styles";
import { ImageType } from "nextjs-api-lib";
import { useTranslation } from "react-i18next";
import { tokens } from "src/locales/tokens";
import { logger } from "@/src/utils/logger";
import { ImageProcessor } from "@/src/utils/image-processor";

interface LogoUploadProps {
  onLogoChange: (file: File | null) => void;
  showError: boolean;
  currentLogo?: string;
  uploadIcon?: any;
  setShopLogo: React.Dispatch<React.SetStateAction<File>>;
  shopLogo: File;
  setShowError?: any;
}

const LogoUpload: React.FC<LogoUploadProps> = ({
  onLogoChange,
  setShopLogo,
  shopLogo,
  showError,
  currentLogo,
  uploadIcon,
  setShowError,
}) => {
  const { t } = useTranslation();
  const [logo, setLogo] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | undefined>(currentLogo);
  const theme = useTheme();

  useEffect(() => {
    setPreviewUrl(currentLogo);
  }, [currentLogo]);

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];

    if (file && file.type.startsWith("image/")) {
      try {
        setShopLogo(file);

        const processedFile = await ImageProcessor.processImage(file, {
          maxSizeMB: 0.5,
          maxWidth: 512,
          maxHeight: 512,
          quality: 0.9,
          maintainAspectRatio: true,
          allowedTypes: [ImageType.JPEG, ImageType.PNG, ImageType.WEBP],
        });

        const newPreviewUrl = URL.createObjectURL(processedFile);

        if (previewUrl && previewUrl !== currentLogo) {
          URL.revokeObjectURL(previewUrl);
        }

        setLogo(processedFile);
        setPreviewUrl(newPreviewUrl);
        setShowError(false);
      } catch (error) {
        logger.error("Error processing logo:", error);
        setLogo(null);
        setPreviewUrl(currentLogo);
        onLogoChange(null);
      }
    } else {
      logger.warn("Invalid file type selected for logo");
      setLogo(null);
      setPreviewUrl(currentLogo);
      onLogoChange(null);
    }
  };

  useEffect(() => {
    return () => {
      if (previewUrl && previewUrl !== currentLogo) {
        URL.revokeObjectURL(previewUrl);
        logger.debug("Cleaned up logo preview URL");
      }
    };
  }, [previewUrl, currentLogo]);

  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        cursor: "pointer",
      }}
      onClick={() => document.getElementById("logo-input")?.click()}
    >
      <input
        id="logo-input"
        type="file"
        accept="image/*"
        style={{ display: "none" }}
        onChange={handleFileChange}
      />
      <Avatar
        src={previewUrl}
        sx={{
          width: 300,
          height: 300,
          borderRadius: "50%",
          border: `2px dashed ${showError ? "red" : theme.palette.primary.main}`,
          mb: 1,
          backgroundColor: logo || currentLogo ? "transparent" : theme.palette.primary.light,
          transition: "border-color 0.3s ease",
        }}
      >
        {!logo && !currentLogo && t(tokens.logoUpload.uploadLogo)}
      </Avatar>
      {showError && (
        <Typography variant="caption" color="error">
          {t(tokens.store.logoRequired)}
        </Typography>
      )}
    </Box>
  );
};

export default LogoUpload;
