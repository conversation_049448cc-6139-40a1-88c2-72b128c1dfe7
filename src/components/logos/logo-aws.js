export const LogoAws = (props) => (
  <svg
    width={41}
    height={24}
    viewBox="0 0 41 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <g clipPath="url(#clip0_11275_169470)">
      <path
        d="M11.9542 8.75601C11.9542 9.24392 12.007 9.63953 12.0993 9.92963C12.2048 10.2197 12.3366 10.5362 12.5213 10.8791C12.5872 10.9846 12.6136 11.0901 12.6136 11.1824C12.6136 11.3143 12.5345 11.4461 12.363 11.578L11.5323 12.1318C11.4136 12.211 11.2949 12.2505 11.1894 12.2505C11.0575 12.2505 10.9257 12.1846 10.7938 12.0659C10.6092 11.8681 10.4509 11.6571 10.3191 11.4461C10.1872 11.2219 10.0553 10.9714 9.91028 10.6681C8.88171 11.8813 7.5894 12.4879 6.03335 12.4879C4.92566 12.4879 4.04214 12.1714 3.39599 11.5384C2.74984 10.9055 2.42017 10.0615 2.42017 9.00656C2.42017 7.88568 2.81577 6.97579 3.62017 6.29007C4.42456 5.60436 5.49269 5.2615 6.85094 5.2615C7.29929 5.2615 7.76083 5.30106 8.24874 5.367C8.73665 5.43293 9.23775 5.53843 9.76522 5.65711V4.69447C9.76522 3.69227 9.55423 2.99337 9.14544 2.58458C8.72346 2.17579 8.01137 1.97799 6.99599 1.97799C6.53445 1.97799 6.05973 2.03073 5.57181 2.14941C5.0839 2.2681 4.60918 2.41315 4.14764 2.59777C3.93665 2.69007 3.77841 2.74282 3.6861 2.7692C3.59379 2.79557 3.52786 2.80876 3.47511 2.80876C3.2905 2.80876 3.19819 2.67689 3.19819 2.39996V1.75381C3.19819 1.54282 3.22456 1.38458 3.2905 1.29227C3.35643 1.19996 3.47511 1.10766 3.65973 1.01535C4.12126 0.777987 4.67511 0.580184 5.32126 0.421943C5.96742 0.250514 6.65313 0.171393 7.37841 0.171393C8.94764 0.171393 10.0949 0.527437 10.8334 1.23952C11.5586 1.95161 11.9279 3.03293 11.9279 4.48348V8.75601H11.9542ZM6.60039 10.7604C7.03555 10.7604 7.4839 10.6813 7.95863 10.523C8.43335 10.3648 8.85533 10.0747 9.21138 9.67909C9.42236 9.42854 9.58061 9.15161 9.65973 8.83513C9.73885 8.51865 9.7916 8.13623 9.7916 7.68788V7.13403C9.40918 7.04172 9.00039 6.9626 8.57841 6.90985C8.15643 6.85711 7.74764 6.83073 7.33885 6.83073C6.45533 6.83073 5.80918 7.00216 5.37401 7.35821C4.93885 7.71425 4.72786 8.21535 4.72786 8.87469C4.72786 9.49447 4.8861 9.95601 5.21577 10.2725C5.53225 10.6022 5.99379 10.7604 6.60039 10.7604ZM17.1894 12.1846C16.952 12.1846 16.7938 12.145 16.6883 12.0527C16.5828 11.9736 16.4905 11.789 16.4114 11.5384L13.3125 1.34502C13.2334 1.08128 13.1938 0.909855 13.1938 0.817547C13.1938 0.606558 13.2993 0.487877 13.5103 0.487877H14.8026C15.0531 0.487877 15.2246 0.527437 15.3169 0.619745C15.4224 0.698866 15.5015 0.883481 15.5806 1.13403L17.796 9.8637L19.8531 1.13403C19.9191 0.870294 19.9982 0.698866 20.1037 0.619745C20.2092 0.540624 20.3938 0.487877 20.6312 0.487877H21.6861C21.9366 0.487877 22.1081 0.527437 22.2136 0.619745C22.3191 0.698866 22.4114 0.883481 22.4641 1.13403L24.5476 9.96919L26.829 1.13403C26.9081 0.870294 27.0004 0.698866 27.0927 0.619745C27.1982 0.540624 27.3696 0.487877 27.607 0.487877H28.8334C29.0443 0.487877 29.163 0.593371 29.163 0.817547C29.163 0.883481 29.1498 0.949415 29.1366 1.02854C29.1235 1.10766 29.0971 1.21315 29.0443 1.35821L25.8663 11.5516C25.7872 11.8153 25.6949 11.9868 25.5894 12.0659C25.4839 12.145 25.3125 12.1978 25.0883 12.1978H23.9542C23.7037 12.1978 23.5323 12.1582 23.4268 12.0659C23.3213 11.9736 23.229 11.8022 23.1762 11.5384L21.1323 3.03293L19.1015 11.5252C19.0356 11.789 18.9564 11.9604 18.8509 12.0527C18.7454 12.145 18.5608 12.1846 18.3235 12.1846H17.1894ZM34.1345 12.5406C33.4487 12.5406 32.763 12.4615 32.1037 12.3033C31.4443 12.145 30.9301 11.9736 30.5872 11.7758C30.3762 11.6571 30.2312 11.5252 30.1784 11.4066C30.1257 11.2879 30.0993 11.156 30.0993 11.0373V10.3648C30.0993 10.0879 30.2048 9.95601 30.4026 9.95601C30.4817 9.95601 30.5608 9.9692 30.6399 9.99557C30.7191 10.0219 30.8377 10.0747 30.9696 10.1274C31.418 10.3252 31.9059 10.4835 32.4202 10.589C32.9476 10.6945 33.4619 10.7472 33.9894 10.7472C34.8202 10.7472 35.4663 10.6022 35.9147 10.3121C36.363 10.0219 36.6004 9.59996 36.6004 9.0593C36.6004 8.69007 36.4817 8.38678 36.2443 8.13623C36.007 7.88568 35.5586 7.6615 34.9125 7.45051L33.0004 6.85711C32.0377 6.55381 31.3257 6.10546 30.8905 5.51205C30.4553 4.93183 30.2312 4.28568 30.2312 3.59996C30.2312 3.04612 30.3498 2.55821 30.5872 2.13623C30.8246 1.71425 31.141 1.34502 31.5366 1.05491C31.9323 0.751613 32.3806 0.527437 32.9081 0.369195C33.4356 0.210954 33.9894 0.14502 34.5696 0.14502C34.8597 0.14502 35.163 0.158206 35.4531 0.197767C35.7564 0.237327 36.0334 0.290074 36.3103 0.342822C36.574 0.408756 36.8246 0.47469 37.0619 0.553811C37.2993 0.632932 37.4839 0.712052 37.6158 0.791173C37.8004 0.896668 37.9323 1.00216 38.0114 1.12084C38.0905 1.22634 38.1301 1.37139 38.1301 1.55601V2.17579C38.1301 2.45271 38.0246 2.59777 37.8268 2.59777C37.7213 2.59777 37.5498 2.54502 37.3257 2.43952C36.574 2.09667 35.7301 1.92524 34.7938 1.92524C34.0421 1.92524 33.4487 2.04392 33.0399 2.29447C32.6312 2.54502 32.4202 2.92744 32.4202 3.4681C32.4202 3.83733 32.552 4.15381 32.8158 4.40436C33.0795 4.65491 33.5674 4.90546 34.2663 5.12964L36.1388 5.72304C37.0883 6.02634 37.774 6.44832 38.1828 6.98898C38.5916 7.52963 38.7894 8.14941 38.7894 8.83513C38.7894 9.40216 38.6707 9.91645 38.4465 10.3648C38.2092 10.8132 37.8927 11.2088 37.4839 11.5252C37.0751 11.8549 36.5872 12.0923 36.0202 12.2637C35.4268 12.4483 34.807 12.5406 34.1345 12.5406Z"
        fill="currentColor"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M36.6267 18.9495C32.2883 22.1539 25.985 23.855 20.5652 23.855C12.9696 23.855 6.12564 21.0462 0.956412 16.378C0.547621 16.0088 0.916852 15.5077 1.40476 15.7978C6.99597 19.0418 13.8927 21.0066 21.0267 21.0066C25.8399 21.0066 31.1278 20.0044 35.9938 17.9473C36.7191 17.6176 37.3388 18.422 36.6267 18.9495Z"
        fill="currentColor"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M38.4333 16.8924C37.8794 16.1803 34.7673 16.5495 33.3563 16.7209C32.9344 16.7737 32.8684 16.4045 33.2508 16.1275C35.73 14.3869 39.8047 14.888 40.2794 15.4682C40.7541 16.0616 40.1476 20.1363 37.8267 22.088C37.4706 22.3913 37.1278 22.233 37.286 21.8374C37.8135 20.5319 38.9871 17.5913 38.4333 16.8924Z"
        fill="currentColor"
      />
    </g>
    <defs>
      <clipPath id="clip0_11275_169470">
        <rect
          width={40}
          height={24}
          fill="white"
          transform="translate(0.5)"
        />
      </clipPath>
    </defs>
  </svg>
);
