import { OrderDeliveryMethod } from "@/src/api/types/cart.types";
import { LocalShipping, LocalShippingOutlined } from "@mui/icons-material";
import {
  Box,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  SelectChangeEvent,
  Typography,
} from "@mui/material";
import React, { useState } from "react";

// Define the available delivery methods
const deliveryMethods: OrderDeliveryMethod[] = [
  { value: "InShop", label: "Nhận hàng tại quầy" },
  { value: "ExpressDelivery", label: "Chuyển phát nhanh" },
];
export default function DeliveryMethodOrder({ selectedMethod, handleDeliveryMethod }) {
  return (
    <Box>
      <Box>
        <Typography variant="h6" sx={{ marginBottom: 1, fontWeight: 600 }}>
          <LocalShipping sx={{ fontSize: 22, marginRight: 0.5, color: "#1976d2" }} /> <PERSON><PERSON><PERSON>ng thức
          giao hàng
        </Typography>
        <Box sx={{ minWidth: 120 }}>
          <FormControl fullWidth>
            <Select
              labelId="delivery-method-select-label"
              id="delivery-method-select"
              value={selectedMethod}
              onChange={handleDeliveryMethod}
            >
              {deliveryMethods.map((method) => (
                <MenuItem key={method.value} value={method.value}>
                  {method.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Box>
      </Box>
    </Box>
  );
}
