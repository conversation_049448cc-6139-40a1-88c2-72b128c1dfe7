import React from "react";
import Grid from "@mui/material/Grid2";
import { Box, Button, Divider, Paper, Typography } from "@mui/material";
import { formatMoney } from "@/src/utils/format-money";
import { Grid02 } from "@untitled-ui/icons-react";
export default function OrderTotalPaymentSummary({
  cartData,
  isEdit,
  handleClickPayment,
  isDisablePayment,
}) {
  const originPrice = cartData.listItems.reduce((total, item) => {
    return total + item.price * item.quantity;
  }, 0);
  const taxPrice = cartData.listItems.reduce((total, item) => {
    const taxRateTemp = item.customTaxRate || item.taxRate || 0;
    return total + item.price * item.quantity * (taxRateTemp / 100);
  }, 0);
  const totalPrice = cartData.listItems.reduce((total, item) => {
    return total + item.price * item.quantity;
  }, 0);

  const totalPriceWithTax =
    totalPrice +
    taxPrice +
    cartData.transportPrice -
    cartData.voucherTransportPrice -
    cartData.voucherPromotionPrice;
  return (
    <Paper
      elevation={0}
      sx={{
        p: 1,
        bgcolor: "background.paper",
        borderColor: "divider",
      }}
    >
      <Typography
        variant="h6"
        sx={{
          mb: 2,
          fontWeight: 600,
        }}
      >
        Tổng quan đơn hàng
      </Typography>

      <Divider sx={{ mb: 2 }} />

      <Grid
        container
        sx={{ mb: 1.5, display: "flex", alignItems: "center", justifyContent: "space-between" }}
      >
        <Grid>
          <Typography variant="body2" color="text.secondary">
            Thành tiền chưa thuế:
          </Typography>
        </Grid>
        <Grid>
          <Box sx={{ display: "flex", justifyContent: "space-between" }}>
            <Typography sx={{ marginRight: 10 }} variant="body2">
              {cartData.listItems.length} sản phẩm
            </Typography>
            <Typography variant="body2" fontWeight={500}>
              {formatMoney(originPrice)}đ
            </Typography>
          </Box>
        </Grid>
      </Grid>
      {/* <Grid
        container
        sx={{ mb: 1.5, display: "flex", alignItems: "center", justifyContent: "space-between" }}
      >
        <Grid>
          <Typography variant="body2" color="text.secondary">
            Thành tiền chưa thuế:
          </Typography>
        </Grid>
        <Grid sx={{ textAlign: "right" }}>
          <Typography variant="body2" fontWeight={500}>
            {formatMoney(cartData.price)}đ
          </Typography>
        </Grid>
      </Grid> */}

      <Grid
        container
        sx={{ mb: 1.5, display: "flex", alignItems: "center", justifyContent: "space-between" }}
      >
        <Grid>
          <Typography variant="body2" color="text.secondary">
            Tiền thuế GTGT:
          </Typography>
        </Grid>
        <Grid sx={{ textAlign: "right" }}>
          <Typography variant="body2">{formatMoney(taxPrice)}đ</Typography>
        </Grid>
      </Grid>

      <Grid
        container
        sx={{ mb: 1.5, display: "flex", alignItems: "center", justifyContent: "space-between" }}
      >
        <Grid>
          <Typography variant="body2" color="text.secondary">
            Phí vận chuyển:
          </Typography>
        </Grid>
        <Grid sx={{ textAlign: "right" }}>
          <Typography variant="body2">{formatMoney(cartData.transportPrice)}đ</Typography>
        </Grid>
      </Grid>

      <Grid
        container
        sx={{ mb: 1.5, display: "flex", alignItems: "center", justifyContent: "space-between" }}
      >
        <Grid>
          <Typography variant="body2" color="text.secondary">
            Giảm giá vận chuyển:
          </Typography>
        </Grid>
        <Grid sx={{ textAlign: "right" }}>
          <Typography variant="body2">{formatMoney(cartData.voucherTransportPrice)}đ</Typography>
        </Grid>
      </Grid>
      <Grid
        container
        sx={{ mb: 1.5, display: "flex", alignItems: "center", justifyContent: "space-between" }}
      >
        <Grid>
          <Typography variant="body2" color="text.secondary">
            Giảm giá sản phẩm:
          </Typography>
        </Grid>
        <Grid sx={{ textAlign: "right" }}>
          <Typography variant="body2">{formatMoney(cartData.voucherPromotionPrice)}đ</Typography>
        </Grid>
      </Grid>

      <Divider sx={{ my: 2 }} />

      <Grid
        container
        sx={{
          mb: isEdit ? 3 : 0,
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
        }}
      >
        <Grid>
          <Typography variant="subtitle1" fontWeight={600}>
            Tổng cộng tiền thanh toán:
          </Typography>
        </Grid>
        <Grid sx={{ textAlign: "right" }}>
          <Typography variant="subtitle1" fontWeight={600} color="primary.main">
            {formatMoney(totalPriceWithTax)}đ
          </Typography>
        </Grid>
      </Grid>
      <Divider sx={{ mb: 2 }} />
      {isEdit && (
        <Box sx={{ display: "flex", justifyContent: "flex-end", mt: 2 }}>
          <Button
            variant="contained"
            onClick={handleClickPayment}
            disabled={isDisablePayment}
            sx={{
              textTransform: "none",
              px: 3,
              py: 1,
              borderRadius: 1.5,
            }}
          >
            Tạo đơn hàng
          </Button>
        </Box>
      )}
    </Paper>
  );
}
