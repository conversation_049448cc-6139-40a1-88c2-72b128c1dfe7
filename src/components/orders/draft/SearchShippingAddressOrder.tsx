import { useCart } from "@/src/api/hooks/cart/use-cart";
import { useUser } from "@/src/api/hooks/user/use-user";
import { useStoreId } from "@/src/hooks/use-store-id";
import { Search } from "@mui/icons-material";
import {
  Box,
  Button,
  Checkbox,
  Divider,
  FormControlLabel,
  InputAdornment,
  Radio,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TablePagination,
  TableRow,
  TextField,
  Typography,
} from "@mui/material";
import _ from "lodash";
import React, { useCallback, useEffect, useState } from "react";

export default function SearchShippingAddressOrder({
  handleSubmit,
  handleClose,
  selectedAddress,
  selectedCustomer,
}) {
  const [searchText, setSearchText] = useState("");
  //   const [selected, setSelected] = useState<any>({});
  const [localSelectedAddress, setLocalSelectedAddress] = useState(selectedAddress); // Local state for selectedAddress
  const [items, setItems] = useState([]);

  const handleFetchItems = (items) => {
    setItems(items);
  };

  const handleSelectOne = (event, item) => {
    setLocalSelectedAddress(item);
  };

  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const { listAddress } = useUser();

  const fetchItemList = async (currentPage, pageSize, searchQuery) => {
    const skip = currentPage * pageSize;
    const limit = pageSize;
    const response = await listAddress(`?skip=${skip}&limit=${limit}`, {
      userId: selectedCustomer.userId,
      search: searchQuery,
    });

    if (response && response.data) {
      handleFetchItems(response.data.data || []);
      setTotalCount(response.data.total || 0);
    }
  };

  // Wrap fetchUserList with debounce
  const debouncedFetchItemList = useCallback(
    _.debounce((currentPage, pageSize, searchQuery) => {
      fetchItemList(currentPage, pageSize, searchQuery);
    }, 400), // Delay 1s
    []
  );

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  useEffect(() => {
    if (selectedCustomer.userId === undefined) return;
    debouncedFetchItemList(page, rowsPerPage, searchText);
    return () => {
      debouncedFetchItemList.cancel(); // Cancel debounce if component unmounts or searchText changes
    };
  }, [page, rowsPerPage, searchText]);

  // Biến đổi dữ liệu từ `selectedAddress`
  useEffect(() => {
    setLocalSelectedAddress(selectedAddress); // Chỉ sao chép vào state, không thay đổi prop
  }, [selectedAddress]);

  return (
    <Box>
      <TextField
        fullWidth
        placeholder="Tìm kiếm số điện thoại, tên, địa chỉ"
        variant="outlined"
        size="small"
        onChange={(e) => setSearchText(e.target.value)}
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <Search />
            </InputAdornment>
          ),
        }}
      />

      <Box>
        {items && items.length > 0 ? (
          <Box sx={{ marginTop: 2 }}>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Địa chỉ</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {items.map((item, index) => (
                    <TableRow key={item.shippingAddressId}>
                      <TableCell padding="checkbox">
                        <FormControlLabel
                          control={
                            <Radio
                              checked={
                                localSelectedAddress.shippingAddressId === item.shippingAddressId
                              }
                              onChange={() => handleSelectOne(null, item)}
                              value={item.shippingAddressId}
                              name="radio-buttons"
                            />
                          }
                          label={
                            <Box marginTop={1}>
                              <Typography>{item.fullName}</Typography>
                              <Typography>{item.phoneNumber}</Typography>
                              <Typography>{item.address}</Typography>
                              <Typography>
                                {[item.wardName, item.districtName, item.provinceName].join(", ")}
                              </Typography>
                            </Box>
                          } // Thêm label cho radio (sử dụng thông tin từ item)
                          sx={{ marginBottom: 2 }} // Tuỳ chỉnh kiểu hiển thị nếu cần
                        />
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
            <TablePagination
              component="div"
              count={totalCount}
              page={page}
              onPageChange={handleChangePage}
              rowsPerPage={rowsPerPage}
              onRowsPerPageChange={handleChangeRowsPerPage}
              rowsPerPageOptions={[5, 10, 20]}
              labelRowsPerPage="Số dòng mỗi trang" // Thay đổi văn bản ở phần này
            />
          </Box>
        ) : (
          <Typography marginBottom={2}>Không có kết quả</Typography>
        )}
        <Divider />

        <Box sx={{ display: "flex", justifyContent: "end", marginTop: 2 }}>
          <Button sx={{ marginRight: 1 }} variant="outlined" onClick={handleClose}>
            Hủy
          </Button>
          <Button variant="contained" onClick={() => handleSubmit(localSelectedAddress)}>
            Xác nhận
          </Button>
        </Box>
      </Box>
    </Box>
  );
}
