import React, { useEffect, forwardRef, useState } from 'react';
import { Box, Typography, TextField, InputAdornment } from '@mui/material';
import { useForm, Controller } from 'react-hook-form';
import { NumericFormat } from 'react-number-format';
import { Grid } from '@mui/system';
interface NumericFormatCustomProps {
  onChange: (event: { target: { name: string; value: string } }) => void;
  name: string;
  [key: string]: any;
}
const NumericFormatCustom = forwardRef(function NumericFormatCustom(props: NumericFormatCustomProps, ref) {
  const { onChange, ...other } = props;
  return (
    <NumericFormat
      {...other}
      getInputRef={ref}
      onValueChange={(values) => {
        onChange({
          target: {
            name: props.name,
            value: values.value
          }
        });
      }}
      thousandSeparator="."
      decimalSeparator=","
      allowLeadingZeros={false}
      valueIsNumericString
      isAllowed={(values) => {
        const { floatValue } = values;
        return floatValue === undefined || floatValue >= 0;
      }}
    />
  );
});
const InputFields = ({ selectedTier, onSubmit, setShowNotification }) => {
  const {
    control,
    handleSubmit,
    formState: { errors },
    trigger,
    getValues
  } = useForm();
  const validateTier = (index, field) => {
    const currentValue = parseFloat(getValues(`${field}${index}`));
    const previousValue = parseFloat(getValues(`${field}${index - 1}`));
    if (index > 0 && currentValue <= previousValue) {
      return `${field === 'pointRate' ? 'Tỷ lệ tích điểm' : 'Tổng chi tiêu'} phải lớn hơn hạng trước`;
    }
    if (currentValue.toString().startsWith('0')) {
      return 'Giá trị không được bắt đầu bằng số 0';
    }
    return true;
  };
  const onSubmitForm = (data) => {
    onSubmit(data);
    setShowNotification(true);
  };
  const formatter = (str) => (str ? str.replace(/\B(?=(\d{3})+(?!\d))/g, ',') : '');
  const parser = (str) => str.replaceAll(',', '');
  const convertFullwidthToHalfwidth = (str) => {
    return str.replace(/[！-～]/g, (r) => {
      return String.fromCharCode(r.charCodeAt(0) - 0xfee0);
    });
  };
  return (
    <form id="membership-form" onSubmit={handleSubmit(onSubmitForm)}>
      {selectedTier === 'single' ? (
        <Grid container spacing={2}>
          <Grid size={{ xs: 6, md: 6 }}>
            <Typography variant="body2" sx={{ marginBottom: 1 }}>
              Tên hạng thành viên
            </Typography>
            <Controller
              name="memberTierName"
              control={control}
              defaultValue=""
              rules={{ required: 'Tên hạng thành viên là bắt buộc' }}
              render={({ field }) => (
                <TextField
                  {...field}
                  variant="outlined"
                  fullWidth
                  size="small"
                  error={!!errors.memberTierName}
                  helperText={errors.memberTierName ? String(errors.memberTierName.message) : ''}
                  sx={{ '& .MuiOutlinedInput-root': { borderWidth: '2px', borderColor: 'black', border: 1 } }} // Thicker border
                  onChange={(e) => {
                    field.onChange(e);
                    trigger('memberTierName');
                  }}
                  onBlur={() => trigger('memberTierName')}
                />
              )}
            />
          </Grid>
          <Grid size={{ xs: 6, md: 6 }}>
            <Typography variant="body2" sx={{ marginBottom: 1 }}>
              Tỷ lệ tích điểm
            </Typography>
            <Controller
              name="pointRate"
              control={control}
              defaultValue=""
              rules={{
                required: 'Tỷ lệ tích điểm là bắt buộc',
                validate: {
                  positive: (value) => parseFloat(value) >= 0 || 'Tỷ lệ tích điểm không được là số âm',
                  max: (value) => parseFloat(value) <= 100 || 'Tỷ lệ tích điểm không được lớn hơn 100%',
                  noLeadingZero: (value) => !value.startsWith('0') || 'Giá trị không được bắt đầu bằng số 0'
                }
              }}
              render={({ field }) => (
                <TextField
                  {...field}
                  variant="outlined"
                  type="number"
                  fullWidth
                  size="small"
                  error={!!errors.pointRate}
                  helperText={errors.pointRate ? String(errors.pointRate.message) : ''}
                  sx={{ '& .MuiOutlinedInput-root': { borderWidth: '2px', borderColor: 'black', border: 1 } }}
                  InputProps={{
                    endAdornment: <InputAdornment position="end">%</InputAdornment>
                  }}
                  onChange={(e) => {
                    field.onChange(e);
                    trigger('pointRate');
                  }}
                  onBlur={() => trigger('pointRate')}
                />
              )}
            />
          </Grid>
        </Grid>
      ) : (
        [...Array(3)].map((_, index) => (
          <React.Fragment key={index}>
            <Grid container spacing={2}>
              <Grid size={{ xs: 12, md: 4 }}>
                <Typography variant="body2" sx={{ marginBottom: 1, marginTop: { xs: 2, md: 0 } }}>
                  {`Tên hạng thành viên ${index + 1}`}
                </Typography>
                <Controller
                  name={`memberTierName${index}`}
                  control={control}
                  defaultValue=""
                  rules={{ required: 'Tên hạng thành viên là bắt buộc' }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      variant="outlined"
                      fullWidth
                      size="small"
                      error={!!errors[`memberTierName${index}`]}
                      helperText={
                        errors[`memberTierName${index}`] ? String(errors[`memberTierName${index}`].message) : ''
                      }
                      sx={{ '& .MuiOutlinedInput-root': { borderWidth: '2px', borderColor: 'black', border: 1 } }}
                      onChange={(e) => {
                        field.onChange(e);
                        trigger(`memberTierName${index}`);
                      }}
                      onBlur={() => trigger(`memberTierName${index}`)}
                    />
                  )}
                />
              </Grid>
              <Grid size={{ xs: 12, md: 4 }} sx={{ marginBottom: 1 }}>
                <Typography variant="body2" sx={{ marginBottom: 1 }}>
                  Tỷ lệ tích điểm
                </Typography>
                <Controller
                  name={`pointRate${index}`}
                  control={control}
                  defaultValue=""
                  rules={{
                    required: 'Tỷ lệ tích điểm là bắt buộc',
                    validate: {
                      positive: (value) => parseFloat(value) >= 0 || 'Tỷ lệ tích điểm không được là số âm',
                      max: (value) => parseFloat(value) <= 100 || 'Tỷ lệ tích điểm không được lớn hơn 100%',
                      greaterThanPrevious: (value) => validateTier(index, 'pointRate'),
                      noLeadingZero: (value) => !value.startsWith('0') || 'Giá trị không được bắt đầu bằng số 0'
                    }
                  }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      variant="outlined"
                      type="number"
                      fullWidth
                      size="small"
                      error={!!errors[`pointRate${index}`]}
                      helperText={errors[`pointRate${index}`] ? String(errors[`pointRate${index}`].message) : ''}
                      sx={{
                        '& .MuiOutlinedInput-root': { borderWidth: '2px', borderColor: 'black', border: 1 }
                      }}
                      InputProps={{
                        endAdornment: <InputAdornment position="end">%</InputAdornment>
                      }}
                      onChange={(e) => {
                        field.onChange(e);
                        trigger(`pointRate${index}`);
                      }}
                      onBlur={() => trigger(`pointRate${index}`)}
                    />
                  )}
                />
              </Grid>
              <Grid size={{ xs: 12, md: 4 }}>
                <Typography variant="body2" sx={{ marginBottom: 1 }}>
                  Tổng chi tiêu nhỏ hơn
                </Typography>
                <Controller
                  name={`totalSpending${index}`}
                  control={control}
                  defaultValue=""
                  rules={{
                    required: 'Tổng chi tiêu là bắt buộc',
                    validate: {
                      greaterThanPrevious: (value) => validateTier(index, 'totalSpending'),
                      noLeadingZero: (value) => !value.startsWith('0') || 'Giá trị không được bắt đầu bằng số 0'
                    }
                  }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      variant="outlined"
                      fullWidth
                      size="small"
                      error={!!errors[`totalSpending${index}`]}
                      helperText={
                        errors[`totalSpending${index}`] ? String(errors[`totalSpending${index}`].message) : ''
                      }
                      sx={{ '& .MuiOutlinedInput-root': { borderWidth: '2px', borderColor: 'black', border: 1 } }}
                      InputProps={{
                        inputComponent: NumericFormatCustom,
                        endAdornment: <InputAdornment position="end">VND</InputAdornment>
                      }}
                      onChange={(e) => {
                        const parsedValue = parser(convertFullwidthToHalfwidth(e.target.value));
                        field.onChange(parsedValue);
                        trigger(`totalSpending${index}`);
                      }}
                      onBlur={() => trigger(`totalSpending${index}`)}
                    />
                  )}
                />
              </Grid>
            </Grid>
          </React.Fragment>
        ))
      )}
    </form>
  );
};

export default InputFields;
