import React, { useState } from "react";
import { useRouter } from "next/router";
import {
  <PERSON>,
  Typography,
  TextField,
  IconButton,
  Tooltip,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Avatar,
  Button,
  TablePagination,
  InputAdornment,
  ToggleButton,
  ToggleButtonGroup,
  useMediaQuery,
  useTheme,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from "@mui/material";
import EditIcon from "@mui/icons-material/Edit";
import DescriptionIcon from "@mui/icons-material/Description";
import FileUploadIcon from "@mui/icons-material/FileUpload";
import Search from "@mui/icons-material/Search";
import { customerPolicyData } from "../../_mock/customer-policy-data";
import { FormProvider, useForm } from "react-hook-form";
import { Grid } from "@mui/system";
import { paths } from "@/src/paths";

export default function CustomerDetail() {
  const router = useRouter();
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(5);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState(null);
  const [pointAction, setPointAction] = useState("add");

  const methods = useForm();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  const handleChangePage = (event, newPage) => setPage(newPage);
  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleEditClick = (customer) => {
    setSelectedCustomer(customer);
    setDialogOpen(true);
  };

  const handleDialogClose = () => {
    setDialogOpen(false);
    setSelectedCustomer(null);
  };

  const handleFormSubmit = (data) => {
    console.log("Dữ liệu biểu mẫu:", data);
    handleDialogClose();
  };

  const handlePointActionChange = (event, newAction) => {
    if (newAction !== null) setPointAction(newAction);
  };

  return (
    <Box sx={{ p: { xs: 1, md: 4 } }}>
      <ExportButton />
      <SearchBox />
      <CustomerTable
        customerPolicyData={customerPolicyData}
        page={page}
        rowsPerPage={rowsPerPage}
        handleChangePage={handleChangePage}
        handleChangeRowsPerPage={handleChangeRowsPerPage}
        handleEditClick={handleEditClick}
        router={router}
        isMobile={isMobile}
      />
      <EditDialog
        dialogOpen={dialogOpen}
        handleDialogClose={handleDialogClose}
        selectedCustomer={selectedCustomer}
        pointAction={pointAction}
        handlePointActionChange={handlePointActionChange}
        methods={methods}
        handleFormSubmit={methods.handleSubmit(handleFormSubmit)}
      />
    </Box>
  );
}

const ExportButton = () => (
  <Box sx={{ mb: 2, display: "flex", justifyContent: "flex-start" }}>
    <Button
      variant="contained"
      startIcon={<FileUploadIcon />}
      sx={{
        display: "flex",
        alignItems: "center",
        gap: 1,
        textTransform: "none",
        borderRadius: "5px",
      }}
    >
      Xuất Excel
    </Button>
  </Box>
);

const SearchBox = () => (
  <Box sx={{ mb: 3, flexShrink: 0 }}>
    <Box sx={{ mb: 3 }}>
      <TextField
        placeholder="Tìm kiếm khách hàng"
        variant="outlined"
        size="small"
        fullWidth
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <Search />
            </InputAdornment>
          ),
        }}
      />
    </Box>
  </Box>
);

const CustomerTable = ({
  customerPolicyData,
  page,
  rowsPerPage,
  handleChangePage,
  handleChangeRowsPerPage,
  handleEditClick,
  router,
  isMobile,
}) => (
  <Box
    sx={{
      border: "1px solid #e0e0e0",
      borderRadius: 1,
      flexGrow: 1,
      display: "flex",
      flexDirection: "column",
      p: { xs: 1, md: 4 },
    }}
  >
    <Grid sx={{ width: isMobile ? "350px" : "100%", overflowX: "auto" }}>
      <TableContainer component={Paper} sx={{ boxShadow: "none" }}>
        <Table stickyHeader>
          <TableHead>
            <TableRow>
              <TableCell>Tên khách hàng</TableCell>
              <TableCell>Số điện thoại</TableCell>
              <TableCell>Tổng điểm đã tích lũy</TableCell>
              <TableCell>Điểm hiện có</TableCell>
              <TableCell>Điểm đã tiêu</TableCell>
              <TableCell>Tổng chi tiêu</TableCell>
              <TableCell>Quản lý</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {customerPolicyData.length > 0 ? (
              customerPolicyData
                .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                .map((row, index) => (
                  <TableRow key={index}>
                    <TableCell>
                      <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                        <Avatar sx={{ width: 24, height: 24 }} />
                        {row.name}
                      </Box>
                    </TableCell>
                    <TableCell>{row.phone}</TableCell>
                    <TableCell>{row.totalPoints}</TableCell>
                    <TableCell>{row.currentPoints}</TableCell>
                    <TableCell>{row.targetPoints}</TableCell>
                    <TableCell>{row.totalAmount}</TableCell>
                    <TableCell>
                      <Box sx={{ display: "flex", justifyContent: "center", gap: 1 }}>
                        <Tooltip title="Chỉnh sửa">
                          <IconButton size="small" onClick={() => handleEditClick(row)}>
                            <EditIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Chi tiết">
                          <IconButton
                            size="small"
                            onClick={() => router.push(paths.customers.pointCustomer)}
                          >
                            <DescriptionIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))
            ) : (
              <TableRow>
                <TableCell colSpan={7} align="center">
                  Không có dữ liệu
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>
    </Grid>
    <Box sx={{ flexShrink: 0 }}>
      <TablePagination
        rowsPerPageOptions={[5, 10, 25]}
        component="div"
        count={customerPolicyData.length}
        rowsPerPage={rowsPerPage}
        page={page}
        onPageChange={handleChangePage}
        onRowsPerPageChange={handleChangeRowsPerPage}
      />
    </Box>
  </Box>
);

const EditDialog = ({
  dialogOpen,
  handleDialogClose,
  selectedCustomer,
  pointAction,
  handlePointActionChange,
  methods,
  handleFormSubmit,
}) => (
  <FormProvider {...methods}>
    <Dialog open={dialogOpen} onClose={handleDialogClose} fullWidth maxWidth="sm">
      <DialogTitle>Điều chỉnh điểm</DialogTitle>
      <DialogContent>
        <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
          <Typography variant="body1">
            Điểm hiện có: {selectedCustomer?.currentPoints} điểm
          </Typography>
          <ToggleButtonGroup
            value={pointAction}
            exclusive
            onChange={handlePointActionChange}
            aria-label="point action"
            sx={{ mb: 2 }}
          >
            <ToggleButton
              value="add"
              aria-label="add points"
              selected={pointAction === "add"}
              sx={{
                "&.Mui-selected": {
                  backgroundColor: "blue",
                  color: "white",
                  "&:hover": { backgroundColor: "darkblue" },
                },
              }}
            >
              Cộng điểm
            </ToggleButton>
            <ToggleButton
              value="subtract"
              aria-label="subtract points"
              selected={pointAction === "subtract"}
              sx={{
                "&.Mui-selected": {
                  backgroundColor: "blue",
                  color: "white",
                  "&:hover": { backgroundColor: "darkblue" },
                },
              }}
            >
              Trừ điểm
            </ToggleButton>
          </ToggleButtonGroup>
          {pointAction === "add" && (
            <TextField
              label="Cộng điểm"
              variant="outlined"
              size="small"
              fullWidth
              {...methods.register("addPoints")}
            />
          )}
          {pointAction === "subtract" && (
            <TextField
              label="Trừ điểm"
              variant="outlined"
              size="small"
              fullWidth
              {...methods.register("subtractPoints")}
            />
          )}
          <TextField
            label="Lý do điều chỉnh"
            variant="outlined"
            size="small"
            fullWidth
            {...methods.register("description")}
          />
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleDialogClose}>Hủy bỏ</Button>
        <Button onClick={handleFormSubmit}>Lưu</Button>
      </DialogActions>
    </Dialog>
  </FormProvider>
);
