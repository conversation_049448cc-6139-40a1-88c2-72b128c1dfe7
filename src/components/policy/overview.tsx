import React from 'react';
import { Box, Typography } from '@mui/material';
import Grid from '@mui/system/Grid';

export default function Overview() {
  return (
    <Box p={3} border="1px solid #e0e0e0" borderRadius={1}>
      <Typography variant="h5" mb={4}>
        Tổng quan dữ liệu
      </Typography>
      <Grid container spacing={2}>
        <Grid size={{ xs: 12, md: 6 }} marginTop={2}>
          <Box>
            <Typography variant="body2">Tổng số điểm của người dùng</Typography>
            <Typography variant="h5" fontWeight="bold">
              0
            </Typography>
          </Box>
        </Grid>
        <Grid size={{ xs: 12, md: 6 }} marginTop={2}>
          <Box>
            <Typography variant="body2">Tổng số người dùng có tích điểm</Typography>
            <Typography variant="h5" fontWeight="bold">
              0
            </Typography>
          </Box>
        </Grid>
      </Grid>
    </Box>
  );
}
