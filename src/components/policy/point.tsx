import React from "react";
import {
  Box,
  Typography,
  TextField,
  Select,
  MenuItem,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Avatar,
  SelectChangeEvent,
  TablePagination,
  InputAdornment,
  Button,
  Popover,
} from "@mui/material";
import { DateRange } from "@mui/icons-material";
import { DateRangePicker } from "react-date-range";
import { transactionData } from "../../_mock/transaction-data";
import "react-date-range/dist/styles.css";
import "react-date-range/dist/theme/default.css";
import Search from "@mui/icons-material/Search";
import Grid from "@mui/system/Grid";

export default function PointHistory() {
  const [transactionType, setTransactionType] = React.useState("");
  const [page, setPage] = React.useState(0);
  const [rowsPerPage, setRowsPerPage] = React.useState(5);
  const [dateRange, setDateRange] = React.useState([
    {
      startDate: new Date(),
      endDate: new Date(),
      key: "selection",
    },
  ]);
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const isMobile = window.innerWidth <= 768;

  const handleTransactionTypeChange = (event: SelectChangeEvent) => {
    setTransactionType(event.target.value);
  };

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleDateRangeClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleDateRangeClose = () => {
    setAnchorEl(null);
  };

  const open = Boolean(anchorEl);
  const id = open ? "date-range-popover" : undefined;

  return (
    <Box sx={{ width: isMobile ? "400px" : "100%", overflowX: "auto", p: { xs: 1, md: 3 } }}>
      <Box
        sx={{
          border: "1px solid #e0e0e0",
          borderRadius: 1,
          flexGrow: 1,
          display: "flex",
          flexDirection: "column",
        }}
      >
        <Box sx={{ p: { xs: 1, md: 3 }, flexShrink: 0 }}>
          <SearchAndFilter
            transactionType={transactionType}
            handleTransactionTypeChange={handleTransactionTypeChange}
            dateRange={dateRange}
            handleDateRangeClick={handleDateRangeClick}
            id={id}
            open={open}
            anchorEl={anchorEl}
            handleDateRangeClose={handleDateRangeClose}
            setDateRange={setDateRange}
          />
          <TransactionTable
            transactionData={transactionData}
            page={page}
            rowsPerPage={rowsPerPage}
            handleChangePage={handleChangePage}
            handleChangeRowsPerPage={handleChangeRowsPerPage}
          />
          {transactionData.length === 0 && <NoData />}
        </Box>
      </Box>
    </Box>
  );
}

const SearchAndFilter = ({
  transactionType,
  handleTransactionTypeChange,
  dateRange,
  handleDateRangeClick,
  id,
  open,
  anchorEl,
  handleDateRangeClose,
  setDateRange,
}) => (
  <>
    <Grid container spacing={2} sx={{ mb: 3 }}>
      <Grid size={{ xs: 12, md: 4 }}>
        <TextField
          placeholder="Tìm kiếm sản phẩm, thông tin khách hàng"
          variant="outlined"
          size="small"
          fullWidth
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Search />
              </InputAdornment>
            ),
          }}
          sx={{
            "& .MuiOutlinedInput-root": {
              borderRadius: "8px",
            },
          }}
        />
      </Grid>
      <Grid size={{ xs: 12, md: 4 }}>
        <Select
          size="small"
          value={transactionType}
          onChange={handleTransactionTypeChange}
          displayEmpty
          fullWidth
          sx={{
            borderRadius: "8px",
            "& .MuiOutlinedInput-notchedOutline": {
              borderRadius: "8px",
            },
            "& .MuiSelect-select": {
              padding: "8px 14px",
            },
          }}
        >
          <MenuItem value="">Loại giao dịch</MenuItem>
          <MenuItem value="purchase">Mua sắm</MenuItem>
          <MenuItem value="referral">Giới thiệu</MenuItem>
          <MenuItem value="adjustment">Điều chỉnh</MenuItem>
        </Select>
      </Grid>
      <Grid size={{ xs: 12, md: 4 }}>
        <Button
          variant="outlined"
          startIcon={<DateRange />}
          onClick={handleDateRangeClick}
          fullWidth
          sx={{
            borderRadius: "8px",
            padding: "8px 14px",
          }}
        >
          {`${dateRange[0].startDate.toLocaleDateString()} - ${dateRange[0].endDate.toLocaleDateString()}`}
        </Button>
      </Grid>
    </Grid>
    <Popover
      id={id}
      open={open}
      anchorEl={anchorEl}
      onClose={handleDateRangeClose}
      anchorOrigin={{
        vertical: "bottom",
        horizontal: "left",
      }}
    >
      <DateRangePicker
        onChange={(item) => setDateRange([item.selection])}
        showSelectionPreview={true}
        moveRangeOnFirstSelection={false}
        months={2}
        ranges={dateRange}
        direction="horizontal"
      />
    </Popover>
  </>
);

const TransactionTable = ({
  transactionData,
  page,
  rowsPerPage,
  handleChangePage,
  handleChangeRowsPerPage,
}) => (
  <>
    <TableContainer component={Paper} sx={{ boxShadow: "none", overflowX: "auto" }}>
      <Table sx={{ minWidth: "100%" }}>
        <TableHead>
          <TableRow>
            <TableCell sx={{ whiteSpace: "nowrap" }}>Loại giao dịch</TableCell>
            <TableCell sx={{ whiteSpace: "nowrap" }}>Chi tiết</TableCell>
            <TableCell sx={{ whiteSpace: "nowrap" }}>Thông tin khách hàng</TableCell>
            <TableCell sx={{ whiteSpace: "nowrap" }}>Thời gian</TableCell>
            <TableCell sx={{ whiteSpace: "nowrap" }}>Giá trị</TableCell>
            <TableCell sx={{ whiteSpace: "nowrap" }}>Ghi chú</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {transactionData.length > 0 ? (
            transactionData
              .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
              .map((transaction, index) => (
                <TableRow key={index}>
                  <TableCell>{transaction.type}</TableCell>
                  <TableCell>{transaction.detail}</TableCell>
                  <TableCell>
                    <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                      <Avatar sx={{ width: 24, height: 24 }} />
                      <Typography>{transaction.user}</Typography>
                    </Box>
                  </TableCell>
                  <TableCell>{transaction.time}</TableCell>
                  <TableCell>
                    <Typography
                      color={transaction.points.startsWith("+") ? "success.main" : "error.main"}
                    >
                      {transaction.points}
                    </Typography>
                  </TableCell>
                  <TableCell>{transaction.note}</TableCell>
                </TableRow>
              ))
          ) : (
            <TableRow>
              <TableCell colSpan={9} align="center">
                Không có nội dung
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </TableContainer>
    <TablePagination
      rowsPerPageOptions={[5, 10, 25]}
      component="div"
      count={transactionData.length}
      rowsPerPage={rowsPerPage}
      page={page}
      onPageChange={handleChangePage}
      onRowsPerPageChange={handleChangeRowsPerPage}
    />
  </>
);

const NoData = () => (
  <Box
    sx={{
      display: "flex",
      flexDirection: "column",
      alignItems: "center",
      justifyContent: "center",
      p: 4,
      flexGrow: 1,
    }}
  >
    <Box
      component="img"
      src="/assets/customerlist/tải xuống.png"
      alt="No data"
      sx={{ width: 200, height: 200, mb: 2 }}
    />
    <Typography color="text.secondary">Không có nội dung</Typography>
  </Box>
);
