import { useItemOptionGroup } from "@/src/api/hooks/item-option-group/use-item-option-group";
import { useStoreId } from "@/src/hooks/use-store-id";
import {
  Autocomplete,
  Box,
  Button,
  Divider,
  FormControl,
  FormHelperText,
  IconButton,
  Stack,
  TextField,
  Tooltip,
  Typography,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import { useItemOption } from "@/src/api/hooks/item-option/use-item-option";
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  AddCircle as AddCircleIcon,
} from "@mui/icons-material";
import { paths } from "@/src/paths";
import { useRouter } from "next/router";
import TruncatedText from "../truncated-text/truncated-text";

export const ProductDialogAddItemGroupOption = ({
  handleClose,
  onSave,
  selectedOptionGroups,
  itemGroupOptions,
}) => {
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [localSelectedOptionGroups, setLocalSelectedOptionGroups] = useState([]);
  const { listItemOptionByGroupId } = useItemOption();
  const router = useRouter();

  const MAX_ITEM_GROUPS = 10;

  const fetchItemOptionByGroupId = async (groupId) => {
    try {
      const response = await listItemOptionByGroupId(0, 999, { itemOptionGroupId: groupId });
      if (response?.data) {
        const { data } = response.data;
        return data;
      }
    } catch (error) {
      console.error("Error fetching item options by group ID:", error);
      return []; // Return an empty array on error
    }
  };

  const handleChangeItemOptionGroup = async (action, index, itemGroup) => {
    if (action === "remove") {
      setLocalSelectedOptionGroups((prevGroups) => {
        const newGroups = [...prevGroups];
        newGroups[index] = {
          itemOptionGroup: {},
          itemOptions: [],
          allItemOptions: [],
        };
        return newGroups;
      });
      return;
    }

    // If itemGroup is null (e.g., user clears Autocomplete), treat as remove
    if (!itemGroup) {
      handleChangeItemOptionGroup("remove", index, null);
      return;
    }

    const newListItemOption = await fetchItemOptionByGroupId(itemGroup.itemOptionGroupId);

    setLocalSelectedOptionGroups((prevGroups) => {
      const newGroups = [...prevGroups];
      newGroups[index] = {
        itemOptionGroup: itemGroup,
        itemOptions: newListItemOption, // Initially set selected options to all available
        allItemOptions: newListItemOption, // Store all options for this group
      };
      return newGroups;
    });
  };

  const removeItemOptionGroup = (index) => {
    setLocalSelectedOptionGroups((prevGroups) => {
      const newData = prevGroups.filter((_, i) => i !== index);
      // Also, clear errors related to the removed item group
      setErrors((prev) => {
        const newErrors = { ...prev };
        Object.keys(newErrors).forEach((key) => {
          if (key.includes(String(index))) {
            delete newErrors[key];
          }
        });
        // Re-index errors for remaining items if necessary (optional, but good practice)
        const reindexedErrors = {};
        newData.forEach((_, newIdx) => {
          if (newErrors[`itemOptionGroup${newIdx}`]) {
            reindexedErrors[`itemOptionGroup${newIdx}`] = newErrors[`itemOptionGroup${newIdx}`];
          }
          if (newErrors[`itemOption${newIdx}`]) {
            reindexedErrors[`itemOption${newIdx}`] = newErrors[`itemOption${newIdx}`];
          }
        });
        return reindexedErrors;
      });
      return newData;
    });
  };

  const onAddItemOption = () => {
    if (localSelectedOptionGroups.length < MAX_ITEM_GROUPS) {
      // Check before adding
      setLocalSelectedOptionGroups([
        ...localSelectedOptionGroups,
        { itemOptionGroup: {}, itemOptions: [], allItemOptions: [] }, // Initialize allItemOptions
      ]);
      setErrors({}); // Clear errors when adding a new row
    }
  };

  const onChangeItemOption = (index, itemOptions) => {
    setLocalSelectedOptionGroups((prevGroups) => {
      const newGroups = [...prevGroups];
      newGroups[index] = {
        ...newGroups[index],
        itemOptions,
      };
      return newGroups;
    });

    setErrors((prev) => {
      const newErrors = { ...prev };
      delete newErrors[`itemOption${index}`];
      return newErrors;
    });
  };

  const handleSave = () => {
    setIsSubmitted(true);

    const isInValid = validateError();

    if (isInValid) {
      return;
    }

    handleClose();
    onSave(localSelectedOptionGroups);
  };

  const validateError = () => {
    const newErrors: Record<string, string> = {};

    localSelectedOptionGroups.forEach((item, index) => {
      if (Object.keys(item.itemOptionGroup).length === 0) {
        newErrors[`itemOptionGroup${index}`] = "Không được để trống";
      }
      if (item.itemOptions.length === 0) {
        newErrors[`itemOption${index}`] = "Không được để trống";
      }
    });

    const countMap = new Map();
    localSelectedOptionGroups.forEach((item) => {
      const groupId = item.itemOptionGroup?.itemOptionGroupId;
      if (groupId) {
        countMap.set(groupId, (countMap.get(groupId) || 0) + 1);
      }
    });
    const duplicatedIds = new Set(
      [...countMap.entries()].filter(([_, count]) => count > 1).map(([id]) => id)
    );
    localSelectedOptionGroups.forEach((item, index) => {
      if (
        item.itemOptionGroup?.itemOptionGroupId &&
        duplicatedIds.has(item.itemOptionGroup.itemOptionGroupId)
      ) {
        newErrors[`itemOptionGroup${index}`] = "Đã có tùy chọn này";
      }
    });

    setErrors(newErrors);

    return Object.keys(newErrors).length > 0;
  };

  const onChangeItemOptionGroup = (index, selectedItemOptionGroup) => {
    handleChangeItemOptionGroup(
      selectedItemOptionGroup === null ? "remove" : "select",
      index,
      selectedItemOptionGroup
    );

    setErrors((prev) => {
      const newErrors = { ...prev };
      delete newErrors[`itemOptionGroup${index}`];
      delete newErrors[`itemOption${index}`];
      return newErrors;
    });
  };

  useEffect(() => {
    if (selectedOptionGroups.length > 0) {
      const initializeGroups = async () => {
        const initializedData = await Promise.all(
          selectedOptionGroups.map(async (group) => {
            if (group.itemOptionGroup?.itemOptionGroupId) {
              const allOptions = await fetchItemOptionByGroupId(
                group.itemOptionGroup.itemOptionGroupId
              );
              return {
                ...group,
                allItemOptions: allOptions,
              };
            }
            return {
              ...group,
              allItemOptions: [],
            };
          })
        );
        setLocalSelectedOptionGroups(initializedData);
      };
      initializeGroups();
    } else {
      setLocalSelectedOptionGroups([]);
    }
  }, [JSON.stringify(selectedOptionGroups)]);

  const isAddButtonDisabled = localSelectedOptionGroups.length >= MAX_ITEM_GROUPS;

  return (
    <Stack spacing={2}>
      {localSelectedOptionGroups.map((item, index) => {
        return (
          <Stack spacing={1} key={index}>
            <Box
              sx={{
                display: "grid",
                gridTemplateColumns: "1fr 1fr auto",
                gap: 1,
                alignItems: "start",
              }}
            >
              <FormControl
                sx={{ minHeight: "65px", width: "100%" }}
                error={!!errors[`itemOptionGroup${index}`]}
              >
                <Autocomplete
                  value={
                    localSelectedOptionGroups[index]?.itemOptionGroup?.itemOptionGroupId
                      ? localSelectedOptionGroups[index]?.itemOptionGroup
                      : null
                  }
                  options={itemGroupOptions}
                  getOptionLabel={(option) => option?.name ?? ""}
                  onChange={(event, newValue) => {
                    onChangeItemOptionGroup(index, newValue);
                  }}
                  isOptionEqualToValue={(option, value) =>
                    option.itemOptionGroupId === value.itemOptionGroupId
                  }
                  renderOption={(props, option) => {
                    const { key, ...restProps } = props;
                    return (
                      <Box
                        component="li"
                        key={key}
                        {...restProps}
                        sx={{ display: "flex", alignItems: "center", p: 1, width: "100%" }}
                      >
                        <TruncatedText text={option.name} width="100%" isLink={true} />
                      </Box>
                    );
                  }}
                  renderInput={(params) => (
                    <TextField {...params} placeholder="Tùy chọn" variant="outlined" />
                  )}
                />
                {isSubmitted && errors[`itemOptionGroup${index}`] && (
                  <FormHelperText>{errors[`itemOptionGroup${index}`]}</FormHelperText>
                )}
              </FormControl>

              <FormControl sx={{ minHeight: "65px" }} error={!!errors[`itemOption${index}`]}>
                <Autocomplete
                  multiple
                  id={`multiple-select-${index}`}
                  options={item?.allItemOptions ?? []}
                  getOptionLabel={(option) => option?.name}
                  value={item.itemOptions}
                  onChange={(e, newValue) => onChangeItemOption(index, newValue)}
                  isOptionEqualToValue={(option, value) => option.id === value.id}
                  renderInput={(params) => <TextField {...params} placeholder="Tìm kiếm" />}
                  renderOption={(props, option) => {
                    const { key, ...restProps } = props;
                    return (
                      <Box component="li" key={key} {...restProps}>
                        <TruncatedText text={option.name} width="100%" isLink={true} />
                      </Box>
                    );
                  }}
                  sx={{
                    "& .MuiChip-root": {
                      maxWidth: "250px",
                      justifyContent: "flex-start",
                    },
                  }}
                />
                {isSubmitted && errors[`itemOption${index}`] && (
                  <FormHelperText>{errors[`itemOption${index}`]}</FormHelperText>
                )}
              </FormControl>

              <Box sx={{ mt: "2px" }}>
                <Tooltip title="Xóa" arrow placement="top">
                  <IconButton
                    onClick={() => removeItemOptionGroup(index)}
                    size="small"
                    sx={{
                      bgcolor: "error.lighter",
                      color: "error.main",
                      "&:hover": { bgcolor: "error.light" },
                      width: 32,
                      height: 32,
                    }}
                  >
                    <DeleteIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              </Box>
            </Box>
          </Stack>
        );
      })}

      <Button
        startIcon={<AddCircleIcon />}
        onClick={onAddItemOption}
        variant="outlined"
        disabled={isAddButtonDisabled}
        sx={{
          borderStyle: "dashed",
          height: 40,
          "&:hover": {
            borderStyle: "dashed",
            bgcolor: "primary.lighter",
          },
          ...(isAddButtonDisabled && {
            opacity: 0.5,
            cursor: "not-allowed",
          }),
        }}
      >
        Thêm tùy chọn ({localSelectedOptionGroups.length}/{MAX_ITEM_GROUPS})
      </Button>

      <Divider sx={{ marginTop: 2, marginBottom: 2 }} />
      <Box display="flex" justifyContent="end">
        <Box display="flex" gap={1}>
          <Button
            variant="outlined"
            onClick={() => {
              handleClose();
              window.open(paths.itemGroups.new, "_blank");
            }}
          >
            Tạo tuỳ chọn
          </Button>
          <Button variant="outlined" onClick={handleClose}>
            Hủy
          </Button>
          <Button variant="contained" onClick={handleSave}>
            Lưu
          </Button>
        </Box>
      </Box>
    </Stack>
  );
};
