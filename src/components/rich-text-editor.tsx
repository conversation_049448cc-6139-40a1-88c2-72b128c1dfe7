import React, { useMemo, useRef, useLayoutEffect } from 'react';
import { Box, SxProps } from '@mui/material';
import { styled } from '@mui/material/styles';
import dynamic from 'next/dynamic';

// Dynamically import Jodit Editor to avoid SSR issues
const JoditEditor = dynamic(() => import('jodit-react'), {
  ssr: false,
  loading: () => <Box>Loading editor...</Box>,
});

// Styled wrapper component for the editor
const EditorWrapper = styled(Box)(({ theme }) => ({
  // Root container - holds the entire editor
  position: 'relative',
  width: '100%',
  height: '100%',
  display: 'flex',
  flexDirection: 'column',

  // Main editor container - wraps the toolbar and content
  '& .jodit-container': {
    border: `1px solid ${theme.palette.divider}`,
    borderRadius: theme.shape.borderRadius,
    display: 'flex',
    flexDirection: 'column',
    height: '100%',
  },

  // Toolbar section - contains formatting controls
  '& .jodit-toolbar': {
    position: 'sticky',
    top: 0,
    zIndex: 2,
    backgroundColor: theme.palette.background.paper,
    borderBottom: `1px solid ${theme.palette.divider}`,
    padding: theme.spacing(1),
    minHeight: '48px',
    flexShrink: 0, // Prevents toolbar from shrinking
  },

  // Workplace - contains the editable area
  '& .jodit-workplace': {
    flex: 1,
    minHeight: 0, // Allows flex container to shrink
    display: 'flex',
    flexDirection: 'column',
  },

  // Content area - where text is edited
  '& .jodit-wysiwyg': {
    flex: 1,
    padding: theme.spacing(2),
    backgroundColor: theme.palette.background.paper,
    overflow: 'auto', // Enables scrolling within content area
  },
}));

// Component props interface
interface RichTextEditorProps {
  value: string;
  onChange: (content: string) => void;
  height?: string | number;
  placeholder?: string;
  sx?: SxProps;
}

// Default configuration for the editor
const defaultConfig = {
  buttons: [
    "source",
    "|",
    "cut",
    "copy",
    "paste",
    "selectall",
    "|",
    "bold",
    "italic",
    "underline",
    "strikethrough",
    "superscript",
    "subscript",
    "|",
    "outdent",
    "indent",
    "|",
    "font",
    "fontsize",
    "brush",
    "paragraph",
    "|",
    "ul",
    "ol",
    "|",
    "table",
    "link",
    "image",
    "video",
    "file",
    "|",
    "align",
    "hr",
    "copyformat",
    "eraser",
    "undo",
    "redo",
    "|",
    "fullsize",
    "preview",
    "print",
    "about",
  ],
  showXPathInStatusbar: false,
  showCharsCounter: false,
  showWordsCounter: false,
  toolbarAdaptive: false,
  toolbarSticky: true,
  toolbarStickyOffset: 0,
  askBeforePasteHTML: false,
  askBeforePasteFromWord: false,
  processPastedHTML: true,
  pasteFromWord: false,
  nl2brInPlainText: true,
  // Optimize for Vietnamese input
  useSplitMode: false,
  spellcheck: true,
  useNativeTooltip: true,
  // Prevent cursor jumping during input
  saveSelectionOnBlur: true,
  uploader: {
    insertImageAsBase64URI: true,
  },
  image: {
    editSrc: true,
    editAlt: true,
    editLink: true,
    editSize: true,
    editMargins: true,
    editBorderRadius: true,
    editAlign: true,
    useImageEditor: true,
    openOnDblClick: true,
  },
  table: {
    selectionCellStyle: "border: 1px solid #ccc",
    useExtraClassesOptions: true,
  },
  link: {
    openInNewTabCheckbox: true,
    noFollowCheckbox: true,
    followOnDblClick: true,
    processPastedLink: true,
  },
};

// Rich Text Editor Component
const RichTextEditor = ({
  value,
  onChange,
  height = '300px',
  placeholder = '',
  sx = {},
}: RichTextEditorProps) => {
  const editorRef = useRef<any>(null);
  const savedSelectionRef = useRef<any>(null);
  const isUpdatingRef = useRef(false);

  // Save selection before content update
  const saveSelection = () => {
    if (editorRef.current) {
      try {
        const jodit = editorRef.current;
        if (jodit.s && jodit.s.save) {
          savedSelectionRef.current = jodit.s.save();
        }
      } catch (error) {
        console.warn('Failed to save selection:', error);
      }
    }
  };

  // Restore selection after content update
  const restoreSelection = () => {
    if (editorRef.current && savedSelectionRef.current) {
      try {
        const jodit = editorRef.current;
        if (jodit.s && jodit.s.restore) {
          jodit.s.restore(savedSelectionRef.current);
        }
      } catch (error) {
        console.warn('Failed to restore selection:', error);
      }
    }
  };

  // Handle editor change with cursor position preservation
  const handleEditorChange = (newContent: string) => {
    if (isUpdatingRef.current) return;
    
    // Save current selection before updating
    saveSelection();
    
    // Update content
    onChange(newContent);
    
    // Mark as updating to prevent recursive calls
    isUpdatingRef.current = true;
  };

  // Restore cursor position after render
  useLayoutEffect(() => {
    if (isUpdatingRef.current) {
      restoreSelection();
      isUpdatingRef.current = false;
    }
  }, [value]);

  // Optimized editor config with useMemo
  const editorConfig = useMemo(
    () => ({
      ...defaultConfig,
      height,
      placeholder: placeholder || '',
      // Improve performance and reduce re-renders
      observer: {
        timeout: 100,
      },
      // Improve cursor stability
      enter: 'p' as const,
    }),
    [placeholder]
  );

  return (
    <EditorWrapper sx={sx}>
      <JoditEditor
        ref={editorRef}
        value={value}
        config={editorConfig}
        onChange={handleEditorChange}
        onBlur={(newContent) => {
          // Final content update on blur for better performance
          if (newContent !== value) {
            onChange(newContent);
          }
        }}
      />
    </EditorWrapper>
  );
};

// Memoize component to prevent unnecessary re-renders
export default React.memo(RichTextEditor);
