import { forwardRef } from 'react';
import NextLink from 'next/link';
import { Link as MuiLink } from '@mui/material';

/**
 * This is a wrapper over `next/link` and MUI Link components.
 * We use this to help us maintain consistency between client-side routing and MUI styling
 */
export const RouterLink = forwardRef(function RouterLink({ component: Component, href, ...props }, ref) {
  if (Component) {
    return (
      <NextLink href={href} passHref legacyBehavior>
        <MuiLink ref={ref} {...props} />
      </NextLink>
    );
  }

  return (
    <NextLink
      ref={ref}
      href={href}
      {...props}
    />
  );
});
