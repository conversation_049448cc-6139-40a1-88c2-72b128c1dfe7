import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  TextField,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Select,
  MenuItem,
  Avatar,
  InputAdornment,
  TablePagination,
  Tooltip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Checkbox,
  DialogContentText,
} from "@mui/material";
import { Search } from "@mui/icons-material";
import { useCallback, useEffect, useState } from "react";
import { useRouter } from "next/router";
import { paths } from "@/src/paths";
import Grid from "@mui/system/Grid";
import { usePartnerEmployee } from "@/src/api/hooks/partner-employee/use-partner-employee";
import _ from "lodash";
import { PartnerEmployeeParamDto } from "@/src/api/services/partner-employee/partner-employee.service";
import dayjs from "dayjs";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import { useRole } from "@/src/api/hooks/role/use-role";
import { useCheckPermission } from "@/src/api/hooks/auth/use-check-permission";
import { useDebounce } from "@/src/hooks/use-debounce";
import TruncatedText from "../../truncated-text/truncated-text";
import { formatTruncatedText } from "@/src/utils/format";

export interface RoleDto {
  functions: any[];
  role: {
    id: string;
    roleName: string;
    roleDescription: string;
    roleIcon: string;
    roleId: string;
    status: string;
    partnerId: string;
    createdBy: string;
    createdDate: string;
    modifiedBy: string | null;
    modifiedDate: string | null;
    isDeleted: boolean;
    deletedAt: string | null;
  };
  userCount: number;
}

export interface EmployeeDto {
  employeeId: string;
  fullname: string;
  email: string | null;
  phoneNumber: string;
  username: string;
  avatar: string;
  roles: RoleDto[];
  status: "Actived" | "Inactived" | string;
  created: string;
  updated: string;
}

const Header = ({
  roleSelected,
  setRoleSelected,
  searchText,
  setSearchText,
  handleRoleManagementClick,
  handleEmployeeManagementClick,
  selected,
  handleDeleteMultiple,
  setPage,
}) => {
  const { listRole } = useRole();
  const [roles, setRoles] = useState<RoleDto[]>([]);

  const getAllRoles = async () => {
    const res = await listRole();
    if (res && res.status === 200) {
      if (res?.data?.data.length > 0) {
        setRoles(res.data.data);
      }
    }
  };
  useEffect(() => {
    getAllRoles();
  }, []);
  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: { xs: "column", md: "row" },
        gap: 2,
        mb: 4,
        alignItems: "center",
      }}
    >
      <TextField
        placeholder="Tìm kiếm theo tên đăng nhập, họ tên, SĐT"
        variant="outlined"
        size="small"
        value={searchText}
        onChange={(e) => {
          setPage(0);
          setSearchText(e.target.value);
        }}
        sx={{ width: { xs: "100%", md: "25rem" } }}
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <Search />
            </InputAdornment>
          ),
        }}
      />

      <Select
        value={roleSelected}
        onChange={(e) => {
          setPage(0);
          setRoleSelected(e.target.value);
        }}
        size="small"
        sx={{ minWidth: { xs: "100%", md: "12.5rem" } }}
      >
        <MenuItem value="all">Tất cả các vai trò</MenuItem>
        {roles
          .filter((role) => role.role.roleId !== "admin" && role.role.roleId !== "staff")
          .map((role) => (
            <MenuItem key={role.role.roleId} value={role.role.roleId}>
              {role.role.roleName}
            </MenuItem>
          ))}
      </Select>

      <Box sx={{ flex: 1 }} />

      {selected && selected.length > 0 && (
        <Button
          variant="outlined"
          color="error"
          startIcon={<DeleteIcon />}
          onClick={handleDeleteMultiple}
          sx={{ height: "2.5rem", width: { xs: "100%", md: "auto" } }}
        >
          Xoá ({selected.length})
        </Button>
      )}

      <Button
        variant="text"
        size="small"
        sx={{
          mr: { md: 0 },
          border: "1px solid #ccc",
          color: "black",
          height: "2.5rem",
          width: { xs: "100%", md: "auto" },
          padding: "0 1rem",
        }}
        onClick={handleRoleManagementClick}
      >
        Vai trò quản lý
      </Button>

      <Button
        variant="contained"
        color="primary"
        size="small"
        sx={{ height: "2.5rem", width: { xs: "100%", md: "auto" }, padding: "0 1rem" }}
        onClick={handleEmployeeManagementClick}
      >
        Thêm nhân viên
      </Button>
    </Box>
  );
};

function getStatusText(status) {
  if (status === "Actived") {
    return "Đang hoạt động";
  } else if (status === "InActived") {
    return "Không hoạt động";
  } else {
    return "Trạng thái không xác định";
  }
}

const EmployeeTable = ({
  employees,
  totalCount,
  page,
  setPage,
  rowsPerPage,
  setRowsPerPage,
  handleChangePage,
  handleChangeRowsPerPage,
  handleDeleteClick,
  open,
  setOpen,
  isFetch,
  setIsFetch,
  selectedPartnerToDelete,
  setSelectedPartnerToDelete,
  selected,
  setSelected,
  openMultipleDeleteDialog,
  setOpenMultipleDeleteDialog,
  confirmDeleteMultiple,
}) => {
  const { deleteMultiPartnerEmployee } = usePartnerEmployee();
  const router = useRouter();
  const handleConfirmDelete = async () => {
    try {
      const res = await deleteMultiPartnerEmployee([selectedPartnerToDelete.employeeId]);
      if (res && res.status === 200) {
        setOpen(false);
        setIsFetch((prevState) => !prevState);
        setSelectedPartnerToDelete(null);
      }
    } catch (error) {
      console.error("Error deleting employee:", error);
    }
  };

  const handleEditClick = (employee) => {
    router.push({
      pathname: `${paths.settings.employeeRole}`,
      query: { id: employee.employeeId },
    });
  };

  const handleSelectAll = (event) => {
    if (event.target.checked) {
      if (employees && employees.length > 0) {
        setSelected(employees.map((employee) => employee.employeeId));
      }
    } else {
      setSelected([]);
    }
  };

  const handleSelectOne = (event, employeeId) => {
    if (event.target.checked) {
      setSelected([...selected, employeeId]);
    } else {
      setSelected(selected.filter((id) => id !== employeeId));
    }
  };

  return (
    <Box>
      <TableContainer component={Paper} variant="outlined">
        <Table>
          <TableHead>
            <TableRow>
              <TableCell padding="checkbox">
                <Checkbox
                  color="primary"
                  onChange={handleSelectAll}
                  checked={
                    employees && employees.length > 0 && selected.length === employees.length
                  }
                  indeterminate={
                    selected.length > 0 && selected.length < (employees ? employees.length : 0)
                  }
                />
              </TableCell>
              <TableCell>STT</TableCell>
              <TableCell>Họ tên</TableCell>
              <TableCell>Vai trò</TableCell>
              <TableCell sx={{ width: "150px" }}>Trạng thái</TableCell>
              <TableCell>Ngày tham gia</TableCell>
              <TableCell sx={{ width: "100px" }}>Quản lý</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {Array.isArray(employees) && employees.length > 0 ? (
              employees.map((employee, index) => (
                <TableRow key={employee.id}>
                  <TableCell padding="checkbox">
                    <Checkbox
                      color="primary"
                      checked={selected.includes(employee.employeeId)}
                      onChange={(event) => handleSelectOne(event, employee.employeeId)}
                    />
                  </TableCell>
                  <TableCell>{page * rowsPerPage + index + 1}</TableCell>
                  <TableCell>
                    <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                      <Avatar
                        src={employee.avatar?.startsWith("http") ? employee.avatar : undefined}
                        sx={{ bgcolor: "primary.main" }}
                      >
                        {!employee.avatar?.startsWith("http") && employee.avatar}
                      </Avatar>

                      <Box>
                        <Typography variant="body1">
                          <TruncatedText
                            {...formatTruncatedText({
                              text: employee.fullname,
                              isGranted: true,
                              actionNeed: () => handleEditClick(employee),
                              width: "300px",
                            })}
                          />
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {employee.phoneNumber}
                        </Typography>
                      </Box>
                    </Box>
                  </TableCell>
                  <TableCell>
                    {employee.roles.map((item) => (
                      <Typography>{item.roleName}</Typography>
                    ))}
                  </TableCell>
                  <TableCell>
                    <span
                      className={
                        (employee.status === "Actived"
                          ? "px-3 py-2 rounded-xl text-green-700 bg-green-100 font-semibold"
                          : employee.status === "InActived"
                          ? "px-3 py-2 rounded-xl text-gray-600 bg-gray-200 font-semibold"
                          : "px-3 py-2 rounded-xl text-gray-500 bg-gray-100 font-semibold") +
                        " inline-block whitespace-nowrap leading-tight"
                      }
                    >
                      {getStatusText(employee.status)}
                    </span>
                  </TableCell>
                  <TableCell>{dayjs(employee.created).format("DD/MM/YYYY HH:mm:ss")}</TableCell>
                  <TableCell>
                    <Tooltip title="Sửa" arrow>
                      <IconButton
                        aria-label="Sửa"
                        tabIndex={0}
                        onClick={() => handleEditClick(employee)}
                        onKeyDown={(e) => {
                          if (e.key === "Enter" || e.key === " ") handleEditClick(employee);
                        }}
                        sx={{ color: "blue" }}
                        size="small"
                      >
                        <EditIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Xoá" arrow>
                      <IconButton
                        aria-label="Xoá"
                        tabIndex={0}
                        onClick={() => handleDeleteClick(employee)}
                        onKeyDown={(e) => {
                          if (e.key === "Enter" || e.key === " ") handleDeleteClick(employee);
                        }}
                        sx={{ color: "red" }}
                        size="small"
                      >
                        <DeleteIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <>
                <TableCell colSpan={12} align="center">
                  <Typography sx={{ textAlign: "center", fontSize: "15px" }}>
                    Không có dữ liệu
                  </Typography>
                </TableCell>
              </>
            )}
          </TableBody>
        </Table>
      </TableContainer>

      <Dialog open={open} onClose={() => setOpen(false)}>
        <DialogTitle>Xác nhận xoá</DialogTitle>
        <DialogContent>
          {selectedPartnerToDelete && (
            <DialogContentText>
              Bạn có chắc chắn muốn xoá nhân viên{" "}
              <strong>{selectedPartnerToDelete.fullname}</strong>?
            </DialogContentText>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpen(false)} variant="outlined">
            Huỷ
          </Button>
          <Button onClick={handleConfirmDelete} color="error" variant="contained">
            Xoá
          </Button>
        </DialogActions>
      </Dialog>

      <Dialog open={openMultipleDeleteDialog} onClose={() => setOpenMultipleDeleteDialog(false)}>
        <DialogTitle>Xác nhận xoá nhiều nhân viên</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Bạn có chắc chắn muốn xoá {selected.length} nhân viên đã chọn không?
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenMultipleDeleteDialog(false)} variant="outlined">
            Huỷ
          </Button>
          <Button onClick={confirmDeleteMultiple} color="error" variant="contained">
            Xoá
          </Button>
        </DialogActions>
      </Dialog>

      <TablePagination
        component="div"
        count={totalCount}
        page={page}
        onPageChange={handleChangePage}
        rowsPerPage={rowsPerPage}
        onRowsPerPageChange={handleChangeRowsPerPage}
        rowsPerPageOptions={[10, 25, 50]}
        labelRowsPerPage="Số dòng mỗi trang"
      />
    </Box>
  );
};

interface EmployeeManagementProps {
  employees?: EmployeeDto[];
}
export default function EmployeeManagement() {
  // const [roleFilter, setRoleFilter] = useState("all");
  const [searchText, setSearchText] = useState("");
  const [roleSelected, setRoleSelected] = useState("all");
  const router = useRouter();
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const [employees, setEmployees] = useState<EmployeeDto[]>();
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [selectedPartnerToDelete, setSelectedPartnerToDelete] = useState(null);
  const [isFetch, setIsFetch] = useState(false);
  const { listPartnerEmployee, deleteMultiPartnerEmployee } = usePartnerEmployee();
  const [selected, setSelected] = useState<string[]>([]);
  const [openMultipleDeleteDialog, setOpenMultipleDeleteDialog] = useState(false);
  const debouncedSearchValue = useDebounce(searchText, 600);

  const handleRoleManagementClick = () => {
    router.push(paths.settings.managementRole);
  };

  const handleEmployeeManagementClick = () => {
    router.push(paths.settings.employeeRole);
  };

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const fetchEmployeeList = async (currentPage, pageSize, searchText, roleSelected, isFetch) => {
    const body: PartnerEmployeeParamDto = {
      PageSize: pageSize,
      PageIndex: currentPage,
      Search: searchText,
      RoleId: roleSelected === "all" ? "" : roleSelected,
    };

    try {
      const response = await listPartnerEmployee(body);

      if (response && response.data) {
        setEmployees(response.data.data || []);
        setTotalCount(response.data.pagination.totalCount || 0);
      }
    } catch (error) {
      console.error("Error fetching employees:", error);
    }
  };

  // Wrap fetchEmployeeList with debounce
  const debouncedFetchEmployeeList = useCallback(
    _.debounce((currentPage, pageSize, debouncedSearchValue, roleSelected, isFetch) => {
      fetchEmployeeList(currentPage, pageSize, debouncedSearchValue, roleSelected, isFetch);
    }, 400), // Delay 400ms
    []
  );

  useEffect(() => {
    debouncedFetchEmployeeList(page, rowsPerPage, debouncedSearchValue, roleSelected, isFetch);

    return () => {
      debouncedFetchEmployeeList.cancel();
    };
  }, [page, rowsPerPage, debouncedFetchEmployeeList, debouncedSearchValue, roleSelected, isFetch]);

  useEffect(() => {
    if (isFetch) {
      fetchEmployeeList(page, rowsPerPage, debouncedSearchValue, roleSelected, isFetch);
    }
  }, [isFetch]);

  useEffect(() => {
    fetchEmployeeList(page, rowsPerPage, debouncedSearchValue, roleSelected, isFetch);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleDeleteClick = (partner) => {
    setSelectedPartnerToDelete(partner);
    setOpenDeleteDialog(true);
  };

  const handleDeleteMultiple = () => {
    if (selected.length > 0) {
      setOpenMultipleDeleteDialog(true);
    }
  };

  const confirmDeleteMultiple = async () => {
    try {
      await deleteMultiPartnerEmployee(selected);

      setOpenMultipleDeleteDialog(false);
      setSelected([]);
      setIsFetch((prevState) => !prevState);
    } catch (error) {
      console.error("Error deleting multiple employees:", error);
    }
  };

  return (
    <Grid container justifyContent="center" sx={{ p: 3, background: "white" }}>
      <Grid size={{ xs: 12, md: 12 }}>
        <Header
          searchText={searchText}
          setSearchText={setSearchText}
          roleSelected={roleSelected}
          setRoleSelected={setRoleSelected}
          handleRoleManagementClick={handleRoleManagementClick}
          handleEmployeeManagementClick={handleEmployeeManagementClick}
          selected={selected}
          handleDeleteMultiple={handleDeleteMultiple}
          setPage={setPage}
        />
        <EmployeeTable
          employees={employees}
          page={page}
          setPage={setPage}
          rowsPerPage={rowsPerPage}
          setRowsPerPage={setRowsPerPage}
          handleChangePage={handleChangePage}
          handleChangeRowsPerPage={handleChangeRowsPerPage}
          totalCount={totalCount}
          handleDeleteClick={handleDeleteClick}
          open={openDeleteDialog}
          setOpen={setOpenDeleteDialog}
          isFetch={isFetch}
          setIsFetch={setIsFetch}
          selectedPartnerToDelete={selectedPartnerToDelete}
          setSelectedPartnerToDelete={setSelectedPartnerToDelete}
          selected={selected}
          setSelected={setSelected}
          openMultipleDeleteDialog={openMultipleDeleteDialog}
          setOpenMultipleDeleteDialog={setOpenMultipleDeleteDialog}
          confirmDeleteMultiple={confirmDeleteMultiple}
        />
      </Grid>
    </Grid>
  );
}
