import React, { useEffect, useState } from "react";
import {
  Box,
  Typography,
  Button,
  TextField,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Checkbox,
  Paper,
  InputAdornment,
  Tooltip,
  IconButton,
  Dialog,
  DialogActions,
  DialogContentText,
  DialogTitle,
  DialogContent,
} from "@mui/material";
import SearchIcon from "@mui/icons-material/Search";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import AddIcon from "@mui/icons-material/Add";
import CircleIcon from "@mui/icons-material/Circle";
import { useRouter } from "next/router";
import { paths } from "@/src/paths";
import { useStoreId } from "@/src/hooks/use-store-id";
import { useShop } from "@/src/api/hooks/shop/use-shop";
import { shopPolicyService } from "@/src/api/services/shop-policy/shop-policy.service";
import Link from "next/link";
import { tokens } from "@/src/locales/tokens";
import { useTranslation } from "react-i18next";
import { useAllPermissions } from "@/src/api/hooks/auth/use-all-permissions";
import { PERMISSION_TYPE_ENUM } from "@/src/constants/constant";
import { usePathname } from "next/navigation";
import TruncatedText from "@/src/components/truncated-text/truncated-text";
import { formatTruncatedText } from "@/src/utils/format";

interface PolicyData {
  id: number;
  title: string;
  createdAt: string;
  updatedAt: string;
  status: "Publish" | "Unpublish";
}
interface ShopPolicyDto {
  shopId: string;
  shopPolicy: any;
  updated: string;
}
interface PolicyDto {
  id: string;
  content: string;
  created: string;
  title: string;
  updated: string;
}

const initialData: PolicyData[] = [
  {
    id: 1,
    title: "Chính sách ứng dụng",
    createdAt: "11:00 30/08/2024",
    updatedAt: "11:00 30/08/2024",
    status: "Publish",
  },
  {
    id: 2,
    title: "Chính sách ứng dụng",
    createdAt: "11:00 30/08/2024",
    updatedAt: "11:00 30/08/2024",
    status: "Unpublish",
  },
];

const Header = ({
  isGranted,
  onAddNew,
  selected,
  isDeleteMany,
  setIsDeleteMany,
  handleDeleteMany,
  transformedData,
}) => {
  const { t } = useTranslation();
  const [listDelete, setListDelete] = useState([]);
  const pathname = usePathname();
  const onDeleteClick = () => {
    setIsDeleteMany(true);
  };

  useEffect(() => {
    setListDelete(transformedData?.filter((x: any) => selected?.includes(x.id)));
  }, [selected]);
  return (
    <>
      <Box sx={{ display: "flex", justifyContent: "flex-end", alignItems: "right", mb: 3, gap: 2 }}>
        {selected && selected?.length > 0 && (
          <Tooltip
            title={
              !isGranted(pathname, PERMISSION_TYPE_ENUM.Delete) ? "Bạn không có quyền xoá" : ""
            }
          >
            <span>
              <Button
                variant="outlined"
                color="error"
                startIcon={<DeleteIcon />}
                onClick={onDeleteClick}
                sx={{
                  height: "40px",
                  borderRadius: "8px",
                  textTransform: "none",
                }}
                disabled={!isGranted(pathname, PERMISSION_TYPE_ENUM.Delete)}
              >
                {`Xoá${selected?.length > 0 ? ` (${selected?.length})` : ""}`}
              </Button>
            </span>
          </Tooltip>
        )}
        <Tooltip
          title={!isGranted(pathname, PERMISSION_TYPE_ENUM.Delete) ? "Bạn không có quyền thêm" : ""}
        >
          <span>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              sx={{
                backgroundColor: "#2f7cf6",
                textTransform: "none",
                "&:hover": {
                  backgroundColor: "#1565c0",
                },
              }}
              disabled={!isGranted(pathname, PERMISSION_TYPE_ENUM.Add)}
              onClick={onAddNew}
            >
              Thêm mới
            </Button>
          </span>
        </Tooltip>
      </Box>
      <Dialog open={isDeleteMany} onClose={() => setIsDeleteMany(false)} maxWidth="sm" fullWidth>
        <DialogTitle>{t(tokens.contentManagement.category.delete.confirmTitle)}</DialogTitle>
        <DialogContent>
          <DialogContentText>
            {t(tokens.contentManagement.category.delete.confirmMessage, {
              name: listDelete.map((item) => item.title).join(", "),
            })}
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setIsDeleteMany(false)} variant="outlined">
            {t(tokens.common.cancel)}
          </Button>
          <Button onClick={() => handleDeleteMany()} color="error" variant="contained" autoFocus>
            {t(tokens.common.delete)}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

const SearchBar = ({
  searchText,
  setSearchText,
  transformedData,
  setTransformedData,
  transformedDataClone,
  setTransformedDataClone,
}) => {
  const handleChange = (e: any) => {
    const searchValue = e?.target?.value.toLowerCase();
    setSearchText(searchValue);

    if (searchValue?.length > 0) {
      const filteredData = transformedData?.filter((item: any) =>
        item.title.toLowerCase().includes(searchValue)
      );

      setTransformedData(filteredData);
    } else {
      setTransformedData((prev) => [...transformedDataClone]);
    }
  };

  return (
    <Box sx={{ mb: 3 }}>
      <TextField
        placeholder="Tìm kiếm"
        variant="outlined"
        size="small"
        sx={{ width: "300px" }}
        onChange={handleChange}
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <SearchIcon sx={{ color: "#666" }} />
            </InputAdornment>
          ),
        }}
      />
    </Box>
  );
};

const PolicyTable = ({
  isGranted,
  data,
  setData,
  transformedData,
  setTransformedData,
  selected,
  setSelected,
  handleSelectAll,
  handleSelect,
  fetchShopPolicy,
}) => {
  const { t } = useTranslation();
  const pathname = usePathname();
  const storeId = useStoreId();
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedPolicy, setSelectedPolicy] = useState<PolicyDto>();
  const router = useRouter();

  const handleDeletePolicy = (policy) => {
    setOpenDialog(true);
    setSelectedPolicy(policy);
  };
  const confirmDelete = async (policy) => {
    let newList = [];
    newList = transformedData?.filter((x) => x.title !== policy.title);
    newList.length === 0 ? null : newList;
    var data = {
      shopId: storeId,
      shopPolicy: JSON.stringify(newList),
    };

    const res = await shopPolicyService.createShopPolicy(data);
    setData((prevData) => {
      let updatedData: any = [...prevData];
      updatedData = {
        ...updatedData,
        shopPolicy: res?.data?.shopPolicy,
      };
      return updatedData;
    });

    fetchShopPolicy();
    setOpenDialog(false);
  };

  return (
    <TableContainer component={Paper} sx={{ boxShadow: "none" }}>
      <Table>
        <TableHead>
          <TableRow>
            <TableCell padding="checkbox">
              <Checkbox
                checked={selected?.length === transformedData?.length}
                indeterminate={selected?.length > 0 && selected.length < transformedData?.length}
                onChange={handleSelectAll}
              />
            </TableCell>
            <TableCell>STT</TableCell>
            <TableCell sx={{ width: "300px" }}>Tiêu đề</TableCell>
            <TableCell>Thời gian tạo</TableCell>
            <TableCell>Thời gian cập nhật</TableCell>
            <TableCell>Quản lý</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {transformedData?.map((row: any, index: number) => (
            <TableRow key={index} hover>
              <TableCell padding="checkbox">
                <Checkbox
                  checked={selected?.includes(row.id)}
                  onChange={() => handleSelect(row.id)}
                />
              </TableCell>
              <TableCell>{index + 1}</TableCell>
              <TableCell>
                <TruncatedText
                  {...formatTruncatedText({
                    text: row.title,
                    isGranted: isGranted(pathname, PERMISSION_TYPE_ENUM.Edit),
                    actionNeed: () => router.push(`${paths.settings.clause.index}/${row.id}`),
                    width: "300px",
                  })}
                />
              </TableCell>
              <TableCell>{row.created}</TableCell>
              <TableCell>{row.updated}</TableCell>

              <TableCell>
                <Box sx={{ display: "flex", gap: 1 }}>
                  {isGranted(pathname, PERMISSION_TYPE_ENUM.Edit) ? (
                    <Link href={paths.settings.clause.index + "/" + row.id} passHref>
                      <Tooltip title={"Sửa điều khoản"}>
                        <IconButton size="small" sx={{ color: "primary.main" }}>
                          <EditIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    </Link>
                  ) : (
                    <Tooltip title="Bạn không có quyền chỉnh sửa">
                      <span>
                        <IconButton size="small" sx={{ color: "gray", opacity: 0.5 }} disabled>
                          <EditIcon fontSize="small" />
                        </IconButton>
                      </span>
                    </Tooltip>
                  )}

                  {isGranted(pathname, PERMISSION_TYPE_ENUM.Delete) ? (
                    <Tooltip title={"Xoá điều khoản"}>
                      <IconButton
                        onClick={() => handleDeletePolicy(row)}
                        size="small"
                        sx={{ color: "error.main" }}
                      >
                        <DeleteIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  ) : (
                    <Tooltip title="Bạn không có quyền xóa">
                      <span>
                        <IconButton size="small" sx={{ color: "gray", opacity: 0.5 }} disabled>
                          <DeleteIcon fontSize="small" />
                        </IconButton>
                      </span>
                    </Tooltip>
                  )}
                </Box>
                <Dialog
                  open={openDialog}
                  onClose={() => setOpenDialog(false)}
                  maxWidth="sm"
                  fullWidth
                >
                  <DialogTitle>
                    {t(tokens.contentManagement.category.delete.confirmTitle)}
                  </DialogTitle>
                  <DialogContent>
                    <DialogContentText>
                      {t(tokens.contentManagement.category.delete.confirmMessage, {
                        name: selectedPolicy?.title,
                      })}
                    </DialogContentText>
                  </DialogContent>
                  <DialogActions>
                    <Button onClick={() => setOpenDialog(false)} variant="outlined">
                      {t(tokens.common.cancel)}
                    </Button>
                    <Button
                      onClick={() => confirmDelete(selectedPolicy)}
                      color="error"
                      variant="contained"
                      autoFocus
                    >
                      {t(tokens.common.delete)}
                    </Button>
                  </DialogActions>
                </Dialog>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
};

const SettingsClauseContent = () => {
  const [selected, setSelected] = useState<number[]>([]);
  const [shopId, setShopId] = useState<string>("");
  const [isDeleteMany, setIsDeleteMany] = useState<boolean>(false);
  const [searchText, setSearchText] = useState<string>("");
  const router = useRouter();
  const storeId = useStoreId();
  const { detailShop } = useShop();
  const { permissions } = useAllPermissions();
  const isGranted = (url, permission) => {
    if (url.endsWith("/")) {
      url = url.slice(0, -1);
    }
    const matchingUrl = Object.keys(permissions).find((key) => key === url);
    if (!matchingUrl) return false;
    return permissions[matchingUrl]?.includes(permission) || false;
  };
  const fetchData = async () => {
    try {
      const detail = await detailShop(storeId);
      setShopId(detail?.data?.shopId);
    } catch (error) {
      console.log({ error });
    }
  };
  useEffect(() => {
    fetchData();
  }, [storeId]);

  const handleSelect = (id: number) => {
    const selectedIndex = selected?.indexOf(id);
    let newSelected: number[] = [];

    if (selectedIndex === -1) {
      newSelected = [...selected, id];
    } else {
      newSelected = selected?.filter((item) => item !== id);
    }

    setSelected(newSelected);
  };

  const handleAddNew = () => {
    router.push(paths.settings.clause.add);
  };

  const [listShopPolicy, setListShopPolicy] = useState<ShopPolicyDto[]>([]);

  const fetchShopPolicy = async () => {
    if (shopId) {
      const res = await shopPolicyService.getListShopPolicy(storeId, 0, 99);

      setListShopPolicy(res?.data?.data);
      const transformedDataX = res?.data?.data?.map((row: any) => {
        try {
          return JSON.parse(row.shopPolicy);
        } catch (error) {
          console.error("JSON parsing error:", error);
          return null;
        }
      });

      if (transformedDataX && transformedDataX.length > 0) {
        setTransformedData(transformedDataX[0]);
        setTransformedDataClone(transformedDataX[0]);
      } else {
        setTransformedData(null);
        setTransformedDataClone(null);
      }
    }
  };
  useEffect(() => {
    fetchShopPolicy();
  }, [shopId]);

  const [transformedData, setTransformedData] = useState([]);
  const [transformedDataClone, setTransformedDataClone] = useState([]);

  const fetchTransData = () => {};
  useEffect(() => {
    fetchTransData();
  }, [storeId]);
  useEffect(() => {
    fetchTransData();
  }, [isDeleteMany, storeId]);

  const handleSelectAll = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.checked) {
      const list = transformedData?.map((item) => item.id);
      setSelected(list);
    } else {
      setSelected([]);
    }
  };

  const handleDeleteMany = async () => {
    const transformedDataX = transformedData?.filter((item) => !selected.includes(item.id));
    var data = {
      shopId: storeId,
      shopPolicy: JSON.stringify(transformedDataX),
    };
    setSelected(transformedDataX?.map((item) => item.id));
    const res = await shopPolicyService.createShopPolicy(data);
    fetchShopPolicy();
    setIsDeleteMany(false);
  };

  return (
    <Box sx={{ p: 3, backgroundColor: "#ffffff" }}>
      <Box sx={{ display: "flex", justifyContent: "space-between", flexWrap: "wrap" }}>
        <SearchBar
          searchText={searchText}
          setSearchText={setSearchText}
          transformedData={transformedData}
          setTransformedData={setTransformedData}
          transformedDataClone={transformedDataClone}
          setTransformedDataClone={transformedDataClone}
        />
        <Header
          isGranted={isGranted}
          onAddNew={handleAddNew}
          selected={selected}
          isDeleteMany={isDeleteMany}
          setIsDeleteMany={setIsDeleteMany}
          handleDeleteMany={handleDeleteMany}
          transformedData={transformedData}
        />
      </Box>
      <Box sx={{ background: "white", boxShadow: 3, borderRadius: 2, p: 2 }}>
        <PolicyTable
          isGranted={isGranted}
          data={listShopPolicy}
          setData={setListShopPolicy}
          transformedData={transformedData}
          setTransformedData={setTransformedData}
          selected={selected}
          setSelected={setSelected}
          handleSelectAll={handleSelectAll}
          handleSelect={handleSelect}
          fetchShopPolicy={fetchShopPolicy}
          // onEdit={handleEdit}
        />
      </Box>
    </Box>
  );
};

export default SettingsClauseContent;
