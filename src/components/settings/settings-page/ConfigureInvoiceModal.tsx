import React, { useState, useEffect } from "react";
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  TextField,
  DialogActions,
  Button,
  MenuItem,
  Typography,
  Box,
  Paper,
  InputBase,
  IconButton,
  InputAdornment,
} from "@mui/material";
import { InvoiceConfigData, InvoiceProvider } from "@/src/api/types/invoice.types";
import SaveIcon from "@mui/icons-material/Save";
import Visibility from "@mui/icons-material/Visibility";
import VisibilityOff from "@mui/icons-material/VisibilityOff";
import { useTaxInvoice } from "@/src/api/hooks/tax-invoice/use-tax-invoice";
import { VietnamFlag } from "@/src/pages/dashboard/settings/employee-role";

interface ConfigureInvoiceModalProps {
  open: boolean;
  onClose: () => void;
  provider: InvoiceProvider | null;
  onSave: (formData: Partial<InvoiceConfigData>) => void;
  existingConfig?: InvoiceConfigData;
}

export default function ConfigureInvoiceModal({
  open,
  onClose,
  provider,
  onSave,
  existingConfig,
}: ConfigureInvoiceModalProps) {
  const { getInforBusinessConfigTaxInvoice } = useTaxInvoice();

  const [formData, setFormData] = useState<Partial<InvoiceConfigData>>({
    username: "",
    password: "",
    sellerTaxCode: "",
    sellerLegalName: "",
    templateCode: "",
    invoiceSeries: "",
    invoiceType: "",
    reservationCode: "",
    provider: provider || undefined,
    isDraft: true,
    sellerAddressLine: "",
    sellerBankAccount: "",
    sellerBankName: "",
    sellerEmail: "",
    sellerPhoneNumber: "",
  });

  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [showPassword, setShowPassword] = useState(false);

  useEffect(() => {
    if (open) {
      if (existingConfig) {
        setFormData({
          username: existingConfig.username || "",
          password: existingConfig.password || "",
          sellerTaxCode: existingConfig.sellerTaxCode || "",
          sellerLegalName: existingConfig.sellerLegalName || "",
          templateCode: existingConfig.templateCode || "",
          invoiceSeries: existingConfig.invoiceSeries || "",
          invoiceType: existingConfig.invoiceType || "",
          reservationCode: existingConfig.reservationCode || "",
          provider: existingConfig.provider || provider,
          isDraft: existingConfig.isDraft ?? true,
          sellerAddressLine: existingConfig.sellerAddressLine || "",
          sellerBankAccount: existingConfig.sellerBankAccount || "",
          sellerBankName: existingConfig.sellerBankName || "",
          sellerEmail: existingConfig.sellerEmail || "",
          sellerPhoneNumber: existingConfig.sellerPhoneNumber || "",
        });
      } else {
        setFormData({
          username: "",
          password: "",
          sellerTaxCode: "",
          sellerLegalName: "",
          templateCode: "",
          invoiceSeries: "",
          invoiceType: "",
          reservationCode: "",
          provider: provider || undefined,
          isDraft: true,
          sellerAddressLine: "",
          sellerBankAccount: "",
          sellerBankName: "",
          sellerEmail: "",
          sellerPhoneNumber: "",
        });
      }
      setErrors({}); // Reset errors khi mở modal
    }
  }, [open, provider, existingConfig]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });

    // Xóa lỗi của trường khi người dùng bắt đầu nhập
    if (value.trim() && errors[name]) {
      setErrors((prev) => ({ ...prev, [name]: "" }));
    }

    if (name === "sellerEmail" && value.trim()) {
      if (!isValidEmail(value)) {
        setErrors((prev) => ({ ...prev, [name]: "Email không hợp lệ" }));
      }
    }
  };

  const isValidPhoneNumber = (phone: string): boolean => {
    if (!phone) return false;

    const cleanPhone = phone.replace(/\D/g, "");

    if (cleanPhone.length === 9) {
      return true;
    }

    if (cleanPhone.length === 10) {
      return cleanPhone.startsWith("0");
    }

    return false;
  };
  const isValidEmail = (email: string): boolean => {
    if (!email) return true;
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const validateForm = () => {
    const newErrors: { [key: string]: string } = {};
    if (!formData.username?.trim()) {
      newErrors.username = "Tên đăng nhập là bắt buộc";
    }
    if (!formData.password?.trim()) {
      newErrors.password = "Mật khẩu là bắt buộc";
    }
    if (!formData.sellerTaxCode?.trim()) {
      newErrors.sellerTaxCode = "Mã số thuế là bắt buộc";
    }
    if (!formData.templateCode?.trim()) {
      newErrors.templateCode = "Mã hóa đơn là bắt buộc";
    }
    if (!formData.invoiceSeries?.trim()) {
      newErrors.invoiceSeries = "Ký hiệu hóa đơn là bắt buộc";
    }
    if (!formData.invoiceType?.trim()) {
      newErrors.invoiceType = "Loại hóa đơn là bắt buộc";
    }
    if (formData.isDraft === undefined) {
      newErrors.isDraft = "Trạng thái xuất là bắt buộc";
    }
    if (formData.sellerEmail?.trim()) {
      if (!isValidEmail(formData.sellerEmail)) {
        newErrors.sellerEmail = "Email không hợp lệ";
      }
    }
    if (!formData.sellerPhoneNumber?.trim()) {
      newErrors.sellerPhoneNumber = "Số điện thoại là bắt buộc";
    } else if (!isValidPhoneNumber(formData.sellerPhoneNumber)) {
      newErrors.sellerPhoneNumber = "Số điện thoại không hợp lệ";
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = () => {
    if (validateForm()) {
      onSave(formData);
    }
  };

  const handleToggleShowPassword = () => {
    setShowPassword((prev) => !prev);
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>Cấu hình {provider}</DialogTitle>
      <DialogContent>
        <Box sx={{ padding: 2, border: "1px solid rgb(223, 223, 223)", borderRadius: 2 }}>
          <Typography variant="h6">Thông tin người bán</Typography>
          <Box sx={{ display: "flex", justifyContent: "space-between" }}>
            <Box sx={{ width: "50%" }}>
              <Typography
                sx={{
                  display: "flex",
                  mb: 0.5,
                  fontWeight: 500,
                  fontSize: "15px",
                  marginTop: 2,
                }}
              >
                Mã số thuế{" "}
                <Typography
                  sx={{ color: "red", marginLeft: 0.5, fontSize: "16px", fontWeight: 500 }}
                >
                  *
                </Typography>
              </Typography>
              <Box sx={{ display: "flex", gap: 1 }}>
                <TextField
                  name="sellerTaxCode"
                  value={formData.sellerTaxCode}
                  onChange={handleChange}
                  fullWidth
                  margin="normal"
                  size="small"
                  placeholder="Nhập mã số thuế"
                  error={!!errors.sellerTaxCode}
                  helperText={errors.sellerTaxCode}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        <Button
                          variant="outlined"
                          size="small"
                          sx={{
                            height: "28px",
                            minWidth: "60px",
                            textTransform: "none",
                            borderRadius: 1,
                            marginRight: 1,
                          }}
                          onClick={async () => {
                            if (!formData.sellerTaxCode) {
                              setErrors((prev) => ({
                                ...prev,
                                sellerTaxCode: "Vui lòng nhập mã số thuế",
                              }));
                              return;
                            }
                            try {
                              const response = await getInforBusinessConfigTaxInvoice(
                                formData.sellerTaxCode
                              );
                              if (response?.data) {
                                setFormData((prev) => ({
                                  ...prev,
                                  sellerLegalName: response?.data?.data?.name || "",
                                  sellerAddressLine: response?.data?.data?.address || "",
                                }));
                              }
                            } catch (error) {
                              console.error("Error searching tax info:", error);
                            }
                          }}
                        >
                          Tra cứu
                        </Button>
                      </InputAdornment>
                    ),
                  }}
                  sx={{
                    marginTop: 0,
                    "& .MuiOutlinedInput-root": {
                      height: "40px",
                      paddingRight: "0",
                      "& .MuiInputAdornment-root": {
                        marginLeft: "0",
                      },
                    },
                  }}
                />
              </Box>
            </Box>
            <Box sx={{ width: "50%", marginLeft: 2 }}>
              <Typography
                sx={{
                  display: "flex",
                  mb: 0.5,
                  fontWeight: 500,
                  fontSize: "15px",
                  marginTop: 2,
                }}
              >
                Tên{" "}
                <Typography
                  sx={{ color: "red", marginLeft: 0.5, fontSize: "16px", fontWeight: 500 }}
                >
                  *
                </Typography>
              </Typography>
              <TextField
                name="sellerLegalName"
                value={formData.sellerLegalName}
                onChange={handleChange}
                fullWidth
                margin="normal"
                size="small"
                error={!!errors.sellerLegalName}
                helperText={errors.sellerLegalName}
                sx={{
                  marginTop: 0,
                  "& .MuiOutlinedInput-root": {
                    height: "40px",
                  },
                }}
              />
            </Box>
          </Box>
          <Box>
            <Box sx={{}}>
              <Typography
                sx={{
                  display: "flex",
                  mb: 0.5,
                  fontWeight: 500,
                  fontSize: "15px",
                }}
              >
                Địa chỉ{" "}
                <Typography
                  sx={{ color: "red", marginLeft: 0.5, fontSize: "16px", fontWeight: 500 }}
                >
                  *
                </Typography>
              </Typography>
              <TextField
                name="sellerAddressLine"
                value={formData.sellerAddressLine}
                onChange={handleChange}
                fullWidth
                margin="normal"
                size="small"
                error={!!errors.sellerAddressLine}
                helperText={errors.sellerAddressLine}
                sx={{
                  marginTop: 0,
                  "& .MuiOutlinedInput-root": {
                    height: "40px",
                  },
                }}
              />
            </Box>
          </Box>
          <Box sx={{ display: "flex", justifyContent: "space-between" }}>
            <Box sx={{ width: "50%" }}>
              <Typography
                sx={{
                  display: "flex",
                  mb: 0.5,
                  fontWeight: 500,
                  fontSize: "15px",
                }}
              >
                Số điện thoại{" "}
                <Typography
                  sx={{ color: "red", marginLeft: 0.5, fontSize: "16px", fontWeight: 500 }}
                >
                  *
                </Typography>
              </Typography>

              <TextField
                name="sellerPhoneNumber"
                value={formData.sellerPhoneNumber}
                onChange={handleChange}
                fullWidth
                margin="normal"
                size="small"
                error={!!errors.sellerPhoneNumber}
                helperText={errors.sellerPhoneNumber}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start" sx={{ mr: 0 }}>
                      <Box sx={{ display: "flex", alignItems: "center", gap: 0.5 }}>
                        <VietnamFlag />
                        <Typography
                          variant="body2"
                          sx={{
                            color: "text.secondary",
                            fontWeight: 500,
                            userSelect: "none",
                          }}
                        >
                          +84
                        </Typography>
                      </Box>
                    </InputAdornment>
                  ),
                }}
                sx={{
                  marginTop: 0,
                  "& .MuiOutlinedInput-root": {
                    height: "40px",
                    "& .MuiInputAdornment-root": {
                      marginRight: 0.5,
                    },
                  },
                }}
              />
            </Box>
            <Box sx={{ width: "50%", marginLeft: 2 }}>
              <Typography
                sx={{
                  display: "flex",
                  mb: 0.5,
                  fontWeight: 500,
                  fontSize: "15px",
                }}
              >
                Email{" "}
              </Typography>
              <TextField
                name="sellerEmail"
                value={formData.sellerEmail}
                onChange={handleChange}
                fullWidth
                margin="normal"
                size="small"
                error={!!errors.sellerEmail}
                helperText={errors.sellerEmail}
                sx={{
                  marginTop: 0,
                  "& .MuiOutlinedInput-root": {
                    height: "40px",
                  },
                }}
              />
            </Box>
          </Box>
        </Box>

        <Box
          sx={{ padding: 2, border: "1px solid rgb(223, 223, 223)", borderRadius: 2, marginTop: 3 }}
        >
          <Typography variant="h6">Thông tin hoá đơn</Typography>
          <Box sx={{ display: "flex", gap: 2 }}>
            <Box sx={{ width: "50%" }}>
              <Typography
                sx={{
                  display: "flex",
                  mb: 0.5,
                  fontWeight: 500,
                  fontSize: "15px",
                  marginTop: 2,
                }}
              >
                Mẫu hóa đơn{" "}
                <Typography
                  sx={{ color: "red", marginLeft: 0.5, fontSize: "16px", fontWeight: 500 }}
                >
                  *
                </Typography>
              </Typography>
              <TextField
                name="templateCode"
                value={formData.templateCode}
                onChange={handleChange}
                fullWidth
                margin="normal"
                error={!!errors.templateCode}
                helperText={errors.templateCode}
                sx={{
                  marginTop: 0,
                  "& .MuiOutlinedInput-root": {
                    height: "40px",
                  },
                }}
              />
            </Box>
            <Box sx={{ width: "50%" }}>
              <Typography
                sx={{
                  display: "flex",
                  mb: 0.5,
                  fontWeight: 500,
                  fontSize: "15px",
                  marginTop: 2,
                }}
              >
                Ký hiệu hóa đơn{" "}
                <Typography
                  sx={{ color: "red", marginLeft: 0.5, fontSize: "16px", fontWeight: 500 }}
                >
                  *
                </Typography>
              </Typography>
              <TextField
                name="invoiceSeries"
                value={formData.invoiceSeries}
                onChange={handleChange}
                fullWidth
                margin="normal"
                error={!!errors.invoiceSeries}
                helperText={errors.invoiceSeries}
                sx={{
                  marginTop: 0,
                  "& .MuiOutlinedInput-root": {
                    height: "40px",
                  },
                }}
              />
            </Box>
          </Box>
          <Box sx={{ display: "flex", gap: 2 }}>
            <Box sx={{ width: "50%" }}>
              <Typography
                sx={{
                  display: "flex",
                  mb: 0.5,
                  fontWeight: 500,
                  fontSize: "15px",
                }}
              >
                Loại hóa đơn{" "}
                <Typography
                  sx={{ color: "red", marginLeft: 0.5, fontSize: "16px", fontWeight: 500 }}
                >
                  *
                </Typography>
              </Typography>
              <TextField
                name="invoiceType"
                value={formData.invoiceType}
                onChange={handleChange}
                fullWidth
                margin="normal"
                error={!!errors.invoiceType}
                helperText={errors.invoiceType}
                sx={{
                  marginTop: 0,
                  "& .MuiOutlinedInput-root": {
                    height: "40px",
                  },
                }}
              />
            </Box>
            <Box sx={{ width: "50%" }}>
              <Typography
                sx={{
                  display: "flex",
                  mb: 0.5,
                  fontWeight: 500,
                  fontSize: "15px",
                }}
              >
                Hình thức xuất{" "}
                <Typography
                  sx={{ color: "red", marginLeft: 0.5, fontSize: "16px", fontWeight: 500 }}
                >
                  *
                </Typography>
              </Typography>
              <TextField
                select
                name="isDraft"
                value={formData.isDraft ? "true" : "false"}
                onChange={(e) => {
                  const value = e.target.value === "true";
                  setFormData({ ...formData, isDraft: value });
                }}
                fullWidth
                error={!!errors.isDraft}
                helperText={errors.isDraft}
                sx={{
                  marginTop: 0,
                  "& .MuiOutlinedInput-root": {
                    height: "40px",
                  },
                }}
              >
                <MenuItem value="true">Nháp</MenuItem>
                <MenuItem value="false">Phát hành</MenuItem>
              </TextField>
            </Box>
          </Box>
        </Box>

        <Box
          sx={{ padding: 2, border: "1px solid rgb(223, 223, 223)", borderRadius: 2, marginTop: 3 }}
        >
          <Typography variant="h6">Thông tin đăng nhập</Typography>
          <Box sx={{ display: "flex", justifyContent: "space-between" }}>
            <Box sx={{ width: "50%" }}>
              <Typography
                sx={{
                  display: "flex",
                  mb: 0.5,
                  fontWeight: 500,
                  fontSize: "15px",
                  marginTop: 2,
                }}
              >
                Tên đăng nhập{" "}
                <Typography
                  sx={{ color: "red", marginLeft: 0.5, fontSize: "16px", fontWeight: 500 }}
                >
                  *
                </Typography>
              </Typography>
              <TextField
                name="username"
                value={formData.username}
                onChange={handleChange}
                fullWidth
                margin="normal"
                size="small"
                error={!!errors.username}
                helperText={errors.username}
                sx={{ marginTop: 0 }}
              />
            </Box>
            <Box sx={{ width: "50%", marginLeft: 2 }}>
              <Typography
                sx={{
                  display: "flex",
                  mb: 0.5,
                  fontWeight: 500,
                  fontSize: "15px",
                  marginTop: 2,
                }}
              >
                Mật khẩu{" "}
                <Typography
                  sx={{ color: "red", marginLeft: 0.5, fontSize: "16px", fontWeight: 500 }}
                >
                  *
                </Typography>
              </Typography>
              <Paper
                variant="outlined"
                component="div"
                sx={{
                  display: "flex",
                  alignItems: "center",
                  width: "100%",
                  borderRadius: 1,
                  border: errors.password ? "1px solid #d32f2f" : "1px solid rgba(0, 0, 0, 0.23)",
                  "&:hover": {
                    borderColor: errors.password ? "#d32f2f" : "rgba(0, 0, 0, 0.87)",
                  },
                  height: 40,
                  overflow: "hidden",
                }}
              >
                <InputBase
                  name="password"
                  type={showPassword ? "text" : "password"}
                  value={formData.password}
                  onChange={handleChange}
                  sx={{
                    ml: 1.5,
                    flex: 1,
                    fontSize: "1rem",
                  }}
                  placeholder="Nhập mật khẩu"
                  fullWidth
                />
                <IconButton
                  sx={{ p: "10px", mr: 0.5 }}
                  aria-label={showPassword ? "Ẩn mật khẩu" : "Hiện mật khẩu"}
                  onClick={handleToggleShowPassword}
                  tabIndex={0}
                >
                  {showPassword ? <VisibilityOff /> : <Visibility />}
                </IconButton>
              </Paper>
              {errors.password && (
                <Typography color="error" variant="caption">
                  {errors.password}
                </Typography>
              )}
            </Box>
          </Box>
        </Box>
      </DialogContent>
      <DialogActions sx={{ marginRight: 4, marginBottom: 2, marginTop: 2 }}>
        <Button variant="outlined" onClick={onClose}>
          Hủy
        </Button>
        <Button
          onClick={handleSubmit}
          color="primary"
          variant="contained"
          startIcon={<SaveIcon fontSize="small" />}
        >
          Lưu cấu hình
        </Button>
      </DialogActions>
    </Dialog>
  );
}
