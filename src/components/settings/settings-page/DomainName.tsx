import { Edit, Language, ErrorOutline } from "@mui/icons-material";
import {
  Box,
  IconButton,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  TextField,
  InputAdornment,
  Button,
  Tooltip,
} from "@mui/material";
import { useCallback, useEffect, useState } from "react";
import { useRouter } from "next/router";
import TitleDialog from "@/src/components/dialog/TitleDialog";
import { paths } from "@/src/paths";
import { useDomainName } from "@/src/api/hooks/domain-name/use-domain-name";
import { useStoreId } from "@/src/hooks/use-store-id";
import DeleteOutlineOutlinedIcon from "@mui/icons-material/DeleteOutlineOutlined";
import useSnackbar from "@/src/hooks/use-snackbar";
import * as yup from "yup";
import debounce from "lodash/debounce";
import dayjs from "dayjs";
import { ApiConfig } from "@/src/api/config/api-client";
import { useAllPermissions } from "@/src/api/hooks/auth/use-all-permissions";
import { usePathname } from "next/navigation";
import { PERMISSION_TYPE_ENUM } from "@/src/constants/constant";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import Link from "next/link";

const getDomainWithoutSub = (domain) => {
  const domainParts = domain.split(".");
  return domainParts.slice(-2).join(".");
};

const MainDomain = ({ domainName }) => (
  <Paper sx={{ p: 3, mb: 4, boxShadow: 3 }}>
    <Typography variant="subtitle1" sx={{ mb: 2 }}>
      Tên miền chính
    </Typography>
    <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
      <Language sx={{ mr: 2 }} />
      <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
        <Typography>{domainName}</Typography>
        <Typography
          variant="body2"
          sx={{
            color: "success.main",
            backgroundColor: "grey.300",
            px: 1,
            py: 0.5,
            borderRadius: 1,
          }}
        >
          Đã kết nối
        </Typography>
      </Box>
    </Box>
    <Typography variant="body2" color="text.secondary" sx={{ ml: 2 }}>
      Ngày 24 tháng 11 năm 2024 Thêm vào
    </Typography>
  </Paper>
);

const DomainList = ({ domainNameList, onEdit, onClickCreateDomain, onDelete, isGranted }) => {
  const router = useRouter();
  const pathname = usePathname();

  const handleLinkClick = () => {
    router.push(paths.settings.ownDomainName);
  };

  return (
    <Paper sx={{ p: 3, boxShadow: 3 }}>
      <Box sx={{ mb: 2, display: "flex", justifyContent: "space-between", alignItems: "center" }}>
        <Typography variant="subtitle1">Danh sách tên miền</Typography>
        {/* <Link href="#" underline="none" onClick={handleLinkClick}>
          Liên kết tên miền hiện có
        </Link> */}
        {domainNameList && domainNameList.length == 0 ? (
          <Tooltip
            title={!isGranted(pathname, PERMISSION_TYPE_ENUM.Add) ? "Bạn không có quyền thêm" : ""}
          >
            <span>
              <Button
                variant="contained"
                color="primary"
                onClick={onClickCreateDomain}
                disabled={!isGranted(pathname, PERMISSION_TYPE_ENUM.Add)}
              >
                Thêm tên miền
              </Button>
            </span>
          </Tooltip>
        ) : null}
      </Box>
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Tên miền</TableCell>
              <TableCell>Trạng thái</TableCell>
              <TableCell>Ngày</TableCell>
              <TableCell align="right">Hoạt động</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {domainNameList.length > 0 && domainNameList ? (
              domainNameList.map((d) => (
                <TableRow key={d.domainNameId}>
                  <TableCell
                    sx={{
                      "&:hover": {
                        color: "primary.main",
                      },
                    }}
                  >
                    <Link
                      href={d?.domain?.startsWith("http") ? d.domain : `https://${d.domain}`}
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      {d.domain}
                    </Link>
                  </TableCell>
                  <TableCell>
                    <Typography
                      variant="body2"
                      sx={{
                        color: "success.main",
                        backgroundColor: "grey.300",
                        px: 1,
                        py: 0.5,
                        borderRadius: 1,
                        display: "inline-block",
                      }}
                    >
                      {d.status == "Connected" ? "Đã kết nối" : "Chưa kết nối"}
                    </Typography>
                  </TableCell>
                  <TableCell>{dayjs(d.created).format("DD/MM/YYYY")}</TableCell>
                  <TableCell align="right">
                    <Tooltip
                      title={
                        !isGranted(pathname, PERMISSION_TYPE_ENUM.Edit)
                          ? "Bạn không có quyền sửa"
                          : "Sửa tên miền"
                      }
                    >
                      <span>
                        <IconButton
                          color="primary"
                          size="small"
                          onClick={() => {
                            onEdit(d);
                          }}
                          disabled={!isGranted(pathname, PERMISSION_TYPE_ENUM.Edit)}
                        >
                          <EditIcon fontSize="small" />
                        </IconButton>
                      </span>
                    </Tooltip>
                    <Tooltip
                      title={
                        !isGranted(pathname, PERMISSION_TYPE_ENUM.Delete)
                          ? "Bạn không có quyền xoá"
                          : "Xoá tên miền"
                      }
                    >
                      <span>
                        <IconButton
                          size="small"
                          onClick={() => {
                            onDelete(d);
                          }}
                          color="error"
                          disabled={!isGranted(pathname, PERMISSION_TYPE_ENUM.Delete)}
                        >
                          <DeleteOutlineOutlinedIcon />
                        </IconButton>
                      </span>
                    </Tooltip>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={4}>Chưa có dữ liệu</TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>
    </Paper>
  );
};

const initPopupData = {
  action: "",
  title: "",

  domain: { domainNameId: "" },
};

interface domainDataType {
  shopId?: string;
  domain?: string;
  domainNameId?: string;
}

const PopupContent = ({ inputDomainName, handleInputChange, error, successMessage }) => {
  return (
    <Box>
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          p: 2,
          borderRadius: 1,
          mb: 2,
        }}
      >
        <ErrorOutline sx={{ color: "red", mr: 1 }} />
        <Typography variant="body2" sx={{ color: "red" }}>
          Tên miền chỉ sửa đổi một lần, vui lòng sửa đổi một cách thận trọng
        </Typography>
      </Box>
      <Typography variant="body2" sx={{ display: "flex", fontSize: "16px", fontWeight: 500 }}>
        Tên miền <Typography sx={{ color: "red", ml: 0.5 }}>*</Typography>
      </Typography>
      <TextField
        autoFocus
        margin="dense"
        id="domain-name"
        type="text"
        variant="outlined"
        fullWidth
        value={inputDomainName}
        onChange={handleInputChange}
        error={!!error} // Hiển thị trạng thái lỗi
        helperText={error || successMessage} // Hiển thị thông báo lỗi
        // InputProps={{
        //   endAdornment: (
        //     <InputAdornment position="end">
        //       .{getDomainWithoutSub(ApiConfig.baseURL)}
        //     </InputAdornment>
        //   ),
        // }}
      />
    </Box>
  );
};

export default function DomainManagement() {
  const [currentDomainName, setCurrentDomainName] = useState(null);
  const [open, setOpen] = useState(false);
  const [inputDomainName, setInputDomainName] = useState("");
  const [popupData, setPopupData] = useState(initPopupData);
  const [domainNameList, setDomainNameList] = useState([]);
  const snackbar = useSnackbar();
  const router = useRouter();
  const {
    createDomainName,
    listShopDomainName,
    updateDomainName,
    deleteDomainNames,
    checkDomainNameExist,
  } = useDomainName();
  const storeId = useStoreId();
  const [error, setError] = useState(""); // Thêm trạng thái lỗi
  const [successMessage, setSuccessMessage] = useState(""); // Success message state
  const { permissions } = useAllPermissions();
  const isGranted = (url, permission) => {
    if (url.endsWith("/")) {
      url = url.slice(0, -1);
    }
    const matchingUrl = Object.keys(permissions).find((key) => key === url);
    if (!matchingUrl) return false;
    return permissions[matchingUrl]?.includes(permission) || false;
  };
  // Validation schema
  const domainValidationSchema = yup
    .string()
    .required("Tên miền không được để trống")
    .min(3, "Tên miền phải có ít nhất 3 ký tự")
    .max(63, "Tên miền không được vượt quá 63 ký tự")
    .matches(/^(?:[a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}$/, "Tên miền không hợp lệ");

  const validateInput = async (value) => {
    try {
      await domainValidationSchema.validate(value);
      setError(""); // Xóa lỗi nếu hợp lệ
      return true;
    } catch (err) {
      setError(err.message); // Đặt lỗi
      return false;
    }
  };

  const validateDomain = async (value) => {
    try {
      const response = await checkDomainNameExist(value);
      if (response && response.data && response.data.isDomainExist) {
        setError("Tên miền đã tồn tại");
        setSuccessMessage(""); // Clear success message
      } else {
        setSuccessMessage("Tên miền hợp lệ");
        setError("");
      }
    } catch (err) {
      setError(err.message); // Đặt lỗi
      return false;
    }
  };
  const handleEditClick = (d) => {
    setPopupData({
      action: "edit",
      title: "Sửa tên miền",
      domain: d,
    });
    setInputDomainName(d.domain);
    setOpen(true);
  };

  const handleClose = () => {
    setPopupData((prevData) => ({
      ...prevData, // Keep the previous values of action and title
      domain: null, // Reset only the domain to null
    }));
    setOpen(false);
    setCurrentDomainName(null);
    setError(""); // Reset the validation error
    setSuccessMessage("");
  };

  const handleSubmit = async () => {
    if (popupData.action === "delete") {
      const domainNameIds = [currentDomainName.domainNameId];
      const response = await deleteDomainNames(domainNameIds);
      if (response && response.data) snackbar.success("Xóa tên miền thành công");
      fetchDomainNames(storeId);
      handleClose();
      return;
    }
    // Kiểm tra tính hợp lệ trước khi thực hiện submit
    // const isValid = await validateInput(inputDomainName);
    // if (!isValid) return;
    if (error) return;

    const domainData: domainDataType = {
      shopId: storeId,
      domain: inputDomainName,
      ...(popupData.domain?.domainNameId && { domainNameId: popupData.domain.domainNameId }), // Conditionally add domainNameId
    };

    if (popupData.domain?.domainNameId) {
      const response = await updateDomainName(domainData);
      if (response && response.data) snackbar.success("Cập nhật tên miền thành công");
    } else {
      const response = await createDomainName(domainData);
      if (response && response.data) snackbar.success("Tạo tên miền thành công");
    }
    fetchDomainNames(storeId);

    setOpen(false);
  };

  // Debounced validation function
  const debouncedValidation = useCallback(
    debounce(async (value) => {
      await validateDomain(value);
    }, 300), // 300ms debounce delay
    []
  );

  const handleInputChange = async (event) => {
    const value = event.target.value;
    setInputDomainName(value);
    const isValid = await validateInput(value);
    // Kiểm tra validation ngay khi nhập
    if (isValid) debouncedValidation(value);
  };

  const handleClickCreateDomain = () => {
    setPopupData({
      action: "create",
      title: "Thêm tên miền",
      domain: null,
    });
    setInputDomainName("");
    setOpen(true);
  };

  const handleClickDeleteDomainName = async (domainName) => {
    setPopupData({
      action: "delete",
      title: `Xóa tên miền ${domainName.domain}`,
      domain: null,
    });
    setInputDomainName("");
    setCurrentDomainName(domainName);
    setOpen(true);
  };

  const fetchDomainNames = async (shopId) => {
    try {
      const response = await listShopDomainName(storeId);
      if (response && response.data) {
        setDomainNameList(response.data.data);
      }
    } catch (error) {
      console.error("Error fetching domain names:", error);
    }
  };

  useEffect(() => {
    if (storeId) {
      fetchDomainNames(storeId);
    }
  }, [storeId]);

  return (
    <Box sx={{ maxWidth: 1200, margin: "0 auto", p: 3 }}>
      {/* <MainDomain domainName={domainName} /> */}
      <DomainList
        domainNameList={domainNameList}
        onEdit={handleEditClick}
        onClickCreateDomain={handleClickCreateDomain}
        onDelete={handleClickDeleteDomainName}
        isGranted={isGranted}
      />

      <TitleDialog
        open={open}
        handleClose={handleClose}
        handleSubmit={handleSubmit}
        title={popupData.title}
        closeBtnTitle="Hủy bỏ"
        submitBtnTitle="Xác nhận"
      >
        {popupData.action == "delete" ? (
          <Typography>Bạn có chắc muốn xóa tên miền {currentDomainName?.domain}</Typography>
        ) : (
          <PopupContent
            inputDomainName={inputDomainName}
            handleInputChange={handleInputChange}
            error={error}
            successMessage={successMessage}
          />
        )}
      </TitleDialog>
    </Box>
  );
}
