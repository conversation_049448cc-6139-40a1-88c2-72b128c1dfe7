import React, { useEffect, useState } from "react";
import {
  DialogContent,
  <PERSON>alogActions,
  Button,
  TextField,
  Box,
  Typography,
  InputAdornment,
  IconButton,
  Dialog,
  DialogTitle,
} from "@mui/material";
import { useForm } from "react-hook-form";
import * as Yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import TitleDialog from "../../dialog/TitleDialog";
import { TransportMethod } from "@/src/api/types/transport-method.types";
import { Visibility, VisibilityOff } from "@mui/icons-material";

export type ShippingProviderFormData = {
  phoneNumber?: string;
  apiAccount?: string;
  customerCode?: string;
  privateKey?: string;
  password?: string;
};

type EditShippingProviderDialogProps = {
  open: boolean;
  onClose: () => void;
  onSubmit: (data: ShippingProviderFormData) => void;
  provider: TransportMethod;
};

export default function EditShippingProviderDialog({
  open,
  onClose,
  onSubmit,
  provider,
}: EditShippingProviderDialogProps) {
  // Tạo dynamic schema theo provider
  const getSchema = () => {
    switch (provider?.transportCode) {
      case "Ahamove":
        return Yup.object().shape({
          phoneNumber: Yup.string().required("Số điện thoại là bắt buộc"),
        });
      case "JTExpress":
        return Yup.object().shape({
          customerCode: Yup.string().required("Customer Code là bắt buộc"),
          password: Yup.string().required("Password là bắt buộc"),
        });
      default:
        return Yup.object().shape({});
    }
  };

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    reset,
  } = useForm<ShippingProviderFormData>({
    resolver: yupResolver(getSchema()),
  });

  const [showPassword, setShowPassword] = useState(false);

  useEffect(() => {
    if (!provider) return;

    switch (provider.transportCode) {
      case "Ahamove":
        reset({
          phoneNumber: provider?.transportConfig?.ahamove?.phoneNumber || "",
        });
        break;
      case "JTExpress":
        reset({
          customerCode: provider?.transportConfig?.jtExpress?.customerCode || "",
          password: provider?.transportConfig?.jtExpress?.password || "",
        });
        break;
      default:
        reset({});
        break;
    }
  }, [provider, reset]);

  const handleClose = () => {
    onClose();
    setShowPassword(false);
  };

  const renderFields = () => {
    switch (provider?.transportCode) {
      case "Ahamove":
        return (
          <Box sx={{ mb: 3 }}>
            <Typography variant="subtitle1" gutterBottom sx={{ display: "flex" }}>
              Số điện thoại đăng ký Ahamove{" "}
              <Typography sx={{ color: "red", ml: 0.5 }}>*</Typography>
            </Typography>
            <TextField
              {...register("phoneNumber")}
              fullWidth
              error={!!errors.phoneNumber}
              helperText={errors.phoneNumber?.message}
            />
          </Box>
        );
      case "JTExpress":
        return (
          <>
            {/* <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle1">API Account</Typography>
              <TextField
                {...register("apiAccount")}
                fullWidth
                error={!!errors.apiAccount}
                helperText={errors.apiAccount?.message}
              />
            </Box> */}
            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle1" sx={{ display: "flex", mb: 0.5 }}>
                Customer Code <Typography sx={{ color: "red", ml: 0.5 }}>*</Typography>
              </Typography>
              <TextField
                {...register("customerCode")}
                fullWidth
                error={!!errors.customerCode}
                helperText={errors.customerCode?.message}
              />
            </Box>
            {/* <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle1">Private Key</Typography>
              <TextField
                {...register("privateKey")}
                type={showPrivateKey ? "text" : "password"}
                fullWidth
                error={!!errors.privateKey}
                helperText={errors.privateKey?.message}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton onClick={() => setShowPrivateKey((prev) => !prev)} edge="end">
                        {showPrivateKey ? <VisibilityOff /> : <Visibility />}
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
              />
            </Box> */}
            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle1" sx={{ display: "flex", mb: 0.5 }}>
                Password <Typography sx={{ color: "red", ml: 0.5 }}>*</Typography>
              </Typography>
              <TextField
                {...register("password")}
                type={showPassword ? "text" : "password"}
                fullWidth
                error={!!errors.password}
                helperText={errors.password?.message}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton onClick={() => setShowPassword((prev) => !prev)} edge="end">
                        {showPassword ? <VisibilityOff /> : <Visibility />}
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
              />
            </Box>
          </>
        );
      default:
        return <Typography>Không có cấu hình phù hợp.</Typography>;
    }
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      fullWidth
      maxWidth="lg"
      sx={{ "& .MuiDialog-paper": { width: "60%" } }}
    >
      <DialogTitle>{`Cấu hình dịch vụ ${provider?.transportName}`}</DialogTitle>
      <form
        onSubmit={handleSubmit(
          (data) => {
            onSubmit(data); // Gửi dữ liệu ra ngoài
            setShowPassword(false);
          },
          (errors) => {
            console.log("🚨 Validate Errors:", errors);
          }
        )}
      >
        <DialogContent dividers>{renderFields()}</DialogContent>
        <DialogActions>
          <Box display="flex" alignItems="flex-end" gap={2}>
            <Button onClick={handleClose} variant="outlined">
              Hủy
            </Button>
            <Button type="submit" variant="contained">
              Lưu
            </Button>
          </Box>
        </DialogActions>
      </form>
    </Dialog>
  );
}
