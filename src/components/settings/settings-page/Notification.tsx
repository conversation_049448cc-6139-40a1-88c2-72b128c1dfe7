import { Box, Card, Checkbox, Typography, Grid } from '@mui/material';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import { useRouter } from 'next/router';
import { paths } from '@/src/paths';

const notificationTitles = [
  {
    key: 'payment',
    title: 'Thông báo thanh toán thành công',
    message: '<PERSON>ảm ơn [Tên khách hàng] đã thanh toán thành công tại [Tên cửa hàng]! Mã đơn hàng: [Mã đơn hàng], số tiền: [Số tiền]. Chi tiết tại: [<PERSON> chi tiết]',
    params: ['[Hotline]', '[<PERSON><PERSON><PERSON><PERSON> thức thanh toán]', '[Số điểm]', '[Thời gian thanh toán]', '[Mã giảm giá]']
  },
  {
    key: 'shipping',
    title: 'Thông báo đang giao hàng',
    message: '<PERSON><PERSON><PERSON> hàng [<PERSON><PERSON> đơn hàng] của bạn đang được giao! Vui lòng giữ điện thoại để nhận hàng. Xem trạng thái: [Link theo dõi].',
    params: ['[Tên khách hàng]', '[Tên shipper]', '[Số điện thoại shipper]', '[Thời gian dự kiến]', '[Tên cửa hàng]']
  },
  {
    key: 'received',
    title: 'Thông báo nhận hàng',
    message: 'Đơn hàng [Mã đơn hàng] của bạn đã được giao thành công! Cảm ơn bạn đã mua sắm tại [Tên cửa hàng]',
    params: ['[Tên khách hàng]', '[Tên shipper]', '[Thời gian giao hàng]', '[Link đánh giá]']
  },
  {
    key: 'orderSuccess',
    title: 'Xác nhận đặt hàng thành công',
    message: 'Cảm ơn [Tên khách hàng] đã đặt hàng tại [Tên cửa hàng]! 🎉 Đơn hàng của bạn [Mã đơn hàng] đã được xác nhận. Xem chi tiết đơn hàng tại: [Link theo dõi].',
    params: ['[Tổng giá trị đơn hàng]', '[Thời gian giao hàng]', '[Hotline]']
  },
  {
    key: 'appointmentSuccess',
    title: 'Xác nhận đặt lịch thành công',
    message: 'Chúc mừng [Tên khách hàng]! Lịch hẹn của bạn tại [Tên cửa hàng] vào [Thời gian] đã được xác nhận. Cảm ơn bạn đã tin tưởng và đặt lịch với chúng tôi!',
    params: ['[Mã đặt lịch]', '[Link chi tiết]', '[Hotline]']
  },
  {
    key: 'cartReminder',
    title: 'Nhắc nhở giỏ hàng chưa thanh toán',
    message: 'Chào [Tên khách hàng], chúng tôi dành tặng bạn mã giảm giá [Mã ưu đãi] để hoàn tất đơn hàng trong giỏ. Mua ngay tại: [Link giỏ hàng].',
    params: ['[Tên cửa hàng]', '[Thời gian ưu đãi còn lại]', '[Giá trị giỏ hàng]']
  },
  {
    key: 'orderReminder',
    title: 'Nhắc nhở đơn hàng chưa thanh toán',
    message: 'Xin chào [Tên khách hàng], đơn hàng [Mã đơn hàng] của bạn tại [Tên cửa hàng] vẫn chưa được thanh toán. Hoàn tất thanh toán ngay để chúng tôi xử lý đơn hàng sớm nhất!',
    params: ['[Link thanh toán]', '[Giá trị đơn hàng]', '[Hạn thanh toán]', '[Phương thức thanh toán]']
  },
  {
    key: 'orderCancel',
    title: 'Xác nhận huỷ đơn hàng',
    message: 'Đơn hàng [Mã đơn hàng] của bạn tại [Tên cửa hàng] đã được hủy thành công. Nếu cần hỗ trợ thêm, vui lòng liên hệ [Hotline]. Cảm ơn bạn đã quan tâm!',
    params: ['[Liên kết đặt lại hàng]']
  },
  {
    key: 'appointmentCancel',
    title: 'Xác nhận huỷ lịch',
    message: 'Đơn hàng [Mã đặt lịch ] của bạn tại [Tên cửa hàng] đã được hủy thành công. Nếu cần hỗ trợ thêm, vui lòng liên hệ [Hotline]. Cảm ơn bạn đã quan tâm!',
    params: ['[Liên kết đặt lại lịch]']
  }
];

const cardStyles = {
  padding: 2,
  bgcolor: '#f5f5f5',
  borderRadius: 1,
  boxShadow: '0px 4px 12px rgba(0, 0, 0, 0.1)',
  cursor: 'pointer',
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'space-between',
  height: '100%',
  '&:hover': {
    boxShadow: '0px 6px 16px rgba(0, 0, 0, 0.2)'
  },
  minHeight: '150px'
};

const checkboxStyles = {
  color: '#e0e0e0',
  '&.Mui-checked': {
    color: '#4caf50'
  },
  '& .MuiSvgIcon-root': {
    fontSize: 20
  },
  padding: 0,
  marginLeft: 1
};

const NotificationCard = ({ title, message, params }) => {
  const router = useRouter();

  const handleClick = () => {
    router.push({
      pathname: paths.settings.management,
      query: { title, message, params: JSON.stringify(params) }
    });
  };

  return (
    <Card sx={cardStyles} onClick={handleClick}>
      <Typography variant="subtitle1" sx={{ marginBottom: 2, fontWeight: 500 }}>
        {title}
      </Typography>
      {['Thông báo hệ thống', 'Zalo'].map((label, i) => (
        <Box
          key={i}
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            marginBottom: i === 0 ? 1 : 0,
            width: '100%'
          }}
        >
          <Typography variant="body2">{label}</Typography>
          <Checkbox checked checkedIcon={<CheckCircleIcon />} sx={checkboxStyles} />
        </Box>
      ))}
    </Card>
  );
};

const AddNewLinkCard = () => {
  const router = useRouter();

  const handleClick = () => {
    router.push(paths.settings.managementAdd);
  };

  return (
    <Card sx={cardStyles} onClick={handleClick}>
      <Typography variant="subtitle1" sx={{ mt: 6, fontWeight: 500, textAlign: 'center', color: 'primary.main' }}>
        Thêm thông báo Zalo mới
      </Typography>
    </Card>
  );
};

export default function NotificationSettings() {
  return (
    <Box sx={{ padding: 3 }}>
      <Grid container spacing={2}>
        {notificationTitles.map((notification, index) => (
          <Grid item xs={12} md={4} key={index}>
            <NotificationCard {...notification} />
          </Grid>
        ))}
        <Grid item xs={12} md={4}>
          <AddNewLinkCard />
        </Grid>
      </Grid>
    </Box>
  );
}
