import React, { useState } from 'react';
import { Box, Card, Typography, Checkbox, TextField, Chip, Button } from '@mui/material';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import { useForm, FormProvider } from 'react-hook-form';
import FormDialog from '../../dialog/FormDialog';
import { useRouter } from 'next/router';
import { paths } from '@/src/paths';
import Grid from '@mui/system/Grid';

const notifications = [
  {
    key: 'welcomeGuide',
    title: 'Chào mừng và hướng dẫn',
    message: '🎉 Chào mừng [Tên đối tác] đã trở thành đối tác Affiliate của [Tên thương hiệu]! Bắt đầu ngay bằng cách truy cập tài khoản của bạn tại [Link quản lý]. Nếu cần hỗ trợ, liên hệ [Hotline/Email]. Chúng tôi luôn sẵn sàng đồng hành cùng bạn! 🔔 Hướng dẫn sử dụng hệ thống Affiliate dành riêng cho bạn! Truy cập: [Link video/tài liệu hướng dẫn]. Đừng ngần ngại liên hệ đội ngũ CSKH nếu bạn gặp bất kỳ khó khăn nào. Cùng nhau tạo nên thành công!',
    params: ['[Số đơn hàng]', '[Số đơn hàng]', '[Số đơn hàng]']
  },
  {
    key: 'weeklyRevenue',
    title: 'Thông báo doanh thu định kỳ tuần',
    message: '📊 Báo cáo doanh thu tuần/tháng của bạn đã sẵn sàng! Tổng doanh thu: [X triệu VNĐ] Tổng hoa hồng: [X triệu VNĐ] Xem chi tiết tại: [Link dashboard]. Cảm ơn bạn đã nỗ lực cùng chúng tôi!.',
    params: ['[Số đơn hàng]', '[Số đơn hàng]', '[Số đơn hàng]']
  },
  {
    key: 'monthlyRevenue',
    title: 'Thông báo doanh thu định kỳ tháng',
    message: '📊 Báo cáo doanh thu tuần/tháng của bạn đã sẵn sàng! Tổng doanh thu: [X triệu VNĐ] Tổng hoa hồng: [X triệu VNĐ] Xem chi tiết tại: [Link dashboard]. Cảm ơn bạn đã nỗ lực cùng chúng tôi!',
    params: ['[Số đơn hàng]', '[Số đơn hàng]', '[Số đơn hàng]']
  },
  {
    key: 'newOrder',
    title: 'Cộng tiền đơn hàng mới',
    message: '🛒 Chúc mừng! Bạn vừa có đơn hàng mới qua đường dẫn Affiliate của mình: - Sản phẩm: [Tên sản phẩm] - Giá trị đơn hàng: [X triệu VNĐ] Tiếp tục chia sẻ và gia tăng doanh thu nhé!',
    params: ['[Số đơn hàng]', '[Số đơn hàng]']
  },
  {
    key: 'commissionPayment',
    title: 'Thanh toán hoa hồng',
    message: '💰 Hoa hồng kỳ [Ngày/tháng] của bạn đã được chuyển vào tài khoản: [Thông tin tài khoản]. Số tiền thanh toán: [X triệu VNĐ]. Cảm ơn bạn đã hợp tác và phát triển cùng [Tên thương hiệu]!',
    params: ['[Số đơn hàng]', '[Số đơn hàng]']
  },
  {
    key: 'activityReminder',
    title: 'Nhắc nhở hoạt động',
    message: '📣 [Tên đối tác] ơi, đừng bỏ lỡ cơ hội tăng thu nhập từ Affiliate! - Bạn chưa tạo link bán hàng nào trong [X ngày]. - Xem sản phẩm hot và bắt đầu ngay: [Link sản phẩm]. Cùng nhau tạo nên thành công nhé!',
    params: ['[Số đơn hàng]']
  },
  {
    key: 'periodicEncouragement',
    title: 'Động viên định kỳ',
    message: '🔥 Đừng nản lòng, [Tên đối tác]! Chúng tôi tin bạn sẽ đạt được kết quả tốt hơn. - Xem các mẹo gia tăng doanh số: [Link tài liệu]. - Cập nhật các sản phẩm hot: [Link sản phẩm]. Đội ngũ [Tên thương hiệu] luôn sẵn sàng hỗ trợ bạn!',
    params: ['[Số đơn hàng]']
  },
  {
    key: 'gratitudeGift',
    title: 'Tri ân/Tặng quà',
    message: 'Chúc mừng năm mới/Merry Christmas! 🎉 [Tên cửa hàng] gửi lời cảm ơn chân thành và chúc bạn một năm thành công và hạnh phúc. Đừng quên tham gia ưu đãi mừng năm mới tại: [Link chương trình].',
    params: ['[Số đơn hàng]']
  },
  {
    key: 'eventNotification',
    title: 'Thông báo sự kiện',
    message: '🎉 Đừng bỏ lỡ cơ hội tăng thu nhập! Chương trình [Tên chương trình ưu đãi] dành riêng cho đối tác Affiliate đã bắt đầu: - Thời gian: [Ngày bắt đầu - Kết thúc] - Hoa hồng tăng thêm: [X%] Tham gia ngay tại: [Link chi tiết].',
    params: ['[Tên sự kiện]', '[Thời gian]', '[Link]']
  },
  {
    key: 'newPolicyProgram',
    title: 'Thông báo chương trình chính sách mới',
    message: '🚀 Sản phẩm mới đã sẵn sàng trên hệ thống Affiliate: [Tên sản phẩm]. - Hoa hồng: [X%] - Link quảng bá: [Link sản phẩm]. Hãy chia sẻ ngay để tăng cơ hội bán hàng!',
    params: ['[Số đơn hàng]']
  }
];

const cardStyles = {
  padding: 2,
  bgcolor: '#f5f5f5',
  borderRadius: 1,
  boxShadow: '0px 4px 12px rgba(0, 0, 0, 0.1)',
  cursor: 'pointer',
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'space-between',
  height: '100%',
  '&:hover': {
    boxShadow: '0px 6px 16px rgba(0, 0, 0, 0.2)'
  },
  minHeight: '150px'
};

const checkboxStyles = {
  color: '#e0e0e0',
  '&.Mui-checked': {
    color: '#4caf50'
  },
  '& .MuiSvgIcon-root': {
    fontSize: 20
  },
  padding: 0,
  marginLeft: 1
};

const NotificationCard = ({ title, message, params, onClick }) => {
  const router = useRouter();

  const handleClick = () => {
    router.push({
      pathname: paths.settings.management,
      query: { title, message, params: JSON.stringify(params) }
    });
  };

  return (
    <Card sx={cardStyles} onClick={onClick || handleClick}>
      <Typography variant="subtitle1" sx={{ marginBottom: 2, fontWeight: 500 }}>
        {title}
      </Typography>
      {['Thông báo hệ thống', 'Zalo'].map((label, i) => (
        <Box
          key={i}
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            marginBottom: i === 0 ? 1 : 0,
            width: '100%'
          }}
        >
          <Typography variant="body2">{label}</Typography>
          <Checkbox checked checkedIcon={<CheckCircleIcon />} sx={checkboxStyles} />
        </Box>
      ))}
    </Card>
  );
};

const AddNewLinkCard = () => {
  const router = useRouter();

  const handleClick = () => {
    router.push(paths.settings.managementAdd);
  };

  return (
    <Card sx={cardStyles} onClick={handleClick}>
      <Typography variant="subtitle1" sx={{ mt: 6, fontWeight: 500, textAlign: 'center', color: 'primary.main' }}>
        Thêm thông báo Zalo mới
      </Typography>
    </Card>
  );
};

export default function NotificationPartner() {
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedNotification, setSelectedNotification] = useState('');
  const methods = useForm();
  const router = useRouter();

  const handleOpenDialog = (title) => {
    if (title === 'Thêm thông báo Zalo mới') {
      router.push(paths.settings.managementAdd);
    } else {
      setSelectedNotification(title);
      setOpenDialog(true);
    }
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedNotification('');
  };

  const dialogContent = notifications.find(notification => notification.title === selectedNotification) || {
    title: '',
    message: '',
    params: []
  };

  return (
    <Box sx={{ p: 3 }}>
      <Grid container spacing={2}>
        {notifications.map((notification, index) => (
          <Grid size={{ xs: 12, md: 4 }} key={index}>
            <NotificationCard
              title={notification.title}
              message={notification.message}
              params={notification.params}
              onClick={() => handleOpenDialog(notification.title)}
            />
          </Grid>
        ))}
        <Grid size={{ xs: 12, md: 4 }}>
          <AddNewLinkCard />
        </Grid>
      </Grid>
      <FormProvider {...methods}>
        <FormDialog
          open={openDialog}
          onClose={handleCloseDialog}
          title={dialogContent.title}
          onSubmit={() => { }}
          submitBtnTitle="Xác nhận"
        >
          <Box sx={{ p: 2 }}>
            <TextField
              multiline
              rows={4}
              fullWidth
              value={dialogContent.message}
              InputProps={{
                readOnly: true
              }}
              sx={{ mb: 3 }}
            />
            {dialogContent.params.length > 0 && (
              <>
                <Typography variant="h6" sx={{ mb: 2 }}>
                  Tham số:
                </Typography>
                <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                  {dialogContent.params.map((param, index) => (
                    <Chip key={index} label={param} />
                  ))}
                </Box>
              </>
            )}
          </Box>
        </FormDialog>
      </FormProvider>
    </Box>
  );
}