// app/dashboard/settings/payment/page.tsx
"use client";
import { useEffect, useState } from "react";
import { useRouter, usePathname } from "next/navigation";
import { useStoreId } from "@/src/hooks/use-store-id";
import useSnackbar from "@/src/hooks/use-snackbar";
import { paymentService, DataActiveStatePayment } from "@/src/api/services/payment/payment.service";
import { paths } from "@/src/paths";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { addPaymentMethodSchema } from "@/src/utils/validations/validationSchema";
import {
  Box,
  Card,
  Divider,
  Typography,
  Switch,
  Button,
  Stack,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  MenuItem,
  Select,
  Tooltip,
} from "@mui/material";
import Grid from "@mui/material/Grid2";
import DragIndicatorOutlinedIcon from "@mui/icons-material/DragIndicatorOutlined";
import PaymentsOutlinedIcon from "@mui/icons-material/PaymentsOutlined";
import Link from "next/link";
import { IPayment } from "@/src/types/payment/payment";
import { PERMISSION_TYPE_ENUM } from "@/src/constants/constant";
import { useAllPermissions } from "@/src/api/hooks/auth/use-all-permissions";

interface PaymentMethodType {
  paymentMethodName: string;
  paymentMethodDetail: string;
  paymentMethodInstruction: string;
  image: string;
  id: number | null;
  isActive: boolean;
}

const LeftColumn = ({ children }: { children: React.ReactNode }) => (
  <Grid size={{ xs: 12, md: 4 }}>
    <Box p={2}>{children}</Box>
  </Grid>
);

const RightColumn = ({ children }: { children: React.ReactNode }) => (
  <Grid size={{ xs: 12, md: 8 }}>
    <Box>{children}</Box>
  </Grid>
);

const defaultValues: PaymentMethodType = {
  paymentMethodName: "",
  paymentMethodDetail: "",
  paymentMethodInstruction: "",
  image: "",
  id: null,
  isActive: true,
};

const typePaymentOptions = [
  { value: "COD", label: "Thanh toán khi nhận hàng (COD)" },
  { value: "Vnpay", label: "VNPAY" },
  { value: "Transfer", label: "Chuyển khoản" },
  { value: "Cash", label: "Tiền mặt" },
];

export default function Payment() {
  const [checkedCod, setCheckedCod] = useState(false);
  const [isActivePayment, setIsActivePayment] = useState(false);
  const snackbar = useSnackbar();
  const router = useRouter();
  const pathname = usePathname();
  const storeId = useStoreId();

  const [paymentMethods, setPaymentMethods] = useState<PaymentMethodType[]>([]);
  const [currentPaymentMethod, setCurrentPaymentMethod] = useState<PaymentMethodType | null>(null);
  const [listPayment, setListPayment] = useState<IPayment[]>([]);
  const [listClonePayment, setListClonePayment] = useState<IPayment[]>([]);
  const [dialogs, setDialogs] = useState({ addPaymentMethod: false });
  const [openAddDialog, setOpenAddDialog] = useState(false);
  const [selectedTypePayment, setSelectedTypePayment] = useState("");
  const [deleteDialog, setDeleteDialog] = useState<{ open: boolean; paymentId?: string }>({
    open: false,
  });

  const { permissions } = useAllPermissions();
  const isGranted = (url, permission) => {
    if (url.endsWith("/")) {
      url = url.slice(0, -1);
    }
    const matchingUrl = Object.keys(permissions).find((key) => key === url);
    if (!matchingUrl) return false;
    return permissions[matchingUrl]?.includes(permission) || false;
  };

  const fetchPayment = async () => {
    const res = await paymentService.getListPayment(storeId, { skip: 0, limit: 999 });
    setListPayment(res?.data?.data || []);
    setListClonePayment(res?.data?.data || []);
  };

  useEffect(() => {
    fetchPayment();
  }, [storeId]);

  const methods = useForm({ defaultValues, resolver: yupResolver(addPaymentMethodSchema) });
  const { handleSubmit, reset } = methods;

  const onSubmitAddPaymentMethod = (data: PaymentMethodType) => {
    if (currentPaymentMethod?.id) {
      setPaymentMethods((prev) =>
        prev.map((method) =>
          method.id === currentPaymentMethod.id ? { ...data, id: currentPaymentMethod.id } : method
        )
      );
    } else {
      const newPaymentMethod = { ...data, isActive: true, id: Date.now() };
      setPaymentMethods((prev) => [...prev, newPaymentMethod]);
    }
    toggleDialog("addPaymentMethod");
  };

  const toggleDialog = (key: string) => {
    setDialogs((prev) => ({ ...prev, [key]: !prev[key] }));
  };

  const handleChangeCod = (event: React.ChangeEvent<HTMLInputElement>) => {
    setCheckedCod(event.target.checked);
  };

  const handleChangeActiveStatePayment = async (
    e: React.ChangeEvent<HTMLInputElement>,
    item: IPayment
  ) => {
    const updatedList = listPayment.map((payment) =>
      payment.paymentId === item.paymentId ? { ...payment, isActive: e.target.checked } : payment
    );
    setListPayment(updatedList);
  };

  const handleSaveStateOfPayment = async () => {
    const changedPayments = listPayment.filter((item) => {
      const originalItem = listClonePayment.find((p) => p.paymentId === item.paymentId);
      return originalItem && item.isActive !== originalItem.isActive;
    });
    if (changedPayments.length === 0) {
      snackbar.info("Không có thay đổi nào cần cập nhật");
      return;
    }
    await Promise.all(
      changedPayments.map(async (item) => {
        const data: DataActiveStatePayment = {
          isActive: item.isActive,
          paymentId: item.paymentId,
          shopId: item.shopId,
        };
        await paymentService.activeStatePayment(data);
      })
    );
    snackbar.success("Cập nhật trạng thái thành công");
    await fetchPayment();
  };

  const handleClickEditPaymentMethod = (paymentMethod: PaymentMethodType) => {
    setCurrentPaymentMethod(paymentMethod);
    toggleDialog("addPaymentMethod");
  };

  const handleActivePaymentMethod = (paymentMethod: PaymentMethodType) => {
    setPaymentMethods((prev) =>
      prev.map((method) =>
        method.id === paymentMethod.id ? { ...method, isActive: !method.isActive } : method
      )
    );
  };

  const handleDeletePaymentMethod = (paymentMethod: PaymentMethodType) => {
    setPaymentMethods((prev) => prev.filter((method) => method.id !== paymentMethod.id));
  };

  useEffect(() => {
    if (!dialogs.addPaymentMethod) {
      reset();
      setCurrentPaymentMethod(null);
    }
  }, [dialogs.addPaymentMethod]);

  return (
    <Box sx={{ flexGrow: 1 }}>
      <Grid container spacing={2}>
        <LeftColumn>
          <Box>
            <Typography fontWeight="bold" marginBottom={2}>
              Cấu hình thanh toán
            </Typography>
            <Typography variant="subtitle2" color="text.secondary">
              Bạn có thể đến trang đặt hàng và thanh toán của cửa hàng trực tuyến
            </Typography>
          </Box>
          <Box sx={{ marginTop: 6 }}>
            <Typography fontWeight="bold" marginBottom={2}>
              Nhà cung cấp dịch vụ thanh toán
            </Typography>
            <Typography variant="subtitle2" color="text.secondary">
              Thiết lập phương thức thanh toán của bên thứ ba như Zalo pay cho cửa hàng trực tuyến
              của bạn
            </Typography>
          </Box>
        </LeftColumn>

        <RightColumn>
          {listPayment?.map((item) => (
            <Card sx={{ p: 2, marginBottom: 2 }} key={item.paymentId}>
              <Box display="flex" justifyContent="space-between" alignItems="flex-start">
                <Box sx={{ cursor: "pointer" }}>
                  {isGranted(pathname, PERMISSION_TYPE_ENUM.Edit) ? (
                    <Box
                      onClick={() => {
                        router.push(`${paths.settings.payment.index}/${item?.paymentId}`);
                      }}
                      style={{ textDecoration: "none" }}
                    >
                      <Typography fontWeight="bold">{item?.name}</Typography>
                    </Box>
                  ) : (
                    <Typography fontWeight="bold">{item?.name}</Typography>
                  )}
                  <Box display="flex" gap={0.5} marginBottom={2} mt={1}>
                    <DragIndicatorOutlinedIcon color="action" />
                    <PaymentsOutlinedIcon />
                    <Typography>{item?.detail}</Typography>
                  </Box>
                </Box>
                <Box
                  display="flex"
                  flexDirection="column"
                  alignItems="flex-end"
                  gap={1}
                  sx={{ minWidth: 60 }}
                >
                  <Switch
                    checked={item?.isActive}
                    onChange={(e) => handleChangeActiveStatePayment(e, item)}
                    inputProps={{ "aria-label": "controlled" }}
                    disabled={!isGranted(pathname, PERMISSION_TYPE_ENUM.Edit)}
                  />
                  <Button
                    variant="outlined"
                    color="error"
                    size="small"
                    onClick={() => setDeleteDialog({ open: true, paymentId: item.paymentId })}
                    disabled={!isGranted(pathname, PERMISSION_TYPE_ENUM.Delete)}
                  >
                    Xoá
                  </Button>
                </Box>
              </Box>
            </Card>
          ))}
        </RightColumn>
      </Grid>

      <Dialog open={deleteDialog.open} onClose={() => setDeleteDialog({ open: false })}>
        <DialogTitle>Xác nhận xoá</DialogTitle>
        <DialogContent>
          <Typography>Bạn có chắc muốn xoá phương thức thanh toán này?</Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialog({ open: false })}>Huỷ</Button>
          <Button
            color="error"
            variant="contained"
            onClick={async () => {
              if (!deleteDialog.paymentId) return;
              try {
                await paymentService.deletePayment(deleteDialog.paymentId);
                snackbar.success("Xoá phương thức thanh toán thành công");
                setDeleteDialog({ open: false });
                await fetchPayment();
              } catch (err) {
                snackbar.error("Xoá phương thức thanh toán thất bại");
              }
            }}
            disabled={!isGranted(pathname, PERMISSION_TYPE_ENUM.Delete)}
          >
            Xoá
          </Button>
        </DialogActions>
      </Dialog>

      <Divider sx={{ marginBottom: 3, marginTop: 3 }} />
      <Stack
        direction="row"
        spacing={2}
        sx={{ alignItems: "end", justifyContent: "flex-end", margin: "0 20px" }}
      >
        <Tooltip
          title={!isGranted(pathname, PERMISSION_TYPE_ENUM.Add) ? "Bạn không có quyền thêm" : ""}
        >
          <span>
            <Button
              variant="contained"
              onClick={() => {
                setSelectedTypePayment("");
                setOpenAddDialog(true);
              }}
              sx={{
                height: 40,
                background: "#2654FE",
                "&:hover": { borderStyle: "dashed", bgcolor: "primary.lighter" },
              }}
              disabled={!isGranted(pathname, PERMISSION_TYPE_ENUM.Add)}
            >
              Thêm phương thức thanh toán
            </Button>
          </span>
        </Tooltip>
        <Tooltip
          title={!isGranted(pathname, PERMISSION_TYPE_ENUM.Edit) ? "Bạn không có quyền lưu" : ""}
        >
          <span>
            <Button
              variant="contained"
              onClick={handleSaveStateOfPayment}
              sx={{
                width: 20,
                borderStyle: "dashed",
                height: 40,
                background: "#2654FE",
                "&:hover": { borderStyle: "dashed", bgcolor: "primary.lighter" },
              }}
              disabled={!isGranted(pathname, PERMISSION_TYPE_ENUM.Edit)}
            >
              Lưu
            </Button>
          </span>
        </Tooltip>
      </Stack>

      <Dialog open={openAddDialog} onClose={() => setOpenAddDialog(false)}>
        <DialogTitle>Thêm phương thức thanh toán</DialogTitle>
        <DialogContent>
          <Select
            fullWidth
            value={selectedTypePayment}
            onChange={(e) => setSelectedTypePayment(e.target.value)}
            displayEmpty
            sx={{ mt: 2 }}
          >
            <MenuItem value="" disabled>
              Chọn phương thức thanh toán
            </MenuItem>
            {typePaymentOptions.map((opt) => (
              <MenuItem key={opt.value} value={opt.value}>
                {opt.label}
              </MenuItem>
            ))}
          </Select>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenAddDialog(false)}>Hủy</Button>
          <Button
            onClick={async () => {
              if (listPayment.some((p) => p.typePay === selectedTypePayment)) {
                snackbar.error("Đã có phương thức thanh toán này");
                return;
              }
              try {
                const isSepay = selectedTypePayment === "Sepay";
                const payload = {
                  typePay: selectedTypePayment,
                  name:
                    typePaymentOptions.find((t) => t.value === selectedTypePayment)?.label ||
                    selectedTypePayment,
                  detail: "",
                  shopId: storeId,
                  isActive: true,
                  platform: isSepay ? ["WebApp", "Pos"] : [],
                };
                const res = await paymentService.createPayment(payload);
                if (res?.data?.paymentId) {
                  snackbar.success("Thêm phương thức thanh toán thành công");
                  setOpenAddDialog(false);
                  router.push(`/dashboard/settings/payment/${res.data.paymentId}`);
                } else {
                  snackbar.error("Không thể tạo phương thức thanh toán");
                }
              } catch (err) {
                snackbar.error("Có lỗi xảy ra khi tạo phương thức thanh toán");
              }
            }}
            disabled={!isGranted(pathname, PERMISSION_TYPE_ENUM.Add) || !selectedTypePayment}
          >
            Thêm
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}
