import { Box, Button, styled, TextField, Typography } from '@mui/material';
import React, { useEffect, useState } from 'react';
import { Controller, useFormContext } from 'react-hook-form';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import { ConstructionOutlined } from '@mui/icons-material';
export type paymentMethodType = {
  id: any;
  paymentMethodName: string;
  paymentMethodDetail: string;
  paymentMethodInstruction: string;
  image: string;
  isActive: boolean;
};

const VisuallyHiddenInput = styled('input')({
  clip: 'rect(0 0 0 0)',
  clipPath: 'inset(50%)',
  height: 1,
  overflow: 'hidden',
  position: 'absolute',
  bottom: 0,
  left: 0,
  whiteSpace: 'nowrap',
  width: 1
});

export default function PaymentMethodForm({ paymentMethod }: { paymentMethod?: paymentMethodType }) {
  const {
    setValue,
    control,
    formState: { errors }
  } = useFormContext();

  // State để lưu trữ ảnh preview
  const [previewImage, setPreviewImage] = useState(null);

  // Hàm xử lý upload file
  const handleImageUpload = (event) => {
    const file = event.target.files[0];
    if (file) {
      // Lưu file vào form state
      setValue('image', file);

      // Tạo preview ảnh
      const reader = new FileReader();
      reader.onloadend = () => {
        setPreviewImage(reader.result);
      };
      reader.readAsDataURL(file);
    }
  };

  useEffect(() => {
    if (paymentMethod) {
      setValue('paymentMethodName', paymentMethod.paymentMethodName);
      setValue('paymentMethodDetail', paymentMethod.paymentMethodDetail);
      setValue('paymentMethodInstruction', paymentMethod.paymentMethodInstruction);
    }
  }, [paymentMethod]);
  return (
    <Box>
      <Box marginBottom={2}>
        <Typography gutterBottom>* Tên phương thức thanh toán</Typography>
        <Controller
          name="paymentMethodName"
          control={control}
          render={({ field }) => (
            <TextField
              {...field}
              variant="outlined"
              fullWidth
              error={!!errors.paymentMethodName}
              helperText={errors.paymentMethodName?.message as string}
            />
          )}
        />
      </Box>
      <Box marginBottom={2}>
        <Typography gutterBottom>Chi tiết khác</Typography>
        <Controller
          name="paymentMethodDetail"
          control={control}
          render={({ field }) => (
            <TextField
              {...field}
              multiline
              rows={4}
              variant="outlined"
              fullWidth
              helperText="Hiển thị cho khách hàng khi họ chọn phương thức thanh toán"
            />
          )}
        />
      </Box>
      <Box marginBottom={2}>
        <Typography gutterBottom>Hướng dẫn thanh toán</Typography>
        <Controller
          name="paymentMethodInstruction"
          control={control}
          render={({ field }) => (
            <TextField
              {...field}
              multiline
              rows={4}
              variant="outlined"
              fullWidth
              helperText="Hiển thị cho khách hàng sau khi họ đặt hàng bằng phương thức thanh toán này"
            />
          )}
        />
      </Box>
      <Box>
        <Typography gutterBottom>Ảnh QR</Typography>

        {/* Upload input */}
        <Controller
          name="image"
          control={control}
          defaultValue={null}
          render={({ field }) => (
            <Button
              component="label"
              role={undefined}
              variant="contained"
              tabIndex={-1}
              startIcon={<CloudUploadIcon />}
            >
              Tải lên ảnh QR
              <VisuallyHiddenInput
                type="file"
                accept="image/*"
                onChange={(event) => {
                  handleImageUpload(event);
                  field.onChange(event); // Đồng bộ với React Hook Form
                }}
              />
            </Button>
          )}
        />

        {/* Preview ảnh */}
        {previewImage && (
          <Box mt={2}>
            <img
              src={previewImage}
              alt="Preview"
              style={{
                maxWidth: '100%',
                maxHeight: '300px',
                border: '1px solid #ddd'
              }}
            />
          </Box>
        )}
      </Box>
    </Box>
  );
}
