import React, { useState } from "react";
import DashboardLayout from "../../../layouts/dashboard";

import {
  AppBar,
  Box,
  Drawer,
  IconButton,
  Stack,
  Toolbar,
  Typography,
  useMediaQuery,
  useTheme,
} from "@mui/material";
import Grid from "@mui/material/Grid2";
import { List, ListItem, ListItemText } from "@mui/material";
import Link from "next/link";
import { useRouter } from "next/router";
import { paths } from "@/src/paths";
import MenuIcon from "@mui/icons-material/Menu";
import TitleTypography from "../../title-typography/title-typography";
import { Padding } from "@/src/styles/CommonStyle";
import { useAllPermissions } from "@/src/api/hooks/auth/use-all-permissions";
export default function SettingLayout({ children }) {
  return (
    <DashboardLayout>
      <Box padding={Padding}>
        <TitleTypography
          sx={{
            mb: 2,
            fontSize: "20px !important",
            fontWeight: "700",
            lineHeight: "1",
            paddingBottom: "20px",
            borderBottom: "1px solid #bdbdbd",
            marginTop: "20px",
          }}
        >
          Cài đặt
        </TitleTypography>
        <Stack
          flexDirection={"row"}
          alignItems={"start"}
          sx={{
            background: "#fff",
            padding: "20px",
            borderRadius: "15px",
            "@media (max-width:1200px)": {
              flexDirection: "column",
              padding: "0 !important",
              overflow: "hidden",
            },
          }}
        >
          <Box
            width={"12%"}
            sx={{
              "@media (max-width:1200px)": {
                width: "100%",
              },
            }}
          >
            <LeftSide />
          </Box>
          <Box
            width={"88%"}
            padding={"20px"}
            sx={{
              "@media (max-width:1200px)": {
                width: "100%",
                padding: 0,
              },
            }}
          >
            {children}
          </Box>
        </Stack>
      </Box>
    </DashboardLayout>
  );
}

const menuDesktop = {
  storeConfig: {
    title: "Cấu hình cửa hàng",
    items: [
      {
        title: "Thanh toán",
        route: paths.settings.payment.index,
      },
      {
        title: "Vận chuyển",
        route: paths.settings.shipping,
      },
      {
        title: "Điểm bán",
        route: paths.settings.location.index,
      },
      {
        title: "Thuế",
        route: paths.settings.invoice,
      },
      // {
      //   title: 'Thuế',
      //   route: paths.settings.tax
      // },
      {
        title: "Điều khoản",
        route: paths.settings.clause.index,
      },
      // {
      //   title: 'Media',
      //   route: paths.settings.media
      // }
    ],
  },
  systemConfig: {
    title: "Quản lý hệ thống",
    items: [
      // {
      //   title: 'Tổng quan',
      //   route: paths.settings.overview
      // },
      {
        title: "Tên miền riêng",
        route: paths.settings.domain,
      },
      {
        title: "Kết nối dịch vụ",
        route: paths.settings.integration,
      },
      {
        title: "Phân quyền",
        route: paths.settings.auth,
        requiresAgency: true,
      },
      // {
      //   title: "Quản lý gói dịch vụ",
      //   route: paths.settings.service,
      //   requiresAgency: true,
      // },
      // {
      //   title: 'Hóa đơn',
      //   route: paths.settings.bill
      // },
      // {
      //   title: 'Thông báo đẩy',
      //   route: paths.settings.notification
      // }
    ],
  },
};

const LeftSide = () => {
  const router = useRouter();
  const [openDrawerMobile, setOpenDrawerMobile] = useState(false);
  const { isAgency } = useAllPermissions();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("lg"));

  // Hàm kiểm tra menu có active không
  const isActive = (path: string) => {
    return router.pathname === path;
  };

  // Hàm xử lý toggle Drawer trên mobile
  const handleDrawerToggle = () => {
    setOpenDrawerMobile(!openDrawerMobile);
  };

  // Lọc các mục trong systemConfig dựa trên isAgency
  const filteredSystemConfigItems = menuDesktop.systemConfig.items.filter(
    (item) => !item.requiresAgency || isAgency
  );

  return (
    <Box sx={{ height: "100%", borderRight: 1, borderColor: "divider" }}>
      {/* Menu Mobile */}
      {isMobile && (
        <>
          <AppBar
            position="sticky"
            sx={{
              backgroundColor: "#fff",
              boxShadow: "none",
            }}
          >
            <Toolbar>
              <IconButton
                edge="start"
                color="primary"
                aria-label="menu"
                onClick={handleDrawerToggle}
              >
                <MenuIcon sx={{ marginRight: 1 }} />
              </IconButton>
            </Toolbar>
          </AppBar>

          <Drawer
            open={openDrawerMobile}
            onClose={handleDrawerToggle}
            sx={{
              width: 240,
              flexShrink: 0,
              "& .MuiDrawer-paper": {
                width: 240,
                boxSizing: "border-box",
              },
            }}
            variant="temporary"
            anchor="left"
          >
            <Box p={2}>
              <Typography variant="h6" marginBottom={2}>
                Cài đặt
              </Typography>
              <Box marginBottom={2}>
                <Typography variant="h6" paddingLeft={2} paddingRight={2}>
                  {menuDesktop.storeConfig.title}
                </Typography>
                <List>
                  {menuDesktop.storeConfig.items.map((item, index) => (
                    <Link href={item.route} passHref key={index}>
                      <ListItem
                        sx={{
                          color: isActive(item.route) ? "primary.main" : "inherit",
                        }}
                      >
                        <ListItemText primary={item.title} />
                      </ListItem>
                    </Link>
                  ))}
                </List>
              </Box>

              <Box marginBottom={2}>
                <Typography variant="h6" paddingLeft={2} paddingRight={2}>
                  {menuDesktop.systemConfig.title}
                </Typography>
                <List>
                  {filteredSystemConfigItems.map((item, index) => (
                    <Link href={item.route} passHref key={index}>
                      <ListItem
                        sx={{
                          color: isActive(item.route) ? "primary.main" : "inherit",
                        }}
                      >
                        <ListItemText primary={item.title} />
                      </ListItem>
                    </Link>
                  ))}
                </List>
              </Box>
            </Box>
          </Drawer>
        </>
      )}

      {/* Menu Desktop */}
      {!isMobile && (
        <Box p={0}>
          <Box marginBottom={2}>
            <Typography variant="h6" paddingLeft={0}>
              {menuDesktop.storeConfig.title}
            </Typography>
            <List>
              {menuDesktop.storeConfig.items.map((item, index) => (
                <Link href={item.route} passHref key={index}>
                  <ListItem
                    sx={{
                      color: isActive(item.route) ? "#2654FE" : "inherit",
                      paddingLeft: "0 !important",
                    }}
                  >
                    <ListItemText primary={item.title} />
                  </ListItem>
                </Link>
              ))}
            </List>
          </Box>
          <Box>
            <Typography variant="h6" paddingLeft={0}>
              {menuDesktop.systemConfig.title}
            </Typography>
            <List>
              {filteredSystemConfigItems.map((item, index) => (
                <Link href={item.route} passHref key={index}>
                  <ListItem
                    sx={{
                      color: isActive(item.route) ? "#2654FE" : "inherit",
                      paddingLeft: "0 !important",
                    }}
                  >
                    <ListItemText primary={item.title} />
                  </ListItem>
                </Link>
              ))}
            </List>
          </Box>
        </Box>
      )}
    </Box>
  );
};
