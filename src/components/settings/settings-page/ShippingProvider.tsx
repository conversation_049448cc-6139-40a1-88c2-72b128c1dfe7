import React from 'react';
import { Box, IconButton, ListItem, ListItemText, Typography } from '@mui/material';
import LocalShippingIcon from '@mui/icons-material/LocalShipping';
import ToggleOnIcon from '@mui/icons-material/ToggleOn';
import ToggleOffIcon from '@mui/icons-material/ToggleOff';

type ShippingProviderProps = {
  provider: string;
  selectedProvider: string | null;
  handleToggleProvider: (provider: string) => void;
  handleEdit: (provider: string) => void;
};

export default function ShippingProvider({
  provider,
  selectedProvider,
  handleToggleProvider,
  handleEdit,
}: ShippingProviderProps) {
  return (
    <ListItem sx={{ px: 0, py: 0.5 }}>
      <LocalShippingIcon sx={{ mr: 1, color: 'primary.main', fontSize: 30 }} />
      <ListItemText primary={provider} />
      <Box sx={{ display: 'flex', alignItems: 'center' }} gap={2}>
        <IconButton onClick={() => handleToggleProvider(provider)} size="large">
          {selectedProvider === provider ? (
            <ToggleOnIcon color="primary" sx={{ fontSize: 35 }} />
          ) : (
            <ToggleOffIcon color="action" sx={{ fontSize: 35 }} />
          )}
        </IconButton>

        <Typography
          sx={{ mr: 1, cursor: 'pointer', color: 'primary.main' }}
          onClick={() => handleEdit(provider)}
        >
          Sửa
        </Typography>
      </Box>
    </ListItem>
  );
}
