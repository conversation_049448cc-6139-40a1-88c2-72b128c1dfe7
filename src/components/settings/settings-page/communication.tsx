import React, { useState } from 'react';
import { Box, Card, Checkbox, Typography, TextField, Chip, Button } from '@mui/material';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import { useForm, FormProvider } from 'react-hook-form';
import FormDialog from '../../dialog/FormDialog';
import { useRouter } from 'next/router';
import { paths } from '@/src/paths';
import Grid from '@mui/system/Grid';

const notificationTitles = [
  {
    key: 'customerAppreciation',
    title: 'Tri ân khách hàng',
    message: 'Tri ân khách hàng thân thiết, [Tên cửa hàng] gửi tặng bạn ưu đãi độc quyền! 🎁 Giảm ngay [Phần trăm giảm giá]% cho sản phẩm [Tên sản phẩm]. Nhập mã "[Mã giảm giá]" khi thanh toán. <PERSON><PERSON> nh<PERSON><PERSON> từ [<PERSON><PERSON><PERSON> b<PERSON><PERSON> đầu] đến [<PERSON><PERSON><PERSON> kết thúc]. <PERSON><PERSON> ngay tại: [<PERSON> sản phẩm]',
    params: ['[Tên khách hàng]', '[Ưu đãi]']
  },
  {
    key: 'newVoucher',
    title: 'Thông báo khi có voucher mới',
    message: '🎉 Tin vui cho bạn! [Tên cửa hàng] vừa ra mắt voucher mới cực hấp dẫn! 🎉 [Ưu đãi]: [Chi tiết ưu đãi] Cách nhận voucher: Mua sắm [Điều kiện nhận voucher] tại [Cửa hàng/Website/Ứng dụng]] Thời gian áp dụng: [Ngày bắt đầu] - [Ngày kết thúc]',
    params: ['[Tên khách hàng]']
  },
  {
    key: 'afterSalesPolicy',
    title: 'Chính sách hậu mãi',
    message: 'Cam kết mang đến sự hài lòng tuyệt đối cho khách hàng, [Tên cửa hàng] áp dụng chính sách hậu mãi chu đáo với các dịch vụ: Đổi trả hàng trong vòng [Số ngày] ngày ([Điều kiện đổi trả]), bảo hành sản phẩm [Thời gian bảo hành] ([Điều kiện bảo hành]), hỗ trợ kỹ thuật 24/7 qua hotline [Số điện thoại] hoặc email [Địa chỉ email]. Chi tiết xem tại [Link chi tiết chính sách hậu mãi].',
    params: ['[]']
  },
  {
    key: 'postTransactionOffer',
    title: 'Ưu đãi sau giao dịch',
    message: 'Cảm ơn bạn đã tin tưởng lựa chọn [Tên cửa hàng]! Nhận ngay ưu đãi [Phần trăm giảm giá]% cho đơn hàng tiếp theo khi mua sắm trong vòng [Thời gian áp dụng]. Áp dụng cho [Sản phẩm/Nhóm sản phẩm]. Xem chi tiết tại [Link chi tiết chương trình].',
    params: ['[]']
  },
  {
    key: 'newProductLaunch',
    title: 'Ra mắt sản phẩm mới',
    message: 'Chào [Tên khách hàng], chúng tôi vừa ra mắt sản phẩm mới tại [Tên cửa hàng]. Xem chi tiết tại [Link].',
    params: ['[Tên khách hàng]', '[Tên cửa hàng]', '[Link]']
  }
];

const cardStyles = {
  padding: 2,
  bgcolor: '#f5f5f5',
  borderRadius: 1,
  boxShadow: '0px 4px 12px rgba(0, 0, 0, 0.1)',
  cursor: 'pointer',
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'space-between',
  height: '100%',
  '&:hover': {
    boxShadow: '0px 6px 16px rgba(0, 0, 0, 0.2)'
  },
  minHeight: '150px'
};

const checkboxStyles = {
  color: '#e0e0e0',
  '&.Mui-checked': {
    color: '#4caf50'
  },
  '& .MuiSvgIcon-root': {
    fontSize: 20
  },
  padding: 0,
  marginLeft: 1
};

const NotificationCard = ({ title, message, params, onClick }) => {
  const router = useRouter();

  const handleClick = () => {
    router.push({
      pathname: paths.settings.management,
      query: { title, message, params: JSON.stringify(params) }
    });
  };

  return (
    <Card sx={cardStyles} onClick={onClick || handleClick}>
      <Typography variant="subtitle1" sx={{ marginBottom: 2, fontWeight: 500 }}>
        {title}
      </Typography>
      {['Thông báo hệ thống', 'Zalo'].map((label, i) => (
        <Box
          key={i}
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            marginBottom: i === 0 ? 1 : 0,
            width: '100%'
          }}
        >
          <Typography variant="body2">{label}</Typography>
          <Checkbox checked checkedIcon={<CheckCircleIcon />} sx={checkboxStyles} />
        </Box>
      ))}
    </Card>
  );
};

const AddNewLinkCard = () => {
  const router = useRouter();

  const handleClick = () => {
    router.push(paths.settings.managementAdd);
  };

  return (
    <Card sx={cardStyles} onClick={handleClick}>
      <Typography variant="subtitle1" sx={{ mt: 6, fontWeight: 500, textAlign: 'center', color: 'primary.main' }}>
        Thêm thông báo Zalo mới
      </Typography>
    </Card>
  );
};

export default function NotificationCommunication() {
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedNotification, setSelectedNotification] = useState('');
  const methods = useForm();
  const router = useRouter();

  const handleOpenDialog = (title) => {
    if (title === 'Thêm thông báo Zalo mới') {
      router.push(paths.settings.managementAdd);
    } else {
      setSelectedNotification(title);
      setOpenDialog(true);
    }
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedNotification('');
  };

  const dialogContent = notificationTitles.find(notification => notification.title === selectedNotification) || {
    title: '',
    message: '',
    params: []
  };

  return (
    <Box sx={{ p: 3 }}>
      <Grid container spacing={2}>
        {notificationTitles.map((notification, index) => (
          <Grid size={{ xs: 12, md: 4 }} key={index}>
            <NotificationCard
              title={notification.title}
              message={notification.message}
              params={notification.params}
              onClick={() => handleOpenDialog(notification.title)}
            />
          </Grid>
        ))}
        <Grid size={{ xs: 12, md: 4 }}>
          <AddNewLinkCard />
        </Grid>
      </Grid>
      <FormProvider {...methods}>
        <FormDialog
          open={openDialog}
          onClose={handleCloseDialog}
          title={dialogContent.title}
          onSubmit={() => { }}
          submitBtnTitle="Xác nhận"
        >
          <Box sx={{ p: 2 }}>
            <TextField
              multiline
              rows={4}
              fullWidth
              value={dialogContent.message}
              InputProps={{
                readOnly: true
              }}
              sx={{ mb: 3 }}
            />
            {dialogContent.params.length > 0 && (
              <>
                <Typography variant="h6" sx={{ mb: 2 }}>
                  Tham số:
                </Typography>
                <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                  {dialogContent.params.map((param, index) => (
                    <Chip key={index} label={param} />
                  ))}
                </Box>
              </>
            )}
          </Box>
        </FormDialog>
      </FormProvider>
    </Box>
  );
}
