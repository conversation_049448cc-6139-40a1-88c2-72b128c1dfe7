import React from "react";
import { IconButton, SvgIcon, useMediaQuery } from "@mui/material";
import ChevronLeftIcon from "@mui/icons-material/ChevronLeft";
import ChevronRightIcon from "@mui/icons-material/ChevronRight";
import { useSidebar } from "src/contexts/sidebar-context";
import { SIDE_NAV_WIDTH, SIDE_NAV_COLLAPSED_WIDTH } from "src/components/main-layout";

export const SidebarToggleButton = () => {
  const { isCollapsed, toggleSidebar } = useSidebar();
  const lgUp = useMediaQuery((theme) => theme.breakpoints.up("lg"));

  if (!lgUp) {
    return null;
  }

  return (
    <IconButton
      onClick={toggleSidebar}
      sx={{
        position: "fixed",
        left: isCollapsed ? SIDE_NAV_COLLAPSED_WIDTH + 10 : SIDE_NAV_WIDTH + 10,
        top: 16,
        zIndex: (theme) => theme.zIndex.appBar + 1,
        backgroundColor: "background.paper",
        border: "1px solid",
        borderColor: "divider",
        width: 32,
        height: 32,
        transition: "left 0.3s ease",
        "&:hover": {
          backgroundColor: "action.hover",
        },
        boxShadow: 1,
      }}
    >
      <SvgIcon sx={{ fontSize: 18 }}>
        {isCollapsed ? <ChevronRightIcon /> : <ChevronLeftIcon />}
      </SvgIcon>
    </IconButton>
  );
};
