import React, { useState } from 'react';
import { Box, TextField, Popover, Typography } from '@mui/material';
import { HexColorPicker } from 'react-colorful';

interface ThemeColorPickerProps {
  label: string;
  value: string;
  onChange: (color: string) => void;
}

const ThemeColorPicker: React.FC<ThemeColorPickerProps> = ({ label, value, onChange }) => {
  const [anchorEl, setAnchorEl] = useState<HTMLDivElement | null>(null);

  const handleClick = (event: React.MouseEvent<HTMLDivElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const open = Boolean(anchorEl);

  return (
    <Box>
      <Typography variant="subtitle1" gutterBottom>
        {label}:
      </Typography>
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
        <Box
          onClick={handleClick}
          sx={{
            width: 40,
            height: 40,
            borderRadius: 1,
            border: '2px solid #ddd',
            backgroundColor: value || '#fff',
            cursor: 'pointer',
            transition: 'border-color 0.2s',
            '&:hover': {
              borderColor: '#aaa'
            }
          }}
        />
        <TextField
          size="small"
          value={value}
          onChange={(e) => onChange(e.target.value)}
          placeholder="#000000"
          sx={{
            flex: 1,
            '& input': {
              textTransform: 'uppercase'
            }
          }}
        />
      </Box>
      <Popover
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left'
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'left'
        }}
      >
        <Box sx={{ p: 2 }}>
          <HexColorPicker color={value} onChange={onChange} />
        </Box>
      </Popover>
    </Box>
  );
};

export default ThemeColorPicker;
