import React, { useEffect, useState } from "react";
import Grid from "@mui/material/Grid2";
import {
  Box,
  Button,
  InputAdornment,
  Typography,
  Stack,
  Alert,
  useTheme,
  alpha,
  Chip,
  TextField,
} from "@mui/material";
import { Controller, FormProvider, useForm, useFormContext } from "react-hook-form";
import {
  CODE_TYPE,
  RELEASE_TYPE,
  DISCOUNT_TYPE,
  LIMIT_TYPE,
  CONDITION_TYPE,
  VOUCHER_TYPE,
  VOUCHER_STATUS,
  REWARD_TYPE,
  VOUCHER_TAB,
  TYPE_DISTRIBUTION,
} from "@/src/api/types/voucher.type";
import { yupResolver } from "@hookform/resolvers/yup";
import * as Yup from "yup";
import { formatDateTimeForApi } from "@/src/utils/date-utils";
import { useStoreId } from "@/src/hooks/use-store-id";
import { useRouter } from "next/router";
import useSnackbar from "@/src/hooks/use-snackbar";
import { paths } from "@/src/paths";
import PromotionVoucherBox4 from "./PromotionVoucherBox4";
import TitleDialog from "../dialog/TitleDialog";
import { useVoucher } from "@/src/api/hooks/voucher/use-voucher";
import dayjs from "dayjs";
import PopupAddProduct from "@/src/pages/dashboard/marketing/affiliate-marketing/component/CommissionPolicy/Policy/ProductCommission/PopupAddProduct";
import CloseIcon from "@mui/icons-material/Close";
import { formatMoney } from "@/src/utils/format-money";
import { CommonMediaUpload } from "@/src/components/common-media-upload";
import { FILE_SIZE_2MB } from "@/src/constants/constant";
import { FileType } from "@/src/constants/file-types";
import { CreateFileGroupRequest, RefType } from "@/src/api/types/media.types";
import {
  ExistingMediaFile,
  scrollToTop,
} from "@/src/pages/dashboard/product/product-management/create/create";
import CustomSwitch from "../custom-switch";
import PromotionVoucherBox3 from "./PromotionVoucherBox3";
import { ImageProcessor } from "@/src/utils/image-processor";
import { useMedia } from "@/src/api/hooks/media/use-media";
import { FormHeader, FormSection, FormRadioGroup, FormActions } from "./shared";
import InfoIcon from "@mui/icons-material/Info";
import CardGiftcardIcon from "@mui/icons-material/CardGiftcard";

export type CustomVoucherFormData = {
  voucherId?: string;
  voucherType?: string;
  voucherCode?: string;
  codeType: string;
  releaseType: string;
  quantity: number;
  maxDiscount: number;
  moneyDiscount: number;
  percentDiscount?: number;
  discountType: string;
  limitType: string;
  categoryIds?: string[];
  productIds?: string[];
  conditionType: string;
  userGroupId?: string;
  userIds?: string[];
  maxUsagePerUser: number;
  isLongTerm: boolean;
  startDate: Date;
  endDate?: Date | null;
  status: string;
  voucherName?: string;
  voucherNameOrigin?: string;
  exchangePoints?: number;
  voucherCodePrefix: string;
  rewardType: string;
  isFixedRewardPoint: boolean;
  rewardPoint: number;
  rewardPointMin: number;
  rewardPointMax: number;
  rewardGiftIds: string[];
  rewardProducts?: any[];
  image?: string;
  originalEndDate?: Date;
  distributionType?: string;
};

const now = new Date();
now.setHours(0, 0, 0, 0);

export const initFormPointPromotionData: CustomVoucherFormData = {
  voucherId: "",
  voucherType: VOUCHER_TYPE.CUSTOM,
  releaseType: RELEASE_TYPE.FREE,
  codeType: CODE_TYPE.COMMON,
  quantity: 1,
  maxDiscount: 0,
  moneyDiscount: 0,
  percentDiscount: 0,
  discountType: DISCOUNT_TYPE.PERCENT,
  limitType: LIMIT_TYPE.ALL,
  categoryIds: [],
  productIds: [],
  conditionType: CONDITION_TYPE.ALL,
  userGroupId: "",
  userIds: [],
  maxUsagePerUser: 1,
  startDate: now,
  status: VOUCHER_STATUS.ACTIVED,
  endDate: null,
  isLongTerm: false,
  voucherCode: "",
  voucherName: "",
  voucherNameOrigin: "",
  exchangePoints: 0,
  voucherCodePrefix: "",
  rewardType: REWARD_TYPE.POINT,
  isFixedRewardPoint: true,
  rewardPoint: 0,
  rewardPointMin: 0,
  rewardPointMax: 0,
  rewardGiftIds: [],
  rewardProducts: [],
  originalEndDate: null,
  distributionType: TYPE_DISTRIBUTION.ON_RECEIVE,
};

const validationSchema = Yup.object({
  voucherId: Yup.string().nullable(),
  voucherCode: Yup.string()
    .matches(/^[A-Za-z0-9_-]+$/, "Mã voucher không được chứa dấu, khoảng cách hoặc ký tự đặc biệt")
    .when("codeType", {
      is: CODE_TYPE.COMMON,
      then: () => Yup.string().required("Mã voucher là bắt buộc"),
      otherwise: () => Yup.string().nullable(),
    }),
  voucherCodePrefix: Yup.string()
    .matches(
      /^[A-Za-z0-9_-]+$/,
      "Tiền tố mã voucher không được chứa dấu, khoảng cách hoặc ký tự đặc biệt"
    )
    .when("codeType", {
      is: CODE_TYPE.UNIQUE,
      then: () => Yup.string().required("Tiền tố mã voucher là bắt buộc"),
      otherwise: () => Yup.string().nullable(),
    }),
  codeType: Yup.string()
    .oneOf([CODE_TYPE.COMMON, CODE_TYPE.UNIQUE], "Loại mã không hợp lệ")
    .required("Loại mã là bắt buộc"),
  releaseType: Yup.string()
    .oneOf([RELEASE_TYPE.FREE, RELEASE_TYPE.POINT], "Loại phát hành không hợp lệ")
    .required("Loại phát hành là bắt buộc"),
  quantity: Yup.number()
    .transform((value, originalValue) => (originalValue === "" ? undefined : value))
    .typeError("Số lượng phải là một số")
    .required("Số lượng không được để trống")
    .positive("Số lượng phải lớn hơn 0")
    .integer("Số lượng phải là số nguyên")
    .min(1, "Số lượng phải ít nhất là 1")
    .max(2147483647, "Số lượng đã vượt mức tối đa"),
  discountType: Yup.string()
    .oneOf([DISCOUNT_TYPE.PERCENT, DISCOUNT_TYPE.MONEY], "Loại giảm giá không hợp lệ")
    .required("Loại giảm giá là bắt buộc"),
  limitType: Yup.string()
    .oneOf([LIMIT_TYPE.ALL, LIMIT_TYPE.CATEGORY, LIMIT_TYPE.PRODUCT], "Loại giới hạn không hợp lệ")
    .nullable(),
  categoryId: Yup.array()
    .of(Yup.string())
    .when("limitType", {
      is: LIMIT_TYPE.CATEGORY,
      then: () => Yup.array().of(Yup.string()).min(1, "Danh mục sản phẩm không được để trống"),
      otherwise: () => Yup.array().nullable(),
    }),
  productIds: Yup.array()
    .of(Yup.string())
    .when("limitType", {
      is: LIMIT_TYPE.PRODUCT,
      then: () => Yup.array().of(Yup.string()).min(1, "Sản phẩm không được để trống"),
      otherwise: () => Yup.array().nullable(),
    }),
  conditionType: Yup.string()
    .oneOf(
      [CONDITION_TYPE.ALL, CONDITION_TYPE.GROUP, CONDITION_TYPE.CUSTOMER],
      "Loại điều kiện không hợp lệ"
    )
    .required("Loại điều kiện là bắt buộc"),
  userGroupId: Yup.string()
    .nullable()
    .when("conditionType", {
      is: CONDITION_TYPE.GROUP,
      then: () => Yup.string().required("Nhóm khách hàng không được để trống"),
      otherwise: () => Yup.string().nullable(),
    }),
  userIds: Yup.array()
    .of(Yup.string())
    .when("conditionType", {
      is: CONDITION_TYPE.CUSTOMER,
      then: () => Yup.array().of(Yup.string()).min(1, "Khách hàng không được để trống"),
      otherwise: () => Yup.array().nullable(),
    }),
  maxUsagePerUser: Yup.number()
    .transform((value, originalValue) => (originalValue === "" ? undefined : value))
    .typeError("Số lần sử dụng phải là một số")
    .required("Số lần sử dụng là bắt buộc")
    .positive("Số lần sử dụng phải lớn hơn 0")
    .integer("Số lần sử dụng phải là số nguyên")
    .min(1, "Số lần sử dụng phải ít nhất là 1")
    .max(2147483647, "Số lần sử dụng đã vượt mức tối đa"),
  isLongTerm: Yup.boolean(),
  startDate: Yup.date().when(["isLongTerm", "voucherId"], (isLongTerm, voucherId) => {
    if (!isLongTerm && !!voucherId) {
      return Yup.date().required("Ngày bắt đầu là bắt buộc");
    }
    if (!isLongTerm) {
      return Yup.date()
        .min(new Date(new Date().setHours(0, 0, 0, 0)), "Ngày bắt đầu phải từ hôm nay trở đi")
        .required("Ngày bắt đầu là bắt buộc");
    }
    return Yup.date().nullable();
  }),
  endDate: Yup.date().when(["isLongTerm", "originalEndDate"], {
    is: (isLongTerm, originalEndDate) => !isLongTerm,
    then: function () {
      return Yup.date()
        .min(Yup.ref("startDate"), "Ngày kết thúc phải sau ngày bắt đầu")
        .test(
          "min-5-minutes-if-changed",
          "Thời gian kết thúc phải sau thời điểm hiện tại ít nhất 5 phút",
          function (value) {
            const { originalEndDate } = this.parent;
            if (!value) return false;
            // Nếu không đổi so với DB thì pass
            if (
              originalEndDate &&
              new Date(originalEndDate).getTime() === new Date(value).getTime()
            ) {
              return true;
            }
            // Nếu đổi thì phải lớn hơn hiện tại + 5 phút
            const minTime = new Date();
            minTime.setMinutes(minTime.getMinutes() + 5);
            return new Date(value) >= minTime;
          }
        )
        .required("Ngày kết thúc là bắt buộc");
    },
    otherwise: () => Yup.date().nullable(),
  }),
  status: Yup.string()
    .oneOf(
      [VOUCHER_STATUS.ACTIVED, VOUCHER_STATUS.INACTIVED, VOUCHER_STATUS.EXPIRED],
      "Trạng thái không hợp lệ"
    )
    .required("Trạng thái là bắt buộc"),
  voucherName: Yup.string()
    .max(255, "Tên voucher không được vượt quá 255 ký tự")
    .transform((value) => (typeof value === "string" ? value.trim() : value))
    .required("Tên voucher là bắt buộc")
    .test("not-empty", "Tên voucher không được để trống", (value) => !!value && value.length > 0),
  voucherNameOrigin: Yup.string().nullable(),
  exchangePoints: Yup.number()
    .transform((value, originalValue) => (originalValue === "" ? undefined : value))
    .when(["releaseType", "codeType"], {
      is: (releaseType, codeType) =>
        releaseType === RELEASE_TYPE.POINT && codeType === CODE_TYPE.COMMON,
      then: () =>
        Yup.number()
          .typeError("Số điểm phải là một số")
          .required("Số điểm là bắt buộc")
          .positive("Số điểm phải lớn hơn 0")
          .integer("Số điểm phải là số nguyên")
          .min(1, "Số điểm phải ít nhất là 1")
          .max(2147483647, "Số điểm đã vượt mức tối đa"),
      otherwise: () => Yup.number().nullable().max(2147483647, "Số điểm đã vượt mức tối đa"),
    }),
  rewardType: Yup.string()
    .oneOf([REWARD_TYPE.POINT, REWARD_TYPE.PRODUCT], "Loại thưởng không hợp lệ")
    .required("Loại thưởng là bắt buộc"),
  isFixedRewardPoint: Yup.boolean(),
  rewardPoint: Yup.number()
    .transform((value, originalValue) => (originalValue === "" ? undefined : value))
    .when(["rewardType", "isFixedRewardPoint"], {
      is: (rewardType, isFixed) => rewardType === REWARD_TYPE.POINT && isFixed === true,
      then: () =>
        Yup.number()
          .typeError("Số điểm thưởng phải là một số")
          .required("Số điểm thưởng là bắt buộc")
          .min(1, "Số điểm thưởng phải lớn hơn 0")
          .max(2147483647, "Số điểm thưởng đã vượt mức tối đa"),
      otherwise: () => Yup.number().nullable().max(2147483647, "Số điểm thưởng đã vượt mức tối đa"),
    }),
  rewardPointMin: Yup.number()
    .transform((value, originalValue) => (originalValue === "" ? undefined : value))
    .when(["rewardType", "isFixedRewardPoint"], {
      is: (rewardType, isFixed) => rewardType === REWARD_TYPE.POINT && isFixed === false,
      then: () =>
        Yup.number()
          .typeError("Số điểm thưởng tối thiểu phải là một số")
          .required("Số điểm thưởng tối thiểu là bắt buộc")
          .min(1, "Số điểm thưởng tối thiểu phải lớn hơn 0")
          .max(2147483647, "Số điểm thưởng tối thiểu đã vượt mức tối đa"),
      otherwise: () =>
        Yup.number().nullable().max(2147483647, "Số điểm thưởng tối thiểu đã vượt mức tối đa"),
    }),
  rewardPointMax: Yup.number()
    .transform((value, originalValue) => (originalValue === "" ? undefined : value))
    .when(["rewardType", "isFixedRewardPoint"], {
      is: (rewardType, isFixed) => rewardType === REWARD_TYPE.POINT && isFixed === false,
      then: () =>
        Yup.number()
          .typeError("Số điểm thưởng tối đa phải là một số")
          .required("Số điểm thưởng tối đa là bắt buộc")
          .min(1, "Số điểm thưởng tối đa phải lớn hơn 0")
          .max(2147483647, "Số điểm thưởng đã vượt mức tối đa")
          .test(
            "max-greater-than-min",
            "Số điểm thưởng tối đa phải lớn hơn số điểm thưởng tối thiểu",
            function (value) {
              const min = this.parent.rewardPointMin;
              return !min || !value || value > min;
            }
          ),
      otherwise: () => Yup.number().nullable().max(2147483647, "Số điểm thưởng đã vượt mức tối đa"),
    }),
  rewardGiftIds: Yup.array()
    .of(Yup.string())
    .when("rewardType", {
      is: REWARD_TYPE.PRODUCT,
      then: () => Yup.array().of(Yup.string()).min(1, "Sản phẩm thưởng không được để trống"),
      otherwise: () => Yup.array().nullable(),
    }),
  image: Yup.mixed().nullable().notRequired(),
  originalEndDate: Yup.date().nullable(),
  distributionType: Yup.string()
    .oneOf(
      [TYPE_DISTRIBUTION.IMMEDIATE, TYPE_DISTRIBUTION.ON_RECEIVE],
      'Loại phân phối phải là "Immediate" hoặc "OnReceive"'
    )
    .when("codeType", {
      is: CODE_TYPE.UNIQUE,
      then: () => Yup.string().required("Loại phân phối là bắt buộc"),
      otherwise: () => Yup.string().nullable().notRequired(),
    }),
});

const generateRandomCode = () => {
  const timestamp = Date.now().toString(36).toUpperCase();
  const randomStr = Math.random().toString(36).substring(2, 8).toUpperCase();
  return `VOUCHER${timestamp}${randomStr}`;
};

export default function CustomVoucherForm({ voucher }) {
  const methods = useForm<CustomVoucherFormData>({
    defaultValues: initFormPointPromotionData,
    resolver: yupResolver(validationSchema) as any,
    mode: "onChange",
  });

  const {
    control,
    handleSubmit,
    watch,
    reset,
    setValue,
    formState: { errors },
    getValues,
  } = methods;

  const storeId = useStoreId();
  const { createVoucher, updateVoucher, getVoucher, loading } = useVoucher();
  const router = useRouter();
  const snackbar = useSnackbar();
  const [isDoneInitDetail, setIsDoneInitDetail] = useState(false);

  const [existingFiles, setExistingFiles] = useState<ExistingMediaFile[]>([]);
  const [localFiles, setLocalFiles] = useState<File[]>([]);
  const [errorMsg, setErrorMsg] = useState<string>("");
  const [defaultGroupId, setDefaultGroupId] = useState<string>("");
  const { getGroups } = require("@/src/api/hooks/media/use-media").useMedia();
  const { uploadFile } = useMedia();
  const [localLoading, setLocalLoading] = useState(false);

  useEffect(() => {
    const fetchDefaultGroup = async () => {
      try {
        const data = {
          ShopId: storeId,
          Skip: 0,
          Limit: 10,
        };
        const response = await getGroups(data);
        if (response?.data?.data?.length > 0) {
          setDefaultGroupId(response.data.data[0].groupFileId);
        }
      } catch (error) {
        console.error("Error fetching groups:", error);
      }
    };
    if (storeId) fetchDefaultGroup();
  }, [storeId]);

  useEffect(() => {
    if (voucher && voucher.image) {
      const imageObj =
        typeof voucher.image === "string"
          ? { link: voucher.image, url: voucher.image, type: FileType.IMAGE }
          : voucher.image;
      setExistingFiles([imageObj]);
      setValue("image", imageObj);
    } else {
      setExistingFiles([]);
      setValue("image", null);
    }
  }, [voucher]);

  const handleFilesChange = async (filesOrObjects: any[]) => {
    setLocalFiles(filesOrObjects);
  };

  const handleRemove = () => {
    setExistingFiles([]);
    setLocalFiles([]);
    setValue("image", null);
  };

  useEffect(() => {
    if (Object.keys(errors).length > 0) {
      console.log("Form validation errors:", {
        errors: Object.entries(errors).map(([field, error]) => ({
          field,
          message: error.message,
          type: error.type,
        })),
      });
    }
  }, [errors]);

  const onSubmit = async (data: CustomVoucherFormData) => {
    if (localLoading) return;
    setLocalLoading(true);
    try {
      if (data.codeType === CODE_TYPE.COMMON) {
        if (!/^[A-Za-z0-9]+$/.test(data.voucherCode || "")) {
          snackbar.error("Mã voucher không được chứa dấu, khoảng cách hoặc ký tự đặc biệt");
          return;
        }
        if (!data.voucherCode) {
          snackbar.error("Mã voucher là bắt buộc");
          return;
        }
        if ((data.voucherCode || "").length > 30) {
          snackbar.error("Mã voucher không được vượt quá 30 ký tự");
          return;
        }
      }
      if (data.codeType === CODE_TYPE.UNIQUE) {
        if (!/^[A-Za-z0-9]+$/.test(data.voucherCodePrefix || "")) {
          snackbar.error("Tiền tố mã voucher không được chứa dấu, khoảng cách hoặc ký tự đặc biệt");
          return;
        }
        if (!data.voucherCodePrefix) {
          snackbar.error("Tiền tố mã voucher là bắt buộc");
          return;
        }
        if ((data.voucherCodePrefix || "").length > 10) {
          snackbar.error("Tiền tố mã voucher không được vượt quá 10 ký tự");
          return;
        }
      }

      if (localFiles.length === 0 && existingFiles.length === 0) {
        snackbar.error("Bạn cần chọn ít nhất 1 ảnh!");
        scrollToTop();
        return;
      }
      let uploadedFiles = [];
      if (localFiles.length > 0) {
        for (const file of localFiles) {
          const processedFile = await ImageProcessor.processImage(file);
          const data: CreateFileGroupRequest = {
            FileUpload: processedFile,
            ShopId: storeId,
            GroupFileId: defaultGroupId,
            RefType: RefType.Voucher,
            RefId: "",
          };
          const response = await uploadFile(data);
          uploadedFiles.push(response.data);
        }
      }
      const newImages = uploadedFiles.map((file) => ({
        type: file.type === "IMAGE" ? FileType.IMAGE : FileType.VIDEO,
        link: file.url || file.link,
        mediaFileId: file.mediaFileId,
      }));

      const formData = {
        ...data,
        shopId: storeId,
        image: newImages.length > 0 ? newImages[0] : existingFiles[0],
        startDate: formatDateTimeForApi(data.startDate),
        endDate: formatDateTimeForApi(data.endDate),
        // For custom voucher, always set limitType to ALL since we don't show product selection
        limitType: LIMIT_TYPE.ALL,
        productIds: [],
        categoryIds: [],
      };

      if (data.conditionType === CONDITION_TYPE.ALL) {
        formData.userIds = [];
        formData.userGroupId = "";
      } else if (data.conditionType === CONDITION_TYPE.CUSTOMER) {
        formData.userGroupId = "";
      } else if (data.conditionType === CONDITION_TYPE.GROUP) {
        formData.userIds = [];
      }

      if (data.isFixedRewardPoint) {
        formData.rewardPointMin = 0;
        formData.rewardPointMax = 0;
      } else {
        formData.rewardPoint = 0;
      }

      if (!voucher) {
        const response = await createVoucher(formData);
        if (response?.data?.result) {
          snackbar.success("Tạo voucher thành công");
          if (formData.codeType == CODE_TYPE.COMMON) {
            router.push(paths.marketing.vouchers.list + "/?tab=" + VOUCHER_TAB.CUSTOM_VOUCHER);
          } else {
            router.push(paths.marketing.vouchers.list + "/?tab=" + VOUCHER_TAB.UNIQUE_VOUCHER);
          }
        }
        return;
      }

      const response = await updateVoucher(formData);
      if (response?.data?.result) {
        snackbar.success("Cập nhật voucher thành công");
        if (formData.codeType == CODE_TYPE.COMMON) {
          router.push(paths.marketing.vouchers.list + "/?tab=" + VOUCHER_TAB.CUSTOM_VOUCHER);
        } else {
          router.push(paths.marketing.vouchers.list + "/?tab=" + VOUCHER_TAB.UNIQUE_VOUCHER);
        }
      }
    } finally {
      setLocalLoading(false);
    }
  };

  const handleCancel = () => {
    router.push(paths.marketing.vouchers.pointPromotionsList);
  };

  const handleGenerateCode = () => {
    const newCode = generateRandomCode();
    setValue("voucherCode", newCode);
  };

  useEffect(() => {
    if (voucher && voucher.voucherDetails && !isDoneInitDetail) {
      reset({
        voucherId: voucher.voucherId,
        voucherType: voucher.voucherType,
        releaseType: voucher.releaseType,
        codeType: voucher.codeType,
        quantity: voucher.quantity,
        maxDiscount: voucher.maxDiscount,
        moneyDiscount: voucher.moneyDiscount,
        percentDiscount: voucher.percentDiscount,
        discountType: voucher.discountType,
        limitType: LIMIT_TYPE.ALL, // Always set to ALL for custom voucher
        categoryIds: [],
        productIds: [],
        conditionType: voucher.conditionType,
        userGroupId: voucher.userGroupId,
        userIds: voucher.userIds,
        maxUsagePerUser: voucher.maxUsagePerUser,
        startDate: voucher.startDate ? dayjs(voucher.startDate).toDate() : null,
        status: voucher.status,
        endDate: voucher.endDate ? dayjs(voucher.endDate).toDate() : null,
        isLongTerm: voucher.isLongTerm,
        voucherCode: voucher.voucherDetails[0]?.voucherCode,
        voucherName: voucher.voucherName,
        voucherNameOrigin: voucher.voucherNameOrigin,
        exchangePoints: voucher.exchangePoints || 0,
        voucherCodePrefix: voucher.voucherCodePrefix,
        rewardType: voucher.rewardType,
        isFixedRewardPoint: voucher.isFixedRewardPoint,
        rewardPoint: voucher.isFixedRewardPoint ? voucher.rewardPoint : 0,
        rewardPointMin: voucher.isFixedRewardPoint ? 0 : voucher.rewardPointMin,
        rewardPointMax: voucher.isFixedRewardPoint ? 0 : voucher.rewardPointMax,
        rewardGiftIds: voucher.rewardGiftIds,
        rewardProducts: voucher.rewardProducts,
        image: voucher.image,
        originalEndDate: voucher.endDate ? dayjs(voucher.endDate).toDate() : null,
        distributionType: voucher.distributionType,
      });
      setIsDoneInitDetail(true);
    }
  }, [voucher]);

  // Add effect to handle distributionType changes
  useEffect(() => {
    const distributionType = getValues("distributionType");
    if (distributionType === TYPE_DISTRIBUTION.IMMEDIATE) {
      setValue("conditionType", CONDITION_TYPE.ALL);
      setValue("userGroupId", "");
      setValue("userIds", []);
    }
  }, [watch("distributionType"), setValue, getValues]);

  const theme = useTheme();

  return (
    <FormProvider {...methods}>
      <form onSubmit={handleSubmit(onSubmit)}>
        <Box
          sx={{ minHeight: "100vh", backgroundColor: alpha(theme.palette.background.default, 0.5) }}
        >
          {/* Form Header */}
          <FormHeader
            title="Voucher giảm giá tùy chỉnh"
            subtitle="Tạo voucher với các tùy chọn giảm giá và phần thưởng linh hoạt"
            onBack={handleCancel}
            status={voucher ? { label: voucher.status, color: "primary" } : undefined}
          />

          {/* Main Content */}
          <Box sx={{ paddingInline: 3, pb: 10 }}>
            <Grid container spacing={3}>
              {/* Left Column - Main Form */}
              <Grid size={{ xs: 12, lg: 8 }}>
                <Stack spacing={3}>
                  {/* Basic Information Section */}
                  <FormSection
                    title="Thông tin cơ bản"
                    subtitle="Thiết lập thông tin chính cho voucher tùy chỉnh"
                    icon={<InfoIcon />}
                    isLoading={!isDoneInitDetail && !!voucher}
                  >
                    <Stack spacing={3}>
                      <Box>
                        <Typography
                          sx={{
                            fontSize: "0.875rem",
                            fontWeight: 500,
                            color: theme.palette.text.primary,
                            mb: 1,
                            display: "flex",
                            alignItems: "center",
                          }}
                        >
                          Tên voucher
                          <Typography
                            component="span"
                            sx={{ color: theme.palette.error.main, ml: 0.5 }}
                          >
                            *
                          </Typography>
                        </Typography>
                        <Controller
                          name="voucherName"
                          control={control}
                          render={({ field }) => (
                            <TextField
                              {...field}
                              placeholder="Nhập tên voucher"
                              error={!!errors.voucherName}
                              helperText={errors.voucherName?.message}
                              fullWidth
                              variant="outlined"
                              sx={{
                                "& .MuiOutlinedInput-root": {
                                  borderRadius: 2,
                                  backgroundColor: theme.palette.background.paper,
                                  "&:hover .MuiOutlinedInput-notchedOutline": {
                                    borderColor: alpha(theme.palette.primary.main, 0.4),
                                  },
                                  "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                                    borderWidth: 1,
                                  },
                                  "& .MuiOutlinedInput-notchedOutline": {
                                    borderColor: alpha(theme.palette.divider, 0.3),
                                    transition: "border-color 0.2s ease-in-out",
                                  },
                                },
                                "& .MuiFormHelperText-root": {
                                  fontSize: "0.75rem",
                                  mt: 0.75,
                                },
                              }}
                            />
                          )}
                        />
                      </Box>

                      <Box>
                        <Typography
                          sx={{
                            fontSize: "0.875rem",
                            fontWeight: 500,
                            color: theme.palette.text.primary,
                            mb: 2,
                          }}
                        >
                          Ảnh voucher
                          <Typography
                            component="span"
                            sx={{ color: theme.palette.error.main, ml: 0.5 }}
                          >
                            *
                          </Typography>
                        </Typography>
                        <CommonMediaUpload
                          caption="Thêm ảnh voucher"
                          maxFiles={1}
                          accept={{ "image/*": [".png", ".jpg", ".jpeg"] }}
                          maxSize={FILE_SIZE_2MB}
                          existingFiles={existingFiles}
                          setExistingFiles={setExistingFiles}
                          localFiles={localFiles}
                          setLocalFiles={setLocalFiles}
                          onFilesChange={handleFilesChange}
                          onRemove={handleRemove}
                          defaultGroupId={defaultGroupId}
                          isShowPreviewImage={true}
                          errorMsg={errorMsg}
                          setErrorMsg={setErrorMsg}
                        />
                      </Box>
                    </Stack>
                  </FormSection>

                  {/* Voucher Code Configuration */}
                  {!voucher && (
                    <FormSection
                      title="Cấu hình mã voucher"
                      subtitle="Chọn loại mã voucher và thiết lập mã"
                    >
                      <Stack spacing={3}>
                        <FormRadioGroup
                          name="codeType"
                          control={control}
                          label="Loại mã voucher"
                          required
                          variant="card"
                          options={[
                            {
                              value: CODE_TYPE.COMMON,
                              label: "Mã chung",
                              description:
                                "Một mã voucher có thể được sử dụng bởi nhiều khách hàng",
                            },
                            {
                              value: CODE_TYPE.UNIQUE,
                              label: "Mã riêng",
                              description: "Mỗi khách hàng sẽ có một mã voucher riêng biệt",
                            },
                          ]}
                          error={errors.codeType}
                        />

                        {watch("codeType") === CODE_TYPE.COMMON && (
                          <Box>
                            <Typography
                              sx={{
                                fontSize: "0.875rem",
                                fontWeight: 500,
                                color: theme.palette.text.primary,
                                mb: 1,
                                display: "flex",
                                alignItems: "center",
                              }}
                            >
                              Mã voucher
                              <Typography
                                component="span"
                                sx={{ color: theme.palette.error.main, ml: 0.5 }}
                              >
                                *
                              </Typography>
                            </Typography>
                            <Controller
                              name="voucherCode"
                              control={control}
                              render={({ field }) => (
                                <TextField
                                  {...field}
                                  placeholder="Nhập mã voucher"
                                  error={!!errors.voucherCode}
                                  helperText={errors.voucherCode?.message}
                                  fullWidth
                                  variant="outlined"
                                  InputProps={{
                                    endAdornment: (
                                      <InputAdornment position="end">
                                        <Button
                                          onClick={handleGenerateCode}
                                          size="small"
                                          variant="outlined"
                                          sx={{
                                            minWidth: "auto",
                                            px: 2,
                                            height: "32px",
                                            fontSize: "0.75rem",
                                            borderRadius: 1.5,
                                          }}
                                        >
                                          Tạo mã
                                        </Button>
                                      </InputAdornment>
                                    ),
                                  }}
                                  sx={{
                                    "& .MuiOutlinedInput-root": {
                                      borderRadius: 2,
                                      backgroundColor: theme.palette.background.paper,
                                      "&:hover .MuiOutlinedInput-notchedOutline": {
                                        borderColor: alpha(theme.palette.primary.main, 0.4),
                                      },
                                      "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                                        borderWidth: 1,
                                      },
                                      "& .MuiOutlinedInput-notchedOutline": {
                                        borderColor: alpha(theme.palette.divider, 0.3),
                                        transition: "border-color 0.2s ease-in-out",
                                      },
                                    },
                                    "& .MuiFormHelperText-root": {
                                      fontSize: "0.75rem",
                                      mt: 0.75,
                                    },
                                  }}
                                />
                              )}
                            />
                          </Box>
                        )}

                        {watch("codeType") === CODE_TYPE.UNIQUE && (
                          <Box>
                            <Typography
                              sx={{
                                fontSize: "0.875rem",
                                fontWeight: 500,
                                color: theme.palette.text.primary,
                                mb: 1,
                                display: "flex",
                                alignItems: "center",
                              }}
                            >
                              Tiền tố mã voucher
                              <Typography
                                component="span"
                                sx={{ color: theme.palette.error.main, ml: 0.5 }}
                              >
                                *
                              </Typography>
                            </Typography>
                            <Controller
                              name="voucherCodePrefix"
                              control={control}
                              render={({ field }) => (
                                <TextField
                                  {...field}
                                  placeholder="Nhập tiền tố mã voucher"
                                  error={!!errors.voucherCodePrefix}
                                  helperText={errors.voucherCodePrefix?.message}
                                  fullWidth
                                  variant="outlined"
                                  sx={{
                                    "& .MuiOutlinedInput-root": {
                                      borderRadius: 2,
                                      backgroundColor: theme.palette.background.paper,
                                      "&:hover .MuiOutlinedInput-notchedOutline": {
                                        borderColor: alpha(theme.palette.primary.main, 0.4),
                                      },
                                      "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                                        borderWidth: 1,
                                      },
                                      "& .MuiOutlinedInput-notchedOutline": {
                                        borderColor: alpha(theme.palette.divider, 0.3),
                                        transition: "border-color 0.2s ease-in-out",
                                      },
                                    },
                                    "& .MuiFormHelperText-root": {
                                      fontSize: "0.75rem",
                                      mt: 0.75,
                                    },
                                  }}
                                />
                              )}
                            />
                          </Box>
                        )}
                      </Stack>
                    </FormSection>
                  )}

                  {/* Distribution Type for Unique Vouchers */}
                  {watch("codeType") === CODE_TYPE.UNIQUE && (
                    <FormSection
                      title="Hình thức phát hành"
                      subtitle="Chọn cách thức phát hành mã voucher"
                    >
                      {!voucher ? (
                        <FormRadioGroup
                          name="distributionType"
                          control={control}
                          label="Loại phát hành"
                          required
                          variant="card"
                          options={[
                            {
                              value: TYPE_DISTRIBUTION.IMMEDIATE,
                              label: "Phát hành ngay lập tức",
                              description: "Tạo tất cả mã voucher ngay khi lưu",
                            },
                            {
                              value: TYPE_DISTRIBUTION.ON_RECEIVE,
                              label: "Khi người dùng nhận",
                              description: "Tạo mã voucher khi khách hàng nhận voucher",
                            },
                          ]}
                          error={errors.distributionType}
                        />
                      ) : (
                        <Alert severity="info" sx={{ borderRadius: 2 }}>
                          <Typography variant="body2">
                            <strong>Hình thức phát hành:</strong>{" "}
                            {watch("distributionType") === TYPE_DISTRIBUTION.IMMEDIATE
                              ? "Phát hành ngay lập tức"
                              : "Khi người dùng nhận"}
                          </Typography>
                        </Alert>
                      )}
                    </FormSection>
                  )}
                  {/* Customer Condition Section */}
                  <FormSection
                    title="Điều kiện khách hàng"
                    subtitle="Thiết lập điều kiện áp dụng cho khách hàng"
                  >
                    <PromotionVoucherBox3 voucher={voucher} showProductSelection={false} />
                  </FormSection>

                  {/* Reward Configuration Section */}
                  <FormSection
                    title="Cấu hình phần thưởng"
                    subtitle="Thiết lập phần thưởng cho voucher tùy chỉnh"
                  >
                    <CustomRewardConfigurationBox />
                  </FormSection>
                </Stack>
              </Grid>

              {/* Right Column - Summary */}
              <Grid size={{ xs: 12, lg: 4 }}>
                <Stack spacing={3}>
                  <FormSection title="Tóm tắt voucher" subtitle="Xem trước thông tin voucher">
                    <Stack spacing={2}>
                      <Box>
                        <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
                          Loại voucher
                        </Typography>
                        <Chip
                          label={`QR tích điểm ${
                            watch("codeType") === CODE_TYPE.COMMON ? "mã chung" : "mã riêng"
                          }`}
                          color="primary"
                          variant="outlined"
                          size="small"
                        />
                      </Box>

                      <Box>
                        <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
                          Số lượng phát hành
                        </Typography>
                        <Typography variant="body2">
                          {String(watch("quantity") || 100)} voucher
                        </Typography>
                      </Box>

                      <Box>
                        <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
                          Phần thưởng
                        </Typography>
                        <Typography variant="body2">
                          {(() => {
                            const rewardType = watch("rewardType");
                            const isFixed = watch("isFixedRewardPoint");
                            const rewardPoint = watch("rewardPoint") || 0;
                            const rewardPointMin = watch("rewardPointMin") || 0;
                            const rewardPointMax = watch("rewardPointMax") || 0;

                            switch (rewardType) {
                              case REWARD_TYPE.POINT:
                                return isFixed
                                  ? `Điểm thưởng cố định: ${rewardPoint} điểm`
                                  : `Điểm thưởng linh hoạt: ${rewardPointMin}-${rewardPointMax} điểm`;
                              case REWARD_TYPE.PRODUCT:
                                const rewardProducts = watch("rewardProducts") || [];
                                const productCodes = rewardProducts
                                  .map((p) => p.itemsCode)
                                  .filter(Boolean);
                                return `Sản phẩm thưởng: ${productCodes.join(", ")}`;
                              default:
                                return "Chưa chọn loại phần thưởng";
                            }
                          })()}
                        </Typography>
                      </Box>
                    </Stack>
                  </FormSection>

                  <FormSection
                    title="Hiệu lực voucher"
                    subtitle="Bật/tắt trạng thái hoạt động của voucher"
                  >
                    <Box
                      sx={{
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "space-between",
                      }}
                    >
                      <Typography variant="body2" sx={{ fontWeight: 500 }}>
                        {watch("status") === VOUCHER_STATUS.ACTIVED
                          ? "Voucher có hiệu lực"
                          : "Voucher không có hiệu lực"}
                      </Typography>
                      <Controller
                        name="status"
                        control={control}
                        render={({ field: { onChange, value } }) => (
                          <CustomSwitch
                            checked={value === VOUCHER_STATUS.ACTIVED}
                            onChange={(e) =>
                              onChange(
                                e.target.checked ? VOUCHER_STATUS.ACTIVED : VOUCHER_STATUS.INACTIVED
                              )
                            }
                          />
                        )}
                      />
                    </Box>
                  </FormSection>
                  <FormSection
                    title="Điều kiện sử dụng"
                    subtitle="Thiết lập các điều kiện và giới hạn sử dụng voucher"
                  >
                    <PromotionVoucherBox4 />
                  </FormSection>
                </Stack>
              </Grid>
            </Grid>

            {/* Form Actions */}
            <FormActions
              onCancel={handleCancel}
              onSave={handleSubmit(onSubmit)}
              isLoading={loading || localLoading}
              variant="fixed"
              additionalActions={voucher?.voucherId && <DeleteBox voucherId={voucher.voucherId} />}
            />
          </Box>
        </Box>
      </form>
    </FormProvider>
  );
}

const CustomRewardConfigurationBox = () => {
  const {
    control,
    formState: { errors },
    setValue,
    watch,
  } = useFormContext<CustomVoucherFormData>();
  const theme = useTheme();

  const [openProductPopup, setOpenProductPopup] = useState(false);
  const [selectedProducts, setSelectedProducts] = useState<any[]>([]);

  // Load selected products when editing voucher
  useEffect(() => {
    const rewardGiftIds = watch("rewardGiftIds");
    const rewardProducts = watch("rewardProducts");

    if (rewardProducts && rewardProducts.length > 0) {
      const formattedProducts = rewardProducts.map((product: any) => ({
        ...product,
        image: product.images?.[0]?.link || product.image || "",
        itemsId: product.itemsId,
        itemsName: product.itemsName || "",
        price: 0,
      }));
      setSelectedProducts(formattedProducts);
    } else if (rewardGiftIds && rewardGiftIds.length > 0) {
      setSelectedProducts(
        rewardGiftIds.map((id: string) => ({
          itemsId: id,
          itemsName: "",
          price: 0,
          image: "",
          quantity: 0,
        }))
      );
    }
  }, [watch("rewardGiftIds"), watch("rewardProducts")]);

  const handleRewardTypeChange = (value: string) => {
    const isFixed = value === "true";
    setValue("isFixedRewardPoint", isFixed);
    if (isFixed) {
      setValue("rewardPointMin", 0);
      setValue("rewardPointMax", 0);
    } else {
      setValue("rewardPoint", 0);
    }
  };

  const handleAddProducts = (products: any[]) => {
    if (products) {
      const formattedProducts = products.map((product) => ({
        ...product,
        price: product.isVariant ? product.listVariant?.[0]?.price || 0 : product.price || 0,
        image: product.images?.[0]?.link || product.image || "",
        itemsId: product.itemsId,
        itemsName: product.itemsName || "",
      }));

      setSelectedProducts(formattedProducts);
      setValue(
        "rewardGiftIds",
        formattedProducts.map((p) => p.itemsId)
      );
      setValue("rewardProducts", formattedProducts);
    }
    setOpenProductPopup(false);
  };

  const handleRemoveProduct = (productId: string) => {
    const newProducts = selectedProducts.filter((p) => p.itemsId !== productId);
    setSelectedProducts(newProducts);
    setValue(
      "rewardGiftIds",
      newProducts.map((p) => p.itemsId)
    );
    setValue("rewardProducts", newProducts);
  };

  const selectedProductIds = selectedProducts.map((p) => p.itemsId);

  return (
    <Stack spacing={3}>
      <FormRadioGroup
        name="rewardType"
        control={control}
        label="Loại phần thưởng"
        required
        variant="card"
        options={[
          {
            value: REWARD_TYPE.POINT,
            label: "Điểm thưởng",
            description: "Tặng điểm thưởng cho khách hàng",
          },
          {
            value: REWARD_TYPE.PRODUCT,
            label: "Sản phẩm",
            description: "Tặng sản phẩm cụ thể cho khách hàng",
            disabled: true, // Temporarily disabled
          },
        ]}
        error={errors.rewardType}
      />

      {watch("rewardType") === REWARD_TYPE.POINT && (
        <Stack spacing={3}>
          <FormRadioGroup
            name="isFixedRewardPoint"
            control={control}
            label="Cách tính điểm thưởng"
            required
            variant="card"
            onChange={handleRewardTypeChange}
            options={[
              {
                value: "true",
                label: "Cố định",
                description: "Tặng một số điểm cố định",
              },
              {
                value: "false",
                label: "Linh hoạt",
                description: "Tặng điểm trong khoảng từ tối thiểu đến tối đa",
              },
            ]}
          />

          {watch("isFixedRewardPoint") == true ? (
            <Box>
              <Typography
                sx={{
                  fontSize: "0.875rem",
                  fontWeight: 500,
                  color: theme.palette.text.primary,
                  mb: 1,
                  display: "flex",
                  alignItems: "center",
                }}
              >
                Số điểm thưởng
                <Typography component="span" sx={{ color: theme.palette.error.main, ml: 0.5 }}>
                  *
                </Typography>
              </Typography>
              <Controller
                name="rewardPoint"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    placeholder="Nhập số điểm thưởng"
                    type="number"
                    error={!!errors.rewardPoint}
                    helperText={errors.rewardPoint?.message}
                    fullWidth
                    variant="outlined"
                    InputProps={{
                      endAdornment: <InputAdornment position="end">điểm</InputAdornment>,
                    }}
                    sx={{
                      "& .MuiOutlinedInput-root": {
                        borderRadius: 2,
                        backgroundColor: theme.palette.background.paper,
                        "&:hover .MuiOutlinedInput-notchedOutline": {
                          borderColor: alpha(theme.palette.primary.main, 0.4),
                        },
                        "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                          borderWidth: 1,
                        },
                        "& .MuiOutlinedInput-notchedOutline": {
                          borderColor: alpha(theme.palette.divider, 0.3),
                          transition: "border-color 0.2s ease-in-out",
                        },
                      },
                      "& .MuiFormHelperText-root": {
                        fontSize: "0.75rem",
                        mt: 0.75,
                      },
                    }}
                  />
                )}
              />
            </Box>
          ) : (
            <Stack direction="row" spacing={2}>
              <Box sx={{ flex: 1 }}>
                <Typography
                  sx={{
                    fontSize: "0.875rem",
                    fontWeight: 500,
                    color: theme.palette.text.primary,
                    mb: 1,
                    display: "flex",
                    alignItems: "center",
                  }}
                >
                  Điểm thưởng tối thiểu
                  <Typography component="span" sx={{ color: theme.palette.error.main, ml: 0.5 }}>
                    *
                  </Typography>
                </Typography>
                <Controller
                  name="rewardPointMin"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      placeholder="Nhập điểm tối thiểu"
                      type="number"
                      error={!!errors.rewardPointMin}
                      helperText={errors.rewardPointMin?.message}
                      fullWidth
                      variant="outlined"
                      InputProps={{
                        endAdornment: <InputAdornment position="end">điểm</InputAdornment>,
                      }}
                      sx={{
                        "& .MuiOutlinedInput-root": {
                          borderRadius: 2,
                          backgroundColor: theme.palette.background.paper,
                          "&:hover .MuiOutlinedInput-notchedOutline": {
                            borderColor: alpha(theme.palette.primary.main, 0.4),
                          },
                          "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                            borderWidth: 1,
                          },
                          "& .MuiOutlinedInput-notchedOutline": {
                            borderColor: alpha(theme.palette.divider, 0.3),
                            transition: "border-color 0.2s ease-in-out",
                          },
                        },
                        "& .MuiFormHelperText-root": {
                          fontSize: "0.75rem",
                          mt: 0.75,
                        },
                      }}
                    />
                  )}
                />
              </Box>
              <Box sx={{ flex: 1 }}>
                <Typography
                  sx={{
                    fontSize: "0.875rem",
                    fontWeight: 500,
                    color: theme.palette.text.primary,
                    mb: 1,
                    display: "flex",
                    alignItems: "center",
                  }}
                >
                  Điểm thưởng tối đa
                  <Typography component="span" sx={{ color: theme.palette.error.main, ml: 0.5 }}>
                    *
                  </Typography>
                </Typography>
                <Controller
                  name="rewardPointMax"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      placeholder="Nhập điểm tối đa"
                      type="number"
                      error={!!errors.rewardPointMax}
                      helperText={errors.rewardPointMax?.message}
                      fullWidth
                      variant="outlined"
                      InputProps={{
                        endAdornment: <InputAdornment position="end">điểm</InputAdornment>,
                      }}
                      sx={{
                        "& .MuiOutlinedInput-root": {
                          borderRadius: 2,
                          backgroundColor: theme.palette.background.paper,
                          "&:hover .MuiOutlinedInput-notchedOutline": {
                            borderColor: alpha(theme.palette.primary.main, 0.4),
                          },
                          "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                            borderWidth: 1,
                          },
                          "& .MuiOutlinedInput-notchedOutline": {
                            borderColor: alpha(theme.palette.divider, 0.3),
                            transition: "border-color 0.2s ease-in-out",
                          },
                        },
                        "& .MuiFormHelperText-root": {
                          fontSize: "0.75rem",
                          mt: 0.75,
                        },
                      }}
                    />
                  )}
                />
              </Box>
            </Stack>
          )}
        </Stack>
      )}

      {watch("rewardType") === REWARD_TYPE.PRODUCT && (
        <Stack spacing={3}>
          <Box>
            <Button variant="outlined" onClick={() => setOpenProductPopup(true)} sx={{ mb: 2 }}>
              Chọn sản phẩm thưởng
            </Button>

            {selectedProducts.length > 0 && (
              <Stack spacing={2}>
                <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                  Sản phẩm đã chọn ({selectedProducts.length})
                </Typography>
                {selectedProducts.map((product) => (
                  <Box
                    key={product.itemsId}
                    sx={{
                      p: 2,
                      border: `1px solid ${alpha(theme.palette.divider, 0.3)}`,
                      borderRadius: 2,
                      position: "relative",
                      display: "flex",
                      alignItems: "center",
                      gap: 2,
                    }}
                  >
                    <Button
                      size="small"
                      onClick={() => handleRemoveProduct(product.itemsId)}
                      sx={{
                        position: "absolute",
                        top: 8,
                        right: 8,
                        minWidth: "auto",
                        p: 0.5,
                      }}
                    >
                      <CloseIcon fontSize="small" />
                    </Button>
                    {product.image && (
                      <Box
                        component="img"
                        src={product.image}
                        alt={product.itemsName}
                        sx={{ width: 60, height: 60, objectFit: "cover", borderRadius: 1 }}
                      />
                    )}
                    <Box sx={{ flex: 1 }}>
                      <Typography variant="body2" sx={{ fontWeight: 500 }}>
                        {product.itemsName}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        Mã: {product.itemsCode}
                      </Typography>
                    </Box>
                  </Box>
                ))}
              </Stack>
            )}
          </Box>
        </Stack>
      )}

      <Box>
        <Typography
          sx={{
            fontSize: "0.875rem",
            fontWeight: 500,
            color: theme.palette.text.primary,
            mb: 1,
            display: "flex",
            alignItems: "center",
          }}
        >
          Tổng số lượng voucher
          <Typography component="span" sx={{ color: theme.palette.error.main, ml: 0.5 }}>
            *
          </Typography>
        </Typography>
        <Controller
          name="quantity"
          control={control}
          render={({ field }) => (
            <TextField
              {...field}
              placeholder="Nhập tổng số lượng"
              type="number"
              error={!!errors.quantity}
              helperText={errors.quantity?.message}
              fullWidth
              variant="outlined"
              sx={{
                "& .MuiOutlinedInput-root": {
                  borderRadius: 2,
                  backgroundColor: theme.palette.background.paper,
                  "&:hover .MuiOutlinedInput-notchedOutline": {
                    borderColor: alpha(theme.palette.primary.main, 0.4),
                  },
                  "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                    borderWidth: 1,
                  },
                  "& .MuiOutlinedInput-notchedOutline": {
                    borderColor: alpha(theme.palette.divider, 0.3),
                    transition: "border-color 0.2s ease-in-out",
                  },
                },
                "& .MuiFormHelperText-root": {
                  fontSize: "0.75rem",
                  mt: 0.75,
                },
              }}
            />
          )}
        />
      </Box>

      <PopupAddProduct
        open={openProductPopup}
        onClose={() => setOpenProductPopup(false)}
        onAddProducts={handleAddProducts}
        selectedProductIds={selectedProductIds}
      />
    </Stack>
  );
};

const DeleteBox = ({ voucherId }) => {
  const [openDialog, setOpenDialog] = useState(false);
  const { deleteVoucher } = useVoucher();
  const router = useRouter();
  const snackbar = useSnackbar();
  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const clickOpenDialog = () => {
    setOpenDialog(true);
  };

  const handleConfirmDelete = async () => {
    const response = await deleteVoucher([voucherId]);
    if (response?.data.result) {
      await router.push(paths.marketing.vouchers.list);
      snackbar.success("Xóa voucher thành công");
    }
  };
  return (
    <>
      <Button variant="contained" color="error" onClick={clickOpenDialog}>
        Xóa voucher
      </Button>

      <TitleDialog
        title="Xóa voucher"
        open={openDialog}
        handleClose={handleCloseDialog}
        submitBtnTitle="Xác nhận"
        handleSubmit={handleConfirmDelete}
      >
        <Typography>Bạn có chắc muốn xóa voucher này?</Typography>
      </TitleDialog>
    </>
  );
};
