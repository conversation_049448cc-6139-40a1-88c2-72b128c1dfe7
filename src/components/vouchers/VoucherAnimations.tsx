import React from 'react';
import { 
  Fade, 
  Slide, 
  Zoom, 
  Grow,
  Collapse,
  Box,
  keyframes,
  styled
} from '@mui/material';
import { TransitionGroup } from 'react-transition-group';

// Keyframe animations
const slideInUp = keyframes`
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
`;

const slideInLeft = keyframes`
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
`;

const slideInRight = keyframes`
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
`;

const fadeInScale = keyframes`
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
`;

const pulse = keyframes`
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
`;

const shimmer = keyframes`
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
`;

// Styled components with animations
export const AnimatedBox = styled(Box)<{ 
  animation?: 'slideInUp' | 'slideInLeft' | 'slideInRight' | 'fadeInScale' | 'pulse';
  delay?: number;
}>`
  animation: ${({ animation, delay = 0 }) => {
    switch (animation) {
      case 'slideInUp':
        return `${slideInUp} 0.6s ease-out ${delay}s both`;
      case 'slideInLeft':
        return `${slideInLeft} 0.6s ease-out ${delay}s both`;
      case 'slideInRight':
        return `${slideInRight} 0.6s ease-out ${delay}s both`;
      case 'fadeInScale':
        return `${fadeInScale} 0.5s ease-out ${delay}s both`;
      case 'pulse':
        return `${pulse} 2s ease-in-out infinite`;
      default:
        return 'none';
    }
  }};
`;

export const ShimmerBox = styled(Box)`
  background: linear-gradient(
    90deg,
    #f0f0f0 25%,
    #e0e0e0 50%,
    #f0f0f0 75%
  );
  background-size: 200px 100%;
  animation: ${shimmer} 1.5s infinite;
`;

// Transition components
interface FadeTransitionProps {
  children: React.ReactNode;
  in?: boolean;
  timeout?: number;
  delay?: number;
}

export const FadeTransition: React.FC<FadeTransitionProps> = ({ 
  children, 
  in: inProp = true, 
  timeout = 300,
  delay = 0 
}) => (
  <Fade in={inProp} timeout={timeout} style={{ transitionDelay: `${delay}ms` }}>
    <div>{children}</div>
  </Fade>
);

export const SlideUpTransition: React.FC<FadeTransitionProps> = ({ 
  children, 
  in: inProp = true, 
  timeout = 300,
  delay = 0 
}) => (
  <Slide 
    direction="up" 
    in={inProp} 
    timeout={timeout} 
    style={{ transitionDelay: `${delay}ms` }}
  >
    <div>{children}</div>
  </Slide>
);

export const ZoomTransition: React.FC<FadeTransitionProps> = ({ 
  children, 
  in: inProp = true, 
  timeout = 300,
  delay = 0 
}) => (
  <Zoom in={inProp} timeout={timeout} style={{ transitionDelay: `${delay}ms` }}>
    <div>{children}</div>
  </Zoom>
);

export const GrowTransition: React.FC<FadeTransitionProps> = ({ 
  children, 
  in: inProp = true, 
  timeout = 300,
  delay = 0 
}) => (
  <Grow in={inProp} timeout={timeout} style={{ transitionDelay: `${delay}ms` }}>
    <div>{children}</div>
  </Grow>
);

// Staggered animation for lists
interface StaggeredListProps {
  children: React.ReactNode[];
  staggerDelay?: number;
  animation?: 'fade' | 'slideUp' | 'zoom' | 'grow';
}

export const StaggeredList: React.FC<StaggeredListProps> = ({ 
  children, 
  staggerDelay = 100,
  animation = 'fade'
}) => {
  const getTransitionComponent = (child: React.ReactNode, index: number) => {
    const delay = index * staggerDelay;
    
    switch (animation) {
      case 'slideUp':
        return (
          <SlideUpTransition key={index} delay={delay}>
            {child}
          </SlideUpTransition>
        );
      case 'zoom':
        return (
          <ZoomTransition key={index} delay={delay}>
            {child}
          </ZoomTransition>
        );
      case 'grow':
        return (
          <GrowTransition key={index} delay={delay}>
            {child}
          </GrowTransition>
        );
      default:
        return (
          <FadeTransition key={index} delay={delay}>
            {child}
          </FadeTransition>
        );
    }
  };

  return (
    <>
      {children.map((child, index) => getTransitionComponent(child, index))}
    </>
  );
};

// Collapse transition for expandable content
interface CollapseTransitionProps {
  children: React.ReactNode;
  in: boolean;
  timeout?: number;
}

export const CollapseTransition: React.FC<CollapseTransitionProps> = ({ 
  children, 
  in: inProp, 
  timeout = 300 
}) => (
  <Collapse in={inProp} timeout={timeout}>
    {children}
  </Collapse>
);

// Page transition wrapper
interface PageTransitionProps {
  children: React.ReactNode;
  direction?: 'left' | 'right' | 'up' | 'down';
}

export const PageTransition: React.FC<PageTransitionProps> = ({ 
  children, 
  direction = 'up' 
}) => (
  <Slide direction={direction} in={true} timeout={500}>
    <Box>{children}</Box>
  </Slide>
);

// Loading animation
export const LoadingPulse: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <AnimatedBox animation="pulse">
    {children}
  </AnimatedBox>
);

// Hover animations
export const HoverScale = styled(Box)`
  transition: transform 0.2s ease-in-out;
  &:hover {
    transform: scale(1.02);
  }
`;

export const HoverLift = styled(Box)`
  transition: all 0.3s ease;
  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }
`;

// Tab transition
interface TabTransitionProps {
  children: React.ReactNode;
  value: number;
  index: number;
}

export const TabTransition: React.FC<TabTransitionProps> = ({ 
  children, 
  value, 
  index 
}) => (
  <FadeTransition in={value === index} timeout={300}>
    <Box sx={{ display: value === index ? 'block' : 'none' }}>
      {children}
    </Box>
  </FadeTransition>
);
