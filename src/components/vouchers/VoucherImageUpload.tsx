import React, { useState, useEffect } from "react";
import { Box, Typography } from "@mui/material";
import { useFormContext } from "react-hook-form";
import useSnackbar from "@/src/hooks/use-snackbar";
import { useMedia } from "@/src/api/hooks/media/use-media";
import { ImageProcessor } from "@/src/utils/image-processor";
import { FileDropzone } from "../file-dropzone";
import { FILE_SIZE_2MB, IMAGE_TYPE } from "@/src/constants/constant";
import { CreateFileGroupRequest, GetGroupFileRequest, RefType } from "@/src/api/types/media.types";
import { useStoreId } from "@/src/hooks/use-store-id";

interface VoucherImageUploadProps {
  name: string;
  label?: string;
}

const VoucherImageUpload: React.FC<VoucherImageUploadProps> = ({ name, label = "Ảnh voucher" }) => {
  const { setValue, watch } = useFormContext();
  const [previewUrls, setPreviewUrls] = useState<string[]>([]);
  const snackbar = useSnackbar();
  const { uploadFile, getGroups } = useMedia();
  const [defaultGroupId, setDefaultGroupId] = useState<string>("");
  const imageValue = watch(name);
  const storeId = useStoreId();

  // Load image when editing
  useEffect(() => {
    if (imageValue) {
      setPreviewUrls([imageValue]);
    } else {
      setPreviewUrls([]);
    }
  }, [imageValue]);

  useEffect(() => {
    const fetchDefaultGroup = async () => {
      try {
        const data: GetGroupFileRequest = {
          ShopId: storeId,
          Skip: 0,
          Limit: 10,
        };
        const response = await getGroups(data);
        if (response?.data?.data?.length > 0) {
          setDefaultGroupId(response.data.data[0].groupFileId);
        }
      } catch (error) {
        // eslint-disable-next-line no-console
        console.error("Error fetching groups:", error);
      }
    };

    fetchDefaultGroup();
  }, []);

  const handleImageDrop = async (acceptedFiles: File[]) => {
    if (acceptedFiles && acceptedFiles[0]) {
      const selectedFile = acceptedFiles[0];

      // Validate file size
      if (selectedFile.size > FILE_SIZE_2MB) {
        snackbar.error(
          `Kích thước ảnh không được vượt quá ${(FILE_SIZE_2MB / 1024 / 1024).toFixed(1)}MB.`
        );
        return;
      }

      // Validate file type
      const allowedTypes = ["image/png", "image/jpeg", "image/jpg", "image/gif"];
      if (!allowedTypes.includes(selectedFile.type)) {
        snackbar.warning("Định dạng ảnh không hợp lệ. Chỉ chấp nhận PNG, JPG, JPEG, GIF.");
        return;
      }

      // Xử lý ảnh trước khi upload
      let processedFile = selectedFile;
      try {
        processedFile = await ImageProcessor.processImage(selectedFile);
      } catch (error) {
        snackbar.warning("Có lỗi khi xử lý ảnh. Vui lòng thử lại.");
        return;
      }

      // Create preview
      const previewUrl = URL.createObjectURL(processedFile);
      setPreviewUrls([previewUrl]);

      // Upload file
      try {
        const data: CreateFileGroupRequest = {
          FileUpload: processedFile,
          GroupFileId: defaultGroupId,
          ShopId: storeId,
          RefType: RefType.Voucher,
          RefId: "",
        };
        const response = await uploadFile(data);
        if (response?.data?.link) {
          setValue(name, response.data.link);
        }
      } catch (error) {
        snackbar.warning("Có lỗi xảy ra khi tải lên ảnh. Vui lòng thử lại.");
      }
    }
  };

  const handleImageRemove = () => {
    setPreviewUrls([]);
    setValue(name, "");
  };

  return (
    <Box>
      <Typography variant="subtitle1" sx={{ mb: 2 }}>
        {label}
      </Typography>
      <FileDropzone
        accept={{ "image/*": IMAGE_TYPE }}
        caption="Kéo thả hoặc click để tải ảnh lên(Tối đa 1MB)"
        maxFiles={1}
        maxSize={FILE_SIZE_2MB}
        onDrop={handleImageDrop}
        onRemove={handleImageRemove}
        showError={false}
        existingFiles={previewUrls}
        previewUrlsImg={previewUrls}
        setPreviewUrlsImg={setPreviewUrls}
      />
    </Box>
  );
};

export default VoucherImageUpload;
