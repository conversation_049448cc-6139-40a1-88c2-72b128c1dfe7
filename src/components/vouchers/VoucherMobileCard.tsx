import React from 'react';
import {
  Box,
  Card,
  Typography,
  IconButton,
  Stack,
  Chip,
  Button,
  Avatar,
  Divider,
  Checkbox,
  useTheme,
  useMediaQuery
} from '@mui/material';
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  QrCode as QrCodeIcon,
  CalendarToday as CalendarIcon,
  LocalOffer as OfferIcon
} from '@mui/icons-material';
import { StatusBadge } from '@/src/pages/dashboard/marketing/vouchers/list';
import TruncatedText from '@/src/components/truncated-text/truncated-text';

interface VoucherMobileCardProps {
  voucher: any;
  index: number;
  page: number;
  rowsPerPage: number;
  selected: boolean;
  onSelect: (event: React.ChangeEvent<HTMLInputElement>, id: string) => void;
  onEdit: (voucherType: string, voucherId: string) => void;
  onDelete: (voucher: any) => void;
  onQRCode: (event: React.MouseEvent, data: any) => void;
  currentShop: any;
  canEdit: boolean;
  canDelete: boolean;
}

export const VoucherMobileCard: React.FC<VoucherMobileCardProps> = ({
  voucher,
  index,
  page,
  rowsPerPage,
  selected,
  onSelect,
  onEdit,
  onDelete,
  onQRCode,
  currentShop,
  canEdit,
  canDelete
}) => {
  const theme = useTheme();
  
  const getVoucherTypeText = (type: string) => {
    switch (type) {
      case 'Custom':
        return 'Tích điểm QR';
      case 'Promotion':
        return 'Giảm giá';
      case 'Transport':
        return 'Vận chuyển';
      default:
        return 'Khác';
    }
  };

  const getVoucherTypeColor = (type: string) => {
    switch (type) {
      case 'Custom':
        return '#9C27B0';
      case 'Promotion':
        return '#4CAF50';
      case 'Transport':
        return '#FF9800';
      default:
        return '#2196F3';
    }
  };

  return (
    <Card
      sx={{
        p: 2,
        mb: 2,
        borderRadius: 3,
        border: '1px solid',
        borderColor: selected ? 'primary.main' : 'divider',
        backgroundColor: selected ? 'primary.50' : 'background.paper',
        transition: 'all 0.2s ease',
        '&:hover': {
          boxShadow: theme.shadows[4],
          transform: 'translateY(-1px)'
        }
      }}
    >
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 2 }}>
        <Checkbox
          checked={selected}
          onChange={(event) => onSelect(event, voucher.voucherId)}
          size="small"
          sx={{ mt: -0.5, mr: 1 }}
        />
        
        <Avatar
          src={voucher.image?.link || currentShop?.shopLogo?.link}
          sx={{ 
            width: 48, 
            height: 48, 
            borderRadius: 2,
            mr: 2
          }}
        >
          <OfferIcon />
        </Avatar>
        
        <Box sx={{ flex: 1, minWidth: 0 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
            <Typography
              variant="body2"
              color="text.secondary"
              sx={{ fontSize: '0.75rem' }}
            >
              #{page * rowsPerPage + index + 1}
            </Typography>
            <Chip
              label={getVoucherTypeText(voucher.voucherType)}
              size="small"
              sx={{
                height: 20,
                fontSize: '0.7rem',
                backgroundColor: `${getVoucherTypeColor(voucher.voucherType)}15`,
                color: getVoucherTypeColor(voucher.voucherType),
                border: `1px solid ${getVoucherTypeColor(voucher.voucherType)}30`
              }}
            />
          </Box>
          
          <TruncatedText
            text={voucher.voucherName}
            typographyProps={{ 
              fontWeight: 600, 
              fontSize: '1rem',
              lineHeight: 1.3,
              mb: 0.5
            }}
          />
          
          <TruncatedText
            text={voucher.voucherDetails?.[0]?.voucherCode || 'N/A'}
            typographyProps={{ 
              fontSize: '0.875rem',
              color: 'text.secondary',
              fontFamily: 'monospace'
            }}
          />
        </Box>
        
        <StatusBadge status={voucher.status} size="small" />
      </Box>

      <Divider sx={{ my: 1.5 }} />

      {/* Stats */}
      <Stack direction="row" spacing={2} sx={{ mb: 2 }}>
        <Box sx={{ textAlign: 'center', flex: 1 }}>
          <Typography variant="h6" color="primary" sx={{ fontSize: '1.1rem', fontWeight: 600 }}>
            {voucher.quantityUsed || 0}
          </Typography>
          <Typography variant="caption" color="text.secondary">
            Đã sử dụng
          </Typography>
        </Box>
        
        <Divider orientation="vertical" flexItem />
        
        <Box sx={{ textAlign: 'center', flex: 1 }}>
          <Typography variant="h6" color="success.main" sx={{ fontSize: '1.1rem', fontWeight: 600 }}>
            {voucher.quantity - (voucher.remainingStock || 0)}
          </Typography>
          <Typography variant="caption" color="text.secondary">
            Đã phát
          </Typography>
        </Box>
        
        <Divider orientation="vertical" flexItem />
        
        <Box sx={{ textAlign: 'center', flex: 1 }}>
          <Typography variant="h6" sx={{ fontSize: '1.1rem', fontWeight: 600 }}>
            {voucher.quantity || 0}
          </Typography>
          <Typography variant="caption" color="text.secondary">
            Tổng số
          </Typography>
        </Box>
      </Stack>

      {/* Date Info */}
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
        <CalendarIcon sx={{ fontSize: 16, color: 'text.secondary', mr: 1 }} />
        <Typography variant="body2" color="text.secondary">
          {voucher.startDate && voucher.endDate 
            ? `${new Date(voucher.startDate).toLocaleDateString('vi-VN')} - ${new Date(voucher.endDate).toLocaleDateString('vi-VN')}`
            : voucher.isLongTerm 
            ? 'Không giới hạn thời gian'
            : 'Chưa thiết lập'
          }
        </Typography>
      </Box>

      {/* Actions */}
      <Stack direction="row" spacing={1} justifyContent="space-between">
        <Button
          variant="outlined"
          size="small"
          startIcon={<QrCodeIcon />}
          onClick={(event) =>
            onQRCode(event, {
              voucherCode: voucher.voucherDetails?.[0]?.voucherCode,
              voucherCodeLink: voucher.voucherDetails?.[0]?.voucherCodeLink,
            })
          }
          sx={{
            borderRadius: 2,
            textTransform: 'none',
            fontSize: '0.8rem',
            flex: 1
          }}
        >
          QR Code
        </Button>
        
        <Stack direction="row" spacing={0.5}>
          <IconButton
            size="small"
            color="primary"
            onClick={() => onEdit(voucher.voucherType, voucher.voucherId)}
            disabled={!canEdit}
            sx={{
              backgroundColor: canEdit ? 'primary.50' : 'grey.100',
              '&:hover': {
                backgroundColor: canEdit ? 'primary.100' : 'grey.200'
              }
            }}
          >
            <EditIcon sx={{ fontSize: 18 }} />
          </IconButton>
          
          <IconButton
            size="small"
            color="error"
            onClick={() => onDelete(voucher)}
            disabled={!canDelete}
            sx={{
              backgroundColor: canDelete ? 'error.50' : 'grey.100',
              '&:hover': {
                backgroundColor: canDelete ? 'error.100' : 'grey.200'
              }
            }}
          >
            <DeleteIcon sx={{ fontSize: 18 }} />
          </IconButton>
        </Stack>
      </Stack>
    </Card>
  );
};

// Hook to determine if mobile view should be used
export const useMobileView = () => {
  const theme = useTheme();
  return useMediaQuery(theme.breakpoints.down('md'));
};
