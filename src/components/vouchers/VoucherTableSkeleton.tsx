import React from "react";
import {
  Box,
  Card,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Skeleton,
  Stack,
  Paper,
} from "@mui/material";

interface VoucherTableSkeletonProps {
  rows?: number;
  showActions?: boolean;
}

export const VoucherTableSkeleton: React.FC<VoucherTableSkeletonProps> = ({
  rows = 5,
  showActions = true,
}) => {
  return (
    <Card elevation={0} sx={{ border: "1px solid", borderColor: "divider" }}>
      {/* Header Skeleton */}
      <Box sx={{ p: 2, borderBottom: 1, borderColor: "divider" }}>
        <Stack direction="row" spacing={2} alignItems="center">
          <Skeleton variant="rectangular" width={24} height={24} />
          <Skeleton variant="text" width={200} height={24} />
          <Box sx={{ flexGrow: 1 }} />
          <Skeleton variant="rectangular" width={120} height={36} sx={{ borderRadius: 1 }} />
          <Skeleton variant="rectangular" width={100} height={36} sx={{ borderRadius: 1 }} />
        </Stack>
      </Box>

      {/* Table Skeleton */}
      <Table>
        <TableHead>
          <TableRow>
            <TableCell padding="checkbox">
              <Skeleton variant="rectangular" width={20} height={20} />
            </TableCell>
            <TableCell>
              <Skeleton variant="text" width={100} />
            </TableCell>
            <TableCell>
              <Skeleton variant="text" width={120} />
            </TableCell>
            <TableCell>
              <Skeleton variant="text" width={80} />
            </TableCell>
            <TableCell>
              <Skeleton variant="text" width={100} />
            </TableCell>
            <TableCell>
              <Skeleton variant="text" width={90} />
            </TableCell>
            {showActions && (
              <TableCell>
                <Skeleton variant="text" width={80} />
              </TableCell>
            )}
          </TableRow>
        </TableHead>
        <TableBody>
          {Array.from({ length: rows }).map((_, index) => (
            <TableRow key={index}>
              <TableCell padding="checkbox">
                <Skeleton variant="rectangular" width={20} height={20} />
              </TableCell>
              <TableCell>
                <Stack spacing={1}>
                  <Skeleton variant="text" width={150} height={20} />
                  <Skeleton variant="text" width={100} height={16} />
                </Stack>
              </TableCell>
              <TableCell>
                <Skeleton variant="rectangular" width={80} height={24} sx={{ borderRadius: 1 }} />
              </TableCell>
              <TableCell>
                <Skeleton variant="text" width={60} />
              </TableCell>
              <TableCell>
                <Stack spacing={0.5}>
                  <Skeleton variant="text" width={80} height={16} />
                  <Skeleton variant="text" width={60} height={14} />
                </Stack>
              </TableCell>
              <TableCell>
                <Skeleton variant="rectangular" width={70} height={24} sx={{ borderRadius: 1 }} />
              </TableCell>
              {showActions && (
                <TableCell>
                  <Stack direction="row" spacing={1}>
                    <Skeleton variant="circular" width={32} height={32} />
                    <Skeleton variant="circular" width={32} height={32} />
                  </Stack>
                </TableCell>
              )}
            </TableRow>
          ))}
        </TableBody>
      </Table>

      {/* Pagination Skeleton */}
      <Box sx={{ p: 2, borderTop: 1, borderColor: "divider" }}>
        <Stack direction="row" justifyContent="space-between" alignItems="center">
          <Skeleton variant="text" width={150} />
          <Stack direction="row" spacing={1} alignItems="center">
            <Skeleton variant="rectangular" width={32} height={32} sx={{ borderRadius: 1 }} />
            <Skeleton variant="text" width={80} />
            <Skeleton variant="rectangular" width={32} height={32} sx={{ borderRadius: 1 }} />
          </Stack>
        </Stack>
      </Box>
    </Card>
  );
};

// Card Grid Skeleton for mobile view
export const VoucherCardSkeleton: React.FC<{ cards?: number }> = ({ cards = 3 }) => {
  return (
    <Stack spacing={2}>
      {Array.from({ length: cards }).map((_, index) => (
        <Paper key={index} sx={{ p: 3, borderRadius: 2 }}>
          <Stack spacing={2}>
            <Stack direction="row" justifyContent="space-between" alignItems="flex-start">
              <Stack spacing={1} flex={1}>
                <Skeleton variant="text" width="60%" height={24} />
                <Skeleton variant="text" width="40%" height={20} />
              </Stack>
              <Skeleton variant="rectangular" width={70} height={24} sx={{ borderRadius: 1 }} />
            </Stack>

            <Stack direction="row" spacing={2}>
              <Skeleton variant="text" width={80} />
              <Skeleton variant="text" width={100} />
            </Stack>

            <Stack direction="row" justifyContent="space-between" alignItems="center">
              <Skeleton variant="text" width={120} />
              <Stack direction="row" spacing={1}>
                <Skeleton variant="circular" width={36} height={36} />
                <Skeleton variant="circular" width={36} height={36} />
              </Stack>
            </Stack>
          </Stack>
        </Paper>
      ))}
    </Stack>
  );
};

// Empty State Component
export const VoucherEmptyState: React.FC<{
  title?: string;
  description?: string;
  action?: React.ReactNode;
}> = ({
  title = "Chưa có voucher nào",
  description = "Tạo voucher đầu tiên để bắt đầu chương trình khuyến mãi",
  action,
}) => {
  return (
    <Box
      sx={{
        textAlign: "center",
        py: 8,
        px: 3,
      }}
    >
      <Box
        sx={{
          width: 120,
          height: 120,
          borderRadius: "50%",
          bgcolor: "grey.100",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          mx: "auto",
          mb: 3,
        }}
      >
        <Box
          sx={{
            fontSize: "3rem",
            opacity: 0.5,
          }}
        >
          🎫
        </Box>
      </Box>

      <Box sx={{ mb: 3 }}>
        <Box sx={{ fontSize: "1.25rem", fontWeight: 600, mb: 1, color: "text.primary" }}>
          {title}
        </Box>
        <Box sx={{ color: "text.secondary", maxWidth: 400, mx: "auto" }}>{description}</Box>
      </Box>

      {action && (
        <Box
          onClick={(e) => e.stopPropagation()}
          sx={{
            "& button": {
              pointerEvents: "auto",
            },
          }}
        >
          {action}
        </Box>
      )}
    </Box>
  );
};
