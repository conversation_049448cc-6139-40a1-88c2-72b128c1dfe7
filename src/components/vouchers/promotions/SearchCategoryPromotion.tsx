import { useStoreId } from "@/src/hooks/use-store-id";
import {
  Box,
  TextField,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Checkbox,
  Typography,
  Button,
  Divider,
  Collapse,
  Card,
  CardMedia,
  Radio,
} from "@mui/material";
import React, { useCallback, useEffect, useState } from "react";
import _ from "lodash";
import { useVoucher } from "@/src/api/hooks/voucher/use-voucher";
import { CategoryType, GetProductCategoryRequest } from "@/src/api/types/product-category.types";
import BoxImage from "../../box-image";
import { StorageService } from "nextjs-api-lib";
import { useProductCategory } from "@/src/api/hooks/dashboard/product/use-category";
import { MediaFile } from "@/src/constants/file-types";

export interface Category {
  categoryDesc: string;
  categoryIcon: string;
  image?: MediaFile;
  categoryId: string;
  categoryLevel: string; // Bạn có thể thay đổi thành number nếu `categoryLevel` là kiểu số
  categoryName: string;
  categoryPosition: number;
  categoryType: CategoryType;
  created: string; // Nếu muốn, bạn có thể sử dụng `Date` thay vì `string`
  isHome: boolean;
  parentId: string;
  parentName: string;
  partnerId: string;
  publish: "Publish" | "Unpublish"; // Nếu chỉ có 2 giá trị, có thể dùng union types
  quantityItems: number;
  ruleActive: "Actived" | "Inactive"; // Giả sử có 2 trạng thái cho `ruleActive`
  shopId: string;
  updated: string; // Bạn có thể thay đổi thành `Date` nếu cần
}

// Import lodash debounce
export default function SearchCategoryPromotion({
  handleClose,
  handleSubmit,
  selectedCategories: selectedCategoriesProp = [],
}) {
  const [searchText, setSearchText] = useState("");

  const storeId = useStoreId();
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const { getProductCategory, loading } = useProductCategory();
  const [selectedCategories, setSelectedCategories] = useState<Category[]>(selectedCategoriesProp);
  const [items, setItems] = useState([]);

  const handleSelectMany = (item) => {
    setSelectedCategories((prev) => {
      const exists = prev.some((cat) => cat.categoryId === item.categoryId);
      if (exists) {
        return prev.filter((cat) => cat.categoryId !== item.categoryId);
      } else {
        return [...prev, item];
      }
    });
  };

  const fetchCategories = async (currentPage, pageSize, searchQuery, shopId) => {
    try {
      const categoryType = "Product";
      const params: GetProductCategoryRequest = {
        categoryType,
        shopId,
        partnerId: StorageService.get("partnerId") as string,
        paging: {
          pageIndex: currentPage,
          pageSize: pageSize,
          search: searchQuery,
          nameType: "Created",
          sortType: "Desc",
          name: "Created",
          sort: "Desc",
        },
      };
      const response = await getProductCategory(params);
      if (response?.data) {
        setItems(response.data.data || []);
        setTotalCount(response.data.total || 0);
      }
    } catch (error) {}
  };
  // Thêm debounce vào fetchCategories
  const debouncedFetchCategories = useCallback(
    _.debounce((currentPage, pageSize, searchQuery, shopId) => {
      fetchCategories(currentPage, pageSize, searchQuery, shopId);
    }, 500), // Thời gian debounce là 500ms
    [] // Thực thi lại debounce khi page, rowsPerPage, searchText thay đổi
  );

  useEffect(() => {
    if (!storeId) return;
    debouncedFetchCategories(page, rowsPerPage, searchText, storeId); // Gọi hàm debounce thay vì fetch trực tiếp
  }, [storeId, page, rowsPerPage, searchText, debouncedFetchCategories]);

  useEffect(() => {
    setSelectedCategories(selectedCategoriesProp);
  }, [selectedCategoriesProp]);

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  return (
    <Box>
      <TextField
        fullWidth
        placeholder="Tìm danh mục"
        variant="outlined"
        size="small"
        onChange={(e) => setSearchText(e.target.value)}
      />

      <Box>
        <Box sx={{ marginTop: 2 }}>
          <TableContainer sx={{ maxHeight: 500, overflowY: "auto" }}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell padding="checkbox"></TableCell>
                  <TableCell>Tên</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {items.map((item, index) => (
                  <TableRow key={index}>
                    <TableCell padding="checkbox">
                      <Checkbox
                        checked={selectedCategories.some(
                          (cat) => cat.categoryId === item.categoryId
                        )}
                        onChange={() => handleSelectMany(item)}
                        value={item.categoryId}
                      />
                    </TableCell>
                    <TableCell>
                      <BoxImage link={item?.image?.link} alt={item?.categoryName}>
                        <Typography> {item?.categoryName}</Typography>
                      </BoxImage>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
          <TablePagination
            component="div"
            count={totalCount}
            page={page}
            onPageChange={handleChangePage}
            rowsPerPage={rowsPerPage}
            onRowsPerPageChange={handleChangeRowsPerPage}
            rowsPerPageOptions={[5, 10, 20]}
            labelRowsPerPage="Số dòng mỗi trang" // Thay đổi văn bản ở phần này
          />
        </Box>
        <Divider />
        <Box sx={{ display: "flex", justifyContent: "end", marginTop: 2 }}>
          <Button sx={{ marginRight: 1 }} variant="outlined" onClick={handleClose}>
            Hủy
          </Button>
          <Button variant="contained" onClick={() => handleSubmit(selectedCategories)}>
            Xác nhận
          </Button>
        </Box>
      </Box>
    </Box>
  );
}
