import { useStoreId } from "@/src/hooks/use-store-id";

import {
  Box,
  TextField,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Checkbox,
  Typography,
  Button,
  Divider,
} from "@mui/material";
import React, { useCallback, useEffect, useState } from "react";
import _ from "lodash";

import { useUser } from "@/src/api/hooks/user/use-user";
export default function SearchCustomerPromotion({
  handleClose,
  handleSubmit,
  selectedCustomers = [],
}) {
  const [searchText, setSearchText] = useState("");
  const [selected, setSelected] = useState(selectedCustomers);
  const [items, setItems] = useState([]);

  const handleFetchItems = (items) => {
    setItems(items);
  };

  // Update selected state when selectedCustomers prop changes
  useEffect(() => {
    setSelected(selectedCustomers);
  }, [selectedCustomers]);
  const handleSelectOne = (event, item) => {
    if (!event.target.checked) {
      // Bỏ chọn
      setSelected((prevSelected) =>
        prevSelected.filter((selectedItem) => selectedItem.userId !== item.userId)
      );
    } else {
      // Chọn
      setSelected((prevSelected) => [...prevSelected, item]);
    }
  };

  const isAllSelected = (arr1, arr2) => {
    const userIds1 = new Set(arr1.map((item) => item.userId));
    const userIds2 = new Set(arr2.map((item) => item.userId));

    return userIds1.size === userIds2.size && [...userIds1].every((id) => userIds2.has(id));
  };

  const isSomeSelected = (arr1, arr2) => {
    const userIds1 = new Set(arr1.map((item) => item.userId));
    return arr2.some((item) => userIds1.has(item.userId));
  };

  const isAllItemSelected = isAllSelected(selected, items);

  const isSomeItemSelected = isSomeSelected(selected, items);
  const handleSelectAll = (event) => {
    const isIndeterminate = !isAllItemSelected && isSomeItemSelected;
    const isChecked = event.target.checked;

    if (!isChecked || isIndeterminate) {
      setSelected([]); // Đặt selected về mảng rỗng
    } else {
      setSelected(items);
    }
  };

  return (
    <Box>
      <TextField
        fullWidth
        placeholder="Tìm kiếm khách hàng"
        variant="outlined"
        size="small"
        onChange={(e) => setSearchText(e.target.value)}
      />

      <Box>
        <UserSearchTable
          searchText={searchText}
          selected={selected}
          handleSelectAll={handleSelectAll}
          handleSelectedItem={handleSelectOne}
          handleFetchItems={handleFetchItems}
          items={items}
          isAllItemSelected={isAllItemSelected}
          isSomeItemSelected={isSomeItemSelected}
        />
        <Divider />
        <Box sx={{ display: "flex", justifyContent: "end", marginTop: 2 }}>
          <Button sx={{ marginRight: 1 }} variant="outlined" onClick={handleClose}>
            Hủy
          </Button>
          <Button variant="contained" onClick={() => handleSubmit(selected)}>
            Xác nhận
          </Button>
        </Box>
      </Box>
    </Box>
  );
}

const UserSearchTable = ({
  searchText,
  selected,
  handleSelectAll,
  handleSelectedItem,
  items,
  handleFetchItems,
  isAllItemSelected,
  isSomeItemSelected,
}) => {
  const storeId = useStoreId();
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const { listUser, loading } = useUser();

  const fetchItemList = async (currentPage, pageSize, searchQuery, shopId) => {
    const skip = currentPage * pageSize;
    const limit = pageSize;
    const response = await listUser(`?skip=${skip}&limit=${limit}`, {
      shopId,
      search: searchQuery,
    });
    if (response?.data) {
      handleFetchItems(response.data.data || []);
      setTotalCount(response.data.total || 0);
    }
  };

  // Wrap fetchUserList with debounce
  const debouncedFetchItemList = useCallback(
    _.debounce((currentPage, pageSize, searchQuery, shopId) => {
      fetchItemList(currentPage, pageSize, searchQuery, shopId);
    }, 400), // Delay 1s
    []
  );

  useEffect(() => {
    if (!storeId) return;
    debouncedFetchItemList(page, rowsPerPage, searchText, storeId);
    return () => {
      debouncedFetchItemList.cancel(); // Cancel debounce if component unmounts or searchText changes
    };
  }, [storeId, page, rowsPerPage, searchText]);

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  return (
    <Box sx={{ marginTop: 2 }}>
      <TableContainer sx={{ maxHeight: 500, overflowY: "auto" }}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell padding="checkbox">
                <Checkbox
                  checked={isAllItemSelected}
                  indeterminate={!isAllItemSelected && isSomeItemSelected}
                  onChange={handleSelectAll}
                />
              </TableCell>
              <TableCell>Khách hàng</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {items.map((item, index) => (
              <TableRow key={item.userId}>
                <TableCell>
                  <Checkbox
                    checked={selected.some((selectedItem) => selectedItem.userId === item.userId)}
                    onChange={(e) => handleSelectedItem(e, item)}
                  />
                </TableCell>
                <TableCell>
                  <Box>
                    <Typography>{item.fullname}</Typography>
                    <Typography variant="subtitle2" color="text.secondary">
                      {item.phoneNumber}
                    </Typography>
                    <Typography variant="subtitle2" color="text.secondary">
                      {item.email}
                    </Typography>
                  </Box>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
      <TablePagination
        component="div"
        count={totalCount}
        page={page}
        onPageChange={handleChangePage}
        rowsPerPage={rowsPerPage}
        onRowsPerPageChange={handleChangeRowsPerPage}
        rowsPerPageOptions={[5, 10, 20]}
        labelRowsPerPage="Số dòng mỗi trang" // Thay đổi văn bản ở phần này
      />
    </Box>
  );
};
