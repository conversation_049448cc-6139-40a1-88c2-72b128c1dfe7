import { useStoreId } from "@/src/hooks/use-store-id";
import {
  Box,
  TextField,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Checkbox,
  Typography,
  Button,
  Divider,
  Collapse,
  Card,
  CardMedia,
  Radio,
} from "@mui/material";
import React, { useCallback, useEffect, useState } from "react";
import _ from "lodash";
import { useUserGroup } from "@/src/api/hooks/user-group/use-user-group";
import { UserGroupQueryParams } from "@/src/api/services/user-group/user-group.service";

// Import lodash debounce
export default function SearchGroupPromotion({ handleClose, handleSubmit, selectedGroupId }) {
  const [searchText, setSearchText] = useState("");
  const [selectedGroup, setSelectedGroup] = useState(null);

  const storeId = useStoreId();
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const { getListUserGroup, loading } = useUserGroup();
  const [items, setItems] = useState([]);

  const fetchTags = async (currentPage, pageSize, searchQuery, shopId) => {
    try {
      const data: UserGroupQueryParams = {
        ShopId: shopId,
        GroupName: searchQuery,
        Paging: {
          PageSize: rowsPerPage,
          PageIndex: page,
        },
      };
      const response = await getListUserGroup(data);
      if (response?.data) {
        setItems(response.data.result || []);
        setTotalCount(response.data.total || 0);

        // If we have a selectedGroupId, find and set the selected group
        if (selectedGroupId && response.data.result) {
          const foundGroup = response.data.result.find(
            (group) => group.groupId === selectedGroupId
          );
          if (foundGroup) {
            setSelectedGroup(foundGroup);
          }
        }
      }
    } catch (error) {}
  };
  // Thêm debounce vào fetchCategories
  const debouncedFetchTags = useCallback(
    _.debounce((currentPage, pageSize, searchQuery, shopId) => {
      fetchTags(currentPage, pageSize, searchQuery, shopId);
    }, 500), // Thời gian debounce là 500ms
    [selectedGroupId] // Thêm selectedGroupId vào dependencies
  );

  useEffect(() => {
    if (!storeId) return;
    debouncedFetchTags(page, rowsPerPage, searchText, storeId); // Gọi hàm debounce thay vì fetch trực tiếp
  }, [storeId, page, rowsPerPage, searchText, debouncedFetchTags]);

  // Reset selected group when selectedGroupId changes
  useEffect(() => {
    if (selectedGroupId && items.length > 0) {
      const foundGroup = items.find((group) => group.groupId === selectedGroupId);
      if (foundGroup) {
        setSelectedGroup(foundGroup);
      } else {
        // Clear selection if the selected group is not in current search results
        setSelectedGroup(null);
      }
    } else if (!selectedGroupId) {
      setSelectedGroup(null);
    }
  }, [selectedGroupId, items]);

  // Clear search when dialog opens with a selected group that's not in current results
  useEffect(() => {
    if (selectedGroupId && !searchText && items.length > 0) {
      const foundGroup = items.find((group) => group.groupId === selectedGroupId);
      if (!foundGroup) {
        // If we have a selected group but it's not in the current page,
        // we might need to search for it or show all results
        setPage(0);
      }
    }
  }, [selectedGroupId, items, searchText]);

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleSelectGroup = (group) => {
    setSelectedGroup(group);
  };

  const handleConfirm = () => {
    if (selectedGroup) {
      handleSubmit({
        groupId: selectedGroup.groupId,
        groupName: selectedGroup.groupName,
      });
    }
  };

  return (
    <Box>
      <TextField
        fullWidth
        placeholder="Tìm nhóm"
        variant="outlined"
        size="small"
        onChange={(e) => setSearchText(e.target.value)}
      />

      <Box>
        <Box sx={{ marginTop: 2 }}>
          <TableContainer sx={{ maxHeight: 500, overflowY: "auto" }}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell padding="checkbox"></TableCell>
                  <TableCell>Tên</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {items.map((item, index) => (
                  <TableRow key={item.groupId}>
                    <TableCell padding="checkbox">
                      <Radio
                        checked={selectedGroup?.groupId === item.groupId}
                        onChange={() => handleSelectGroup(item)}
                        value={item.groupId}
                        name="radio-buttons"
                      />
                    </TableCell>
                    <TableCell>
                      <Typography> {item?.groupName}</Typography>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
          <TablePagination
            component="div"
            count={totalCount}
            page={page}
            onPageChange={handleChangePage}
            rowsPerPage={rowsPerPage}
            onRowsPerPageChange={handleChangeRowsPerPage}
            rowsPerPageOptions={[5, 10, 20]}
            labelRowsPerPage="Số dòng mỗi trang"
          />
        </Box>
        <Divider />
        <Box sx={{ display: "flex", justifyContent: "end", marginTop: 2 }}>
          <Button sx={{ marginRight: 1 }} variant="outlined" onClick={handleClose}>
            Hủy
          </Button>
          <Button variant="contained" onClick={handleConfirm} disabled={!selectedGroup}>
            Xác nhận
          </Button>
        </Box>
      </Box>
    </Box>
  );
}
