import { useStoreId } from "@/src/hooks/use-store-id";
import { formatMoney } from "@/src/utils/format-money";
import {
  Box,
  TextField,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Checkbox,
  Typography,
  Button,
  Divider,
  Collapse,
  Card,
  CardMedia,
} from "@mui/material";
import React, { useCallback, useEffect, useState } from "react";
import _ from "lodash";
import { useCart } from "@/src/api/hooks/cart/use-cart";
import { KeyboardArrowDown, KeyboardArrowUp } from "@mui/icons-material";
import CheckBoxIcon from "@mui/icons-material/CheckBox";
import CheckBoxOutlineBlankIcon from "@mui/icons-material/CheckBoxOutlineBlank";
import IndeterminateCheckBoxIcon from "@mui/icons-material/IndeterminateCheckBox";
import { isAllSelected, isSomeSelected } from "../../orders/draft/SearchItemOrder";
export default function SearchItemsPromotion({ handleClose, handleSubmit }) {
  const [searchText, setSearchText] = useState("");
  const [selected, setSelected] = useState([]);

  const [items, setItems] = useState([]);

  const handleFetchItems = (items) => {
    setItems(items);
  };
  const handleSelectOne = (event, item) => {
    // Chỉ cho phép chọn sản phẩm cha, không chọn từng biến thể
    let formattedItems = null;
    // Chọn toàn bộ biến thể của sản phẩm (nếu có)
    formattedItems = [formatResult(item, item)];

    const isIndeterminate =
      item.isVariant &&
      item.listVariant.some((variant) => selected.some((s) => s.itemsId === variant.itemsId));

    if (isIndeterminate || !event.target.checked) {
      // Bỏ chọn
      setSelected((prevSelected) =>
        prevSelected.filter((selectedItem) =>
          formattedItems.every((updatedItem) => selectedItem.itemsId !== updatedItem.itemsId)
        )
      );
    } else {
      // Chọn
      setSelected((prevSelected) => [...prevSelected, ...formattedItems]);
    }
  };
  const isAllItemSelected = isAllSelected(selected, items);

  const isSomeItemSelected = isSomeSelected(selected, items);
  const handleSelectAll = (event) => {
    const isIndeterminate = !isAllItemSelected && isSomeItemSelected;
    const isChecked = event.target.checked;

    if (isIndeterminate) {
      setSelected([]);
    } else if (isChecked) {
      // Chỉ chọn product cha, không chọn từng biến thể
      const allProducts = items.map((row) => formatResult(row, row));
      setSelected(allProducts);
    } else {
      setSelected([]);
    }
  };

  return (
    <Box>
      <TextField
        fullWidth
        placeholder="Tìm kiếm sản phẩm, dịch vụ"
        variant="outlined"
        size="small"
        onChange={(e) => setSearchText(e.target.value)}
      />

      <Box>
        <OrderItemSearchTable
          searchText={searchText}
          selected={selected}
          handleSelectAll={handleSelectAll}
          handleSelectedItem={handleSelectOne}
          handleFetchItems={handleFetchItems}
          items={items}
          isAllItemSelected={isAllItemSelected}
          isSomeItemSelected={isSomeItemSelected}
        />
        <Divider />
        <Box sx={{ display: "flex", justifyContent: "end", marginTop: 2 }}>
          <Button sx={{ marginRight: 1 }} variant="outlined" onClick={handleClose}>
            Hủy
          </Button>
          <Button variant="contained" onClick={() => handleSubmit(selected)}>
            Xác nhận
          </Button>
        </Box>
      </Box>
    </Box>
  );
}

const OrderItemSearchTable = ({
  searchText,
  selected,
  handleSelectAll,
  handleSelectedItem,
  items,
  handleFetchItems,
  isAllItemSelected,
  isSomeItemSelected,
}) => {
  const storeId = useStoreId();
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const { searchItems, loading: productLoading } = useCart();

  const fetchItemList = async (currentPage, pageSize, searchQuery, shopId) => {
    const skip = currentPage * pageSize;
    const limit = pageSize;
    const response = await searchItems(skip, limit, shopId, searchQuery);

    if (response?.data) {
      handleFetchItems(response.data.data || []);
      setTotalCount(response.data.total || 0);
    }
  };

  // Wrap fetchUserList with debounce
  const debouncedFetchItemList = useCallback(
    _.debounce((currentPage, pageSize, searchQuery, shopId) => {
      fetchItemList(currentPage, pageSize, searchQuery, shopId);
    }, 400), // Delay 1s
    []
  );

  useEffect(() => {
    if (!storeId) return;
    debouncedFetchItemList(page, rowsPerPage, searchText, storeId);
    return () => {
      debouncedFetchItemList.cancel(); // Cancel debounce if component unmounts or searchText changes
    };
  }, [storeId, page, rowsPerPage, searchText]);

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  return (
    <Box sx={{ marginTop: 2 }}>
      <TableContainer sx={{ maxHeight: 500, overflowY: "auto" }}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell padding="checkbox">
                <Checkbox
                  checked={isAllItemSelected}
                  indeterminate={!isAllItemSelected && isSomeItemSelected}
                  onChange={handleSelectAll}
                />
              </TableCell>
              <TableCell>Tên</TableCell>
              <TableCell>Số lượng tồn kho</TableCell>
              <TableCell>Giá bán</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {items.map((item, index) => (
              <SubtableRow
                row={item}
                key={index}
                selected={selected}
                handleSelected={handleSelectedItem}
              />
            ))}
          </TableBody>
        </Table>
      </TableContainer>
      <TablePagination
        component="div"
        count={totalCount}
        page={page}
        onPageChange={handleChangePage}
        rowsPerPage={rowsPerPage}
        onRowsPerPageChange={handleChangeRowsPerPage}
        rowsPerPageOptions={[5, 10, 20]}
        labelRowsPerPage="Số dòng mỗi trang" // Thay đổi văn bản ở phần này
      />
    </Box>
  );
};

const SubtableRow = ({ row, selected, handleSelected }) => {
  const isVariant = row.isVariant;
  const selectedSet = new Set(selected.map((s) => s.itemsId));

  const [open, setOpen] = useState(true);
  const isDisabled = (row) => {
    // Check if all items in listVariant have quantity = 0
    const allOutOfStock = row.listVariant.every((item) => item.quantity === 0);

    return allOutOfStock; // Return true if all variants have quantity 0, meaning disabled
  };
  const setListVariantItemsId = new Set(row.listVariant.map((s) => s.itemsId));

  const checkIfAllInSet = () => {
    return [...setListVariantItemsId].every((item) => selectedSet.has(item));
  };

  const checkIfSomeInSet = () => {
    return [...setListVariantItemsId].some((item) => selectedSet.has(item));
  };

  return (
    <>
      <TableRow>
        <TableCell padding="checkbox">
          <Checkbox
            checked={checkIfAllInSet()}
            indeterminate={checkIfSomeInSet() && !checkIfAllInSet()}
            onChange={(e) => handleSelected(e, row)}
            // disabled={isDisabled(row)}
          />
        </TableCell>
        <TableCell>
          <Box display="flex" gap={1} alignItems="center">
            <Box
              sx={{
                width: 40, // Kích thước chiều ngang
                height: 40, // Kích thước chiều dọc (vuông)
                overflow: "hidden", // Ẩn phần hình ảnh vượt ra ngoài
                borderRadius: "8px", // Bo góc, nếu cần
                boxShadow: 3,
                flexShrink: 0, // Đổ bóng
              }}
            >
              <img
                src={row.images?.[0]?.link} // Thay đường dẫn hình ảnh tại đây
                alt={row.itemsName}
                style={{
                  width: "100%",
                  height: "100%",
                  objectFit: "cover", // Đảm bảo hình ảnh vừa khít khung mà không bị méo
                }}
              />
            </Box>
            <Typography> {row.itemsName}</Typography>
          </Box>
        </TableCell>
        <TableCell>{!isVariant && row.listVariant?.[0]?.quantity}</TableCell>
        <TableCell>
          {isVariant ? (
            <IconButton sx={{ fontSize: "2rem" }} color="primary" onClick={() => setOpen(!open)}>
              {open ? (
                <KeyboardArrowUp sx={{ fontSize: "2rem" }} />
              ) : (
                <KeyboardArrowDown sx={{ fontSize: "2rem" }} />
              )}
            </IconButton>
          ) : (
            `${formatMoney(row.listVariant?.[0]?.price || 0)}đ`
          )}
        </TableCell>
      </TableRow>

      {/* Subtable */}
      {isVariant && (
        <TableRow>
          <TableCell colSpan={4} style={{ padding: 0 }}>
            <Collapse in={open} timeout="auto" unmountOnExit>
              <Box sx={{ paddingLeft: 2 }}>
                <Table>
                  <TableHead>
                    <TableRow>
                      {/* Bỏ checkbox ở subtable */}
                      <TableCell padding="checkbox"></TableCell>
                      <TableCell>Tên</TableCell>
                      <TableCell>Số lượng tồn kho</TableCell>
                      <TableCell>Giá bán</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {row.listVariant?.map((variant) => (
                      <TableRow key={variant.itemsId}>
                        <TableCell />
                        <TableCell>
                          <Box sx={{ display: "flex", alignItems: "center" }}>
                            {/* Không còn checkbox ở đây */}
                            <Box display="flex" gap={1} alignItems="center">
                              <Box
                                sx={{
                                  width: 40, // Kích thước chiều ngang
                                  height: 40, // Kích thước chiều dọc (vuông)
                                  overflow: "hidden", // Ẩn phần hình ảnh vượt ra ngoài
                                  borderRadius: 1, // Bo góc, nếu cần
                                  boxShadow: 3,
                                  flexShrink: 0, // Đổ bóng
                                }}
                              >
                                <img
                                  src={variant.variantImage?.link} // Thay đường dẫn hình ảnh tại đây
                                  alt={row.itemsName}
                                  style={{
                                    width: "100%",
                                    height: "100%",
                                    objectFit: "cover", // Đảm bảo hình ảnh vừa khít khung mà không bị méo
                                  }}
                                />
                              </Box>
                              <Typography>
                                {[
                                  variant.variantValueOne,
                                  variant.variantValueTwo,
                                  variant.variantValueThree,
                                ]
                                  .filter((value) => value != null && value !== "")
                                  .join("/")}
                              </Typography>
                            </Box>
                          </Box>
                        </TableCell>
                        <TableCell>{variant.quantity}</TableCell>
                        <TableCell>{formatMoney(variant.price)}đ</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </Box>
            </Collapse>
          </TableCell>
        </TableRow>
      )}
    </>
  );
};

const formatResult = (childrenItem, parentItem) => {
  const formattedItem: ProductVoucherPromotionType = {
    itemsCode: parentItem.itemsCode,
    itemsType: parentItem.itemsType,
    itemsName: parentItem.itemsName,
    images: parentItem.images,
    isVariant: parentItem.isVariant,
    itemsId: childrenItem.itemsId,
    variantImage: childrenItem.variantImage,
    priceCapital: childrenItem.priceCapital,
    priceReal: childrenItem.priceReal,
    price: childrenItem.price,
  };

  return formattedItem;
};

export type ProductVoucherPromotionType = {
  itemsCode: string;
  itemsType: string;
  itemsName: string;
  images: { type: string; link: string }[];
  isVariant: boolean;
  itemsId: string;
  variantImage: { type: string; link: string };
  priceCapital: number;
  priceReal: number;
  price: number;
};
