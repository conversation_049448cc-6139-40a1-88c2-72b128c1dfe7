import React from "react";
import { Box, Button, Stack, useTheme, alpha, CircularProgress } from "@mui/material";
import SaveIcon from "@mui/icons-material/Save";
import CancelIcon from "@mui/icons-material/Cancel";

interface FormActionsProps {
  onCancel: () => void;
  onSave?: () => void;
  isLoading?: boolean;
  saveText?: string;
  cancelText?: string;
  disabled?: boolean;
  variant?: "fixed" | "inline";
  sx?: any;
  additionalActions?: React.ReactNode;
}

const FormActions: React.FC<FormActionsProps> = ({
  onCancel,
  onSave,
  isLoading = false,
  saveText = "Lưu",
  cancelText = "Hủy bỏ",
  disabled = false,
  variant = "fixed",
  sx = {},
  additionalActions,
}) => {
  const theme = useTheme();

  const actionButtons = (
    <Stack direction="row" spacing={2} alignItems="center">
      {additionalActions}
      <Button
        variant="outlined"
        onClick={onCancel}
        disabled={isLoading}
        startIcon={<CancelIcon />}
        sx={{
          borderRadius: 1,
          px: 3,
          py: 1.25,
          borderColor: alpha(theme.palette.divider, 0.5),
          color: theme.palette.text.secondary,
          "&:hover": {
            borderColor: theme.palette.error.main,
            backgroundColor: alpha(theme.palette.error.main, 0.04),
            color: theme.palette.error.main,
          },
        }}
      >
        {cancelText}
      </Button>
      {onSave && (
        <Button
          type="submit"
          variant="contained"
          onClick={onSave}
          disabled={disabled || isLoading}
          startIcon={isLoading ? <CircularProgress size={16} color="inherit" /> : <SaveIcon />}
          sx={{
            borderRadius: 1,
            px: 3,
            py: 1.25,
            background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${alpha(
              theme.palette.primary.main,
              0.8
            )} 100%)`,
            boxShadow: `0 4px 12px ${alpha(theme.palette.primary.main, 0.3)}`,
            "&:hover": {
              background: `linear-gradient(135deg, ${alpha(
                theme.palette.primary.main,
                0.9
              )} 0%, ${alpha(theme.palette.primary.main, 0.7)} 100%)`,
              boxShadow: `0 6px 16px ${alpha(theme.palette.primary.main, 0.4)}`,
              transform: "translateY(-1px)",
            },
            "&:disabled": {
              background: alpha(theme.palette.action.disabled, 0.12),
              color: alpha(theme.palette.action.disabled, 0.26),
              boxShadow: "none",
            },
            transition: "all 0.2s ease-in-out",
          }}
        >
          {isLoading ? "Đang lưu..." : saveText}
        </Button>
      )}
    </Stack>
  );

  if (variant === "fixed") {
    return (
      <Box
        sx={{
          position: "fixed",
          bottom: 0,
          left: 0,
          right: 0,
          zIndex: 1000,
          backgroundColor: alpha(theme.palette.background.paper, 0.95),
          backdropFilter: "blur(8px)",
          borderTop: `1px solid ${alpha(theme.palette.divider, 0.12)}`,
          p: "8px",
          ...sx,
        }}
      >
        <Box
          sx={{
            mx: "auto",
            display: "flex",
            justifyContent: "flex-end",
          }}
        >
          {actionButtons}
        </Box>
      </Box>
    );
  }

  return (
    <Box
      sx={{
        display: "flex",
        justifyContent: "flex-end",
        pt: 3,
        mt: 3,
        borderTop: `1px solid ${alpha(theme.palette.divider, 0.12)}`,
        ...sx,
      }}
    >
      {actionButtons}
    </Box>
  );
};

export default FormActions;
