import React from "react";
import {
  FormControl,
  RadioGroup,
  FormControlLabel,
  Radio,
  Typography,
  Box,
  useTheme,
  alpha,
  Paper,
} from "@mui/material";
import { Controller } from "react-hook-form";

interface RadioOption {
  value: string;
  label: string;
  description?: string;
  disabled?: boolean;
}

interface FormRadioGroupProps {
  name: string;
  control: any;
  label: string;
  options: RadioOption[];
  required?: boolean;
  error?: any;
  row?: boolean;
  sx?: any;
  variant?: "standard" | "card";
  onChange?: (value: string) => void;
}

const FormRadioGroup: React.FC<FormRadioGroupProps> = ({
  name,
  control,
  label,
  options,
  required = false,
  error,
  row = false,
  sx = {},
  variant = "standard",
  onChange,
}) => {
  const theme = useTheme();

  const renderStandardRadio = (option: RadioOption, field: any) => (
    <FormControlLabel
      key={option.value}
      value={option.value}
      control={
        <Radio
          sx={{
            color: alpha(theme.palette.primary.main, 0.6),
            "&.Mui-checked": {
              color: theme.palette.primary.main,
            },
          }}
        />
      }
      label={
        <Box>
          <Typography variant="body2" sx={{ fontWeight: 500 }}>
            {option.label}
          </Typography>
          {option.description && (
            <Typography
              variant="caption"
              sx={{
                color: theme.palette.text.secondary,
                display: "block",
                mt: 0.25,
              }}
            >
              {option.description}
            </Typography>
          )}
        </Box>
      }
      disabled={option.disabled}
      sx={{
        margin: 0,
        padding: 1,
        borderRadius: 2,
        "&:hover": {
          backgroundColor: alpha(theme.palette.primary.main, 0.04),
        },
      }}
    />
  );

  const renderCardRadio = (
    option: RadioOption,
    field: any,
    onChangeCallback?: (value: string) => void
  ) => (
    <Paper
      key={option.value}
      elevation={0}
      sx={{
        border: `2px solid ${
          field.value === option.value
            ? theme.palette.primary.main
            : alpha(theme.palette.divider, 0.3)
        }`,
        borderRadius: 2,
        p: 2,
        cursor: option.disabled ? "not-allowed" : "pointer",
        transition: "all 0.2s ease-in-out",
        backgroundColor:
          field.value === option.value
            ? alpha(theme.palette.primary.main, 0.04)
            : theme.palette.background.paper,
        "&:hover": {
          borderColor: option.disabled
            ? alpha(theme.palette.divider, 0.3)
            : alpha(theme.palette.primary.main, 0.6),
          backgroundColor: option.disabled
            ? theme.palette.background.paper
            : alpha(theme.palette.primary.main, 0.02),
        },
        opacity: option.disabled ? 0.6 : 1,
      }}
      onClick={() => {
        if (!option.disabled) {
          field.onChange(option.value);
          if (onChangeCallback) {
            onChangeCallback(option.value);
          }
        }
      }}
    >
      <FormControlLabel
        value={option.value}
        control={
          <Radio
            sx={{
              color: alpha(theme.palette.primary.main, 0.6),
              "&.Mui-checked": {
                color: theme.palette.primary.main,
              },
            }}
          />
        }
        label={
          <Box sx={{ ml: 1 }}>
            <Typography variant="body2" sx={{ fontWeight: 500 }}>
              {option.label}
            </Typography>
            {option.description && (
              <Typography
                variant="caption"
                sx={{
                  color: theme.palette.text.secondary,
                  display: "block",
                  mt: 0.5,
                }}
              >
                {option.description}
              </Typography>
            )}
          </Box>
        }
        disabled={option.disabled}
        sx={{ margin: 0, width: "100%" }}
      />
    </Paper>
  );

  return (
    <FormControl component="fieldset" sx={sx}>
      <Box sx={{ mb: 2 }}>
        <Typography
          component="legend"
          sx={{
            fontSize: "0.875rem",
            fontWeight: 500,
            color: theme.palette.text.primary,
            display: "flex",
            alignItems: "center",
          }}
        >
          {label}
          {required && (
            <Typography
              component="span"
              sx={{
                color: theme.palette.error.main,
                ml: 0.5,
                fontSize: "0.875rem",
              }}
            >
              *
            </Typography>
          )}
        </Typography>
      </Box>

      <Controller
        name={name}
        control={control}
        render={({ field }) => (
          <RadioGroup
            {...field}
            row={row && variant === "standard"}
            onChange={(e) => {
              field.onChange(e);
              if (onChange) {
                onChange(e.target.value);
              }
            }}
          >
            <Box
              sx={{
                display: "flex",
                flexDirection: variant === "card" || !row ? "column" : "row",
                gap: variant === "card" ? 2 : 1,
                width: "100%",
              }}
            >
              {options.map((option) =>
                variant === "card"
                  ? renderCardRadio(option, field, onChange)
                  : renderStandardRadio(option, field)
              )}
            </Box>
          </RadioGroup>
        )}
      />

      {error && (
        <Typography
          variant="caption"
          sx={{
            color: theme.palette.error.main,
            mt: 1,
            fontSize: "0.75rem",
          }}
        >
          {error.message}
        </Typography>
      )}
    </FormControl>
  );
};

export default FormRadioGroup;
