import React from "react";
import { Card, CardContent, Typography, Box, Divider, useTheme, alpha, Fade } from "@mui/material";

interface FormSectionProps {
  title: string;
  subtitle?: string;
  icon?: React.ReactNode;
  children: React.ReactNode;
  elevation?: number;
  sx?: any;
  headerAction?: React.ReactNode;
  isLoading?: boolean;
}

const FormSection: React.FC<FormSectionProps> = ({
  title,
  subtitle,
  icon,
  children,
  elevation = 0,
  sx = {},
  headerAction,
  isLoading = false,
}) => {
  const theme = useTheme();

  return (
    <Fade in timeout={300}>
      <Card
        elevation={elevation}
        sx={{
          borderRadius: 1,
          border: `1px solid ${alpha(theme.palette.divider, 1)}`,
          background: theme.palette.background.paper,
          transition: "all 0.2s ease-in-out",
          ...sx,
        }}
      >
        <CardContent sx={{ p: 0 }}>
          {/* Header */}
          <Box
            sx={{
              p: 3,
              pb: subtitle ? 2 : 3,
              background: alpha(theme.palette.primary.main, 0.02),
              borderBottom: `1px solid ${alpha(theme.palette.divider, 0.08)}`,
            }}
          >
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
              }}
            >
              <Box sx={{ display: "flex", alignItems: "center", gap: 1.5 }}>
                {icon && (
                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      width: 40,
                      height: 40,
                      borderRadius: 2,
                      background: alpha(theme.palette.primary.main, 0.1),
                      color: theme.palette.primary.main,
                    }}
                  >
                    {icon}
                  </Box>
                )}
                <Box>
                  <Typography
                    variant="h6"
                    sx={{
                      fontWeight: 600,
                      color: theme.palette.text.primary,
                      fontSize: "1.1rem",
                    }}
                  >
                    {title}
                  </Typography>
                  {subtitle && (
                    <Typography
                      variant="body2"
                      sx={{
                        color: theme.palette.text.secondary,
                        mt: 0.5,
                        fontSize: "0.875rem",
                      }}
                    >
                      {subtitle}
                    </Typography>
                  )}
                </Box>
              </Box>
              {headerAction && <Box sx={{ ml: 2 }}>{headerAction}</Box>}
            </Box>
          </Box>

          {/* Content */}
          <Box sx={{ p: 3 }}>
            {isLoading ? (
              <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
                {[...Array(3)].map((_, index) => (
                  <Box key={index} sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
                    <Box sx={{ width: "30%", height: 16 }} component="div">
                      <div
                        style={{
                          width: "100%",
                          height: "100%",
                          backgroundColor: "#f0f0f0",
                          borderRadius: 4,
                        }}
                      />
                    </Box>
                    <Box sx={{ width: "100%", height: 40 }} component="div">
                      <div
                        style={{
                          width: "100%",
                          height: "100%",
                          backgroundColor: "#f0f0f0",
                          borderRadius: 8,
                        }}
                      />
                    </Box>
                  </Box>
                ))}
              </Box>
            ) : (
              children
            )}
          </Box>
        </CardContent>
      </Card>
    </Fade>
  );
};

export default FormSection;
