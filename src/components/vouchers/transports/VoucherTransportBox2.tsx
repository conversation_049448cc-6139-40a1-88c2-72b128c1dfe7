import {
  Box,
  Button,
  Divider,
  FormControl,
  FormControlLabel,
  FormLabel,
  IconButton,
  InputAdornment,
  Pagination,
  Radio,
  RadioGroup,
  TextField,
  Typography,
} from "@mui/material";
import { useEffect, useState } from "react";
import { Controller, useFormContext } from "react-hook-form";

import { CategoryType } from "@/src/api/types/product-category.types";

import DeleteOutlineOutlinedIcon from "@mui/icons-material/DeleteOutlineOutlined";

import { useProductCategory } from "@/src/api/hooks/dashboard/product/use-category";
import { useVoucher } from "@/src/api/hooks/voucher/use-voucher";
import { useStoreId } from "@/src/hooks/use-store-id";
import { VoucherTransportFormData } from "../VoucherTransportForm";
import CustomerConditionPromotion from "../promotions/CustomerConditionPromotion";
import CustomSwitch from "../../custom-switch";

export default function VoucherTransportBox2({ voucher }) {
  const {
    setValue,
    getValues,
    watch,
    control,
    formState: { errors },
  } = useFormContext<VoucherTransportFormData>(); // Use context to get control
  const limitType = watch("limitType");

  const storeId = useStoreId();

  return (
    <Box>
      <Box>
        <FormControl fullWidth sx={{ marginTop: 2 }}>
          <Typography gutterBottom>Đơn hàng tối thiểu</Typography>
          <Controller
            name="minOrder"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                type="number"
                variant="outlined"
                error={!!errors.minOrder}
                helperText={errors.minOrder?.message}
                onWheel={(e) => e.target instanceof HTMLElement && e.target.blur()}
                InputProps={{
                  endAdornment: <InputAdornment position="start">₫</InputAdornment>,
                }}
                inputProps={{ min: 0 }}
              />
            )}
          />
        </FormControl>
      </Box>

      <Box marginTop={2}>
        <CustomerConditionPromotion voucher={voucher} />
      </Box>

      <Box marginTop={4}>
        <Typography>Sử dụng</Typography>

        <FormControl fullWidth sx={{ marginTop: 2 }}>
          <Typography gutterBottom>Tổng số lần có thể sử dụng chiết khấu</Typography>

          <Controller
            name="maxUsagePerUser"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                placeholder="Nhập số lần mỗi khách hàng được sử dụng voucher này"
                type="number"
                variant="outlined"
                error={!!errors.maxUsagePerUser}
                helperText={errors.maxUsagePerUser?.message}
              />
            )}
          />
        </FormControl>
      </Box>
    </Box>
  );
}
