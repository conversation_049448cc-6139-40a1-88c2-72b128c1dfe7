import { useState } from "react";
import {
  Box,
  Typography,
  TextField,
  Button,
  Paper,
  Grid,
  FormControl,
  Select,
  MenuItem,
  IconButton,
  Radio,
  RadioGroup,
  FormControlLabel,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Checkbox,
  Chip,
  InputAdornment,
  Container,
  type SelectChangeEvent,
} from "@mui/material";
import { Delete as DeleteIcon, Search as SearchIcon } from "@mui/icons-material";

export default function CustomerCategorization() {
  const [filterType, setFilterType] = useState("and");
  const [selectedTab, setSelectedTab] = useState("auto");
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(5);

  const handleFilterTypeChange = (event: SelectChangeEvent) => {
    setFilterType(event.target.value);
  };

  const rows = [
    {
      id: 1,
      name: "Tuấ<PERSON> E<PERSON>",
      phone: "0349487999",
      status: "Active",
      tags: ["<PERSON><PERSON><PERSON>", "VIP"],
      rank: "Đồng",
    },
    {
      id: 2,
      name: "<PERSON><PERSON><PERSON> E<PERSON>",
      phone: "0349487999",
      status: "Block",
      tags: ["Khách khó tính"],
      rank: "Bạc",
    },
    { id: 3, name: "Tuấn Evo", phone: "0349487999", status: "Active", tags: [], rank: "Vàng" },
  ];

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Paper sx={{ p: 3, borderRadius: 1 }}>
        <Grid container spacing={3}>
          <Grid item xs={12} md={5}>
            <Box sx={{ mb: 2 }}>
              <Typography variant="body1" gutterBottom>
                Tên nhóm
              </Typography>
              <TextField fullWidth variant="outlined" size="small" />
            </Box>
            <Box>
              <Typography variant="body1" gutterBottom>
                Giới thiệu
              </Typography>
              <TextField fullWidth variant="outlined" multiline rows={8} size="small" />
            </Box>
          </Grid>
          <Grid item xs={12} md={7}>
            <Typography variant="body1" gutterBottom>
              Điều kiện lọc
            </Typography>
            <Box
              sx={{
                border: "1px solid #e0e0e0",
                borderRadius: 1,
                p: 2,
                minHeight: 285,
              }}
            >
              <Box
                sx={{
                  border: "1px solid #e0e0e0", // Add border
                  borderRadius: 1, // Optional: Add rounded corners
                  p: 1,
                  mb: 1,
                }}
              >
                <Grid container spacing={1} alignItems="center">
                  <Grid item xs={4}>
                    <FormControl fullWidth size="small">
                      <Select displayEmpty>
                        <MenuItem value="">
                          <em>-</em>
                        </MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={4}>
                    <FormControl fullWidth size="small">
                      <Select displayEmpty>
                        <MenuItem value="">
                          <em>-</em>
                        </MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={3}>
                    <TextField fullWidth size="small" />
                  </Grid>
                  <Grid item xs={1}>
                    <IconButton size="small" color="error">
                      <DeleteIcon fontSize="small" />
                    </IconButton>
                  </Grid>
                </Grid>
              </Box>
              <Box sx={{ mb: 1 }}>
                <FormControl size="small">
                  <Select
                    value={filterType}
                    onChange={handleFilterTypeChange}
                    displayEmpty
                    size="small"
                    sx={{
                      minWidth: 60,
                      height: 30,
                      fontSize: "0.875rem",
                    }}
                  >
                    <MenuItem value="and">Và</MenuItem>
                  </Select>
                </FormControl>
              </Box>
              <Box
                sx={{
                  border: "1px solid #e0e0e0",
                  borderRadius: 1,
                  p: 1,
                }}
              >
                <Grid container spacing={1} alignItems="center">
                  <Grid item xs={4}>
                    <FormControl fullWidth size="small">
                      <Select displayEmpty>
                        <MenuItem value="">
                          <em>-</em>
                        </MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={8} sx={{ display: "flex", justifyContent: "flex-end" }}>
                    <IconButton
                      size="small"
                      color="error"
                      sx={{
                        border: "none", // Remove border
                        boxShadow: "none", // Remove shadow
                        marginRight: 0, // Ensure it aligns flush with the right edge
                      }}
                    >
                      <DeleteIcon fontSize="small" />
                    </IconButton>
                  </Grid>
                </Grid>
              </Box>
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" color="primary">
                  Thêm điều kiện
                </Typography>
                <Box sx={{ display: "flex", gap: 1, mt: 1 }}>
                  <FormControl size="small" sx={{ minWidth: 120 }}>
                    <Select
                      value={filterType}
                      onChange={handleFilterTypeChange}
                      displayEmpty
                      sx={{
                        textTransform: "none",
                      }}
                    >
                      <MenuItem value="and">Và</MenuItem>
                      <MenuItem value="or">Hoặc</MenuItem>
                    </Select>
                  </FormControl>
                </Box>
              </Box>
            </Box>
          </Grid>
        </Grid>
        <Box sx={{ mt: 2, mb: 4 }}>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} sm={4}>
              <Box sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
                <Button
                  variant="contained"
                  sx={{
                    textTransform: "none",
                    borderRadius: 1,
                    px: 4,
                    width: "40%",
                    backgroundColor: "#2654FE",
                    color: "#fff",
                  }}
                >
                  Lọc
                </Button>
                <TextField
                  placeholder="Tìm kiếm"
                  variant="outlined"
                  size="small"
                  sx={{ width: "80%" }}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <SearchIcon fontSize="small" />
                      </InputAdornment>
                    ),
                  }}
                />
              </Box>
            </Grid>

            {/* Thêm tự động */}
            <Grid item xs={12} sm={4}>
              <RadioGroup
                row
                value={selectedTab}
                onChange={(e) => setSelectedTab(e.target.value)}
                sx={{ display: "flex", justifyContent: "center", gap: 2, mb: 6 }}
              >
                <FormControlLabel
                  value="auto"
                  control={<Radio color="primary" />}
                  label="Thêm tự động"
                />
              </RadioGroup>
            </Grid>

            {/* Thêm thủ công */}
            <Grid item xs={12} sm={4}>
              <RadioGroup
                row
                value={selectedTab}
                onChange={(e) => setSelectedTab(e.target.value)}
                sx={{ display: "flex", justifyContent: "center", gap: 2, mb: 6 }}
              >
                <FormControlLabel value="manual" control={<Radio />} label="Thêm thủ công" />
              </RadioGroup>
            </Grid>
          </Grid>
        </Box>
        <TableContainer
          component={Paper}
          variant="outlined"
          sx={{ mb: 3, maxHeight: 400, overflowY: "auto" }}
        >
          <Table size="small" stickyHeader>
            <TableHead>
              <TableRow sx={{ height: 60 }}>
                <TableCell padding="checkbox">
                  <Checkbox size="small" disabled={selectedTab === "auto"} />
                </TableCell>
                <TableCell>Tên khách hàng</TableCell>
                <TableCell>Số điện thoại</TableCell>
                <TableCell>Trạng thái</TableCell>
                <TableCell>Tag</TableCell>
                <TableCell>Hạng thành viên</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {rows
                .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage) // Slice rows for pagination
                .map((row) => (
                  <TableRow key={row.id} sx={{ height: 60 }}>
                    <TableCell padding="checkbox">
                      <Checkbox size="small" disabled={selectedTab === "auto"} />{" "}
                      {/* Disable if "Thêm tự động" */}
                    </TableCell>
                    <TableCell>{row.name}</TableCell>
                    <TableCell>{row.phone}</TableCell>
                    <TableCell>{row.status}</TableCell>
                    <TableCell>
                      {row.tags.map((tag, index) => (
                        <Chip
                          key={index}
                          label={tag}
                          size="small"
                          sx={{
                            mr: 0.5,
                            bgcolor:
                              tag === "Mới" ? "#f5f5f5" : tag === "VIP" ? "#f5f5f5" : "#f5f5f5",
                            borderRadius: 1,
                          }}
                        />
                      ))}
                    </TableCell>
                    <TableCell>{row.rank}</TableCell>
                  </TableRow>
                ))}
            </TableBody>
          </Table>
        </TableContainer>
        <Box
          sx={{
            display: "flex",
            justifyContent: "flex-end",
            alignItems: "center",
            p: 1,
            borderTop: "1px solid #e0e0e0",
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center", mr: 2 }}>
            <Typography variant="body2" sx={{ mr: 1 }}>
              Số dòng mỗi trang
            </Typography>
            <Select
              value={rowsPerPage}
              onChange={(e) => {
                setRowsPerPage(Number(e.target.value));
                setPage(0); // Reset to the first page when rows per page changes
              }}
              size="small"
              sx={{
                height: 24,
                minWidth: 60,
                "& .MuiOutlinedInput-notchedOutline": {
                  border: "none", // Remove the border
                },
              }}
            >
              <MenuItem value={5}>5</MenuItem>
              <MenuItem value={10}>10</MenuItem>
              <MenuItem value={25}>25</MenuItem>
            </Select>
          </Box>
          <Typography variant="body2" sx={{ mr: 2 }}>
            {`${page * rowsPerPage + 1}-${Math.min((page + 1) * rowsPerPage, rows.length)} of ${
              rows.length
            }`}
          </Typography>
          <Box>
            <IconButton size="small" disabled={page === 0} onClick={() => setPage(page - 1)}>
              <Typography variant="body2">&lt;</Typography>
            </IconButton>
            <IconButton
              size="small"
              disabled={page >= Math.ceil(rows.length / rowsPerPage) - 1}
              onClick={() => setPage(page + 1)}
            >
              <Typography variant="body2">&gt;</Typography>
            </IconButton>
          </Box>
        </Box>
        <Box sx={{ display: "flex", justifyContent: "flex-end", gap: 2 }}>
          <Button
            variant="outlined"
            sx={{
              textTransform: "none",
              borderRadius: 1,
              px: 3,
            }}
          >
            Hủy bỏ
          </Button>
          <Button
            variant="contained"
            color="primary"
            sx={{
              textTransform: "none",
              borderRadius: 1,
              px: 3,
            }}
          >
            Thêm nhóm
          </Button>
        </Box>
      </Paper>
    </Container>
  );
}
