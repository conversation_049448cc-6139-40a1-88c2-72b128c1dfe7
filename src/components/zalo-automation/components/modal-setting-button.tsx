import React from "react";
import {
  Box,
  FormControl,
  IconButton,
  MenuItem,
  Select,
  TextField,
  Typography,
} from "@mui/material";
import useSnackbar from "@/src/hooks/use-snackbar";
import ClearIcon from "@mui/icons-material/Clear";
import { FILE_SIZE_2MB } from "@/src/constants/constant";
import TitleDialog from "../../dialog/TitleDialog";
const ModalSettingButton = ({
  dialogOpen,
  handleDialogClose,
  handleDialogSubmit,
  setSelectedButton,
  selectedButton,
  resizeImage,
  defaultGroupId,
  uploadFile,
  buttonActions,
  errors,
  handleArrayPayloadChange,
  handleSinglePayloadChange,
}) => {
  const snackbar = useSnackbar();
  return (
    <>
      <TitleDialog
        open={dialogOpen}
        handleClose={handleDialogClose}
        handleSubmit={handleDialogSubmit}
        title="Cài đặt Button"
        closeBtnTitle="Hủy"
        submitBtnTitle="Lưu"
      >
        <Box sx={{ p: 2 }}>
          <Typography variant="body1" sx={{ mb: 1, fontWeight: 400 }}>
            Hình ảnh{" "}
            <Typography component="span" sx={{ color: "red" }}>
              *{" "}
            </Typography>
          </Typography>
          <Box sx={{ display: "flex", gap: 1, mb: 2 }}>
            <IconButton
              color="primary"
              size="small"
              onClick={() =>
                setSelectedButton({
                  ...selectedButton,
                  imageIcon:
                    "https://cdn.cnvloyalty.com/cnv-platform/1/ecommerce/385/17/2023/6/15/tham-khao_2023-06-15-09-47-37-146.png",
                })
              }
            >
              {/* <VisibilityIcon fontSize="small" /> */}
              <img
                style={{ width: 40 }}
                src="https://cdn.cnvloyalty.com/cnv-platform/1/ecommerce/385/17/2023/6/15/tham-khao_2023-06-15-09-47-37-146.png"
              />
            </IconButton>
            <IconButton
              color="primary"
              size="small"
              onClick={() =>
                setSelectedButton({
                  ...selectedButton,
                  imageIcon:
                    "https://cdn.cnvloyalty.com/cnv-platform/1/ecommerce/385/17/2023/6/15/lien-he_2023-06-15-09-47-04-537.png",
                })
              }
            >
              {/* <PhoneIcon fontSize="small" /> */}
              <img
                style={{ width: 40 }}
                src="https://cdn.cnvloyalty.com/cnv-platform/1/ecommerce/385/17/2023/6/15/lien-he_2023-06-15-09-47-04-537.png"
              />
            </IconButton>
            <IconButton
              color="primary"
              size="small"
              onClick={() =>
                setSelectedButton({
                  ...selectedButton,
                  imageIcon:
                    "https://cdn.cnvloyalty.com/cnv-platform/1/ecommerce/385/17/2023/6/15/mua-hang_2023-06-15-09-47-24-878.png",
                })
              }
            >
              {/* <PhotoCameraIcon fontSize="small" /> */}
              <img
                style={{ width: 40 }}
                src="https://cdn.cnvloyalty.com/cnv-platform/1/ecommerce/385/17/2023/6/15/mua-hang_2023-06-15-09-47-24-878.png"
              />
            </IconButton>
          </Box>
          <Box
            sx={{
              position: "relative",
              width: "100%",
              height: 120,
              border: "1px dashed #ccc",
              borderRadius: 1,
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              justifyContent: "center",
              mb: 2,
              textAlign: "center",
            }}
          >
            {selectedButton?.imageIcon ? (
              <>
                <img style={{ width: 40, objectFit: "cover" }} src={selectedButton.imageIcon} />
                <IconButton
                  size="small"
                  sx={{
                    position: "absolute",
                    top: 4,
                    right: 4,
                    backgroundColor: "#fff",
                    "&:hover": { backgroundColor: "#f0f0f0" },
                  }}
                  onClick={() => setSelectedButton({ ...selectedButton, imageIcon: null })}
                >
                  <ClearIcon fontSize="small" />
                </IconButton>
              </>
            ) : (
              <>
                <Typography
                  variant="body2"
                  color="primary"
                  component="a"
                  href="#"
                  onClick={(e) => {
                    e.preventDefault();
                    document.getElementById("fileInput").click();
                  }}
                  sx={{ textDecoration: "underline", cursor: "pointer", mt: 1 }}
                >
                  Click để tải lên
                </Typography>
                <Typography variant="caption" color="textSecondary" sx={{ mt: 1 }}>
                  Files hỗ trợ JPG, PNG, GIF
                </Typography>
                <Typography variant="caption" color="textSecondary" sx={{ mt: 1 }}>
                  Tỷ lệ khung hình là 1:1 và tối đa 1MB.
                </Typography>
              </>
            )}
            <input
              id="fileInput"
              type="file"
              accept="image/png, image/jpeg, image/jpg, image/gif"
              hidden
              onChange={async (e) => {
                const file = e.target.files[0];
                if (file) {
                  if (file.size > FILE_SIZE_2MB) {
                    snackbar.error(
                      `Kích thước ảnh không được vượt quá ${(FILE_SIZE_2MB / 1024 / 1024).toFixed(
                        1
                      )}MB.`
                    );
                    return;
                  }
                  const allowedTypes = ["image/png", "image/jpeg", "image/jpg", "image/gif"];
                  if (!allowedTypes.includes(file.type)) {
                    alert("Định dạng ảnh không hợp lệ. Chỉ chấp nhận PNG, JPG, JPEG, GIF.");
                    return;
                  }

                  try {
                    const objectUrl = URL.createObjectURL(file);
                    setSelectedButton({
                      ...selectedButton,
                      imageIcon: objectUrl,
                      file: file,
                    });
                  } catch (error) {
                    alert("Có lỗi xảy ra khi tải lên ảnh. Vui lòng thử lại.");
                  }
                }
              }}
            />
          </Box>
          <Typography variant="body1" sx={{ mb: 0.5 }}>
            Tên button (Tối đa 35 ký tự){" "}
            <Typography component="span" sx={{ color: "red" }}>
              *{" "}
            </Typography>
          </Typography>
          <TextField
            fullWidth
            size="small"
            variant="outlined"
            value={selectedButton?.title || ""}
            onChange={(e) => setSelectedButton({ ...selectedButton, title: e.target.value })}
            sx={{ mb: 2 }}
          />
          <FormControl fullWidth size="small" sx={{ mb: 2 }}>
            <Typography variant="body1" sx={{ mb: 0.5 }}>
              Liên kết đến{" "}
              <Typography component="span" sx={{ color: "red" }}>
                *{" "}
              </Typography>
            </Typography>
            <Select
              value={selectedButton?.type}
              onChange={(e) => {
                const type = e.target.value;
                const newSelectedAction = buttonActions.find((button) => button.type === type);
                setSelectedButton({
                  ...selectedButton,
                  type: newSelectedAction.type,
                  payload: newSelectedAction.payload,
                });
              }}
            >
              {buttonActions.map((button, index) => (
                <MenuItem key={index} value={button.type}>
                  {button.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          {selectedButton && Array.isArray(selectedButton.payload) ? (
            selectedButton.payload?.map((item, index) => (
              <Box key={index}>
                <Typography variant="body1" sx={{ mb: 0.5 }}>
                  Giá trị{" "}
                  <Typography component="span" sx={{ color: "red" }}>
                    *{" "}
                  </Typography>
                </Typography>
                <TextField
                  fullWidth
                  size="small"
                  variant="outlined"
                  label=""
                  placeholder={"Nhập giá trị"}
                  value={item.value || ""}
                  error={
                    selectedButton.type === "oa.open.phone"
                      ? errors[`phone_${index}`]
                      : errors[`url_${index}`]
                  }
                  helperText={
                    selectedButton.type === "oa.open.phone"
                      ? errors[`phone_${index}`]
                        ? "Số điện thoại gồm 10 số và bắt đầu từ 0"
                        : ""
                      : errors[`url_${index}`]
                      ? "Url không hợp lệ"
                      : ""
                  }
                  onChange={(e) => handleArrayPayloadChange(index, e.target.value)}
                />
              </Box>
            ))
          ) : (
            <>
              <Typography variant="body1" sx={{ mb: 0.5 }}>
                Giá trị{" "}
                <Typography component="span" sx={{ color: "red" }}>
                  *{" "}
                </Typography>
              </Typography>
              {/* <TextField
                fullWidth
                size="small"
                variant="outlined"
                placeholder="Nhập giá trị"
                value={selectedButton?.payload?.url || selectedButton?.payload?.phone_code || ""}
                // error={errors.phone_code}
                error={selectedButton?.type === "oa.open.phone" ? errors[`phone`] : errors[`url`]}
                // helperText={errors.phone_code ? "Số điện thoại gồm 10 số và bắt đầu từ 0" : ""}
                helperText={
                  selectedButton?.type === "oa.open.phone"
                    ? errors[`phone`]
                      ? "Số điện thoại gồm 10 số và bắt đầu từ 0"
                      : ""
                    : errors[`url`]
                    ? "Url không hợp lệ"
                    : ""
                }
                onChange={(e) => handleSinglePayloadChange(e)}
              /> */}
              <TextField
                fullWidth
                size="small"
                variant="outlined"
                placeholder="Nhập giá trị"
                value={
                  selectedButton?.type?.includes("phone")
                    ? selectedButton?.payload?.phone_code || ""
                    : selectedButton?.payload?.url || ""
                }
                error={selectedButton?.type === "oa.open.phone" ? errors[`phone`] : errors[`url`]}
                helperText={
                  selectedButton?.type === "oa.open.phone"
                    ? errors[`phone`]
                      ? "Số điện thoại gồm 10 số và bắt đầu từ 0"
                      : ""
                    : errors[`url`]
                    ? "Url không hợp lệ"
                    : ""
                }
                onChange={handleSinglePayloadChange}
              />
            </>
          )}
        </Box>
      </TitleDialog>
    </>
  );
};

export default ModalSettingButton;
