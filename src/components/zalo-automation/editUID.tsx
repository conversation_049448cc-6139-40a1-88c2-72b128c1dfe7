// import React, { useEffect, useState } from "react";
// import { Container, Grid, Paper, Typography, Divider, Popover, Button, Box } from "@mui/material";
// import LeftPanel from "./leftPanel";
// import RightPanel from "./rightPanel";
// import EmojiPicker from "emoji-picker-react";
// import LeftPanelNewText from "./leftPanelNewText";
// import toast from "react-hot-toast";
// import { ACTIVE_STATUS, TEMPLATE_TYPE, TEXT_ALIGN_LEFT, ZALO_TABS } from "@/src/constants/constant";
// import { useZaloTemplate } from "@/src/api/hooks/zalo-template/zalo-template";
// import { logger } from "@/src/utils/logger";
// import {
//   ISendType,
//   IZaloElement,
//   IZaloTemplate,
//   ZaloElementTypeEnum,
// } from "@/src/api/types/zalo-template.types";
// import useSnackbar from "@/src/hooks/use-snackbar";
// import { useStoreId } from "@/src/hooks/use-store-id";

// interface TransactionNotificationFormProps {
//   type?: number;
//   onClose: () => void;
//   fetchZaloTemplate: () => void;
//   selectedTemplate?: any;
// }

// const INITIAL_ELEMENT = {
//   BANNER: { type: ZaloElementTypeEnum.BANNER, content: "" },
//   HEADER: { type: ZaloElementTypeEnum.HEADER, align: TEXT_ALIGN_LEFT, content: "" },
//   CONTENT: { type: ZaloElementTypeEnum.CONTENT, align: TEXT_ALIGN_LEFT, content: "" },
//   SUB_CONTENT: { type: ZaloElementTypeEnum.CONTENT, align: TEXT_ALIGN_LEFT, content: "" },
// };

// export default function TransactionNotificationForm({
//   type = 0,
//   onClose,
//   fetchZaloTemplate,
//   selectedTemplate,
// }: TransactionNotificationFormProps) {
//   const {
//     getTriggerEventsByType,
//     getSendTypes,
//     getMessageTypesByTemplateType,
//     createZaloTemplate,
//     updateZaloTemplate,
//     getTriggerPrameter,
//   } = useZaloTemplate();
//   const snackbar = useSnackbar();
//   const storeId = useStoreId();
//   const [triggerEvents, setTriggerEvents] = useState<any[]>([]);
//   const [sendTypes, setSendTypes] = useState<ISendType[]>([]);
//   const [messageTypes, setMessageTypes] = useState<any[]>([]);
//   const [buttons, setButtons] = useState<any[]>([]);
//   const [bannerImage, setBannerImage] = useState<IZaloElement>(INITIAL_ELEMENT.BANNER);
//   const [title, setTitle] = useState<IZaloElement>(INITIAL_ELEMENT.HEADER);
//   const [content, setContent] = useState<IZaloElement>(INITIAL_ELEMENT.CONTENT);
//   const [subContent, setSubContent] = useState<IZaloElement>(INITIAL_ELEMENT.SUB_CONTENT);
//   const [templateName, setTemplateName] = useState();
//   const [templateType, setTemplateType] = useState<any>(TEMPLATE_TYPE[2].value);
//   const [selectedTriggerEvent, setSelectedTriggerEvent] = useState<any>(null);
//   const [selectedSendType, setSelectedSendType] = useState<any>(null);
//   const [selectedMessageType, setSelectedMessageType] = useState<any>(null);
//   const [newParameters, setNewParameters] = useState<any[]>([]);
//   const [emojiAnchorEl, setEmojiAnchorEl] = useState<null | HTMLElement>(null);
//   const [activeInput, setActiveInput] = useState<null | string>(null);
//   const [triggerPrameters, setTriggerPrameters] = useState<any[]>([]);

//   const [isLoading, setIsLoading] = useState(true);

//   useEffect(() => {
//     const loadData = async () => {
//       await fetchInitialData();
//       setIsLoading(false);
//     };
//     loadData();
//   }, []);

//   useEffect(() => {
//     // fetchInitialData();
//     if (selectedTemplate) {
//       setTemplateName(selectedTemplate.name);
//       setTemplateType(
//         TEMPLATE_TYPE.find((t) => t.text === selectedTemplate.type)?.value || TEMPLATE_TYPE[0].value
//       );
//       setSelectedSendType(sendTypes.find((t) => t === selectedTemplate.sendType) || sendTypes[0]);
//       setBannerImage(
//         selectedTemplate.transaction?.message.attachment.payload.elements.find(
//           (e: IZaloElement) => e.type === ZaloElementTypeEnum.BANNER
//         ) || INITIAL_ELEMENT.BANNER
//       );
//       setTitle(
//         selectedTemplate.transaction.message.attachment.payload.elements.find(
//           (e: IZaloElement) => e.type === ZaloElementTypeEnum.HEADER
//         ) || INITIAL_ELEMENT.HEADER
//       );
//       const contentElements =
//         selectedTemplate.transaction.message.attachment.payload.elements.filter(
//           (e: IZaloElement) => e.type === ZaloElementTypeEnum.CONTENT
//         );

//       setContent(
//         contentElements[0] || {
//           type: ZaloElementTypeEnum.CONTENT,
//           align: TEXT_ALIGN_LEFT,
//           content: selectedTemplate.transaction.message?.text,
//         }
//       );
//       setSubContent(contentElements[1] || INITIAL_ELEMENT.CONTENT);
//       setButtons(
//         (selectedTemplate.transaction.message.attachment.payload.buttons || []).map((button) => ({
//           ...button,
//           payload: Object.entries(button.payload).map(([name, value]) => ({
//             name,
//             value,
//           })),
//         }))
//       );
//     }
//   }, [isLoading]);

//   useEffect(() => {
//     if (messageTypes && messageTypes.length > 0 && selectedTemplate) {
//       const matchedMessageType = messageTypes.find(
//         (m) => m.typeName === selectedTemplate.messageType
//       );
//       setSelectedMessageType(matchedMessageType || null);
//     }
//   }, [messageTypes, selectedTemplate]);

//   useEffect(() => {
//     if (triggerEvents && triggerEvents.length > 0) {
//       if (selectedTemplate) {
//         setSelectedTriggerEvent(
//           triggerEvents.find((e) => e.code === selectedTemplate.triggerEvent)
//         );
//       }
//     }
//   }, [triggerEvents]);

//   useEffect(() => {
//     if (sendTypes && sendTypes.length > 0) {
//       if (selectedTemplate) {
//         setSelectedSendType(
//           sendTypes.find((e) => e.id === selectedTemplate.sendType.id) || sendTypes[0]
//         );
//       }
//     }
//   }, [sendTypes]);

//   useEffect(() => {
//     if (selectedMessageType && selectedTemplate) {
//       const filteredParams =
//         selectedTemplate.transaction.message.attachment.payload.elements
//           .find((e: IZaloElement) => e.type === ZaloElementTypeEnum.TABLE)
//           ?.contentTable.filter(
//             (param: any) =>
//               !selectedMessageType.defaultParameters?.some((p: any) => p.value === param.value)
//           ) || [];

//       const formattedParams = filteredParams.map((param: any) => ({
//         name: param.key,
//         value: param.value,
//       }));
//       setNewParameters(formattedParams);
//     }
//   }, [selectedMessageType]);

//   useEffect(() => {
//     if (templateType == TEMPLATE_TYPE[0].value) {
//       fetchMessageTypesByTemplateType(TEMPLATE_TYPE[templateType].text);
//     }
//   }, [templateType]);

//   const fetchInitialData = async () => {
//     await Promise.all([fetchTriggerEvents(), fetchSendTypes(), fetchTriggerPrameter()]);
//   };

//   const fetchTriggerEvents = async () => {
//     try {
//       const response = await getTriggerEventsByType(ZALO_TABS[type].text);
//       const events = response.data.data;
//       setTriggerEvents(events);
//     } catch (error) {
//       logger.error("Error fetching trigger events:", error);
//     }
//   };

//   const fetchSendTypes = async () => {
//     try {
//       const response = await getSendTypes();
//       const types = response.data.data;
//       setSendTypes(types);
//     } catch (error) {
//       logger.error("Error fetching send types:", error);
//     }
//   };

//   const fetchMessageTypesByTemplateType = async (type: string) => {
//     try {
//       const response = await getMessageTypesByTemplateType(type);
//       const types = response.data.data;
//       setMessageTypes(types);
//     } catch (error) {
//       logger.error("Error fetching message types:", error);
//     }
//   };

//   const fetchTriggerPrameter = async () => {
//     try {
//       const response = await getTriggerPrameter();
//       setTriggerPrameters(response.data.data);
//     } catch (error) {
//       logger.error("Error fetching shops:", error);
//     }
//   };

//   const handleEmojiClick = (emojiData: any) => {
//     const updateState = (prev: IZaloElement) => ({
//       ...prev,
//       content: (prev.content || "") + emojiData.emoji,
//     });
//     if (activeInput === "title") setTitle(updateState);
//     else if (activeInput === "content") setContent(updateState);
//     else if (activeInput === "subContent") setSubContent(updateState);
//     setEmojiAnchorEl(null);
//     setActiveInput(null);
//   };

//   const handleCreateZaloTemplate = async (zaloTemplate: IZaloTemplate) => {
//     try {
//       const response = await createZaloTemplate(zaloTemplate);
//       if (response?.data) {
//         snackbar.success("Tạo mẫu tin Zalo thành công");
//         fetchZaloTemplate();
//         onClose();
//       }
//     } catch (error) {
//       logger.error("Error creating Zalo template:", error);
//       snackbar.error("Có lỗi xảy ra khi tạo mẫu tin Zalo");
//     }
//   };

//   const validateFields = (templateType) => {
//     const commonFields = [
//       { value: templateName, message: "Vui lòng nhập tên mẫu tin" },
//       { value: bannerImage?.imageUrl, message: "Vui lòng chọn ảnh banner" },
//       { value: title.content, message: "Vui lòng nhập tiêu đề" },
//       { value: content.content, message: "Vui lòng nhập nội dung" },
//       { value: subContent?.content, message: "Vui lòng nhập nội dung phụ" },
//       { value: selectedSendType, message: "Vui lòng chọn thời gian gửi" },
//       { value: selectedTriggerEvent, message: "Vui lòng chọn sự kiện kích hoạt" },
//     ];
//     const contentTable = [
//       ...[...(selectedMessageType?.defaultParameters || []), ...(newParameters || [])]
//         .filter((param, index, self) => index === self.findIndex((p) => p.name === param.name))
//         .map((param: any) => ({
//           key: param.name,
//           value: param.value,
//         })),
//     ];

//     contentTable.forEach((item, index) => {
//       if (!item.key || item.key.trim() === "") {
//         commonFields.push({
//           value: null,
//           message: `Vui lòng nhập key cho tham số thứ ${index + 1} trong bảng thông tin`,
//         });
//       }
//       if (!item.value || item.value.trim() === "") {
//         commonFields.push({
//           value: null,
//           message: `Vui lòng nhập value cho tham số thứ ${index + 1} trong bảng thông tin`,
//         });
//       }
//     });
//     const transformedButtons = buttons.map((button) => ({
//       ...button,
//       payload: button.payload.reduce(
//         (acc, item) => ({
//           ...acc,
//           [item.name]: item.value,
//         }),
//         {}
//       ),
//     }));

//     transformedButtons.forEach((button, index) => {
//       if (!button.title || button.title.trim() === "") {
//         commonFields.push({
//           value: null,
//           message: `Vui lòng nhập tiêu đề cho button thứ ${index + 1}`,
//         });
//       }

//       if (!button.imageIcon || button.imageIcon.trim() === "") {
//         commonFields.push({
//           value: null,
//           message: `Vui lòng chọn ảnh icon cho button thứ ${index + 1}`,
//         });
//       }

//       if (!button.type || button.type.trim() === "") {
//         commonFields.push({
//           value: null,
//           message: `Vui lòng chọn loại cho button thứ ${index + 1}`,
//         });
//       }

//       if (!button.payload || Object.keys(button.payload).length === 0) {
//         commonFields.push({
//           value: null,
//           message: `Vui lòng nhập payload cho button thứ ${index + 1}`,
//         });
//       } else {
//         Object.entries(button.payload).forEach(([key, value], payloadIndex) => {
//           if (!key || key.trim() === "") {
//             commonFields.push({
//               value: null,
//               message: `Vui lòng nhập giá trị thứ ${payloadIndex + 1} trong button thứ ${
//                 index + 1
//               }`,
//             });
//           }
//           if (value === null || value === undefined || value.toString().trim() === "") {
//             commonFields.push({
//               value: null,
//               message: `Vui lòng nhập giá trị thứ ${payloadIndex + 1} trong button thứ ${
//                 index + 1
//               }`,
//             });
//           }
//         });
//       }
//     });

//     const transactionFields = [
//       { value: selectedMessageType, message: "Vui lòng chọn loại tin giao dịch" },
//     ];

//     const consultationFields = [
//       { value: templateName, message: "Vui lòng nhập tên mẫu tin" },
//       { value: content.content, message: "Vui lòng nhập nội dung thông báo văn bản" },
//       { value: selectedSendType, message: "Vui lòng chọn thời gian gửi" },
//       { value: selectedTriggerEvent, message: "Vui lòng chọn sự kiện kích hoạt" },
//     ];

//     let requiredFields = [...commonFields];
//     switch (templateType) {
//       case 0:
//         requiredFields = [...commonFields, ...transactionFields];
//         break;
//       case 1:
//         requiredFields = [...commonFields];
//         break;
//       case 2:
//         requiredFields = [...consultationFields];
//         break;
//       default:
//         toast.error("Vui lòng chọn loại tin");
//         return false;
//     }
//     for (const field of requiredFields) {
//       if (!field.value) {
//         toast.error(field.message);
//         return false;
//       }
//     }
//     return true;
//   };

//   const handleSubmit = async () => {
//     if (!validateFields(templateType)) return;
//     const zaloTemplate: IZaloTemplate = {
//       Name: templateName,
//       ShopId: storeId,
//       TemplateCategory: ZALO_TABS[type].text,
//       Type: TEMPLATE_TYPE[templateType].text,
//       MessageType: templateType === TEMPLATE_TYPE[0].value ? selectedMessageType?.typeName : null,
//       SendType: selectedSendType,
//       Status: ACTIVE_STATUS,
//       TriggerEvent: selectedTriggerEvent.code,
//       Transaction: {
//         recipient: { userId: "" },
//         message: {
//           text: templateType === TEMPLATE_TYPE[2].value ? content.content : "",
//           attachment: {
//             type: "template",
//             payload: {
//               templateType: "",
//               language: "vi",
//               elements:
//                 templateType !== TEMPLATE_TYPE[2].value
//                   ? [
//                       bannerImage,
//                       title,
//                       content,
//                       {
//                         type: ZaloElementTypeEnum.TABLE,
//                         contentTable: [
//                           ...[
//                             ...(selectedMessageType?.defaultParameters || []),
//                             ...(newParameters || []),
//                           ]
//                             .filter(
//                               (param, index, self) =>
//                                 index === self.findIndex((p) => p.name === param.name)
//                             )
//                             .map((param: any) => ({
//                               key: param.name,
//                               value: param.value,
//                             })),
//                         ],
//                       },
//                       subContent,
//                     ]
//                   : bannerImage
//                   ? [
//                       {
//                         ...bannerImage,
//                         type: bannerImage.type,
//                         mediaType: "image",
//                         url: bannerImage.imageUrl,
//                       },
//                     ]
//                   : [],
//               buttons:
//                 templateType !== TEMPLATE_TYPE[2].value
//                   ? buttons.map((button) => ({
//                       ...button,
//                       payload: button.payload.reduce(
//                         (acc, item) => ({
//                           ...acc,
//                           [item.name]: item.value,
//                         }),
//                         {}
//                       ),
//                     }))
//                   : [],
//             },
//           },
//         },
//       },
//     };
//     if (selectedTemplate) {
//       try {
//         const response = await updateZaloTemplate({ ...zaloTemplate, id: selectedTemplate.id });
//         if (response?.data) {
//           snackbar.success("Cập nhật mẫu tin Zalo thành công");
//           fetchZaloTemplate();
//           onClose();
//         }
//       } catch (error) {
//         snackbar.error("Có lỗi xảy ra khi cập nhật mẫu tin Zalo");
//       }
//     } else {
//       handleCreateZaloTemplate(zaloTemplate);
//     }
//   };

//   const isNewTextTemplate = templateType === TEMPLATE_TYPE[2].value;

//   return (
//     <Container sx={{ py: 1 }}>
//       <Paper elevation={1} sx={{ p: 1, borderRadius: 2 }}>
//         <Typography variant="h6" sx={{ mb: 2, fontWeight: "bold" }}>
//           {ZALO_TABS[type].label} {templateName ? `- ${templateName}` : ""}
//         </Typography>
//         <Divider sx={{ my: 2 }} />
//         <Grid container spacing={3}>
//           {isNewTextTemplate ? (
//             <LeftPanelNewText
//               bannerImage={bannerImage}
//               setBannerImage={setBannerImage}
//               content={content}
//               setContent={setContent}
//               triggerPrameters={triggerPrameters}
//             />
//           ) : (
//             <LeftPanel
//               title={title}
//               setTitle={setTitle}
//               content={content}
//               setContent={setContent}
//               subContent={subContent}
//               setSubContent={setSubContent}
//               buttons={buttons}
//               setButtons={setButtons}
//               bannerImage={bannerImage}
//               setBannerImage={setBannerImage}
//               selectedMessageType={selectedMessageType}
//               newParameter={newParameters}
//               setNewParameter={setNewParameters}
//               triggerPrameters={triggerPrameters}
//             />
//           )}
//           <RightPanel
//             triggerEvents={triggerEvents}
//             sendTypes={sendTypes}
//             title={title}
//             setTitle={setTitle}
//             content={content}
//             setContent={setContent}
//             subContent={subContent}
//             setSubContent={setSubContent}
//             templateName={templateName}
//             setTemplateName={setTemplateName}
//             buttons={buttons}
//             templateType={templateType}
//             setTemplateType={setTemplateType}
//             messageType={messageTypes}
//             setMessageType={setMessageTypes}
//             bannerImage={bannerImage}
//             selectedMessageType={selectedMessageType}
//             setSelectedMessageType={setSelectedMessageType}
//             selectedTriggerEvent={selectedTriggerEvent}
//             setSelectedTriggerEvent={setSelectedTriggerEvent}
//             selectedSendTypes={selectedSendType}
//             setSelectedSendTypes={setSelectedSendType}
//             isEdit={selectedTemplate ? true : false}
//             newParameter={newParameters}
//           />
//         </Grid>
//       </Paper>

//       <Popover
//         open={Boolean(emojiAnchorEl)}
//         anchorEl={emojiAnchorEl}
//         onClose={() => {
//           setEmojiAnchorEl(null);
//           setActiveInput(null);
//         }}
//         anchorOrigin={{ vertical: "top", horizontal: "left" }}
//         transformOrigin={{ vertical: "bottom", horizontal: "left" }}
//       >
//         <EmojiPicker onEmojiClick={handleEmojiClick} />
//       </Popover>

//       <Box sx={{ display: "flex", justifyContent: "flex-end", mt: 3 }}>
//         <Button
//           variant="contained"
//           sx={{
//             backgroundColor: "#e0e0e0",
//             color: "#000",
//             "&:hover": { backgroundColor: "#d6d6d6" },
//             mr: 2,
//           }}
//           onClick={onClose}
//         >
//           Hủy bỏ
//         </Button>
//         <Button variant="contained" color="primary" onClick={handleSubmit}>
//           Xác nhận
//         </Button>
//       </Box>
//     </Container>
//   );
// }
