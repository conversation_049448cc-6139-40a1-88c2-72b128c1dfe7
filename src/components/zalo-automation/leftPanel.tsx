import { useEffect, useState } from "react";
import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Divider,
  FormControl,
  Grid,
  IconButton,
  InputAdornment,
  Menu,
  MenuItem,
  Paper,
  Popover,
  Select,
  TextField,
  Typography,
} from "@mui/material";
import useSnackbar from "@/src/hooks/use-snackbar";
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Settings as SettingsIcon,
  FormatAlignLeft as FormatAlignLeftIcon,
  FormatAlignCenter as FormatAlignCenterIcon,
  FormatAlignRight as FormatAlignRightIcon,
} from "@mui/icons-material";
import EmojiPicker from "emoji-picker-react";
import TitleDialog from "../dialog/TitleDialog";
import VisibilityIcon from "@mui/icons-material/Visibility";
import PhoneIcon from "@mui/icons-material/Phone";
import PhotoCameraIcon from "@mui/icons-material/PhotoCamera";
import Autocomplete from "@mui/material/Autocomplete";
import ClearIcon from "@mui/icons-material/Clear";
import { v4 as uuidv4 } from "uuid";
import { useMedia } from "@/src/api/hooks/media/use-media";
import { useZaloTemplate } from "@/src/api/hooks/zalo-template/zalo-template";
import { logger } from "@/src/utils/logger";
import {
  IZaloButton,
  IZaloElement,
  ZaloElementTypeEnum,
} from "@/src/api/types/zalo-template.types";
import {
  FILE_SIZE_2MB,
  TEMPLATE_TYPE,
  TEXT_ALIGN_CENTER,
  TEXT_ALIGN_LEFT,
  TEXT_ALIGN_RIGHT,
} from "@/src/constants/constant";
import { EnhancedTextField } from "./highlightedTextField";
import ModalSettingButton from "./components/modal-setting-button";
import { CreateFileGroupRequest, GetGroupFileRequest, RefType } from "@/src/api/types/media.types";
import { useStoreId } from "@/src/hooks/use-store-id";
import { FileType } from "@/src/constants/file-types";

const LeftPanel = ({
  title,
  setTitle,
  content,
  setContent,
  subContent,
  setSubContent,
  buttons,
  setButtons,
  bannerImage,
  setBannerImage,
  selectedMessageType,
  newParameter,
  setNewParameter,
  triggerPrameters,
  templateType,
  setTemplateType,
  selectedMsgTypeTrans,
  setSelectedMessageType,
  messageType,
  selectedButton,
  setSelectedButton,
}) => {
  const { getButtonActions } = useZaloTemplate();
  const snackbar = useSnackbar();
  // State cho emoji picker và text alignment
  const [emojiAnchorEl, setEmojiAnchorEl] = useState(null);
  const [activeInput, setActiveInput] = useState(null);
  // State cho dialog cài đặt của button
  const [dialogOpen, setDialogOpen] = useState(false);
  const [defaultGroupId, setDefaultGroupId] = useState<string>("");
  const { uploadFile, getGroups } = useMedia();
  const [buttonActions, setButtonActions] = useState<any[]>([]);
  const [titleAnchorEl, setTitleAnchorEl] = useState(null);
  const [contentAnchorEl, setContentAnchorEl] = useState(null);
  const [subContentAnchorEl, setSubContentAnchorEl] = useState(null);
  const [openDialogDeleteContent, setOpenDialogDeleteContent] = useState(false);
  const [deleteContentIndex, setDeleteContentIndex] = useState(null);
  const [openDialogDeleteButton, setOpenDialogDeleteButton] = useState(false);
  const [deleteButtonId, setDeleteButtonId] = useState(null);
  const [errors, setErrors] = useState<any>({});
  const [isError, setIsError] = useState<boolean>(false);
  const [indexButtonSetting, setIndexbuttonSetting] = useState<number | null>(null);
  const storeId = useStoreId();
  const validatePhoneNumber = (phone) => {
    if (!phone) return true;
    return /^0[0-9]{9}$/.test(phone);
  };

  const validateWebsite = (url: string) => {
    if (!url) return true;
    return /^https:\/\/[^\s/$.?#].[^\s]*$/.test(url);
  };

  const checkPhoneError = (phone, index = null) => {
    const isValid = validatePhoneNumber(phone);

    if (index !== null) {
      setErrors((prev) => ({
        ...prev,
        [`phone_${index}`]: !isValid,
        [`url_${index}`]: false,
      }));
    } else {
      setErrors((prev) => ({
        ...prev,
        phone: !isValid,
      }));
    }

    return isValid;
  };

  useEffect(() => {
    if (selectedButton?.type?.includes("phone")) {
      if (Array.isArray(selectedButton.payload)) {
        // Validate từng số trong mảng
        const newErrors = {};
        selectedButton.payload.forEach((item, index) => {
          if (item.value) {
            newErrors[`phone_${index}`] = !validatePhoneNumber(item.value);
          }
        });
        setErrors(newErrors);
      } else if (selectedButton?.payload?.phone) {
        // Validate số điện thoại đơn
        const isValid = validatePhoneNumber(selectedButton.payload.phone);
        setErrors({ phone: !isValid });
      }
    }
  }, [selectedButton]);

  useEffect(() => {
    const fetchDefaultGroup = async () => {
      try {
        const params: GetGroupFileRequest = {
          ShopId: storeId,
          Limit: 100,
          Skip: 0,
        };
        const response = await getGroups(params);
        if (response.data?.data?.length > 0) {
          setDefaultGroupId(response.data.data[0].groupFileId);
        }
      } catch (error) {}
    };
    fetchDefaultGroup();
    fetchButtonActons();
  }, [storeId]);

  const fetchButtonActons = async () => {
    try {
      const response = await getButtonActions();
      setButtonActions(response.data.data);
    } catch (error) {
      logger.error("Error fetching shops:", error);
    }
  };

  // Xử lý emoji click
  const handleEmojiClick = (emojiData: any) => {
    const updateState = (prev: IZaloElement) => ({
      ...prev,
      content: (prev.content || "") + emojiData.emoji,
    });
    if (activeInput === "title") setTitle(updateState);
    else if (activeInput === "content") setContent(updateState);
    else if (activeInput === "subContent") setSubContent(updateState);
    setEmojiAnchorEl(null);
    setActiveInput(null);
  };

  // Xử lý căn chỉnh text
  const handleTitleAlignmentChange = (newAlignment) => {
    setTitle({
      ...title,
      align: newAlignment,
    });
  };
  const handleContentAlignmentChange = (newAlignment) => {
    setContent({
      ...content,
      align: newAlignment,
    });
  };
  const handleSubContentAlignmentChange = (newAlignment) => {
    setSubContent({
      ...subContent,
      align: newAlignment,
    });
  };

  // Xử lý button
  const handleAddButton = () => {
    if (buttons.length < 4) {
      setButtons([
        ...buttons,
        {
          id: uuidv4().toString(),
          title: `Tên button #${buttons.length + 1}`,
          type: buttonActions[0]?.type,
          payload: buttonActions[0]?.payload,
        },
      ]);
    }
  };
  const handleRemoveButton = (id) => {
    setButtons(buttons.filter((b) => b.id !== id));
  };

  // Xử lý upload file ảnh
  const handleFileUpload = async (event) => {
    const file = event.target.files[0];
    if (file) {
      if (file.size > FILE_SIZE_2MB) {
        snackbar.error(
          `Kích thước ảnh không được vượt quá ${(FILE_SIZE_2MB / 1024 / 1024).toFixed(1)}MB.`
        );
        return;
      }
      const allowedTypes = ["image/png", "image/jpeg", "image/jpg", "image/gif"];
      if (!allowedTypes.includes(file.type)) {
        alert("Định dạng ảnh không hợp lệ. Chỉ chấp nhận PNG, JPG, JPEG, GIF.");
        return;
      }

      const objectUrl = URL.createObjectURL(file);
      setBannerImage({
        ...bannerImage,
        imageUrl: objectUrl,
        mediaType: FileType.IMAGE,
        type: ZaloElementTypeEnum.BANNER,
        file: file,
      });
    }
  };
  const handleRemoveBannerImage = () => {
    setBannerImage(null);
  };
  // Xử lý thay đổi tham số
  const handleParameterChange = (value, index) => {
    const selectedParam = triggerPrameters.find((param) => param.value === value);
    setNewParameter(newParameter.map((p, i) => (i === index ? { ...selectedParam } : p)));
  };

  // Xử lý click vào icon Settings của button
  const handleSettingsClick = (button) => {
    setSelectedButton(button);
    setDialogOpen(true);
  };

  // Đóng dialog
  const handleDialogClose = () => {
    setDialogOpen(false);
    setSelectedButton(null);
  };

  // Xử lý submit dialog (có thể update thông tin button nếu cần)
  const handleDialogSubmit = () => {
    if (errors.phone === true || errors.url_0 === true) {
      // snackbar.warning("Hãy nhập đúng số điện thoại");
    } else {
      setButtons((prev) => {
        const existingIndex = prev.findIndex((btn, index) => index === indexButtonSetting);

        if (existingIndex !== -1) {
          return prev.map((btn, index) => (index === existingIndex ? selectedButton : btn));
        } else {
          return [...prev, selectedButton];
        }
      });
      handleDialogClose();
    }
  };

  const resizeImage = (file, maxWidth, maxHeight) => {
    return new Promise((resolve, reject) => {
      const img = new Image();
      const reader = new FileReader();

      reader.onload = (e) => {
        if (typeof e.target?.result === "string") {
          img.src = e.target.result;
        }
      };

      img.onload = () => {
        const canvas = document.createElement("canvas");
        canvas.width = maxWidth;
        canvas.height = maxHeight;
        const ctx = canvas.getContext("2d");
        ctx.drawImage(img, 0, 0, maxWidth, maxHeight);

        canvas.toBlob((blob) => {
          resolve(new File([blob], file.name, { type: file.type }));
        }, file.type);
      };

      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  };

  const handleClose = (type) => {
    switch (type) {
      case "title":
        setTitleAnchorEl(null);
        break;
      case "content":
        setContentAnchorEl(null);
        break;
      case "subContent":
        setSubContentAnchorEl(null);
        break;
      default:
        break;
    }
  };

  const handleClickParam = (type, param) => {
    if (type === "title") {
      setTitle({
        ...title,
        content: (title?.content || "") + " " + param.value,
      });
    } else if (type === "content") {
      setContent({
        ...content,
        content: (content?.content || "") + " " + param.value,
      });
    } else if (type === "subContent") {
      setSubContent({
        ...subContent,
        content: (subContent?.content || "") + " " + param.value,
      });
    }
    handleClose(type);
  };

  const handleTitleFlashClick = (event) => setTitleAnchorEl(event.currentTarget);
  const handleContentFlashClick = (event) => setContentAnchorEl(event.currentTarget);
  const handleSubContentFlashClick = (event) => setSubContentAnchorEl(event.currentTarget);

  const handleDeleteButtonClick = (index) => {
    setDeleteButtonId(index);
    setOpenDialogDeleteButton(true);
  };

  const handleConfirmDeleteButton = () => {
    setButtons(buttons.filter((b, index) => index !== deleteButtonId));
    setOpenDialogDeleteButton(false);
    setDeleteButtonId(null);
  };

  const handleCancelDeleteButton = () => {
    setDeleteButtonId(null);
    setOpenDialogDeleteButton(false);
  };

  const handleDeleteContentClick = (index) => {
    setDeleteContentIndex(index);
    setOpenDialogDeleteContent(true);
  };

  const handleCancelDeleteContent = () => {
    setDeleteContentIndex(null);
    setOpenDialogDeleteContent(false);
  };

  const handleConfirmDeleteContent = () => {
    if (deleteContentIndex !== null) {
      if (newParameter.length > 0) {
        setNewParameter(newParameter.filter((_, i) => i !== deleteContentIndex));
      } else {
        setSelectedMessageType((prev) =>
          Array.isArray(prev) ? prev.filter((_, i) => i !== deleteContentIndex) : []
        );
      }
    }
    setDeleteContentIndex(null);
    setOpenDialogDeleteContent(false);
  };

  const handleArrayPayloadChange = (index, newValue) => {
    const isPhone = selectedButton?.type?.includes("phone");
    if (newValue.length === 0) {
      return;
    }
    if (isPhone) {
      if (!/^[0-9]*$/.test(newValue)) {
        return;
      }

      if (newValue.length > 0 && newValue[0] !== "0") {
        newValue = "0" + newValue.substring(1);
      }

      if (newValue.length > 10) {
        newValue = newValue.substring(0, 10);
      }

      checkPhoneError(newValue, index);
    } else {
      const isValidUrl = validateWebsite(newValue);
      setErrors((prev) => ({
        ...prev,
        [`url_${index}`]: !isValidUrl,
        [`phone_${index}`]: false,
      }));
      // if (!isValidUrl) {
      //   return;
      // }
    }

    const updatedPayload = selectedButton.payload.map((p, i) =>
      i === index ? { ...p, value: newValue } : p
    );

    setSelectedButton({
      ...selectedButton,
      payload: updatedPayload,
    });
  };

  const handleSinglePayloadChange = (e) => {
    const currentValue = e.target.value;
    const isPhone = selectedButton?.type?.includes("phone");

    if (isPhone) {
      if (!/^\d*$/.test(currentValue)) {
        return;
      }

      let formattedValue = currentValue;
      if (formattedValue && formattedValue[0] !== "0") {
        formattedValue = "0" + formattedValue.slice(1);
      }
      if (formattedValue.length > 10) {
        formattedValue = formattedValue.slice(0, 10);
      }

      setSelectedButton((prev) => ({
        ...prev,
        payload: {
          phone_code: formattedValue,
        },
      }));
      checkPhoneError(currentValue, indexButtonSetting);
    } else {
      const isValidUrl = validateWebsite(currentValue);
      setSelectedButton((prev) => ({
        ...prev,
        payload: {
          url: currentValue,
        },
      }));
    }
  };

  return (
    <Grid
      item
      xs={12}
      md={8}
      sx={{
        overflowY: "auto",
        maxHeight: "700px",
        // maxHeight: "1200px",
        // minHeight: "200px",
        width: "55%",
        display: "flex",
        flexDirection: "column",
        padding: 2,
        "&::-webkit-scrollbar": {
          width: "8px",
        },
        "&::-webkit-scrollbar-track": {
          background: "#f1f1f1",
        },
        "&::-webkit-scrollbar-thumb": {
          background: "#888",
          borderRadius: "4px",
        },
        "&::-webkit-scrollbar-thumb:hover": {
          background: "#555",
        },
      }}
    >
      {/* Phần upload Banner */}

      <Grid item xs={12} md={12} sx={{ marginBottom: 1 }}>
        <Typography variant="subtitle1" gutterBottom>
          Loại tin <span style={{ color: "red" }}>*</span>
        </Typography>
        <FormControl fullWidth size="small">
          <Select
            value={templateType}
            onChange={(e) => {
              setTemplateType(e.target.value);
            }}
          >
            {TEMPLATE_TYPE.map((option, index) => (
              <MenuItem key={index} value={option.value}>
                {option.label}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Grid>
      {templateType === TEMPLATE_TYPE[0].value && (
        <Grid item xs={12} md={12} sx={{ marginBottom: 1 }}>
          <Typography variant="subtitle1" gutterBottom>
            Loại tin giao dịch <span style={{ color: "red" }}>*</span>
          </Typography>
          <FormControl fullWidth size="small">
            <Select
              value={selectedMessageType?.typeName || selectedMsgTypeTrans}
              onChange={(e) => {
                const selectedId = e.target.value;
                const newSelectedMessageType = messageType.find(
                  (message) => message.typeName === selectedId
                );
                setSelectedMessageType(newSelectedMessageType);
              }}
            >
              {messageType?.map((message, index) => (
                <MenuItem key={index} value={message.typeName}>
                  {message.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>
      )}
      <Box sx={{ mb: 1 }}>
        <Typography variant="subtitle1" gutterBottom>
          Banner <span style={{ color: "red" }}>*</span>
        </Typography>
        <Paper
          variant="outlined"
          sx={{
            p: 2,
            height: 100,
            display: "flex",
            flexDirection: "column",
            justifyContent: "center",
            alignItems: "center",
            border: "1px solid #ccc",
            position: "relative",
          }}
        >
          {bannerImage?.imageUrl ? (
            <Box
              sx={{
                position: "relative",
                width: "100%",
                height: "100%",
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              <img
                src={bannerImage.imageUrl}
                alt="Uploaded Banner"
                style={{ maxWidth: "100%", maxHeight: "100%", objectFit: "contain" }}
              />
              <IconButton
                size="small"
                sx={{
                  position: "absolute",
                  top: 4,
                  right: 4,
                  backgroundColor: "#fff",
                  "&:hover": { backgroundColor: "#f0f0f0" },
                }}
                onClick={handleRemoveBannerImage}
              >
                <ClearIcon fontSize="small" />
              </IconButton>
            </Box>
          ) : (
            <>
              <Typography variant="body2" color="primary" align="center">
                Thả file của bạn vào đây hoặc
              </Typography>
              <Button size="small" variant="text" color="primary" component="label">
                click để tải lên
                <input
                  type="file"
                  accept="image/png, image/jpeg, image/jpg, image/gif"
                  hidden
                  size={FILE_SIZE_2MB}
                  onChange={handleFileUpload}
                />
              </Button>
              <Typography variant="caption" color="textSecondary" align="center">
                Định dạng cho phép: PNG, JPG, JPEG, GIF
              </Typography>
              <Typography variant="caption" color="textSecondary" align="center">
                Kích thước ảnh không vượt quá 1MB
              </Typography>
            </>
          )}
        </Paper>
      </Box>

      {/* Phần nhập tiêu đề */}
      <Box>
        <Typography variant="subtitle1" gutterBottom>
          Tiêu đề <span style={{ color: "red" }}>*</span>
        </Typography>
        <EnhancedTextField
          fullWidth
          variant="outlined"
          size="small"
          value={title?.content}
          onChange={(e) => {
            const newValue = e.target.value;

            // Nếu người dùng đang xóa ký tự → luôn cho phép
            if (newValue.length <= title?.content?.length || newValue.length <= 100) {
              setTitle({
                ...title,
                content: newValue,
              });
            }
            // Nếu người dùng đang cố gắng nhập thêm khi đã đủ 100 → không làm gì cả
          }}
          inputProps={{
            style: { textAlign: title?.align ? title?.align : "left" },
            maxLength: 100,
          }}
        />

        <Box sx={{ display: "flex", justifyContent: "flex-end", alignItems: "center", mt: 1 }}>
          <IconButton
            size="small"
            color={title?.align === TEXT_ALIGN_LEFT ? "primary" : "default"}
            onClick={() => handleTitleAlignmentChange("left")}
          >
            <FormatAlignLeftIcon fontSize="small" />
          </IconButton>
          <IconButton
            size="small"
            color={title?.align === TEXT_ALIGN_CENTER ? "primary" : "default"}
            onClick={() => handleTitleAlignmentChange("center")}
          >
            <FormatAlignCenterIcon fontSize="small" />
          </IconButton>
          <IconButton
            size="small"
            color={title?.align === TEXT_ALIGN_RIGHT ? "primary" : "default"}
            onClick={() => handleTitleAlignmentChange("right")}
          >
            <FormatAlignRightIcon fontSize="small" />
          </IconButton>
          {/* <IconButton
            size="small"
            color="default"
            onClick={(e) => {
              setEmojiAnchorEl(e.currentTarget);
              setActiveInput("title");
            }}
          >
            <span role="img" aria-label="emoji">
              😊
            </span>
          </IconButton> */}
          <IconButton size="small" color="default" onClick={handleTitleFlashClick}>
            <span role="img" aria-label="flash">
              ⚡
            </span>
          </IconButton>
          <Menu
            anchorEl={titleAnchorEl}
            open={Boolean(titleAnchorEl)}
            onClose={() => handleClose("title")}
            anchorOrigin={{
              vertical: "bottom",
              horizontal: "left",
            }}
            transformOrigin={{
              vertical: "top",
              horizontal: "left",
            }}
            MenuListProps={{
              onMouseLeave: () => handleClose("title"),
              onClick: (e) => {
                // Prevent closing on click inside menu
                e.stopPropagation();
              },
            }}
            disableAutoFocusItem
          >
            {triggerPrameters.map((param, index) => (
              <MenuItem
                key={index}
                value={param.value}
                onClick={(e) => {
                  e.stopPropagation();
                  handleClickParam("title", param);
                }}
              >
                {param.name}
              </MenuItem>
            ))}
          </Menu>
          <Box
            sx={{
              px: 1,
              py: 0.5,
              border: "1px solid #ccc",
              color: "black",
              borderRadius: "4px",
              fontSize: "0.75rem",
              textAlign: "center",
            }}
          >
            {100 - title?.content?.length || 0}
          </Box>
        </Box>
      </Box>

      {/* Phần nhập nội dung */}
      <Box>
        <Typography variant="subtitle1" gutterBottom>
          Nội dung <span style={{ color: "red" }}>*</span>
        </Typography>
        <EnhancedTextField
          fullWidth
          variant="outlined"
          multiline
          rows={1}
          size="small"
          maxLength={250}
          value={content?.content}
          onChange={(e) => {
            const value = e.target.value;
            if (value.length <= 250) {
              setContent({
                ...content,
                content: value,
              });
            }
          }}
          inputProps={{ style: { textAlign: content?.align } }}
        />
        <Box sx={{ display: "flex", justifyContent: "flex-end", alignItems: "center", mt: 1 }}>
          <IconButton
            size="small"
            color={content?.align === TEXT_ALIGN_LEFT ? "primary" : "default"}
            onClick={() => handleContentAlignmentChange("left")}
          >
            <FormatAlignLeftIcon fontSize="small" />
          </IconButton>
          <IconButton
            size="small"
            color={content?.align === TEXT_ALIGN_CENTER ? "primary" : "default"}
            onClick={() => handleContentAlignmentChange("center")}
          >
            <FormatAlignCenterIcon fontSize="small" />
          </IconButton>
          <IconButton
            size="small"
            color={content?.align === TEXT_ALIGN_RIGHT ? "primary" : "default"}
            onClick={() => handleContentAlignmentChange("right")}
          >
            <FormatAlignRightIcon fontSize="small" />
          </IconButton>
          {/* <IconButton
            size="small"
            color="default"
            onClick={(e) => {
              setEmojiAnchorEl(e.currentTarget);
              setActiveInput("content");
            }}
          >
            <span role="img" aria-label="emoji">
              😊
            </span>
          </IconButton> */}
          <IconButton size="small" color="default" onClick={handleContentFlashClick}>
            <span role="img" aria-label="flash">
              ⚡
            </span>
          </IconButton>
          <Menu
            anchorEl={contentAnchorEl}
            open={Boolean(contentAnchorEl)}
            onClose={() => handleClose("content")}
            anchorOrigin={{
              vertical: "bottom",
              horizontal: "left",
            }}
            transformOrigin={{
              vertical: "top",
              horizontal: "left",
            }}
            MenuListProps={{
              onMouseLeave: () => handleClose("content"),
              onClick: (e) => {
                e.stopPropagation();
              },
            }}
            disableAutoFocusItem
          >
            {triggerPrameters.map((param, index) => (
              <MenuItem
                key={index}
                value={param.value}
                onClick={(e) => {
                  e.stopPropagation();
                  handleClickParam("content", param);
                }}
              >
                {param.name}
              </MenuItem>
            ))}
          </Menu>
          <Box
            sx={{
              px: 1,
              py: 0.5,
              border: "1px solid #ccc",
              color: "black",
              borderRadius: "4px",
              fontSize: "0.75rem",
              textAlign: "center",
            }}
          >
            {250 - (content?.content?.length || 0)}
          </Box>
        </Box>
      </Box>

      {/* Phần nhập bảng thông tin */}
      <Box sx={{ mb: 3 }}>
        <Grid container spacing={2} justifyContent="space-between" alignItems="center">
          <Grid item>
            <Typography variant="subtitle1" gutterBottom>
              Bảng thông tin <span style={{ color: "red" }}>*</span>
            </Typography>
          </Grid>
          <Grid item sx={{ textAlign: "right" }}>
            <Typography variant="caption" sx={{ color: "red", fontSize: "0.6rem" }}>
              Tên tham số tối đa 25 ký tự
            </Typography>
          </Grid>
        </Grid>
        <Paper
          variant="outlined"
          sx={{
            p: 2,
            border: "1px solid #ccc",
            borderRadius: 1,
          }}
        >
          {/* {Array.isArray(selectedMessageType) &&
            selectedMessageType.length > 0 &&
            selectedMessageType?.map((param, index) => (
              <Grid container spacing={1} key={index} sx={{ mb: 1, alignItems: "center" }}>
                <Grid item xs={6}>
                  <FormControl fullWidth size="small">
                    <Select value={param.name} disabled>
                      <MenuItem value={param.name}>{param.name}</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={6}>
                  <Box
                    sx={{
                      padding: "7px 6px",
                      border: "1px solid #979797",
                      borderRadius: 1,
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "space-between",
                    }}
                  >
                    <Paper
                      sx={{
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "space-between",
                        padding: "2px 8px",
                        marginLeft: 0.5,
                        backgroundColor: "#377dff",
                        color: "white",
                        borderRadius: "6px",
                        maxWidth: "fit-content",
                        cursor: "pointer",
                      }}
                    >
                      <Typography variant="body2">{param.value}</Typography>
                    </Paper>
                    
                  </Box>
                </Grid>
              </Grid>
            ))} */}
          {newParameter?.map((param, index) => (
            <Grid container spacing={1} key={index} sx={{ mb: 1, alignItems: "center" }}>
              <Grid item xs={6}>
                <FormControl fullWidth size="small">
                  <Select
                    value={param.value}
                    onChange={(e) => handleParameterChange(e.target.value, index)}
                  >
                    {triggerPrameters?.map((param, index) => (
                      <MenuItem key={index} value={param.value}>
                        {param.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={6}>
                <Box
                  sx={{
                    padding: "5px 6px",
                    border: "1px solid #979797",
                    borderRadius: 1,
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "space-between",
                  }}
                >
                  <Paper
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "space-between",
                      padding: "2px 8px",
                      marginLeft: 0.5,
                      backgroundColor: param.value ? "#377dff" : "transparent",
                      color: param.value ? "white" : "black",
                      borderRadius: "6px",
                      maxWidth: "fit-content",
                      cursor: "pointer",
                    }}
                  >
                    <Typography variant="body2">{param.value ? param.value : "Giá trị"}</Typography>
                  </Paper>
                  <Box sx={{ paddingLeft: 0, borderLeft: 1, borderColor: "#979797" }}>
                    <IconButton
                      size="small"
                      color="error"
                      onClick={() => handleDeleteContentClick(index)}
                    >
                      <DeleteIcon fontSize="small" />
                    </IconButton>
                  </Box>
                </Box>
              </Grid>
            </Grid>
          ))}
          <Box sx={{ textAlign: "right", mt: 2 }}>
            <Button
              variant="outlined"
              size="small"
              startIcon={<AddIcon />}
              onClick={() =>
                (newParameter?.length | 0) <
                  // + (selectedMessageType?.length | 0)
                  5 &&
                setNewParameter([...newParameter, { name: "", value: "", dataType: "String" }])
              }
              fullWidth
            >
              Thêm tham số (
              {
                newParameter?.length || 0
                // + (selectedMessageType?.length || 0)
              }
              /5)
            </Button>
          </Box>
        </Paper>
      </Box>

      {/* Phần nhập nội dung phụ */}
      <Box>
        <Typography variant="subtitle1" gutterBottom>
          Nội dung phụ <span style={{ color: "red" }}>*</span>
        </Typography>
        <EnhancedTextField
          fullWidth
          variant="outlined"
          multiline
          rows={1}
          size="small"
          maxLength={250}
          value={subContent?.content}
          onChange={(e) => {
            const value = e.target.value;
            if (value.length <= 250) {
              setSubContent({
                ...subContent,
                content: value,
              });
            }
          }}
          inputProps={{
            style: { textAlign: subContent?.align },
          }}
        />
        <Box sx={{ display: "flex", justifyContent: "flex-end", alignItems: "center", mt: 1 }}>
          <IconButton
            size="small"
            color={subContent?.align === TEXT_ALIGN_LEFT ? "primary" : "default"}
            onClick={() => handleSubContentAlignmentChange("left")}
          >
            <FormatAlignLeftIcon fontSize="small" />
          </IconButton>
          <IconButton
            size="small"
            color={subContent?.align === TEXT_ALIGN_CENTER ? "primary" : "default"}
            onClick={() => handleSubContentAlignmentChange("center")}
          >
            <FormatAlignCenterIcon fontSize="small" />
          </IconButton>
          <IconButton
            size="small"
            color={subContent?.align === TEXT_ALIGN_RIGHT ? "primary" : "default"}
            onClick={() => handleSubContentAlignmentChange("right")}
          >
            <FormatAlignRightIcon fontSize="small" />
          </IconButton>
          {/* <IconButton
            size="small"
            color="default"
            onClick={(e) => {
              setEmojiAnchorEl(e.currentTarget);
              setActiveInput("subContent");
            }}
          >
            <span role="img" aria-label="emoji">
              😊
            </span>
          </IconButton> */}
          <IconButton size="small" color="default" onClick={handleSubContentFlashClick}>
            <span role="img" aria-label="flash">
              ⚡
            </span>
          </IconButton>
          <Menu
            anchorEl={subContentAnchorEl}
            open={Boolean(subContentAnchorEl)}
            onClose={() => handleClose("subContent")}
            anchorOrigin={{
              vertical: "bottom",
              horizontal: "left",
            }}
            transformOrigin={{
              vertical: "top",
              horizontal: "left",
            }}
            MenuListProps={{
              onMouseLeave: () => handleClose("subContent"),
              onClick: (e) => {
                e.stopPropagation();
              },
            }}
            disableAutoFocusItem
          >
            {triggerPrameters.map((param, index) => (
              <MenuItem
                key={index}
                value={param.value}
                onClick={(e) => {
                  e.stopPropagation();
                  handleClickParam("subContent", param);
                }}
              >
                {param.name}
              </MenuItem>
            ))}
          </Menu>
          <Box
            sx={{
              px: 1,
              py: 0.5,
              border: "1px solid #ccc",
              color: "black",
              borderRadius: "4px",
              fontSize: "0.75rem",
              textAlign: "center",
            }}
          >
            {250 - (subContent?.content?.length || 0)}
          </Box>
        </Box>
      </Box>

      {/* Phần hiển thị danh sách Buttons */}
      <Box sx={{ mb: 3 }}>
        <Grid container spacing={2} alignItems="center" justifyContent="space-between">
          <Grid item>
            <Typography variant="subtitle1" gutterBottom>
              Buttons
            </Typography>
          </Grid>
          <Grid item sx={{ textAlign: "right" }}>
            <Typography variant="caption" sx={{ color: "red", fontSize: "0.6rem" }}>
              Tên buttons tối đa 35 ký tự
            </Typography>
          </Grid>
        </Grid>
        <Paper
          variant="outlined"
          sx={{
            p: 2,
            border: "1px solid #ccc",
            borderRadius: 1,
          }}
        >
          {buttons.map((btn, index) => (
            <Grid container spacing={1} key={index} sx={{ mb: 1, alignItems: "center" }}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  size="small"
                  variant="outlined"
                  defaultValue={btn.title}
                  value={btn.title}
                  disabled={true}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        <Box sx={{ display: "flex", alignItems: "center" }}>
                          <Divider
                            orientation="vertical"
                            flexItem
                            sx={{ bgcolor: "gray", mx: 1, width: 2 }}
                          />
                          <IconButton
                            size="small"
                            color="primary"
                            onClick={() => {
                              handleSettingsClick(btn);
                              setIndexbuttonSetting(index);
                            }}
                          >
                            <SettingsIcon fontSize="small" />
                          </IconButton>
                          <Divider
                            orientation="vertical"
                            flexItem
                            sx={{ bgcolor: "gray", mx: 1, width: 2 }}
                          />
                          <IconButton
                            size="small"
                            color="error"
                            onClick={() => handleDeleteButtonClick(index)}
                          >
                            <DeleteIcon fontSize="small" />
                          </IconButton>
                          <Dialog
                            open={openDialogDeleteButton}
                            onClose={handleCancelDeleteButton}
                            maxWidth="sm"
                            fullWidth
                          >
                            <DialogTitle>Xác nhận xoá</DialogTitle>
                            <DialogContent>
                              <DialogContentText>
                                Bạn có chắc chắn muốn xóa button này không?
                              </DialogContentText>
                            </DialogContent>
                            <DialogActions>
                              <Button
                                onClick={handleCancelDeleteButton}
                                variant="outlined"
                                sx={{ minWidth: 100 }}
                              >
                                Hủy
                              </Button>
                              <Button
                                onClick={() => handleConfirmDeleteButton()}
                                color="error"
                                variant="contained"
                                autoFocus
                                sx={{ minWidth: 100 }}
                              >
                                Xóa
                              </Button>
                            </DialogActions>
                          </Dialog>
                        </Box>
                      </InputAdornment>
                    ),
                  }}
                />
              </Grid>
            </Grid>
          ))}
          <Box sx={{ textAlign: "right", mt: 2 }}>
            <Button
              variant="outlined"
              size="small"
              startIcon={<AddIcon />}
              onClick={handleAddButton}
              fullWidth
            >
              Thêm buttons ({buttons.length}/4)
            </Button>
          </Box>
        </Paper>
      </Box>

      {/* Popover cho emoji picker */}
      <Popover
        open={Boolean(emojiAnchorEl)}
        anchorEl={emojiAnchorEl}
        onClose={() => {
          setEmojiAnchorEl(null);
          setActiveInput(null);
        }}
        anchorOrigin={{
          vertical: "top",
          horizontal: "left",
        }}
        transformOrigin={{
          vertical: "bottom",
          horizontal: "left",
        }}
      >
        <EmojiPicker onEmojiClick={handleEmojiClick} />
      </Popover>

      <ModalSettingButton
        handleSinglePayloadChange={handleSinglePayloadChange}
        dialogOpen={dialogOpen}
        buttonActions={buttonActions}
        defaultGroupId={defaultGroupId}
        errors={errors}
        setSelectedButton={setSelectedButton}
        selectedButton={selectedButton}
        handleArrayPayloadChange={handleArrayPayloadChange}
        handleDialogClose={handleDialogClose}
        handleDialogSubmit={handleDialogSubmit}
        resizeImage={resizeImage}
        uploadFile={uploadFile}
      />
      <Dialog
        open={openDialogDeleteContent}
        onClose={handleCancelDeleteContent}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Xác nhận xoá</DialogTitle>
        <DialogContent>
          <DialogContentText>Bạn có chắc chắn muốn xóa thông tin này không?</DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCancelDeleteContent} variant="outlined" sx={{ minWidth: 100 }}>
            Hủy
          </Button>
          <Button
            onClick={handleConfirmDeleteContent}
            color="error"
            variant="contained"
            autoFocus
            sx={{ minWidth: 100 }}
          >
            Xóa
          </Button>
        </DialogActions>
      </Dialog>
    </Grid>
  );
};

export default LeftPanel;
