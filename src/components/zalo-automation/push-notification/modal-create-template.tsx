import React, { useEffect, useState } from "react";
import { Container, Grid, Paper, Typography, Divider, Popover, Button, Box } from "@mui/material";
import EmojiPicker from "emoji-picker-react";
import toast from "react-hot-toast";
import { ACTIVE_STATUS, TEMPLATE_TYPE, TEXT_ALIGN_LEFT, ZALO_TABS } from "@/src/constants/constant";
import { useZaloTemplate } from "@/src/api/hooks/zalo-template/zalo-template";
import { logger } from "@/src/utils/logger";
import {
  ISendType,
  IZaloButton,
  IZaloElement,
  IZaloTemplate,
  ZaloElementTypeEnum,
} from "@/src/api/types/zalo-template.types";
import useSnackbar from "@/src/hooks/use-snackbar";
import { useStoreId } from "@/src/hooks/use-store-id";
import LeftPanelNewText from "../leftPanelNewText";
import LeftPanel from "../leftPanel";
import RightPanel from "../rightPanel";
import {
  TriggerParameterDto,
  useTriggerEvent,
} from "@/src/api/hooks/trigger-event/use-trigger-event";
import { CreateFileGroupRequest, GetGroupFileRequest, RefType } from "@/src/api/types/media.types";
import { useMedia } from "@/src/api/hooks/media/use-media";

interface TransactionNotificationFormProps {
  isOpenModalCreateTemplate: boolean;
  type?: number;
  onClose: () => void;
  fetchZaloTemplate: () => void;
  selectedTemplate?: any;
  selectedTriggerEvent?: any;
}

const INITIAL_ELEMENT = {
  BANNER: { type: ZaloElementTypeEnum.BANNER, content: "" },
  HEADER: { type: ZaloElementTypeEnum.HEADER, align: TEXT_ALIGN_LEFT, content: "" },
  CONTENT: { type: ZaloElementTypeEnum.CONTENT, align: TEXT_ALIGN_LEFT, content: "" },
  SUB_CONTENT: { type: ZaloElementTypeEnum.CONTENT, align: TEXT_ALIGN_LEFT, content: "" },
};
export interface IMessageType {
  id: string;
  name: string;
  description: string;
  type: string;
  typeName: string;
  defaultParameters: any[];
  createdDate: string;
  modifiedDate: string | null;
  createdBy: string | null;
  modifiedBy: string | null;
  isDeleted: boolean;
  deletedAt: string | null;
}

export interface INewParameters {
  name: string;
  value: string;
}

export default function ModalCreateTemplate({
  isOpenModalCreateTemplate,
  type = 0,
  onClose,
  fetchZaloTemplate,
  selectedTemplate,
  selectedTriggerEvent,
}: TransactionNotificationFormProps) {
  const {
    getTriggerEventsByType,
    getSendTypes,
    getMessageTypesByTemplateType,
    createZaloTemplate,
    updateZaloTemplate,
    updateTemplate,
    getTriggerPrameter,
  } = useZaloTemplate();
  const { getListParamByTriggerEventId } = useTriggerEvent();
  const { getDetailTemplateById } = useZaloTemplate();
  const snackbar = useSnackbar();
  const storeId = useStoreId();
  const [triggerEvents, setTriggerEvents] = useState<any[]>([]);
  const [sendTypes, setSendTypes] = useState<ISendType[]>([]);
  const [messageTypes, setMessageTypes] = useState<any[]>([]);
  const [buttons, setButtons] = useState<IZaloButton[]>([]);
  const [bannerImage, setBannerImage] = useState<IZaloElement>(INITIAL_ELEMENT.BANNER);
  const [title, setTitle] = useState<IZaloElement>(INITIAL_ELEMENT.HEADER);
  const [content, setContent] = useState<IZaloElement>(INITIAL_ELEMENT.CONTENT);
  const [subContent, setSubContent] = useState<IZaloElement>(INITIAL_ELEMENT.SUB_CONTENT);
  const [templateName, setTemplateName] = useState<string>("");
  const [templateType, setTemplateType] = useState<any>(TEMPLATE_TYPE[2].value);
  const [selectedSendType, setSelectedSendType] = useState<any>(null);
  const [selectedMessageType, setSelectedMessageType] = useState<any>(null);
  const [newParameters, setNewParameters] = useState<INewParameters[]>([]);
  const [emojiAnchorEl, setEmojiAnchorEl] = useState<null | HTMLElement>(null);
  const [activeInput, setActiveInput] = useState<null | string>(null);
  const [triggerPrameters, setTriggerPrameters] = useState<TriggerParameterDto[]>([]);
  const shopId = useStoreId();
  const [isLoading, setIsLoading] = useState(true);
  const [template, setTemplate] = useState<IZaloTemplate>();
  const [selectedButton, setSelectedButton] = useState<any>(null);
  const [defaultGroupId, setDefaultGroupId] = useState<string>("");
  const { getGroups, uploadFile } = useMedia();

  const [selectedMsgTypeTrans, setSelectedMsgTypeTrans] = useState<string>("");

  useEffect(() => {
    const fetchDefaultGroup = async () => {
      try {
        const params: GetGroupFileRequest = {
          ShopId: storeId,
          Limit: 100,
          Skip: 0,
        };
        const response = await getGroups(params);
        if (response.data?.data?.length > 0) {
          setDefaultGroupId(response.data.data[0].groupFileId);
        }
      } catch (error) {}
    };
    fetchDefaultGroup();
  }, [storeId]);

  const fetchDetailTemplate = async () => {
    if (selectedTemplate && selectedTemplate?.templateId) {
      const res = await getDetailTemplateById(shopId, selectedTemplate.templateId);
      if (res && res.status === 200) {
        setTemplate(res?.data?.data);
        const data: IZaloTemplate = res?.data?.data;
        setBannerImage(data?.transaction?.message?.attachment?.payload?.elements[0]);
        setTitle((prev: IZaloElement) => ({
          ...prev,
          align: TEXT_ALIGN_LEFT,
          type: ZaloElementTypeEnum.HEADER,
          content: data?.transaction?.message?.attachment?.payload?.elements[1]?.content || "",
        }));
        if (data?.type === "Consultation") {
          setContent((prev: IZaloElement) => ({
            ...prev,
            content: data.transaction?.message?.text || "",
            align: "left",
            type: ZaloElementTypeEnum.CONTENT,
            contentTable: null,
            imageUrl: "",
            mediaType: "",
            url: "",
          }));
        } else {
          setContent((prev: IZaloElement) => ({
            ...prev,
            align: TEXT_ALIGN_LEFT,
            type: ZaloElementTypeEnum.CONTENT,
            content: data?.transaction?.message?.attachment?.payload?.elements[2]?.content || "",
          }));
        }
        setSubContent((prev: IZaloElement) => ({
          ...prev,
          align: TEXT_ALIGN_LEFT,
          type: ZaloElementTypeEnum.CONTENT,
          content: data?.transaction?.message?.attachment?.payload?.elements[4]?.content || "",
        }));

        setTemplateName(data?.name);
        const matched = TEMPLATE_TYPE.find((item) => item.text === data?.type);
        const value = matched?.value;
        setTemplateType(value);
        setButtons(data?.transaction?.message?.attachment?.payload?.buttons);
        const contentTable =
          data?.transaction?.message?.attachment?.payload?.elements?.[3]?.contentTable;

        setSelectedMsgTypeTrans(res?.data?.data?.messageType);

        if (Array.isArray(contentTable)) {
          const mapped = contentTable.map((item) => ({
            name: item.key,
            value: item.value,
          }));

          setSelectedMessageType(mapped);
        }
      }
    }
  };

  useEffect(() => {
    fetchDetailTemplate();
  }, [isOpenModalCreateTemplate, messageTypes]);

  useEffect(() => {
    const loadData = async () => {
      await fetchInitialData();
      setIsLoading(false);
    };
    loadData();
  }, []);

  useEffect(() => {
    // fetchInitialData();
    if (selectedTemplate) {
      setTemplateName(selectedTemplate.name);
      setTemplateType(
        TEMPLATE_TYPE.find((t) => t.text === selectedTemplate.type)?.value || TEMPLATE_TYPE[0].value
      );
      setSelectedSendType(sendTypes.find((t) => t === selectedTemplate.sendType) || sendTypes[0]);
      setBannerImage(
        selectedTemplate.transaction?.message.attachment.payload.elements.find(
          (e: IZaloElement) => e.type === ZaloElementTypeEnum.BANNER
        ) || INITIAL_ELEMENT.BANNER
      );
      setTitle(
        selectedTemplate.transaction?.message.attachment.payload.elements.find(
          (e: IZaloElement) => e.type === ZaloElementTypeEnum.HEADER
        ) || INITIAL_ELEMENT.HEADER
      );
      const contentElements =
        selectedTemplate.transaction?.message.attachment.payload.elements.filter(
          (e: IZaloElement) => e.type === ZaloElementTypeEnum.CONTENT
        );

      setContent({
        type: ZaloElementTypeEnum.CONTENT,
        align: TEXT_ALIGN_LEFT,
        content: selectedTemplate.transaction?.message?.text,
      });
      setSubContent(INITIAL_ELEMENT.SUB_CONTENT);
      setButtons(
        (selectedTemplate.transaction?.message.attachment.payload.buttons || []).map((button) => ({
          ...button,
          payload: Object.entries(button.payload).map(([name, value]) => ({
            name,
            value,
          })),
        }))
      );
    }
  }, [isLoading]);

  useEffect(() => {
    if (messageTypes && messageTypes.length > 0 && selectedTemplate) {
      const matchedMessageType = messageTypes.find(
        (m) => m.typeName === selectedTemplate.messageType
      );
      setSelectedMessageType(matchedMessageType || null);
    }
  }, [messageTypes, selectedTemplate]);

  useEffect(() => {
    if (sendTypes && sendTypes.length > 0) {
      if (selectedTemplate) {
        setSelectedSendType(
          // sendTypes.find((e) => e.id === selectedTemplate.sendType.id) ||
          sendTypes[0]
        );
      }
    }
  }, [sendTypes]);

  useEffect(() => {
    if (selectedMessageType && selectedTemplate) {
      const filteredParams =
        selectedTemplate.transaction?.message.attachment.payload.elements
          .find((e: IZaloElement) => e.type === ZaloElementTypeEnum.TABLE)
          ?.contentTable.filter(
            (param: any) => !selectedMessageType?.some((p: any) => p.value === param.value)
          ) || [];

      const formattedParams = filteredParams.map((param: any) => ({
        name: param.key,
        value: param.value,
      }));
      setNewParameters(formattedParams);
    }
  }, [selectedMessageType]);

  useEffect(() => {
    if (templateType == TEMPLATE_TYPE[0].value) {
      fetchMessageTypesByTemplateType(TEMPLATE_TYPE[templateType].text);
    }
  }, [templateType]);

  const fetchInitialData = async () => {
    await Promise.all([fetchTriggerEvents(), fetchSendTypes(), fetchTriggerPrameter()]);
  };

  const fetchTriggerEvents = async () => {
    try {
      const response = await getTriggerEventsByType(ZALO_TABS[type].text);
      const events = response.data.data;
      setTriggerEvents(events);
    } catch (error) {
      logger.error("Error fetching trigger events:", error);
    }
  };

  const fetchSendTypes = async () => {
    try {
      const response = await getSendTypes();
      const types = response.data.data;
      setSendTypes(types);
    } catch (error) {
      logger.error("Error fetching send types:", error);
    }
  };

  const fetchMessageTypesByTemplateType = async (type: string) => {
    try {
      const response = await getMessageTypesByTemplateType(type);
      const types = response.data.data;
      if (types.length > 0) {
        setMessageTypes(types);
      }
    } catch (error) {
      logger.error("Error fetching message types:", error);
    }
  };

  const fetchTriggerPrameter = async () => {
    try {
      const response = await getListParamByTriggerEventId(selectedTriggerEvent.triggerEventId);
      setTriggerPrameters(response.data.data);
    } catch (error) {
      logger.error("Error fetching shops:", error);
    }
  };

  const handleEmojiClick = (emojiData: any) => {
    const updateState = (prev: IZaloElement) => ({
      ...prev,
      content: (prev.content || "") + emojiData.emoji,
    });
    if (activeInput === "title") setTitle(updateState);
    else if (activeInput === "content") setContent(updateState);
    else if (activeInput === "subContent") setSubContent(updateState);
    setEmojiAnchorEl(null);
    setActiveInput(null);
  };

  const handleCreateZaloTemplate = async (zaloTemplate: IZaloTemplate) => {
    try {
      const response = await createZaloTemplate(zaloTemplate);
      if (response?.data) {
        snackbar.success("Tạo mẫu tin Zalo thành công");
        fetchZaloTemplate();
        onClose();
      }
    } catch (error) {
      logger.error("Error creating Zalo template:", error);
      snackbar.error("Có lỗi xảy ra khi tạo mẫu tin Zalo");
    }
  };

  const validateFields = (templateType) => {
    const commonFields = [
      { value: bannerImage?.imageUrl, message: "Vui lòng chọn ảnh banner" },
      { value: title.content, message: "Vui lòng nhập tiêu đề" },
      { value: content.content, message: "Vui lòng nhập nội dung" },
      { value: subContent?.content, message: "Vui lòng nhập nội dung phụ" },
      { value: selectedTriggerEvent, message: "Vui lòng chọn sự kiện kích hoạt" },
      { type: "advanced", value: newParameters, message: "Vui lòng chọn tham số kích hoạt" },
      { type: "advanced", value: selectedMessageType, message: "Vui lòng chọn tham số kích hoạt" },
    ];

    const transformedButtons = buttons.map((button) => ({
      ...button,
      payload:
        Array.isArray(button.payload) &&
        button.payload.length > 0 &&
        button?.payload?.reduce(
          (acc, item) => ({
            ...acc,
            [item.name]: item.value,
          }),
          {}
        ),
    }));

    transformedButtons.forEach((button, index) => {
      if (!button.title || button.title.trim() === "") {
        commonFields.push({
          value: null,
          message: `Vui lòng nhập tiêu đề cho button thứ ${index + 1}`,
        });
      }

      if (!button.imageIcon || button.imageIcon.trim() === "") {
        commonFields.push({
          value: null,
          message: `Vui lòng chọn ảnh icon cho button thứ ${index + 1}`,
        });
      }

      if (!button.type || button.type.trim() === "") {
        commonFields.push({
          value: null,
          message: `Vui lòng chọn loại cho button thứ ${index + 1}`,
        });
      }
    });

    const transactionFields = [
      { value: selectedMessageType, message: "Vui lòng chọn loại tin giao dịch" },
    ];

    const consultationFields = [
      // { value: templateName, message: "Vui lòng nhập tên mẫu tin" },
      { value: bannerImage?.imageUrl, message: "Vui lòng chọn ảnh banner" },
      { value: content.content, message: "Vui lòng nhập nội dung thông báo văn bản" },
      // { value: selectedSendType, message: "Vui lòng chọn thời gian gửi" },
      // { value: selectedTriggerEvent, message: "Vui lòng chọn sự kiện kích hoạt" },
    ];

    let requiredFields = [...commonFields];
    switch (templateType) {
      case 0:
        requiredFields = [...commonFields, ...transactionFields];
        break;
      case 1:
        requiredFields = [...commonFields];
        break;
      case 2:
        requiredFields = [...consultationFields];
        break;
      default:
        toast.error("Vui lòng chọn loại tin");
        return false;
    }

    const advancedFields = requiredFields.filter((field) => field.type === "advanced");
    const normalFields = requiredFields.filter((field) => field.type !== "advanced");

    for (const field of normalFields) {
      if (!field.value || field.value.length === 0) {
        toast.error(field.message);
        return false;
      }
    }

    if (advancedFields.length > 0) {
      const allAdvancedEmpty = advancedFields.every(
        (field) => !Array.isArray(field.value) || field.value.length === 0
      );

      if (allAdvancedEmpty) {
        toast.error("Vui lòng chọn tham số kích hoạt");
        return false;
      }
    }
    return true;
  };

  const handleSubmit = async () => {
    let newBannerImage = bannerImage;

    if (bannerImage?.file) {
      const data: CreateFileGroupRequest = {
        FileUpload: bannerImage.file,
        GroupFileId: defaultGroupId,
        ShopId: storeId,
        RefType: RefType.ZaloUID,
      };

      const response = await uploadFile(data);
      if (response?.data?.link) {
        newBannerImage = {
          ...bannerImage,
          imageUrl: response?.data?.link,
          mediaType: response?.data?.type,
          type: ZaloElementTypeEnum.BANNER,
        };
        setBannerImage(newBannerImage);
      }
    }

    const updatedButtons = await Promise.all(
      buttons.map(async (button) => {
        if (button.file) {
          const data: CreateFileGroupRequest = {
            FileUpload: button.file,
            GroupFileId: defaultGroupId,
            ShopId: storeId,
            RefType: RefType.ZaloUID,
          };
          const response = await uploadFile(data);
          if (response?.data?.link) {
            return {
              ...button,
              imageIcon: response.data.link,
            };
          }
        }
        return button;
      })
    );
    setButtons(updatedButtons);
    if (!validateFields(templateType)) return;
    const zaloTemplate: IZaloTemplate = {
      // name: templateName,
      shopId: storeId,
      refId: selectedTriggerEvent.triggerEventId,
      refType: "TriggerEvent",
      templateCategory: ZALO_TABS[type].text,
      type: TEMPLATE_TYPE[templateType].text,
      messageType: templateType === TEMPLATE_TYPE[0].value ? selectedMessageType?.typeName : null,
      // sendType: selectedSendType,
      status: ACTIVE_STATUS,
      // TriggerEvent: selectedTriggerEvent.code,
      transaction: {
        recipient: { userId: "" },
        message: {
          text: templateType === TEMPLATE_TYPE[2].value ? content.content : "",
          attachment: {
            type: "template",
            payload: {
              templateType: "",
              language: "vi",
              elements:
                templateType !== TEMPLATE_TYPE[2].value
                  ? [
                      newBannerImage,
                      title,
                      content,
                      {
                        type: ZaloElementTypeEnum.TABLE,
                        contentTable: [
                          ...[
                            ...(Array.isArray(selectedMessageType)
                              ? selectedMessageType
                              : selectedMessageType?.defaultParameters || []),
                            ...(newParameters || []),
                          ]
                            .filter(
                              (param, index, self) =>
                                index === self.findIndex((p) => p.name === param.name)
                            )
                            .map((param: any) => ({
                              key: param.name || null,
                              value: param.value || null,
                              style: "",
                            })),
                        ],
                      },
                      subContent,
                    ]
                  : newBannerImage
                  ? [
                      {
                        ...newBannerImage,
                        type: newBannerImage.type,
                        mediaType: "image",
                        url: newBannerImage.imageUrl,
                        contentTable: [{ style: "", key: "", value: "" }],
                        align: "left",
                      },
                    ]
                  : [],
              buttons:
                templateType !== TEMPLATE_TYPE[2].value
                  ? updatedButtons.map((button) => ({
                      ...button,
                      payload: Array.isArray(button.payload)
                        ? button.payload.reduce(
                            (acc, item) => ({
                              ...acc,
                              [item.name]: item.value,
                            }),
                            {}
                          )
                        : button.payload,
                    }))
                  : [],
            },
          },
        },
      },
    };
    if (selectedTemplate.templateId !== "") {
      zaloTemplate.templateId = selectedTemplate.templateId;
    }

    if (selectedTemplate?.templateId !== "") {
      try {
        const response = await updateTemplate(zaloTemplate);
        if (response?.data) {
          snackbar.success("Cập nhật mẫu tin Zalo thành công");
          // fetchZaloTemplate();
          onClose();
        }
      } catch (error) {
        snackbar.error("Có lỗi xảy ra khi cập nhật mẫu tin Zalo");
      }
    } else {
      handleCreateZaloTemplate(zaloTemplate);
    }
  };

  const isNewTextTemplate = templateType === TEMPLATE_TYPE[2].value;

  return (
    <Box sx={{ display: "flex", flexDirection: "column", height: "100%", overflow: "hidden" }}>
      <Box sx={{ mb: 2 }}>
        <Typography variant="h6" sx={{ fontWeight: "bold" }}>
          Chi tiết mẫu thông báo
        </Typography>
      </Box>
      {/* <Divider sx={{ my: 2 }} /> */}
      <Box
        sx={{
          flexGrow: 1,
          display: "flex",
          flexDirection: "column",
          overflow: "auto",
        }}
      >
        <Box
          sx={{
            display: "flex",
            flexGrow: 1,
            overflow: "auto",
            mb: 2,
            border: "1px solid #ccc",
            borderRadius: 0.8,
          }}
        >
          {" "}
          {templateType === 2 ? (
            <LeftPanelNewText
              bannerImage={bannerImage}
              setBannerImage={setBannerImage}
              content={content}
              setContent={setContent}
              triggerPrameters={triggerPrameters}
              templateType={templateType}
              setTemplateType={setTemplateType}
            />
          ) : (
            <LeftPanel
              title={title}
              setTitle={setTitle}
              content={content}
              setContent={setContent}
              subContent={subContent}
              setSubContent={setSubContent}
              buttons={buttons}
              setButtons={setButtons}
              bannerImage={bannerImage}
              setBannerImage={setBannerImage}
              selectedMessageType={selectedMessageType}
              newParameter={newParameters}
              setNewParameter={setNewParameters}
              triggerPrameters={triggerPrameters}
              templateType={templateType}
              setTemplateType={setTemplateType}
              selectedMsgTypeTrans={selectedMsgTypeTrans}
              setSelectedMessageType={setSelectedMessageType}
              messageType={messageTypes}
              selectedButton={selectedButton}
              setSelectedButton={setSelectedButton}
            />
          )}
          <RightPanel
            triggerEvents={triggerEvents}
            sendTypes={sendTypes}
            title={title}
            setTitle={setTitle}
            content={content}
            setContent={setContent}
            subContent={subContent}
            setSubContent={setSubContent}
            templateName={templateName}
            setTemplateName={setTemplateName}
            buttons={buttons}
            templateType={templateType}
            setTemplateType={setTemplateType}
            messageType={messageTypes}
            setMessageType={setMessageTypes}
            bannerImage={bannerImage}
            selectedMessageType={selectedMessageType}
            setSelectedMessageType={setSelectedMessageType}
            // selectedTriggerEvent={selectedTriggerEvent}
            // setSelectedTriggerEvent={setSelectedTriggerEvent}
            selectedSendTypes={selectedSendType}
            setSelectedSendTypes={setSelectedSendType}
            isEdit={selectedTemplate ? true : false}
            newParameter={newParameters}
            selectedMsgTypeTrans={selectedMsgTypeTrans}
          />
        </Box>
      </Box>
      <Box
        sx={{
          display: "flex",
          justifyContent: "flex-end",
        }}
      >
        {" "}
        <Button
          variant="contained"
          sx={{
            backgroundColor: "#e0e0e0",
            color: "#000",
            "&:hover": { backgroundColor: "#d6d6d6" },
            mr: 2,
          }}
          onClick={onClose}
        >
          Hủy bỏ
        </Button>
        <Button variant="contained" color="primary" onClick={handleSubmit}>
          Xác nhận
        </Button>
      </Box>

      <Popover
        open={Boolean(emojiAnchorEl)}
        anchorEl={emojiAnchorEl}
        onClose={() => {
          setEmojiAnchorEl(null);
          setActiveInput(null);
        }}
        anchorOrigin={{ vertical: "top", horizontal: "left" }}
        transformOrigin={{ vertical: "bottom", horizontal: "left" }}
      >
        <EmojiPicker onEmojiClick={handleEmojiClick} />
      </Popover>
    </Box>
  );
}
