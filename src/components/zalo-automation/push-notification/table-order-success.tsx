import { useTriggerEvent } from "@/src/api/hooks/trigger-event/use-trigger-event";
import {
  ParamGetListTemplate,
  ParamUpdateStatusTemplate,
} from "@/src/api/services/trigger-event/trigger-event.service";
import { formatPrice } from "@/src/api/types/membership.types";
import { useStoreId } from "@/src/hooks/use-store-id";
import useSnackbar from "@/src/hooks/use-snackbar";
import { rowPerPageOptionsDefault } from "@/src/types/store/enum-type";
import { ArrowBack, ArrowBackIos, Edit } from "@mui/icons-material";
import {
  Box,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TablePagination,
  TableRow,
  Tooltip,
  Typography,
  Switch,
  CircularProgress,
  IconButton,
  Dialog,
  DialogContent,
  DialogTitle,
  DialogActions,
  Button,
  Link,
  Chip,
  Paper,
  Modal,
  styled,
} from "@mui/material";
import dayjs from "dayjs";
import React, { useEffect, useState } from "react";
import ModalUpdateTemplate from "./modal-update-template";
import { useZaloTemplate } from "@/src/api/hooks/zalo-template/zalo-template";
import ModalCreateTemplate from "./modal-create-template";
import { useAllPermissions } from "@/src/api/hooks/auth/use-all-permissions";
import { usePathname } from "next/navigation";
import { PERMISSION_TYPE_ENUM } from "@/src/constants/constant";

export interface TemplateDto {
  shopId: string;
  triggerEventId: string;
  channelType: string;
  channelName: string;
  templateId: string;
  policyName: string;
  policyLink: string;
  price: number;
  isActive: boolean;
}

const TableOrderSuccess = ({
  isEditTriggerEvent,
  setIsEditTriggerEvent,
  setSelectedTriggerEvent,
  selectedTriggerEvent,
  fetchTriggerEvent,
  fetchZaloTemplate,
}) => {
  const pathname = usePathname();
  const { getListTemplateByTriggerEventId, activeStatusTemplate } = useTriggerEvent();
  const { getDetailTemplateById } = useZaloTemplate();

  const [listTemplate, setListTemplate] = useState<TemplateDto[]>([]);
  const [loading, setLoading] = useState<{ [key: string]: boolean }>({});
  const [isOpenModalUpdateTemplate, setIsOpenModalUpdateTemplate] = useState<boolean>(false);
  const [isOpenModalCreateTemplate, setIsOpenModalCreateTemplate] = useState<boolean>(false);

  const shopId = useStoreId();
  const snackbar = useSnackbar();
  const [selectedTemplate, setSelectedTemplate] = useState<any | null>(null);
  const [confirmDialog, setConfirmDialog] = useState({
    open: false,
    templateId: "",
    newStatus: false,
  });

  const { permissions } = useAllPermissions();
  const isGranted = (url, permission) => {
    if (url.endsWith("/")) {
      url = url.slice(0, -1);
    }
    const matchingUrl = Object.keys(permissions).find((key) => key === url);
    if (!matchingUrl) return false;
    return permissions[matchingUrl]?.includes(permission) || false;
  };

  const fetchTemplateByTriggerEventId = async () => {
    const params: ParamGetListTemplate = {
      shopId: shopId,
      triggerEventId: selectedTriggerEvent?.triggerEventId,
    };
    const res = await getListTemplateByTriggerEventId(params);
    if (res && res?.status === 200) {
      setListTemplate(res?.data?.data);
    }
  };
  useEffect(() => {
    fetchTemplateByTriggerEventId();
  }, [shopId, isEditTriggerEvent]);

  const handleStatusChange = async (templateId: string, newStatus: boolean) => {
    setConfirmDialog({
      open: true,
      templateId,
      newStatus,
    });
  };

  const handleConfirmStatusChange = async () => {
    try {
      const params: ParamUpdateStatusTemplate = {
        templateId: confirmDialog.templateId,
        isActive: confirmDialog.newStatus,
        triggerEventId: selectedTriggerEvent?.triggerEventId,
      };
      setLoading((prev) => ({ ...prev, [confirmDialog.templateId]: true }));
      const response = await activeStatusTemplate(params);
      if (response?.status === 200) {
        setListTemplate((prev) =>
          prev.map((template) =>
            template.templateId === confirmDialog.templateId
              ? { ...template, isActive: confirmDialog.newStatus }
              : template
          )
        );
        fetchTriggerEvent();
        snackbar.success("Cập nhật trạng thái thành công");
      } else {
        snackbar.error("Cập nhật trạng thái thất bại");
      }
    } catch (error) {
      snackbar.error("Cập nhật trạng thái thất bại");
    } finally {
      setLoading((prev) => ({ ...prev, [confirmDialog.templateId]: false }));
      setConfirmDialog((prev) => ({ ...prev, open: false }));
    }
  };

  const handleClosePopup = () => {
    setIsOpenModalUpdateTemplate(false);
  };

  return (
    <>
      <Box>
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            marginTop: 1.5,
            marginRight: 2,
            marginLeft: 2,
          }}
        >
          <Box
            sx={{
              cursor: "pointer",
              "&:hover": {
                backgroundColor: "#f0f0f0",
              },
              borderRadius: 99,
              p: 1,
              display: "inline-flex",
              alignItems: "center",
            }}
            onClick={() => {
              fetchZaloTemplate();
              setIsEditTriggerEvent(false);
              setSelectedTriggerEvent(null);
              fetchTriggerEvent();
            }}
          >
            <ArrowBack />
          </Box>{" "}
          <Typography sx={{ fontSize: 20, fontWeight: 600, marginLeft: 1 }}>
            {selectedTriggerEvent?.eventName}
          </Typography>
        </Box>
        <Box sx={{ padding: 3 }}>
          <Paper
            elevation={0}
            sx={{
              borderRadius: 2,
              overflow: "hidden",
              border: "1px solid",
              borderColor: "divider",
            }}
          >
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow sx={{ backgroundColor: "background.paper" }}>
                    {[
                      { header: "STT", width: "60px" },
                      { header: "Kênh", width: "120px" },
                      { header: "Trạng thái", width: "180px" },
                      { header: "Giới hạn gửi tin", width: "150px" },
                      { header: "Chi phí/ tin", width: "150px" },
                      { header: "Quản lý", width: "120px" },
                    ].map((item) => (
                      <TableCell
                        key={item.header}
                        sx={{
                          fontWeight: 600,
                          minWidth: item.width,
                          py: 3,
                          color: "text.primary",
                          fontSize: "0.875rem",
                          borderBottom: "2px solid",
                          borderColor: "divider",
                        }}
                      >
                        {item.header}
                      </TableCell>
                    ))}
                  </TableRow>
                </TableHead>
                <TableBody>
                  {listTemplate.map((temp, index) => (
                    <TableRow
                      key={temp.templateId}
                      hover
                      sx={{
                        "&:last-child td, &:last-child th": { border: 0 },
                        "&:hover": { backgroundColor: "action.hover" },
                        transition: "background-color 0.2s ease",
                      }}
                    >
                      <TableCell sx={{ py: 2.5, color: "text.secondary" }}>{index + 1}</TableCell>
                      <TableCell>
                        <Box sx={{ display: "flex", alignItems: "center" }}>
                          <Chip
                            label={temp.channelName}
                            size="small"
                            variant="outlined"
                            sx={{
                              fontWeight: 500,
                              px: 1,
                            }}
                          />
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: "flex", alignItems: "center", gap: 1.5 }}>
                          {loading[temp.templateId] ? (
                            <CircularProgress size={20} thickness={5} />
                          ) : (
                            <Tooltip
                              title={
                                !isGranted(pathname, PERMISSION_TYPE_ENUM.Edit)
                                  ? "Bạn không có quyền sửa"
                                  : ""
                              }
                            >
                              <span>
                                <Switch
                                  checked={temp.isActive}
                                  disabled={!isGranted(pathname, PERMISSION_TYPE_ENUM.Edit)}
                                  onChange={(e) =>
                                    handleStatusChange(temp.templateId, e.target.checked)
                                  }
                                  size="small"
                                  color="primary"
                                />
                              </span>
                            </Tooltip>
                          )}
                          <Typography
                            variant="body2"
                            sx={{
                              color: temp.isActive ? "success.main" : "text.disabled",
                              fontWeight: 500,
                            }}
                          >
                            {temp.isActive ? "Đang hoạt động" : "Không hoạt động"}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        {temp.policyLink ? (
                          <Link
                            href={temp.policyLink}
                            target="_blank"
                            rel="noopener noreferrer"
                            sx={{
                              textDecoration: "none",
                              "&:hover": { textDecoration: "underline" },
                              fontWeight: 500,
                              color: "primary.main",
                            }}
                          >
                            {temp.policyName}
                          </Link>
                        ) : (
                          <span>{temp.policyName}</span>
                        )}
                      </TableCell>

                      <TableCell>
                        <Typography
                          sx={{
                            fontWeight: 500,
                            color: "success.main",
                          }}
                        >
                          {formatPrice(temp.price)} VNĐ
                        </Typography>
                      </TableCell>
                      <TableCell>
                        {temp.channelType === "UID" && (
                          <Tooltip
                            title={
                              !isGranted(pathname, PERMISSION_TYPE_ENUM.Edit)
                                ? "Bạn không có quyền sửa"
                                : "Sửa template"
                            }
                          >
                            <span>
                              <IconButton
                                size="small"
                                disabled={!isGranted(pathname, PERMISSION_TYPE_ENUM.Edit)}
                                sx={{
                                  color: "primary.main",
                                  "&:hover": {
                                    backgroundColor: "primary.light",
                                    transform: "scale(1.1)",
                                  },
                                  transition: "all 0.2s ease",
                                }}
                                onClick={async () => {
                                  if (temp.templateId) {
                                    const res = await getDetailTemplateById(
                                      shopId,
                                      temp.templateId
                                    );
                                    if (res?.status === 200) {
                                      setSelectedTemplate(res.data.data);
                                      setIsOpenModalUpdateTemplate(true);
                                    }
                                  } else {
                                    setIsOpenModalCreateTemplate(true);
                                    setSelectedTemplate(temp);
                                  }
                                }}
                              >
                                <Edit />
                              </IconButton>
                            </span>
                          </Tooltip>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Paper>
        </Box>
      </Box>

      <Modal
        open={isOpenModalUpdateTemplate}
        onClose={handleClosePopup}
        aria-labelledby="modal-update-template"
        aria-describedby="modal-update-template-description"
      >
        <Box
          sx={{
            position: "absolute",
            top: "50%",
            left: "50%",
            transform: "translate(-50%, -50%)",
            width: "90%",
            maxWidth: "1200px",
            height: "auto",
            maxHeight: "90vh",
            backgroundColor: "#fff",
            borderRadius: 1,
            padding: 2,
            overflow: "hidden",
            display: "flex",
            flexDirection: "column",
          }}
        >
          <ModalUpdateTemplate
            isOpenModalUpdateTemplate={isOpenModalUpdateTemplate}
            onClose={handleClosePopup}
            fetchZaloTemplate={fetchTemplateByTriggerEventId}
            selectedTemplate={selectedTemplate}
            selectedTriggerEvent={selectedTriggerEvent}
          />
        </Box>
      </Modal>

      <Modal
        open={isOpenModalCreateTemplate}
        onClose={() => setIsOpenModalCreateTemplate(false)}
        aria-labelledby="modal-update-template"
        aria-describedby="modal-update-template-description"
      >
        <Box
          sx={{
            position: "absolute",
            top: "50%",
            left: "50%",
            transform: "translate(-50%, -50%)",
            width: "90%",
            maxWidth: "1200px",
            height: "auto",
            maxHeight: "90vh",
            backgroundColor: "#fff",
            borderRadius: 1,
            padding: 2,
            overflow: "hidden",
            display: "flex",
            flexDirection: "column",
          }}
        >
          <ModalCreateTemplate
            // type={tabValue}
            isOpenModalCreateTemplate={isOpenModalCreateTemplate}
            onClose={() => setIsOpenModalCreateTemplate(false)}
            fetchZaloTemplate={fetchTemplateByTriggerEventId}
            selectedTemplate={selectedTemplate}
            selectedTriggerEvent={selectedTriggerEvent}
          />
        </Box>
      </Modal>

      <Dialog
        open={confirmDialog.open}
        onClose={() => setConfirmDialog((prev) => ({ ...prev, open: false }))}
      >
        <DialogTitle sx={{ mt: 1 }}>Xác nhận thay đổi trạng thái</DialogTitle>
        <DialogContent>
          Bạn có chắc chắn muốn {confirmDialog.newStatus ? "bật" : "tắt"} template này?
        </DialogContent>
        <DialogActions sx={{ m: 2 }}>
          <Button
            onClick={() => setConfirmDialog((prev) => ({ ...prev, open: false }))}
            color="inherit"
          >
            Hủy
          </Button>
          <Button onClick={handleConfirmStatusChange} variant="contained" color="primary">
            Xác nhận
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default TableOrderSuccess;
