import { useState } from "react";
import {
  <PERSON>ppBar,
  <PERSON><PERSON><PERSON>,
  IconButton,
  Typography,
  Box,
  Paper,
  TextField,
  InputAdornment,
  Avatar,
  Container,
  CssBaseline,
  ThemeProvider,
  createTheme,
} from "@mui/material";
import {
  ArrowBackIos as ArrowBackIosIcon,
  MoreVert,
  SentimentSatisfiedAlt,
  MoreHoriz,
  Mic,
  AttachFile,
  VerifiedRounded,
} from "@mui/icons-material";
import ImageIcon from "@mui/icons-material/Image";
import VerifiedIcon from "@mui/icons-material/Verified";
import { borderColor } from "@mui/system";
const theme = createTheme({
  palette: {
    primary: {
      main: "#2196f3",
    },
  },
});

export const ContentWithVariables = ({ content, textAlign }) => {
  if (!content) return <Typography variant="body2"></Typography>;

  const parts = [];
  let lastIndex = 0;
  const regex = /\{\{([^}]+)\}\}/g;
  let match;

  while ((match = regex.exec(content)) !== null) {
    if (match.index > lastIndex) {
      parts.push(content?.substring(lastIndex, match.index));
    }

    const variableValue = match[0];

    parts.push(
      <Paper
        key={match.index}
        component="span"
        sx={{
          display: "inline-flex",
          alignItems: "center",
          padding: "2px 8px",
          margin: "0 2px",
          backgroundColor: "#377dff",
          color: "white",
          borderRadius: "6px",
          cursor: "pointer",
        }}
      >
        <Typography variant="body2" component="span">
          {variableValue}
        </Typography>
      </Paper>
    );

    lastIndex = match.index + match[0].length;
  }

  if (lastIndex < content?.length) {
    parts.push(content?.substring(lastIndex));
  }

  return (
    <Typography
      variant="body2"
      component="div"
      sx={{
        mb: 0.5,
        wordBreak: "break-word",
        textAlign: textAlign || "left",
      }}
    >
      {parts.map((part, index) => (typeof part === "string" ? part : part))}
    </Typography>
  );
};

export default function ChatInterface({ content, bannerImage }) {
  const [message, setMessage] = useState("");

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Container
        disableGutters
        maxWidth="sm"
        sx={{
          minHeight: "600px",
          display: "flex",
          flexDirection: "column",
          bgcolor: "#f5f5f5",
          p: 0,
          overflow: "hidden",
          border: "1px solid #ddd",
          borderRadius: { xs: 0, sm: 2 },
        }}
      >
        <AppBar position="static" sx={{ bgcolor: "#2196f3" }}>
          <Toolbar>
            <IconButton edge="start" color="inherit">
              <ArrowBackIosIcon />
            </IconButton>
            <Box sx={{ display: "flex", ml: 1, flexDirection: "column" }}>
              <Typography
                variant="subtitle1"
                sx={{ fontWeight: "bold", display: "flex", alignItems: "center" }}
              >
                Evotech
                <VerifiedRounded
                  sx={{ color: "#ff8c00", ml: 0.5, marginBottom: 0.5, fontSize: 18 }}
                />
              </Typography>
              <Typography variant="caption" sx={{ color: "rgba(255,255,255,0.7)" }}>
                Tài khoản OA
              </Typography>
            </Box>
            <Box sx={{ flexGrow: 1 }} />
            <IconButton color="inherit">
              <MoreVert />
            </IconButton>
          </Toolbar>
        </AppBar>
        <Box
          sx={{
            flexGrow: 1,
            overflow: "auto",
            bgcolor: "#e3f2fd",
            p: 2,
            display: "flex",
            flexDirection: "column",
          }}
        >
          <Box sx={{ display: "flex", mb: 2 }}>
            <Avatar sx={{ bgcolor: "#3f51b5", width: 36, height: 36 }}>E</Avatar>
            <Paper
              elevation={1}
              sx={{
                ml: 1,
                borderRadius: 2,
                width: "100%",
                bgcolor: "white",
                textAlign: "left",
              }}
            >
              {bannerImage?.imageUrl && (
                <Box
                  component="img"
                  src={bannerImage.imageUrl}
                  alt="Uploaded Banner"
                  sx={{ width: "100%", height: "auto", aspectRatio: 2, objectFit: "cover" }}
                />
              )}
              <Box sx={{ p: 2, display: "flex", flexDirection: "column", alignItems: "start" }}>
                <ContentWithVariables content={content?.content} textAlign={content?.align} />
                <Typography variant="caption" sx={{ color: "text.secondary", marginTop: 0.5 }}>
                  11:00
                </Typography>
              </Box>
            </Paper>
          </Box>
        </Box>
        <Paper
          elevation={3}
          sx={{
            p: 1,
            display: "flex",
            alignItems: "center",
            borderTop: "1px solid #e0e0e0",
          }}
        >
          <TextField
            fullWidth
            variant="standard"
            placeholder="Tin nhắn"
            size="small"
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            sx={{
              mx: 1,
              "& .MuiInput-underline:before": {
                borderBottom: "none",
              },
              "& .MuiInput-underline:after": {
                borderBottom: "none",
              },
              "& .MuiInputBase-root": {
                borderRadius: 3,
                padding: "5px 10px",
              },
            }}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <IconButton size="small">
                    <SentimentSatisfiedAlt />
                  </IconButton>
                  <IconButton size="small">
                    <AttachFile />
                  </IconButton>
                </InputAdornment>
              ),
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton size="small">
                    <Mic />
                  </IconButton>
                  <IconButton size="small">
                    <ImageIcon />
                  </IconButton>
                  <IconButton size="small">
                    <MoreHoriz />
                  </IconButton>
                </InputAdornment>
              ),
            }}
          />
        </Paper>
      </Container>
    </ThemeProvider>
  );
}
