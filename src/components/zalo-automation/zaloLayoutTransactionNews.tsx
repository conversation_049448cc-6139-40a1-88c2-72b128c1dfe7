import React, { useEffect, useState } from "react";
import {
  AppBar,
  Box,
  Typography,
  IconButton,
  Paper,
  Divider,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  TextField,
  InputAdornment,
  Toolbar,
  ThemeProvider,
  createTheme,
  CssBaseline,
  Avatar,
} from "@mui/material";
import {
  ArrowBackIos as ArrowBackIosIcon,
  MoreVert,
  LocationOn,
  ShoppingCart,
  Support,
  MoreHoriz,
  Mic,
  InsertEmoticon,
  CheckCircle,
  AttachFile,
  VerifiedRounded,
} from "@mui/icons-material";
import VolumeDownIcon from "@mui/icons-material/VolumeDown";
import ImageIcon from "@mui/icons-material/Image";
import SmartphoneIcon from "@mui/icons-material/Smartphone";
import { TEMPLATE_TYPE } from "@/src/constants/constant";
import { IZaloButton } from "@/src/api/types/zalo-template.types";
import dayjs from "dayjs";
import { ContentWithVariables } from "./zaloLayoutNewText";

const theme = createTheme({
  typography: {
    fontFamily: [
      "-apple-system",
      "BlinkMacSystemFont",
      '"Segoe UI"',
      "Roboto",
      '"Helvetica Neue"',
      "Arial",
      "sans-serif",
    ].join(","),
  },
});

const StartAdornment = (
  <InputAdornment position="start">
    <InsertEmoticon />
  </InputAdornment>
);

const EndAdornment = (
  <InputAdornment position="end">
    <MoreHoriz />
    <Mic />
    <ImageIcon />
  </InputAdornment>
);

const TextFieldStyles = {
  "& .MuiInputBase-root": {
    borderRadius: 3,
    height: 60,
    bgcolor: "white",
    paddingX: 1,
  },
};

const AppBarContent = (
  <AppBar position="static" sx={{ bgcolor: "#0095ff" }}>
    <Toolbar>
      <IconButton edge="start" color="inherit" aria-label="back">
        <ArrowBackIosIcon />
      </IconButton>
      <Box sx={{ flexGrow: 0 }}>
        <Typography variant="h6" component="div">
          Evotech
          <VerifiedRounded sx={{ color: "#ff8c00", ml: 0.5, marginBottom: 0.5, fontSize: 18 }} />
        </Typography>
        <Typography variant="caption" component="div">
          Tài khoản QA
        </Typography>
      </Box>
      <IconButton color="inherit" sx={{ ml: "auto" }}>
        <MoreVert />
      </IconButton>
    </Toolbar>
  </AppBar>
);

const TimeStamp = () => {
  const [currentTime, setCurrentTime] = useState(dayjs());

  useEffect(() => {
    const timerId = setInterval(() => {
      setCurrentTime(dayjs());
    }, 1000);
    return () => clearInterval(timerId);
  }, []);

  return (
    <Box sx={{ display: "flex", justifyContent: "center", mb: 1 }}>
      <Chip
        label={currentTime.format("HH:mm DD/MM/YYYY")}
        size="small"
        sx={{ bgcolor: "#e0e0e0", borderRadius: 1, fontSize: "0.75rem" }}
      />
    </Box>
  );
};

function ActionList(buttons: IZaloButton[]) {
  return (
    Array.isArray(buttons) &&
    buttons.length > 0 && (
      <List component={Paper} sx={{ borderRadius: 0, marginLeft: 0 }}>
        {buttons.map((button, index) => (
          <React.Fragment key={index}>
            <ListItem
              sx={{ marginLeft: 0, padding: "8px 24px" }}
              component="button"
              // onClick={button.onClick}
            >
              <ListItemIcon>
                {/* {button.icon.name} */}
                <img style={{ width: 30, objectFit: "cover" }} src={button.imageIcon} />
              </ListItemIcon>
              <ListItemText primary={button.title} />
              <IconButton edge="end">
                <ArrowBackIosIcon sx={{ transform: "rotate(180deg)" }} />
              </IconButton>
            </ListItem>
            {index < buttons.length - 1 && <Divider variant="inset" component="li" />}
          </React.Fragment>
        ))}
      </List>
    )
  );
}

export default function DeliveryStatusPage({
  transactionType,
  title,
  content,
  subContent,
  bannerImage,
  buttons,
  newParameter,
  selectedMessageType,
}) {
  const [message, setMessage] = useState("");

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Box sx={{ height: "800px", display: "flex", flexDirection: "column", bgcolor: "#f5f5f5" }}>
        {AppBarContent}
        <Box sx={{ flex: 1, overflow: "auto", p: 1 }}>
          <TimeStamp />
          <Paper elevation={1} sx={{ mb: 0, borderRadius: 0, overflow: "hidden" }}>
            <Box sx={{ p: 1, bgcolor: "#f5f5f5", display: "flex", alignItems: "center" }}>
              <VolumeDownIcon fontSize="small" sx={{ mr: 1 }} />
              <Typography variant="subtitle2" fontWeight="bold">
                {TEMPLATE_TYPE[transactionType]?.label}
              </Typography>
            </Box>
            <Box sx={{}}>
              {bannerImage?.imageUrl ? (
                <Box
                  component="img"
                  src={bannerImage.imageUrl}
                  alt="Uploaded Banner"
                  sx={{ width: "100%", height: "auto", aspectRatio: 2, objectFit: "cover" }}
                />
              ) : (
                <Box
                  component="img"
                  src="/logo/image.png"
                  alt="Default Banner"
                  sx={{ width: "100%", height: "auto", objectFit: "contain" }}
                />
              )}
            </Box>
            <Box sx={{ paddingInline: 3, paddingBlock: 1 }}>
              <ContentWithVariables content={title?.content || ""} textAlign={title?.align} />
              <Box sx={{ padding: "8px 0 0" }}></Box>
              <ContentWithVariables content={content?.content || ""} textAlign={content?.align} />
              <Box sx={{ padding: "8px 0 0" }}></Box>
              {/* {Array.isArray(selectedMessageType) && selectedMessageType?.length > 0 && (
                <Box
                  sx={{
                    display: "flex",
                    flexDirection: "column",
                    width: "100%",
                    padding: "16px 0 0",
                    borderRadius: "8px",
                  }}
                >
                  {selectedMessageType?.map((item, index) => {
                    return item.name !== "" ? (
                      <Box
                        sx={{
                          display: "flex",
                          marginBottom: "8px",
                        }}
                        key={index}
                      >
                        <Typography
                          sx={{
                            width: "40%",
                            color: "#666666",
                            fontSize: "14px",
                            textAlign: "left",
                          }}
                        >
                          {item.name}
                        </Typography>
                        <Typography
                          sx={{
                            width: "60%",
                            fontWeight: "500",
                            fontSize: "14px",
                            textAlign: "left",
                          }}
                        >
                          <ContentWithVariables content={item.value} textAlign={"left"} />
                        </Typography>
                      </Box>
                    ) : null;
                  })}
                </Box>
              )} */}

              {Array.isArray(newParameter) && newParameter.length > 0 && (
                <Box
                  sx={{
                    display: "flex",
                    flexDirection: "column",
                    width: "100%",
                    padding: "0px 0 16px",
                    borderRadius: "8px",
                  }}
                >
                  {newParameter.map((item, index) => {
                    return item.name !== "" ? (
                      <Box
                        sx={{
                          display: "flex",
                          marginBottom: "8px",
                        }}
                        key={index}
                      >
                        <Typography
                          sx={{
                            width: "40%",
                            color: "#666666",
                            fontSize: "14px",
                            textAlign: "left",
                          }}
                        >
                          {item?.name}
                        </Typography>
                        <Typography
                          sx={{
                            width: "60%",
                            fontWeight: "500",
                            fontSize: "14px",
                            textAlign: "left",
                          }}
                        >
                          <ContentWithVariables content={item.value} textAlign={"left"} />
                        </Typography>
                      </Box>
                    ) : null;
                  })}
                </Box>
              )}
              <ContentWithVariables
                content={subContent?.content || ""}
                textAlign={subContent?.align}
              />

              {/* <Box sx={{ display: "grid", gridTemplateColumns: "1fr 2fr", gap: 0.5, fontSize: "0.875rem", mb: 1 }}>
                                {parameters.map((param, index) => (
                                    <React.Fragment key={index}>
                                        <Typography variant="body2" sx={{ textAlign: "left", ml: 4 }}>{param.key}</Typography>
                                        <Typography variant="body2">{param.value}</Typography>
                                    </React.Fragment>
                                ))}
                            </Box> */}
            </Box>
          </Paper>
          {ActionList(buttons)}
        </Box>
        <Paper
          elevation={3}
          sx={{
            p: 1,
            display: "flex",
          }}
        >
          <TextField
            fullWidth
            variant="standard"
            placeholder="Tin nhắn"
            size="small"
            value={onmessage}
            onChange={(e) => postMessage(e.target.value)}
            sx={{
              mx: 1,
              "& .MuiInput-underline:before": {
                borderBottom: "none",
              },
              "& .MuiInput-underline:after": {
                borderBottom: "none",
              },
              "& .MuiInputBase-root": {
                padding: "5px 10px",
              },
            }}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <IconButton size="small">
                    <InsertEmoticon />
                  </IconButton>
                  <IconButton size="small">
                    <AttachFile />
                  </IconButton>
                </InputAdornment>
              ),
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton size="small">
                    <Mic />
                  </IconButton>
                  <IconButton size="small">
                    <ImageIcon />
                  </IconButton>
                  <IconButton size="small">
                    <MoreHoriz />
                  </IconButton>
                </InputAdornment>
              ),
            }}
          />
        </Paper>
      </Box>
    </ThemeProvider>
  );
}
