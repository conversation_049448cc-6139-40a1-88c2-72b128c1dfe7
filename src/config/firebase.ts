// import { initializeApp } from "firebase/app";
// import { getMessaging, getToken, onMessage } from "firebase/messaging";
// import Router from "next/router";

// // Load config based on environment
// const env = process.env.NODE_ENV || "development";
// const firebaseConfig = require(`../firebaseConfig/${env}/firebase-config.json`);

// // Initialize Firebase
// const app = initializeApp(firebaseConfig);

// // Initialize Firebase Cloud Messaging
// export const messaging = typeof window !== "undefined" ? getMessaging(app) : null;

// Lưu token vào localStorage
export const requestNotificationPermission = async () => {
  // try {
  //   if (!messaging) return null;
  //   // Check if we already have a token
  //   const existingToken = localStorage.getItem("fcmToken");
  //   if (existingToken) {
  //     return existingToken;
  //   }
  //   const permission = await Notification.requestPermission();
  //   if (permission === "granted") {
  //     const token = await getToken(messaging, {
  //       vapidKey:
  //         "BO83ooG8A-Nee6EN_H5jFU4vJkdhHk8cqyrMSFikAzEgHOGIeLiR25INf_KgF7cwypldaKEbVMtSq9knFLXLvPI",
  //     });
  //     // Save token
  //     if (token) {
  //       localStorage.setItem("fcmToken", token);
  //     }
  //     return token;
  //   }
  //   return null;
  // } catch (error) {
  //   console.error("Error getting notification permission:", error);
  //   return null;
  // }
};

// Handle foreground messages
export const onMessageListener = (listener: any) => {
  // return new Promise((resolve) => {
  // return onMessage(messaging, listener);
  // });
};
