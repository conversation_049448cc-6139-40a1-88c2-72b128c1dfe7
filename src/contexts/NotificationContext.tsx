import React, { createContext, useContext, useEffect, useState } from "react";
import { requestNotificationPermission, onMessageListener } from "../config/firebase";

interface NotificationContextType {
  notifications: any[];
}

const NotificationContext = createContext<NotificationContextType>({
  notifications: [],
});

export const NotificationProvider = ({ children }: { children: React.ReactNode }) => {
  const [notifications, setNotifications] = useState<any[]>([]);

  useEffect(() => {
    const unsubscribe = onMessageListener((payload: any) => {
      setNotifications((prev) => [...prev, payload]);

      // Show notification using browser's Notification API when app is in foreground
      if (Notification.permission === "granted") {
        new Notification(payload.notification.title, {
          body: payload.notification.body,
          icon: "/icon.png",
        });
      }
    });

    return unsubscribe;
  }, []);

  return (
    <NotificationContext.Provider value={{ notifications }}>
      {children}
    </NotificationContext.Provider>
  );
};

export const useNotification = () => useContext(NotificationContext);
