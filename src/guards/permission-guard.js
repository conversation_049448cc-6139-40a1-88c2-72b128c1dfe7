import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useCheckPermission } from "../api/hooks/auth/use-check-permission";
import { PERMISSION_TYPE_ENUM } from "../constants/constant";
import { paths } from "../paths";

const permissionRules = [
  {
    path: "/pos",
    base: "/pos/list-location",
    permission: PERMISSION_TYPE_ENUM.View,
  },
  {
    path: "/dashboard/store/content-management/create-popup-ad",
    base: "/dashboard/store/content-management",
    permission: PERMISSION_TYPE_ENUM.Add,
  },
  {
    path: "/dashboard/store/content-management/create-article-category",
    base: "/dashboard/store/content-management",
    permission: PERMISSION_TYPE_ENUM.Add,
  },
  {
    path: "/dashboard/store/content-management/create-article",
    base: "/dashboard/store/content-management",
    permission: PERMISSION_TYPE_ENUM.Add,
  },
  {
    path: "/dashboard/product/category-management/create",
    base: "/dashboard/product/category-management",
    permission: PERMISSION_TYPE_ENUM.Add,
  },
  {
    path: "/dashboard/product/product-management/create",
    base: "/dashboard/product/product-management",
    permission: PERMISSION_TYPE_ENUM.Add,
  },
  {
    path: "/dashboard/product/product-management/",
    exclude: ["/dashboard/product/product-management/create"],
    base: "/dashboard/product/product-management",
    permission: PERMISSION_TYPE_ENUM.Edit,
  },
  {
    path: "/dashboard/item-groups/new",
    base: "/dashboard/item-groups/list",
    permission: PERMISSION_TYPE_ENUM.Add,
  },
  {
    path: "/dashboard/item-groups/",
    exclude: ["/dashboard/item-groups/new", "/dashboard/item-groups/list"],
    base: "/dashboard/item-groups/list",
    permission: PERMISSION_TYPE_ENUM.Edit,
  },
  {
    path: "/dashboard/price-list/",
    // exclude: ["/dashboard/price-list/new", "/dashboard/price-list/list"],
    base: "/dashboard/price-list/list",
    permission: PERMISSION_TYPE_ENUM.Edit,
  },
  {
    path: "/dashboard/orders/draft/create",
    base: "/dashboard/orders/draft/list-draft-order",
    permission: PERMISSION_TYPE_ENUM.Add,
  },
  {
    path: "/dashboard/orders/draft",
    exclude: ["/dashboard/orders/draft/create", "/dashboard/orders/draft/list-draft-order"],
    base: "/dashboard/orders/draft/list-draft-order",
    permission: PERMISSION_TYPE_ENUM.Edit,
  },
  {
    path: "/dashboard/orders",
    exclude: [
      "/dashboard/orders/draft/create",
      "/dashboard/orders/draft/list-draft-order",
      "/dashboard/orders/draft",
    ],
    base: "/dashboard/orders/list",
    permission: PERMISSION_TYPE_ENUM.View,
  },
  {
    path: "/dashboard/customers/new",
    base: "/dashboard/customers/list",
    permission: PERMISSION_TYPE_ENUM.Add,
  },
  {
    path: "/dashboard/customers/",
    exclude: [
      "/dashboard/customers/new",
      "/dashboard/customers/list",
      "/dashboard/customers/paid-membership",
      "/dashboard/customers/customer-classification",
    ],
    base: "/dashboard/customers/list",
    permission: PERMISSION_TYPE_ENUM.View,
  },
  {
    path: "/dashboard/marketing/vouchers/voucher_promotions/new",
    base: "/dashboard/marketing/vouchers/list",
    permission: PERMISSION_TYPE_ENUM.Add,
  },
  {
    path: "/dashboard/marketing/vouchers/voucher_promotions",
    exclude: ["/dashboard/marketing/vouchers/voucher_promotions/new"],
    base: "/dashboard/marketing/vouchers/list",
    permission: PERMISSION_TYPE_ENUM.Edit,
  },
  {
    path: "/dashboard/marketing/vouchers/voucher_transports/new",
    base: "/dashboard/marketing/vouchers/list",
    permission: PERMISSION_TYPE_ENUM.Add,
  },
  {
    path: "/dashboard/marketing/vouchers/voucher_transports",
    exclude: ["/dashboard/marketing/vouchers/voucher_transports/new"],
    base: "/dashboard/marketing/vouchers/list",
    permission: PERMISSION_TYPE_ENUM.Edit,
  },
  {
    path: "/dashboard/settings/location/add",
    base: "/dashboard/settings/location",
    permission: PERMISSION_TYPE_ENUM.Add,
  },
  {
    path: "/dashboard/settings/location/",
    exclude: ["/dashboard/settings/location/add"],
    base: "/dashboard/settings/location",
    permission: PERMISSION_TYPE_ENUM.Edit,
  },
  {
    path: "/dashboard/settings/payment/",
    base: "/dashboard/settings/payment",
    permission: PERMISSION_TYPE_ENUM.Edit,
  },
  {
    path: "/dashboard/settings/clause/add",
    base: "/dashboard/settings/clause",
    permission: PERMISSION_TYPE_ENUM.Add,
  },
  {
    path: "/dashboard/settings/clause/",
    exclude: ["/dashboard/settings/clause/add"],
    base: "/dashboard/settings/clause",
    permission: PERMISSION_TYPE_ENUM.Edit,
  },
  {
    path: "/dashboard/marketing/vouchers/custom/",
    exclude: ["/dashboard/marketing/vouchers/custom/new"],
    base: "/dashboard/marketing/vouchers/list",
    permission: PERMISSION_TYPE_ENUM.Edit,
  },
  {
    path: "/dashboard/marketing/vouchers/custom/new",
    base: "/dashboard/marketing/vouchers/list",
    permission: PERMISSION_TYPE_ENUM.Add,
  },
  {
    path: "/dashboard/app/gamification/campaign/",
    base: "/dashboard/app/gamification",
    permission: PERMISSION_TYPE_ENUM.View,
  },
];

export const PermissionGuard = ({ children, url, redirectTo = "/dashboard/package" }) => {
  const router = useRouter();

  const rule = permissionRules.find(
    (r) => url.startsWith(r.path) && (!r.exclude || !r.exclude.some((ex) => url.startsWith(ex)))
  );

  const modifiedUrl = rule ? rule.base : url;
  const permission = rule ? rule.permission : PERMISSION_TYPE_ENUM.View;

  const { hasPermission, loading, isAgency } = useCheckPermission({ url: modifiedUrl, permission });

  useEffect(() => {
    if (
      url.startsWith(paths.dashboard.account.index) ||
      (url == paths.dashboard.index && isAgency)
    ) {
      return;
    }
    if (!loading && !hasPermission) {
      router.push(isAgency ? redirectTo : "/403");
    }
  }, [hasPermission, isAgency, loading, router, redirectTo]);

  if (loading) return <div />;
  if (
    !hasPermission &&
    url !== paths.dashboard.index &&
    !url.startsWith(paths.dashboard.account.index)
  )
    return null;
  return <>{children}</>;
};
