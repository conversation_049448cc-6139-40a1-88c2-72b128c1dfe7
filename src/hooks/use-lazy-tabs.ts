import { useState, useEffect, useCallback } from 'react';

interface UseLazyTabsOptions {
  initialTab?: number;
  preloadNext?: boolean;
  cacheSize?: number;
}

interface TabState {
  loaded: boolean;
  loading: boolean;
  error: string | null;
  data?: any;
}

export const useLazyTabs = (
  totalTabs: number,
  options: UseLazyTabsOptions = {}
) => {
  const {
    initialTab = 0,
    preloadNext = true,
    cacheSize = 3
  } = options;

  const [currentTab, setCurrentTab] = useState(initialTab);
  const [tabStates, setTabStates] = useState<Record<number, TabState>>(() => {
    const initial: Record<number, TabState> = {};
    for (let i = 0; i < totalTabs; i++) {
      initial[i] = {
        loaded: i === initialTab,
        loading: false,
        error: null
      };
    }
    return initial;
  });

  const [loadedTabs, setLoadedTabs] = useState<Set<number>>(new Set([initialTab]));

  // Mark tab as loaded
  const markTabLoaded = useCallback((tabIndex: number, data?: any) => {
    setTabStates(prev => ({
      ...prev,
      [tabIndex]: {
        ...prev[tabIndex],
        loaded: true,
        loading: false,
        error: null,
        data
      }
    }));
    setLoadedTabs(prev => new Set([...prev, tabIndex]));
  }, []);

  // Mark tab as loading
  const markTabLoading = useCallback((tabIndex: number) => {
    setTabStates(prev => ({
      ...prev,
      [tabIndex]: {
        ...prev[tabIndex],
        loading: true,
        error: null
      }
    }));
  }, []);

  // Mark tab as error
  const markTabError = useCallback((tabIndex: number, error: string) => {
    setTabStates(prev => ({
      ...prev,
      [tabIndex]: {
        ...prev[tabIndex],
        loading: false,
        error
      }
    }));
  }, []);

  // Change tab and trigger loading if needed
  const changeTab = useCallback((newTab: number) => {
    if (newTab < 0 || newTab >= totalTabs) return;
    
    setCurrentTab(newTab);
    
    // Load current tab if not loaded
    if (!tabStates[newTab].loaded && !tabStates[newTab].loading) {
      markTabLoading(newTab);
    }
    
    // Preload next tab if enabled
    if (preloadNext && newTab + 1 < totalTabs) {
      const nextTab = newTab + 1;
      if (!tabStates[nextTab].loaded && !tabStates[nextTab].loading) {
        markTabLoading(nextTab);
      }
    }
  }, [totalTabs, tabStates, preloadNext, markTabLoading]);

  // Clean up old tabs if cache size exceeded
  useEffect(() => {
    if (loadedTabs.size > cacheSize) {
      const tabsArray = Array.from(loadedTabs);
      const oldestTabs = tabsArray
        .filter(tab => tab !== currentTab)
        .slice(0, loadedTabs.size - cacheSize);
      
      if (oldestTabs.length > 0) {
        setTabStates(prev => {
          const newStates = { ...prev };
          oldestTabs.forEach(tab => {
            newStates[tab] = {
              loaded: false,
              loading: false,
              error: null
            };
          });
          return newStates;
        });
        
        setLoadedTabs(prev => {
          const newSet = new Set(prev);
          oldestTabs.forEach(tab => newSet.delete(tab));
          return newSet;
        });
      }
    }
  }, [loadedTabs, cacheSize, currentTab]);

  // Check if tab should be rendered
  const shouldRenderTab = useCallback((tabIndex: number) => {
    return tabStates[tabIndex].loaded || tabStates[tabIndex].loading;
  }, [tabStates]);

  // Get tab state
  const getTabState = useCallback((tabIndex: number) => {
    return tabStates[tabIndex];
  }, [tabStates]);

  // Preload specific tab
  const preloadTab = useCallback((tabIndex: number) => {
    if (tabIndex >= 0 && tabIndex < totalTabs && 
        !tabStates[tabIndex].loaded && 
        !tabStates[tabIndex].loading) {
      markTabLoading(tabIndex);
    }
  }, [totalTabs, tabStates, markTabLoading]);

  // Reset all tabs
  const resetTabs = useCallback(() => {
    const initial: Record<number, TabState> = {};
    for (let i = 0; i < totalTabs; i++) {
      initial[i] = {
        loaded: i === currentTab,
        loading: false,
        error: null
      };
    }
    setTabStates(initial);
    setLoadedTabs(new Set([currentTab]));
  }, [totalTabs, currentTab]);

  return {
    currentTab,
    changeTab,
    tabStates,
    shouldRenderTab,
    getTabState,
    markTabLoaded,
    markTabLoading,
    markTabError,
    preloadTab,
    resetTabs,
    loadedTabs: Array.from(loadedTabs)
  };
};

// Hook for lazy loading data
export const useLazyData = <T>(
  loadFn: () => Promise<T>,
  dependencies: any[] = []
) => {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const load = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const result = await loadFn();
      setData(result);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  }, dependencies);

  const reload = useCallback(() => {
    load();
  }, [load]);

  const reset = useCallback(() => {
    setData(null);
    setError(null);
    setLoading(false);
  }, []);

  return {
    data,
    loading,
    error,
    load,
    reload,
    reset
  };
};

// Performance optimization hook
export const usePerformanceOptimization = () => {
  const [isVisible, setIsVisible] = useState(true);
  const [shouldUpdate, setShouldUpdate] = useState(true);

  // Throttle updates when component is not visible
  useEffect(() => {
    const handleVisibilityChange = () => {
      setIsVisible(!document.hidden);
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);

  // Debounce updates
  const throttleUpdates = useCallback((delay: number = 100) => {
    setShouldUpdate(false);
    setTimeout(() => setShouldUpdate(true), delay);
  }, []);

  return {
    isVisible,
    shouldUpdate,
    throttleUpdates
  };
};
