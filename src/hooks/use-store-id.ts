import { useState, useEffect } from 'react';
import { StorageService } from 'nextjs-api-lib';
import { logger } from '../utils/logger';

export const useStoreId = () => {
  const [storeId, setStoreId] = useState<string>('');

  useEffect(() => {
    try {
      const storedStoreId = StorageService.get<string>('currentStoreId');
      if (storedStoreId) {
        setStoreId(storedStoreId);
      } else {
        logger.warn('StoreId not found in localStorage');
      }
    } catch (error) {
      logger.error('Error handling storeId:', error);
    }
  }, []);

  return storeId || '';
};
