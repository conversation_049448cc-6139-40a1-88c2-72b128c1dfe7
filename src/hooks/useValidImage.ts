import { useEffect, useState } from "react";

/**
 * T<PERSON><PERSON> về link ảnh hợp lệ:
 *  - Ưu tiên `src`
 *  - Nếu `src` lỗi 404 (hoặc không có), dùng `fallback`
 */
export const useValidImage = (src?: string, fallback?: string) => {
  const [validSrc, setValidSrc] = useState<string>(() => src || fallback || "");

  useEffect(() => {
    let isActive = true;

    if (!src) {
      setValidSrc(fallback || "");
      return;
    }

    const img = new Image();
    img.src = src;

    img.onload = () => isActive && setValidSrc(src);
    img.onerror = () => isActive && setValidSrc(fallback || "");

    return () => {
      isActive = false;
    };
  }, [src, fallback]);

  return validSrc;
};
