const GraduationHat01 = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={24}
    height={24}
    fill="none"
    {...props}>
    <path
      fill="currentColor"
      d="M12.3578 3.679c-.1312-.0656-.1968-.0984-.2656-.1113a.4997.4997 0 0 0-.1844 0c-.0688.0129-.1344.0457-.2656.1113L2 8.5l9.6422 4.8212c.1312.0656.1968.0984.2656.1113a.5014.5014 0 0 0 .1844 0c.0688-.0129.1344-.0457.2656-.1113L22 8.5001 12.3578 3.679Z"
      opacity={0.12}
    />
    <path
      stroke="currentColor"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M5 10.0001v6.0111c0 .359 0 .5384.0546.6969a.9995.9995 0 0 0 .231.3736c.1173.1198.2778.2001.5989.3606l5.4 2.7c.2623.1312.3935.1968.5311.2226.1219.0228.2469.0228.3688 0 .1376-.0258.2688-.0914.5311-.2226l5.4-2.7c.3211-.1605.4816-.2408.5989-.3606a.999.999 0 0 0 .2309-.3736C19 16.5496 19 16.3702 19 16.0112v-6.0111m-17-1.5 9.6422-4.8211c.1312-.0656.1968-.0984.2656-.1113a.4997.4997 0 0 1 .1844 0c.0688.0129.1344.0457.2656.1113L22 8.5l-9.6422 4.8212c-.1312.0656-.1968.0984-.2656.1113a.5014.5014 0 0 1-.1844 0c-.0688-.0129-.1344-.0457-.2656-.1113L2 8.5001Z"
    />
  </svg>
);

export default GraduationHat01;
