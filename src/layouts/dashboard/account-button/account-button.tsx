import { useState, useEffect } from "react";
import { useRouter } from "next/router";
import User01Icon from "@untitled-ui/icons-react/build/esm/User01";
import Avatar from "@mui/material/Avatar";
import Box from "@mui/material/Box";
import Menu from "@mui/material/Menu";
import MenuItem from "@mui/material/MenuItem";
import Typography from "@mui/material/Typography";
import Divider from "@mui/material/Divider";

import { useLogout } from "@/src/api/hooks/auth/use-logout";
import { TokenService } from "nextjs-api-lib";
import { paths } from "@/src/paths";
import { logger } from "@/src/utils/logger";
import { formatDisplayPhoneNumber } from "@/src/utils/format";
import { useAppSelector } from "@/src/redux/hooks";

export const AccountButton = () => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const router = useRouter();
  const { profile } = useAppSelector((state) => state.profile);
  const { logout } = useLogout();

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleAccountSettings = () => {
    handleMenuClose();
    router.push(paths.dashboard.account.settings);
  };
  const handleManageShop = () => {
    router.push(paths.index);
  };

  const handleLogout = async () => {
    try {
      const refreshToken = TokenService.getRefreshToken("partner");
      const tokenFCM = "";
      await logout(refreshToken, tokenFCM);
      router.push(paths.auth.login);
    } catch (error) {
      logger.error("Logout failed:", error);
    }
  };

  if (!profile) {
    return null;
  }

  return (
    <Box>
      <Box
        onClick={handleMenuOpen}
        sx={{
          color: "text.secondary",
          fontWeight: 500,
          cursor: "pointer",
          display: "flex",
          alignItems: "center",
        }}
      >
        <Avatar
          sx={{
            height: 32,
            width: 32,
          }}
          src={profile.avatar}
        >
          <User01Icon />
        </Avatar>
      </Box>

      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
        PaperProps={{
          sx: {
            width: 280,
          },
        }}
      >
        <MenuItem sx={{ display: "flex", alignItems: "center", p: 2 }}>
          <Avatar src={profile.avatar} sx={{ mr: 1 }} />
          <Box>
            <Typography variant="body2" color="textPrimary">
              {formatDisplayPhoneNumber(profile.phoneNumber)}
            </Typography>
            <Typography variant="body2" color="textSecondary">
              {profile.email}
            </Typography>
          </Box>
        </MenuItem>
        <Divider sx={{ mb: "8px" }} />
        <MenuItem onClick={handleManageShop}>Quản lý cửa hàng</MenuItem>
        <Divider />
        <MenuItem onClick={handleAccountSettings}>Cài đặt tài khoản</MenuItem>
        <Divider />
        <MenuItem onClick={handleLogout} sx={{ color: "error.main" }}>
          Đăng xuất
        </MenuItem>
      </Menu>
    </Box>
  );
};
