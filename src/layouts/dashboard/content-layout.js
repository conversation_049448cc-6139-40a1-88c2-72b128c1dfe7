import PropTypes from "prop-types";
import { memo, useMemo } from "react";
import { withAuthGuard } from "src/hocs/with-auth-guard";
import { PermissionGuard } from "src/guards/permission-guard";
import { usePathname } from "src/hooks/use-pathname";
import { settings } from "nprogress";
import { HorizontalLayout } from "./horizontal-layout";
import { VerticalLayout } from "./vertical-layout";
import { useSections } from "./config";

/**
 * A simplified content-only layout that doesn't include navigation components
 * This is used within the MainLayout which already has the TopNav and SideNav
 */
const ContentLayoutBase = memo((props) => {
  const { children } = props;
  const pathFromHook = usePathname();
  const sections = useSections();

  let pathname = pathFromHook || window.location.pathname;
  if (pathname.endsWith("/")) {
    pathname = pathname.slice(0, -1);
  }

  const LayoutComponent = useMemo(
    () => (settings.layout === "horizontal" ? HorizontalLayout : VerticalLayout),
    [settings.layout]
  );
  return (
    <PermissionGuard url={pathname}>
      <LayoutComponent sections={sections} navColor={settings.navColor || "primary"} {...props} />
    </PermissionGuard>
  );
});

ContentLayoutBase.displayName = "ContentLayoutBase";

ContentLayoutBase.propTypes = {
  children: PropTypes.node,
};

export const ContentLayout = withAuthGuard(ContentLayoutBase);
