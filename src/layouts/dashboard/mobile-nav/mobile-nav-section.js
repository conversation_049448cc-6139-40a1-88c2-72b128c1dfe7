import PropTypes from "prop-types";
import Stack from "@mui/material/Stack";
import { MobileNavItem } from "./mobile-nav-item";
import { Box, Collapse } from "@mui/material";
import React, { useState } from "react";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";

const renderItems = ({ depth = 0, items, pathname }) =>
  items.reduce(
    (acc, item) =>
      reduceChildRoutes({
        acc,
        depth,
        item,
        pathname,
      }),
    []
  );

const reduceChildRoutes = ({ acc, depth, item, pathname }) => {
  const checkPath = !!(item.path && pathname);
  const partialMatch = checkPath ? pathname.includes(item.path) : false;
  // const exactMatch = checkPath ? pathname === item.path : false;
  const exactMatch = checkPath ? pathname.replace(/\/$/, "") === item.path : false;

  if (item.items) {
    acc.push(
      <MobileNavItem
        active={partialMatch}
        depth={depth}
        disabled={item.disabled}
        icon={item.icon}
        key={item.title}
        label={item.label}
        open={partialMatch}
        title={item.title}
      >
        <Stack
          component="ul"
          spacing={0.5}
          sx={{
            listStyle: "none",
            m: 0,
            p: 0,
          }}
        >
          {renderItems({
            depth: depth + 1,
            items: item.items,
            pathname,
          })}
        </Stack>
      </MobileNavItem>
    );
  } else {
    acc.push(
      <MobileNavItem
        active={exactMatch}
        depth={depth}
        disabled={item.disabled}
        external={item.external}
        icon={item.icon}
        key={item.title}
        label={item.label}
        path={item.path}
        title={item.title}
      />
    );
  }

  return acc;
};

export const MobileNavSection = (props) => {
  const { items = [], pathname, subheader = "", ...other } = props;
  const [isOpen, setIsOpen] = useState(true);

  return (
    <Stack
      component="ul"
      className="list-item-sidebar"
      spacing={0.5}
      sx={{
        listStyle: "none",
        m: 0,
        p: 0,
        paddingLeft: "25px",
      }}
      {...other}
    >
      <>
        {subheader && (
          <Box
            onClick={() => setIsOpen((prev) => !prev)}
            component="li"
            sx={{
              color: "#000",
              fontWeight: "700",
              fontSize: 14,
              lineHeight: 1.66,
              mb: 1,
              ml: 1,
              textTransform: "none",
              cursor: "pointer",
              display: "flex",
              alignItems: "center",
              marginBottom: "5px !important",

              justifyContent: "space-between",
              gap: "6px",
            }}
          >
            {subheader}
            <ExpandMoreIcon
              sx={{
                transition: "transform 0.3s",
                transform: isOpen ? "rotate(180deg)" : "rotate(0deg)",
                fontSize: 18,
              }}
            />
          </Box>
        )}
        <Collapse in={isOpen} timeout="auto" unmountOnExit>
          {renderItems({ items, pathname })}
        </Collapse>
      </>
    </Stack>
  );
};

MobileNavSection.propTypes = {
  items: PropTypes.array,
  pathname: PropTypes.string,
  subheader: PropTypes.node,
};
