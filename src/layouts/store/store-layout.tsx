import { Box } from "@mui/material";
import React, { useState, ReactElement, useEffect } from "react";
import { StoreNav } from "./store-nav";
import { StorageService } from "nextjs-api-lib";
import { useAppSelector } from "@/src/redux/hooks";

interface PageProps {
  searchQuery?: string;
}

interface StoreLayoutProps {
  children: ReactElement<PageProps>;
}

const StoreLayout: React.FC<StoreLayoutProps> = ({ children }) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [userProfile, setUserProfile] = useState<any>(null);

  const childWithProps = React.cloneElement(children, {
    searchQuery,
  });

  return (
    <Box sx={{ display: "flex", flexDirection: "column", height: "100vh", overflow: "hidden" }}>
      <StoreNav searchQuery={searchQuery} setSearchQuery={setSearchQuery} />
      <Box
        sx={{
          flex: 1,
          overflowY: "hidden",
          backgroundColor: "background.paper",
          backgroundImage: "url('/assets/gradient-bg.svg')",
          backgroundPosition: "top center",
          backgroundRepeat: "no-repeat",
          color: "common.white",
          display: "flex",
          justifyContent: "center",
        }}
      >
        {childWithProps}
      </Box>
    </Box>
  );
};

export default StoreLayout;
