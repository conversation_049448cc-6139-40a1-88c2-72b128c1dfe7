import { FC, useEffect, useRef } from "react";
import Box from "@mui/material/Box";
import Stack from "@mui/material/Stack";
import Button from "@mui/material/Button";
import InputBase from "@mui/material/InputBase";
import SearchIcon from "@mui/icons-material/Search";
import StorefrontIcon from "@mui/icons-material/Storefront";
import { paths } from "src/paths";
import { useRouter } from "next/router";
import { UserMenu } from "@/src/layouts/store/user-menu";
import { useAppDispatch, useAppSelector } from "@/src/redux/hooks";
import { getProfile } from "@/src/redux/slices/profileSlice";

interface StoreNavProps {
  searchQuery: string;
  setSearchQuery: (query: string) => void;
}

export const StoreNav: FC<StoreNavProps> = ({ searchQuery, setSearchQuery }) => {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { profile } = useAppSelector((state) => state.profile);
  const profileFetchedRef = useRef(false);

  const handleCreateStore = () => {
    router.push(paths.store.create);
  };

  useEffect(() => {
    if (!profileFetchedRef.current) {
      dispatch(getProfile());
      profileFetchedRef.current = true;
    }
  }, [dispatch]);

  return (
    <Box
      component="header"
      sx={{
        backgroundColor: "background.paper",
        position: "sticky",
        top: 0,
        zIndex: 1000,
        boxShadow: 1,
      }}
    >
      <Stack
        direction="row"
        alignItems="center"
        justifyContent="space-between"
        spacing={2}
        sx={{ minHeight: 64, px: 3, py: 1 }}
      >
        <Box
          sx={{
            alignItems: "center",
            display: "flex",
            flex: 1,
            maxWidth: { xs: "100%", sm: 400 },
            px: 2,
          }}
        >
          <SearchIcon sx={{ color: "text.secondary", mr: 1 }} />
          <InputBase
            placeholder="Tên cửa hàng"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            sx={{ flex: 1 }}
          />
        </Box>
        <Stack
          alignItems="center"
          direction="row"
          spacing={2}
          sx={{ justifyContent: "flex-end", width: { xs: "100%", sm: "auto" } }}
        >
          <Button variant="contained" startIcon={<StorefrontIcon />} onClick={handleCreateStore}>
            <Box sx={{ display: { xs: "none", sm: "block" } }}>Tạo cửa hàng</Box>
            <Box sx={{ display: { xs: "block", sm: "none" } }}>Tạo</Box>
          </Button>
          <UserMenu
            userId={profile?.phoneNumber}
            userEmail={profile?.email}
            userAvatar={profile?.avatar}
          />
        </Stack>
      </Stack>
    </Box>
  );
};
