import { FC, useEffect, useState } from "react";
import { useRouter } from "next/router";
import Box from "@mui/material/Box";
import Menu from "@mui/material/Menu";
import MenuItem from "@mui/material/MenuItem";
import Typography from "@mui/material/Typography";
import SvgIcon from "@mui/material/SvgIcon";
import Avatar from "@mui/material/Avatar";
import Divider from "@mui/material/Divider";
import ChevronDownIcon from "@untitled-ui/icons-react/build/esm/ChevronDown";
import { useSearchParams } from "src/hooks/use-search-params";
import { paths } from "src/paths";
import { useLogout } from "@/src/api/hooks/auth/use-logout";
import { TokenService } from "nextjs-api-lib";
import { logger } from "src/utils/logger";
import { formatDisplayPhoneNumber } from "@/src/utils/format";
import { onMessageListener, requestNotificationPermission } from "@/src/config/firebase";
import { useAppDispatch } from "@/src/redux/hooks";
import { revokeDevice } from "@/src/redux/slices/profileSlice";

interface UserMenuProps {
  userId: string;
  userEmail: string;
  userAvatar: string;
}

export const UserMenu: FC<UserMenuProps> = ({ userId, userEmail, userAvatar }) => {
  const dispatch = useAppDispatch();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const router = useRouter();
  const searchParams = useSearchParams();
  const returnTo = searchParams.get("returnTo");
  const { logout } = useLogout();
  const deviceToken = localStorage.getItem("fcmToken");

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleAccountSettings = () => {
    handleMenuClose();
    router.push(paths.store.settings);
  };
  const handleManageShop = () => {
    router.push(paths.index);
  };

  const handleLogout = async () => {
    try {
      const refreshToken = TokenService.getRefreshToken("partner");
      const tokenFCM = "";
      deviceToken && (await dispatch(revokeDevice(deviceToken)));
      await logout(refreshToken, tokenFCM);
      router.push(paths.auth.login);
    } catch (error) {
      logger.error("Logout failed", error);
    }
  };

  return (
    <Box>
      <Box
        sx={{
          color: "text.secondary",
          fontWeight: 500,
          cursor: "pointer",
          display: "flex",
          alignItems: "center",
        }}
        onClick={handleMenuOpen}
      >
        <Typography variant="body1" color="textSecondary">
          {formatDisplayPhoneNumber(userId)}
        </Typography>
        <SvgIcon fontSize="small" sx={{ color: "text.secondary", ml: 0.5 }}>
          <ChevronDownIcon />
        </SvgIcon>
      </Box>
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
        PaperProps={{
          sx: {
            width: 280,
          },
        }}
      >
        <Box sx={{ display: "flex", alignItems: "center", p: 2 }}>
          <Avatar src={userAvatar} sx={{ mr: 1 }} />
          <Box>
            <Typography variant="body2" color="textPrimary">
              {formatDisplayPhoneNumber(userId)}
            </Typography>
            <Typography variant="body2" color="textSecondary">
              {userEmail}
            </Typography>
          </Box>
        </Box>
        <Divider sx={{ mb: "8px" }} />
        <MenuItem onClick={handleManageShop}>Quản lý cửa hàng</MenuItem>
        <Divider />
        <MenuItem onClick={handleAccountSettings}>Cài đặt tài khoản</MenuItem>
        <Divider />
        <MenuItem onClick={handleLogout} sx={{ color: "error.main" }}>
          Đăng xuất
        </MenuItem>
      </Menu>
    </Box>
  );
};
