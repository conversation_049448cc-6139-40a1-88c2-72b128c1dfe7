import { tokens } from "../tokens";

export const en = {
  [tokens.common.languageChanged]: "Language changed",
  [tokens.common.cancel]: "Cancel",
  [tokens.common.delete]: "Delete",
  [tokens.common.save]: "Save",
  [tokens.common.none]: "None",
  [tokens.nav.academy]: "Academy",
  [tokens.nav.account]: "Account",
  [tokens.nav.analytics]: "Analytics",
  [tokens.nav.auth]: "Auth",
  [tokens.nav.blog]: "Blog",
  [tokens.nav.browse]: "Browse",
  [tokens.nav.calendar]: "Calendar",
  [tokens.nav.chat]: "Chat",
  [tokens.nav.checkout]: "Checkout",
  [tokens.nav.concepts]: "Concepts",
  [tokens.nav.contact]: "Contact",
  [tokens.nav.course]: "Course",
  [tokens.nav.create]: "Create",
  [tokens.nav.crypto]: "Crypto",
  [tokens.nav.customers]: "Customers",
  [tokens.nav.dashboard]: "Dashboard",
  [tokens.nav.details]: "Details",
  [tokens.nav.ecommerce]: "E-Commerce",
  [tokens.nav.edit]: "Edit",
  [tokens.nav.error]: "Error",
  [tokens.nav.feed]: "Feed",
  [tokens.nav.fileManager]: "File Manager",
  [tokens.nav.finance]: "Finance",
  [tokens.nav.fleet]: "Fleet",
  [tokens.nav.forgotPassword]: "Forgot Password",
  [tokens.nav.invoiceList]: "Invoices",
  [tokens.nav.jobList]: "Job Listings",
  [tokens.nav.kanban]: "Kanban",
  [tokens.nav.list]: "List",
  [tokens.nav.login]: "Login",
  [tokens.nav.logistics]: "Logistics",
  [tokens.nav.mail]: "Mail",
  [tokens.nav.management]: "Management",
  [tokens.nav.orderList]: "Orders",
  [tokens.nav.overview]: "Overview",
  [tokens.nav.pages]: "Pages",
  [tokens.nav.postCreate]: "Post Create",
  [tokens.nav.postDetails]: "Post Details",
  [tokens.nav.postList]: "Post List",
  [tokens.nav.pricing]: "Pricing",
  [tokens.nav.productList]: "Products",
  [tokens.nav.profile]: "Profile",
  [tokens.nav.register]: "Register",
  [tokens.nav.resetPassword]: "Reset Password",
  [tokens.nav.socialMedia]: "Social Media",
  [tokens.nav.verifyCode]: "Verify Code",
  forgotPassword: {
    title: "Forgot Password",
    description:
      "Verification information will be sent to Zalo, please enter the registered phone number.",
    phoneLabel: "Phone Number",
    submitButton: "Submit",
    verifyTitle: "Verify Code",
    codeLabel: "Verification Code",
    verifyButton: "Verify",
    resetTitle: "Reset Password",
    newPasswordLabel: "New Password",
    confirmPasswordLabel: "Confirm Password",
    successMessage: "Password reset successful!",
    loginButton: "Login",
    backButton: "Back",
    phoneInvalid: "Invalid phone number",
    phoneRequired: "Phone number is required",
    codeInvalid: "Invalid verification code",
    codeRequired: "Verification code is required",
    newPasswordRequired: "New password is required",
    confirmPasswordRequired: "Confirm password is required",
    passwordsMustMatch: "Passwords must match",
  },
  [tokens.store.title]: "Create New Store",
  [tokens.store.storeNameLabel]: "Store Name",
  [tokens.store.businessTypeLabel]: "Business Type",
  [tokens.store.createButton]: "Create Store",
  [tokens.store.joinDateLabel]: "Join Date",
  [tokens.store.expiryDateLabel]: "Expiry Date",
  [tokens.store.statusLabel]: "Status",
  [tokens.store.detailsButton]: "Details",
  [tokens.store.emptyStateMessage]: "No stores available, please create a new store.",
  [tokens.store.createStoreButton]: "Create Store",
  [tokens.businessTypes.retail]: "Retail",
  [tokens.businessTypes.wholesale]: "Wholesale",
  [tokens.businessTypes.service]: "Service",
  [tokens.store.storeNameRequired]: "Store name is required",
  [tokens.store.businessTypeRequired]: "Business type is required",
  [tokens.store.logoRequired]: "Store logo is required",
  [tokens.store.activatedLabel]: "Activated",
  [tokens.logoUpload.uploadLogo]: "Upload Logo",
  [tokens.logoUpload.description]: "Store Logo (512x512px)",
  [tokens.logoUpload.errorProcessing]: "Error processing image. Please try again.",
  [tokens.logoUpload.invalidFile]: "Please select a valid image file.",
  [tokens.store.createSuccess]: "Store created successfully!",
  [tokens.store.createError]: "Failed to create store. Please try again.",
  [tokens.common.cancel]: "Cancel",
  [tokens.common.delete]: "Delete",
  [tokens.store.deleteConfirmTitle]: "Delete Store",
  [tokens.store.deleteConfirmMessage]: 'Are you sure you want to delete store "{{storeName}}"?',
  [tokens.store.deleteSuccess]: "Store deleted successfully!",
  [tokens.store.deleteError]: "Failed to delete store. Please try again.",
  [tokens.settings.title]: "Account Settings",
  [tokens.settings.personalInfo]: "Personal Information",
  [tokens.settings.firstName]: "First Name",
  [tokens.settings.lastName]: "Last Name",
  [tokens.settings.phone]: "Phone Number",
  [tokens.settings.email]: "Email",
  [tokens.settings.notificationEmail]: "Notification Email",
  [tokens.settings.language]: "Select Language",
  [tokens.settings.languageSection]: "System Language",
  [tokens.settings.saveButton]: "Save Changes",
  [tokens.settings.firstNameRequired]: "First name is required",
  [tokens.settings.lastNameRequired]: "Last name is required",
  [tokens.settings.phoneRequired]: "Phone number is required",
  [tokens.settings.phoneInvalid]: "Invalid phone number",
  [tokens.settings.emailRequired]: "Email is required",
  [tokens.settings.emailInvalid]: "Invalid email format",
  [tokens.settings.updateSuccess]: "Profile updated successfully!",
  [tokens.settings.passwordUpdateSuccess]: "Password updated successfully!",
  [tokens.settings.updateError]: "Failed to update profile. Please try again.",
  [tokens.contentManagement.title]: "Content Management",
  [tokens.contentManagement.tabs.popupAds]: "Popup Ads",
  [tokens.contentManagement.tabs.articleCategories]: "Article Categories",
  [tokens.contentManagement.tabs.articles]: "Articles",
  [tokens.contentManagement.popupAds.addNew]: "Add New",
  [tokens.contentManagement.popupAds.search]: "Search ads",
  [tokens.contentManagement.popupAds.emptyStateMessage]: "No popup ads available yet",
  [tokens.contentManagement.popupAds.emptyStateButton]: "Create New Ad",
  [tokens.contentManagement.popupAds.table.number]: "No.",
  [tokens.contentManagement.popupAds.table.name]: "Ad Name",
  [tokens.contentManagement.popupAds.table.runTime]: "Run Time",
  [tokens.contentManagement.popupAds.table.views]: "Views",
  [tokens.contentManagement.popupAds.table.creationDate]: "Creation Date",
  [tokens.contentManagement.popupAds.table.status]: "Status",
  [tokens.contentManagement.popupAds.table.actions]: "Actions",
  [tokens.contentManagement.popupAds.dialog.deleteTitle]: "Delete Advertisement",
  [tokens.contentManagement.popupAds.dialog.deleteMessage]:
    'Are you sure you want to delete "{{name}}" advertisement?',
  [tokens.contentManagement.popupAds.dialog.deleteConfirm]: "Delete",
  [tokens.contentManagement.popupAds.dialog.deleteCancel]: "Cancel",
  [tokens.contentManagement.popupAds.snackbar.copySuccess]: "Ad copied successfully",
  [tokens.contentManagement.popupAds.snackbar.deleteSuccess]: "Ad deleted successfully",
  [tokens.contentManagement.popupAds.snackbar.deleteError]:
    "Failed to delete ad. Please try again.",
  [tokens.contentManagement.popupAds.emptyState.title]: "No Popup Ads",
  [tokens.contentManagement.popupAds.emptyState.message]: "You haven't created any popup ads yet",
  [tokens.contentManagement.popupAds.emptyState.subtitle]:
    "Create popup ads to attract customers and increase engagement with your store",
  [tokens.contentManagement.popupAds.emptyState.button]: "Create New Ad",
  [tokens.contentManagement.popupAds.create.title]: "Create Popup Ad",
  [tokens.contentManagement.popupAds.create.editTitle]: "Edit Popup Ad",
  [tokens.contentManagement.popupAds.create.form.adContent]: "Ad Content",
  [tokens.contentManagement.popupAds.create.form.link]: "Link",
  [tokens.contentManagement.popupAds.create.form.isActive]: "Publish",
  [tokens.contentManagement.popupAds.create.form.saveButton]: "Save",
  [tokens.contentManagement.popupAds.create.form.cancelButton]: "Cancel",
  [tokens.datePicker.today]: "Today",
  [tokens.datePicker.clear]: "Clear",
  [tokens.datePicker.ok]: "OK",
  [tokens.datePicker.cancel]: "Cancel",
  [tokens.datePicker.invalidDate]: "Invalid date",
  [tokens.contentManagement.popupAds.create.form.contentTitle]: "Ad Content",
  [tokens.contentManagement.popupAds.create.form.adContent]: "Ad Title",
  [tokens.contentManagement.popupAds.create.form.imageTitle]: "Image",
  [tokens.contentManagement.popupAds.create.form.imageCaption]:
    "Drag and drop or click to upload (600x800px)",
  [tokens.contentManagement.popupAds.create.form.displayPages]: "Display Pages",
  [tokens.contentManagement.popupAds.create.form.displayFrequency]: "Display Frequency",
  [tokens.contentManagement.popupAds.create.form.displayTime]: "Display Time",
  [tokens.contentManagement.popupAds.create.form.startDate]: "Start Date",
  [tokens.contentManagement.popupAds.create.form.endDate]: "End Date",
  [tokens.contentManagement.popupAds.create.form.pages.home]: "Home Page",
  [tokens.contentManagement.popupAds.create.form.pages.products]: "Products Page",
  [tokens.contentManagement.popupAds.create.form.pages.account]: "Account Page",
  [tokens.contentManagement.popupAds.create.form.pages.success]: "Success Page",
  [tokens.contentManagement.popupAds.create.form.frequency.once]: "Show Once",
  [tokens.contentManagement.popupAds.create.form.frequency.everyVisit]: "Show Every Visit",
  [tokens.contentManagement.popupAds.create.form.nameRequired]: "Ad title is required",
  [tokens.contentManagement.popupAds.create.form.nameMaxLength]: "Ad title is limit 255 character",
  [tokens.contentManagement.popupAds.create.form.imageRequired]: "Ad image is required",
  [tokens.contentManagement.popupAds.create.form.linkRequired]: "Link is required",
  [tokens.contentManagement.popupAds.create.form.linkInvalid]: "Invalid URL format",
  [tokens.contentManagement.popupAds.create.form.linkMaxLength]: "Link is limit 255 character",
  [tokens.contentManagement.popupAds.create.form.dateRequired]: "Date is required",
  [tokens.contentManagement.popupAds.create.form.endDateInvalid]:
    "End date must be after start date",
  [tokens.contentManagement.popupAds.create.form.createSuccess]: "Ad created successfully",
  [tokens.contentManagement.popupAds.create.form.createError]:
    "Failed to create ad. Please try again.",
  [tokens.contentManagement.popupAds.create.success]: "Ad created successfully!",
  [tokens.contentManagement.popupAds.deleteConfirmTitle]: "Confirm Delete",
  [tokens.contentManagement.popupAds.deleteConfirmMessage]:
    'Are you sure you want to delete "{{name}}" advertisement?',
  [tokens.contentManagement.popupAds.deleteSuccess]: "Advertisement deleted successfully!",
  [tokens.contentManagement.popupAds.deleteError]:
    "Failed to delete advertisement. Please try again!",
  [tokens.contentManagement.popupAds.notification.deleteSuccess]:
    "Advertisement deleted successfully!",
  [tokens.contentManagement.popupAds.notification.deleteError]:
    "Failed to delete advertisement. Please try again!",
  [tokens.contentManagement.popupAds.edit.success]: "Advertisement updated successfully!",
  [tokens.contentManagement.popupAds.edit.error]:
    "Failed to update advertisement. Please try again!",
  [tokens.contentManagement.articleCategory.create.title]: "Create Article Category",
  [tokens.contentManagement.articleCategory.create.editTitle]: "Edit Article Category",
  [tokens.contentManagement.articleCategory.create.success]:
    "Article category created successfully!",
  [tokens.contentManagement.articleCategory.create.form.contentTitle]: "Category Information",
  [tokens.contentManagement.articleCategory.create.form.categoryName]: "Category Name",
  [tokens.contentManagement.articleCategory.create.form.isActive]: "Publish",
  [tokens.contentManagement.articleCategory.create.form.saveButton]: "Save",
  [tokens.contentManagement.articleCategory.create.form.cancelButton]: "Cancel",
  [tokens.contentManagement.articleCategory.create.form.nameRequired]: "Category name is required",
  [tokens.contentManagement.articleCategory.create.form.nameMaxLength]:
    "Category name is limit 255 characters",

  [tokens.contentManagement.articleCategory.create.form.createSuccess]:
    "Category created successfully",
  [tokens.contentManagement.articleCategory.create.form.createError]:
    "Failed to create category. Please try again.",
  [tokens.contentManagement.articleCategory.edit.success]: "Category updated successfully!",
  [tokens.contentManagement.articleCategory.edit.error]:
    "Failed to update category. Please try again!",
  [tokens.contentManagement.articleCategory.addNew]: "Add New",
  [tokens.contentManagement.articleCategory.search]: "Search categories",
  [tokens.contentManagement.articleCategory.table.number]: "No.",
  [tokens.contentManagement.articleCategory.table.name]: "Category Name",
  [tokens.contentManagement.articleCategory.table.creationDate]: "Creation Date",
  [tokens.contentManagement.articleCategory.table.status]: "Status",
  [tokens.contentManagement.articleCategory.table.actions]: "Actions",
  [tokens.contentManagement.articleCategory.emptyState.title]: "No Article Categories",
  [tokens.contentManagement.articleCategory.emptyState.message]:
    "You haven't created any article categories yet",
  [tokens.contentManagement.articleCategory.emptyState.subtitle]:
    "Create article categories to organize and manage your content",
  [tokens.contentManagement.articleCategory.emptyState.button]: "Create New Category",
  [tokens.contentManagement.articleCategory.deleteConfirmTitle]: "Confirm Delete",
  [tokens.contentManagement.articleCategory.deleteConfirmMessage]:
    'Are you sure you want to delete "{{name}}" category?',
  [tokens.contentManagement.articleCategory.deleteSuccess]: "Category deleted successfully!",
  [tokens.contentManagement.articleCategory.deleteError]:
    "Failed to delete category. Please try again!",
  [tokens.contentManagement.articleCategory.delete.confirmTitle]: "Delete Category",
  [tokens.contentManagement.articleCategory.delete.confirmMessage]:
    'Are you sure you want to delete category "{{name}}"?',
  [tokens.contentManagement.articleCategory.delete.successMessage]: "Category deleted successfully",
  [tokens.contentManagement.articleCategory.delete.errorMessage]:
    "Failed to delete category. Please try again!",
  [tokens.contentManagement.article.create.title]: "Create Article",
  [tokens.contentManagement.article.create.editTitle]: "Edit Article",
  [tokens.contentManagement.article.create.success]: "Article created successfully!",
  [tokens.contentManagement.article.create.form.contentTitle]: "Article Information",
  [tokens.contentManagement.article.create.form.title]: "Title",
  [tokens.contentManagement.article.create.form.description]: "Short Description",
  [tokens.contentManagement.article.create.form.category]: "Category",
  [tokens.contentManagement.article.create.form.image]: "Image",
  [tokens.contentManagement.article.create.form.imageCaption]:
    "Drag and drop or click to upload (16:9 ratio)",
  [tokens.contentManagement.article.create.form.content]: "Content",
  [tokens.contentManagement.article.create.form.isActive]: "Publish",
  [tokens.contentManagement.article.create.form.saveButton]: "Save",
  [tokens.contentManagement.article.create.form.cancelButton]: "Cancel",
  [tokens.contentManagement.article.create.form.titleRequired]: "Title is required",
  [tokens.contentManagement.article.create.form.titleMaxLength]: "Title is litmit 255 characters",
  [tokens.contentManagement.article.create.form.descriptionRequired]: "Description is required",
  [tokens.contentManagement.article.create.form.descriptionMaxLength]:
    "Description is limit 255 characters",
  [tokens.contentManagement.article.create.form.categoryRequired]: "Category is required",
  [tokens.contentManagement.article.create.form.imageRequired]: "Image is required",
  [tokens.contentManagement.article.create.form.contentRequired]: "Content is required",
  [tokens.contentManagement.article.edit.success]: "Article updated successfully!",
  [tokens.contentManagement.article.edit.error]: "Failed to update article. Please try again!",
  [tokens.contentManagement.article.addNew]: "Add New Article",
  [tokens.contentManagement.article.search]: "Search articles",
  [tokens.contentManagement.article.table.number]: "No.",
  [tokens.contentManagement.article.table.title]: "Title",
  [tokens.contentManagement.article.table.image]: "Article Image",
  [tokens.contentManagement.article.table.creationDate]: "Creation Date",
  [tokens.contentManagement.article.table.status]: "Status",
  [tokens.contentManagement.article.table.actions]: "Actions",
  [tokens.contentManagement.article.emptyState.title]: "No Articles Yet",
  [tokens.contentManagement.article.emptyState.subtitle]:
    "Create articles to share information with your customers",
  [tokens.contentManagement.article.emptyState.button]: "Create New Article",
  [tokens.contentManagement.article.snackbar.copySuccess]: "Article link copied successfully",
  [tokens.contentManagement.article.snackbar.deleteSuccess]: "Article deleted successfully",
  [tokens.contentManagement.article.snackbar.deleteError]: "Failed to delete article",
  [tokens.contentManagement.article.deleteConfirmTitle]: "Confirm Delete",
  [tokens.contentManagement.article.deleteConfirmMessage]:
    'Are you sure you want to delete article "{{title}}"?',
  [tokens.contentManagement.article.create.form.imageTitle]: "Article Image",
  [tokens.contentManagement.article.delete.confirmTitle]: "Delete Article",
  [tokens.contentManagement.article.delete.confirmMessage]:
    'Are you sure you want to delete article "{{name}}"?',
  [tokens.contentManagement.article.delete.successMessage]: "Article deleted successfully",
  [tokens.contentManagement.article.delete.errorMessage]:
    "Failed to delete article. Please try again!",
  [tokens.contentManagement.category.create.productTitle]: "Create Product Category",
  [tokens.contentManagement.category.create.serviceTitle]: "Create Service Category",
  [tokens.contentManagement.category.create.success]: "Category created successfully!",
  [tokens.contentManagement.category.create.error]: "Failed to create category. Please try again.",
  [tokens.contentManagement.category.create.form.contentTitle]: "Category Information",
  [tokens.contentManagement.category.create.form.categoryName]: "Category Name",
  [tokens.contentManagement.category.create.form.parentCategory]: "Parent Category",
  [tokens.contentManagement.category.create.form.position]: "Display Position",
  [tokens.contentManagement.category.create.form.imageTitle]: "Category Image",
  [tokens.contentManagement.category.create.form.imageCaption]:
    "Drag and drop or click to upload (512x512px)",
  [tokens.contentManagement.category.create.form.isActive]: "Publish",
  [tokens.contentManagement.category.create.form.nameRequired]: "Category name is required",
  [tokens.contentManagement.category.create.form.positionRequired]: "Display position is required",
  [tokens.contentManagement.category.create.form.positionMin]:
    "Display position must be greater than or equal to 0",
  [tokens.contentManagement.category.create.form.imageRequired]: "Category image is required",
  [tokens.contentManagement.category.create.title]: "Create Category",
  [tokens.contentManagement.category.edit.title]: "Edit Category",
  [tokens.contentManagement.category.create.success]: "Category created successfully!",
  [tokens.contentManagement.category.create.error]: "Failed to create category",
  [tokens.contentManagement.category.edit.success]: "Category updated successfully!",
  [tokens.contentManagement.category.edit.error]: "Failed to update category",
  [tokens.contentManagement.category.create.form.isHome]: "Display on Homepage",
  contentManagement: {
    category: {
      tabs: {
        product: "Product Categories",
        service: "Service Categories",
      },
      create: {
        title: "Create Category",
        productTitle: "Create Product Category",
        serviceTitle: "Create Service Category",
        form: {
          contentTitle: "Category Information",
          categoryName: "Category Name",
          parentCategory: "Parent Category",
          position: "Display Position",
          imageTitle: "Category Image",
          imageCaption: "Drag and drop or click to upload (512x512px)",
          isActive: "Publish",
          isHome: "Display on Homepage",
          nameRequired: "Category name is required",
          positionRequired: "Display position is required",
          positionMin: "Display position must be greater than or equal to 0",
          imageRequired: "Category image is required",
        },
        success: "Category created successfully!",
        error: "Failed to create category",
      },
      edit: {
        title: "Edit Category",
        success: "Category updated successfully!",
        error: "Failed to update category",
      },
    },
    product: {
      create: {
        media: {
          libraryTitle: "Media Library",
          tabImages: "Images",
          tabVideos: "Videos",
        },
      },
    },
  },
  common: {
    save: "Save",
    cancel: "Cancel",
    none: "None",
  },
  PRO_INVALID_OLDPASS: "Old password is incorrect",
  PRO_NEWPASS_INCORRECT: "New password is invalid",
  BASE_USER_AUTH_NOT_FOUND: "User not found with the provided information",
  [tokens.common.validation.maxLength]: "Maximum length is {{max}} characters",
  [tokens.common.validation.maxNumber]: "Maximum value is {{max}}",
  [tokens.common.validation.minValue]: "Minimum value is {{min}}",
  [tokens.contentManagement.product.tabs.product]: "Products",
  [tokens.contentManagement.product.tabs.service]: "Services",
  [tokens.contentManagement.product.search.placeholder]: "Search by name, barcode, SKU, supplier",
  [tokens.contentManagement.product.search.filter]: "Filter",
  [tokens.contentManagement.product.search.category]: "Category",
  [tokens.contentManagement.product.table.product]: "Product",
  [tokens.contentManagement.product.table.price]: "Sale Price/Price ($)",
  [tokens.contentManagement.product.table.stock]: "Stock",
  [tokens.contentManagement.product.table.status]: "Status",
  [tokens.contentManagement.product.table.featured]: "Featured Product",
  [tokens.contentManagement.product.table.category]: "Category",
  [tokens.contentManagement.product.table.actions]: "Actions",
  [tokens.contentManagement.product.status.active]: "Active",
  [tokens.contentManagement.product.status.inactive]: "Inactive",
  [tokens.contentManagement.product.status.featured]: "Featured",
  [tokens.contentManagement.product.status.notFeatured]: "Not Featured",
  [tokens.contentManagement.product.actions.create]: "Create New",
  [tokens.contentManagement.product.actions.edit]: "Edit Product",
  [tokens.contentManagement.product.actions.delete]: "Delete Product",
  [tokens.contentManagement.product.emptyState.title]: "No Products Yet",
  [tokens.contentManagement.product.emptyState.subtitle]:
    "Create products to start selling to your customers",
  [tokens.contentManagement.product.emptyState.button]: "Create New Product",
  [tokens.contentManagement.product.delete.confirmTitle]: "Delete Product",
  [tokens.contentManagement.product.delete.confirmMessage]:
    'Are you sure you want to delete "{{name}}"?',
  [tokens.contentManagement.product.delete.successMessage]: "Product deleted successfully",
  [tokens.contentManagement.product.delete.errorMessage]:
    "Failed to delete product. Please try again!",
  [tokens.contentManagement.search.filter]: "Filter",
  [tokens.contentManagement.search.category]: "Category",
  [tokens.contentManagement.search.selectCategory]: "Select category",
  [tokens.contentManagement.search.clearFilter]: "Clear filter",
  [tokens.contentManagement.product.search.searchButton]: "Search",
  [tokens.contentManagement.product.create.typeLabel]: "Product Type",
  [tokens.contentManagement.product.create.physicalProduct]: "Physical Product",
  [tokens.contentManagement.product.create.physicalDescription]:
    "Products with shipping/self-pickup options",
  [tokens.contentManagement.product.create.service]: "Service",
  [tokens.contentManagement.product.create.serviceDescription]:
    "Services without shipping requirements",
  [tokens.contentManagement.product.create.nameLabel]: "Product Name",
  [tokens.contentManagement.product.create.descriptionLabel]: "Product Details",
  [tokens.contentManagement.product.create.displaySettings]: "Display Settings",
  [tokens.contentManagement.product.create.visibility]: "Show Product",
  [tokens.contentManagement.product.create.featured]: "Featured Product",
  [tokens.contentManagement.product.create.displayOrder]: "Display Order",
  [tokens.contentManagement.product.create.categories]: "Categories",
  [tokens.contentManagement.product.create.parentCategory]: "Parent Category",
  [tokens.contentManagement.product.create.childCategory]: "Child Category",
  [tokens.contentManagement.product.create.images]: "Product Images/Videos",
  [tokens.contentManagement.product.create.variants]: "Product Variants",
  [tokens.contentManagement.product.create.hasVariants]: "Product has variants",
  [tokens.contentManagement.product.create.editVariants]: "Edit Variants",
  [tokens.contentManagement.product.create.inventory.title]: "Inventory Management",
  [tokens.contentManagement.product.create.inventory.maxQuantity]: "Maximum Purchase Quantity",
  [tokens.contentManagement.product.create.inventory.stock]: "Stock Quantity",
  [tokens.contentManagement.product.create.pricing.title]: "Price Information",
  [tokens.contentManagement.product.create.pricing.price]: "Retail Price",
  [tokens.contentManagement.product.create.pricing.priceCapital]: "Cost Price",
  [tokens.contentManagement.product.create.pricing.priceCapitalHint]:
    "The cost price helps calculate profit margins",
  [tokens.contentManagement.product.create.pricing.priceReal]: "Sale Price",
  [tokens.contentManagement.product.create.shipping.title]: "Shipping",
  [tokens.contentManagement.product.create.shipping.weight]: "Weight",
  [tokens.contentManagement.product.create.shipping.weightUnit]: "Gram",
  [tokens.contentManagement.product.create.shipping.dimensions]: "Dimensions",
  [tokens.contentManagement.product.create.shipping.dimensionsDescription]:
    "Used to calculate shipping fees at checkout and mark pricing during shipping.",
  [tokens.contentManagement.product.create.shipping.length]: "Length",
  [tokens.contentManagement.product.create.shipping.width]: "Width",
  [tokens.contentManagement.product.create.shipping.height]: "Height",
  [tokens.contentManagement.product.create.shipping.dimensionUnit]: "cm",
  [tokens.contentManagement.product.create.shipping.warehouse]: "Warehouse Location",
  [tokens.contentManagement.product.create.shipping.selectWarehouse]: "Select warehouse",
  [tokens.contentManagement.product.create.seo.title]: "SEO Optimization",
  [tokens.contentManagement.product.create.seo.description]:
    "Add product title and description to improve SEO",
  [tokens.contentManagement.product.create.seo.editButton]: "Edit SEO Properties",
  [tokens.contentManagement.product.create.seo.dialog.title]: "Edit SEO",
  [tokens.contentManagement.product.create.seo.dialog.pageTitle]: "Page Title",
  [tokens.contentManagement.product.create.seo.dialog.pageDesc]: "Page Description",
  [tokens.contentManagement.product.create.seo.dialog.tags]: "Tags",
  [tokens.contentManagement.product.create.seo.dialog.url]: "URL",
  [tokens.contentManagement.product.create.displaySettings]: "Display Settings",
  [tokens.contentManagement.product.create.visibility]: "Show Product",
  [tokens.contentManagement.product.create.featured]: "Featured Product",
  [tokens.contentManagement.product.create.displayOrder]: "Display Order",
  [tokens.contentManagement.product.create.soldCount]: "Sold Count",
  [tokens.contentManagement.product.create.displayOrderMin]:
    "Display order must be greater than or equal to 0",
  [tokens.contentManagement.product.create.soldCountMin]:
    "Sold count must be greater than or equal to 0",
  [tokens.contentManagement.product.create.dropToUpload]: "Drop files to upload",
  [tokens.contentManagement.product.create.uploadFiles]: "Upload files",
  [tokens.contentManagement.product.create.dragAndDrop]: "Or drag and drop files to upload",
  [tokens.contentManagement.product.create.addFromUrl]: "Add from URL",
  [tokens.contentManagement.product.create.addFromLibrary]: "Add from Library",
  [tokens.contentManagement.product.create.purchaseQuantity.title]: "Quantity Management",
  [tokens.contentManagement.product.create.purchaseQuantity.purchaseLimit]: "Purchase Limit",
  [tokens.contentManagement.product.create.purchaseQuantity.purchaseLimitHint]:
    "Maximum quantity per order (0 = unlimited)",
  [tokens.contentManagement.product.create.purchaseQuantity.stock]: "Stock",
  [tokens.contentManagement.product.create.purchaseQuantity.stockHint]: "Current stock quantity",
  [tokens.contentManagement.product.create.variant.title]: "Product Variants",
  [tokens.contentManagement.product.create.variant.description]:
    "Add variants if this product comes in multiple versions, like different sizes or colors",
  [tokens.contentManagement.product.create.variant.hasVariants]:
    "This product has multiple variants",
  [tokens.contentManagement.product.create.variant.editButton]: "Edit Variants",
  [tokens.contentManagement.product.create.variant.dialog.title]: "Edit Product Variants",
  [tokens.contentManagement.product.create.variant.dialog.addVariant]: "Add Variant",
  [tokens.contentManagement.product.create.variant.dialog.variantName]: "Variant Name",
  [tokens.contentManagement.product.create.variant.dialog.variantValue]: "Variant Value",
  [tokens.contentManagement.product.create.variant.dialog.deleteVariant]: "Delete",
  [tokens.contentManagement.product.create.variant.dialog.noVariants]: "No variants added yet",
  [tokens.contentManagement.product.create.variant.dialog.saveButton]: "Save Changes",
  [tokens.contentManagement.product.create.variant.dialog.cancelButton]: "Cancel",
  [tokens.contentManagement.product.create.media.title]: "Product Images/Videos",
  [tokens.contentManagement.product.create.media.description]: "Add up to 9 images/videos",
  [tokens.contentManagement.product.create.media.dropToUpload]: "Drop files to upload",
  [tokens.contentManagement.product.create.media.dragAndDrop]: "Or drag and drop files to upload",
  [tokens.contentManagement.product.create.media.addFromUrl]: "Add from URL",
  [tokens.contentManagement.product.create.media.addFromLibrary]: "Add from Library",
  [tokens.contentManagement.product.variant.dialog.title]: "Add Product Variants",
  [tokens.contentManagement.product.variant.dialog.addSpecification]: "Add Specification",
  [tokens.contentManagement.product.variant.dialog.specificationName]: "Specification Name",
  [tokens.contentManagement.product.variant.dialog.specificationValue]: "Value",
  [tokens.contentManagement.product.variant.dialog.specificationNamePlaceholder]:
    "e.g., Color, Size",
  [tokens.contentManagement.product.variant.dialog.specificationValuePlaceholder]:
    "Enter value and press Enter",
  [tokens.contentManagement.product.variant.dialog.deleteSpecification]: "Delete Specification",
  [tokens.contentManagement.product.variant.dialog.addValue]: "Add Value",
  [tokens.contentManagement.product.variant.dialog.specificationCount]: "{{count}} values",
  [tokens.contentManagement.product.variant.dialog.variantsCreated]:
    "{{count}} variants created from specifications",
  [tokens.contentManagement.product.variant.dialog.validation.nameRequired]:
    "Specification name is required",
  [tokens.contentManagement.product.variant.dialog.validation.valuesRequired]:
    "At least one value is required",
  [tokens.contentManagement.product.create.pricing.validation.priceRequired]:
    "Retail price is required",
  [tokens.contentManagement.product.create.pricing.validation.priceMin]:
    "Retail price must be greater than 0",
  [tokens.contentManagement.product.create.pricing.validation.priceCapitalRequired]:
    "Cost price is required",
  [tokens.contentManagement.product.create.pricing.validation.priceCapitalMin]:
    "Cost price must be greater than 0",
  [tokens.contentManagement.product.create.pricing.validation.priceRealRequired]:
    "Sale price is required",
  [tokens.contentManagement.product.create.pricing.validation.priceRealMin]:
    "Sale price must be greater than 0",
  [tokens.contentManagement.product.variant.table.image]: "Image",
  [tokens.contentManagement.product.variant.table.specification]: "Specification",
  [tokens.contentManagement.product.variant.table.costPrice]: "Cost Price",
  [tokens.contentManagement.product.variant.table.listPrice]: "List Price",
  [tokens.contentManagement.product.variant.table.salePrice]: "Sale Price",
  [tokens.contentManagement.product.variant.table.stock]: "Stock",
  [tokens.contentManagement.product.variant.table.maxQuantity]: "Max Quantity",
  [tokens.contentManagement.product.variant.imageUpload.button]: "Add Image",
  [tokens.contentManagement.product.variant.table.costPriceInput]: "Enter cost price",
  [tokens.contentManagement.product.variant.table.listPriceInput]: "Enter list price",
  [tokens.contentManagement.product.variant.table.salePriceInput]: "Enter sale price",
  [tokens.contentManagement.product.variant.table.stockInput]: "Enter stock quantity",
  [tokens.contentManagement.product.variant.table.maxQuantityInput]: "Enter max purchase quantity",
  [tokens.contentManagement.product.variant.validation.priceCapitalRequired]:
    "Cost price is required",
  [tokens.contentManagement.product.variant.validation.priceRequired]: "List price is required",
  [tokens.contentManagement.product.variant.validation.priceRealRequired]: "Sale price is required",
  [tokens.contentManagement.product.variant.validation.quantityRequired]:
    "Stock quantity is required",
  [tokens.contentManagement.product.variant.validation.quantityPurchaseRequired]:
    "Max purchase quantity is required",
  [tokens.contentManagement.product.variant.validation.priceCapitalMin]:
    "Cost price must be greater than 0",
  [tokens.contentManagement.product.variant.validation.priceMin]:
    "List price must be greater than 0",
  [tokens.contentManagement.product.variant.validation.priceRealMin]:
    "Sale price must be greater than 0",
  [tokens.contentManagement.product.variant.validation.quantityMin]:
    "Stock quantity must be greater than or equal to 0",
  [tokens.contentManagement.product.variant.validation.quantityPurchaseMin]:
    "Max purchase quantity must be greater than or equal to 0",
  [tokens.contentManagement.product.variant.validation.priceCapitalLessThanPrice]:
    "Cost price must be less than or equal to list price",
  [tokens.contentManagement.product.variant.validation.priceLessThanPriceReal]:
    "List price must be less than or equal to sale price",
  [tokens.contentManagement.product.variant.placeholder.costPrice]: "Enter cost price",
  [tokens.contentManagement.product.variant.placeholder.listPrice]: "Enter list price",
  [tokens.contentManagement.product.variant.placeholder.salePrice]: "Enter sale price",
  [tokens.contentManagement.product.variant.helperText.costPrice]:
    "Cost price must be less than list price",
  [tokens.contentManagement.product.variant.helperText.listPrice]:
    "List price must be between cost and sale price",
  [tokens.contentManagement.product.variant.helperText.salePrice]:
    "Sale price must be greater than list price",
  [tokens.contentManagement.product.variant.validation.priceGreaterThanZero]:
    "List price must be greater than 0",
  [tokens.contentManagement.product.variant.validation.priceCapitalGreaterThanZero]:
    "Cost price must be greater than 0",
  [tokens.contentManagement.product.variant.validation.priceRealGreaterThanZero]:
    "Sale price must be greater than 0",
  [tokens.contentManagement.product.variant.validation.priceLessPriceReal]:
    "List price must be less than sale price",
  [tokens.contentManagement.product.variant.validation.priceRealGreaterPrice]:
    "Sale price must be greater than list price",
  [tokens.contentManagement.product.variant.validation.priceGreaterPriceCapital]:
    "List price must be greater than cost price",
  [tokens.contentManagement.product.variant.validation.quantityGreaterThanZero]:
    "Stock quantity must be greater than 0",
  [tokens.contentManagement.product.variant.helperText.stock]:
    "Enter stock quantity greater than 0",
  [tokens.contentManagement.product.variant.helperText.maxQuantity]:
    "Enter maximum purchase quantity greater than 0",
  user: {
    deleteSuccess: "Delete customer success!",
  },
  shippingAddress: {
    address: {
      required: "Address is required",
    },
  },
};
