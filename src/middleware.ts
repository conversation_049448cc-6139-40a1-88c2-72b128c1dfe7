import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Skip middleware for static files and API routes
  if (
    pathname.startsWith("/_next") ||
    pathname.startsWith("/api") ||
    pathname.includes(".") ||
    pathname.startsWith("/static")
  ) {
    return NextResponse.next();
  }

  // Nếu pathname kết thúc bằng / và không phải là root path
  if (pathname !== "/" && pathname.endsWith("/")) {
    // Chuyển hướng đến path không có dấu / ở cuối
    return NextResponse.redirect(new URL(pathname.slice(0, -1), request.url));
  }

  return NextResponse.next();
}

// Chỉ áp dụng middleware cho các routes cần thiết
export const config = {
  matcher: ["/dashboard/:path*", "/auth/:path*"],
};
