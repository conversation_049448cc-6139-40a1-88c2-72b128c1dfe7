import { Seo } from "@/src/components/seo";
import { paths } from "@/src/paths";
import { Visibility, VisibilityOff } from "@mui/icons-material";
import {
  Box,
  Button,
  Card,
  CardContent,
  IconButton,
  InputAdornment,
  TextField,
  Typography,
  CircularProgress,
} from "@mui/material";
import { useFormik } from "formik";
import React, { ReactElement, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import AuthLayout from "src/layouts/auth";
import * as Yup from "yup";
import { useForgotPassword } from "@/src/api/hooks";
import TitleTypography from "@/src/components/title-typography/title-typography";
import ErrorAlert from "src/components/error-alert";
import { formatPhoneNumber, isValidPhoneNumber } from "@/src/utils/format";

const ForgotPasswordPage: React.FC & {
  getLayout?: (page: ReactElement) => ReactElement;
} = () => {
  const { t } = useTranslation();
  const { checkPhoneNumber, sendOtp, verifyOtp, loading, error } = useForgotPassword();
  const [step, setStep] = useState(1);
  const [showPassword, setShowPassword] = useState(false);
  const [timer, setTimer] = useState(300); // 5 minutes in seconds
  const [isShowError, setIsShowError] = useState(false);

  useEffect(() => {
    let countdown: NodeJS.Timeout;
    if (step === 2) {
      countdown = setInterval(() => {
        setTimer((prev) => (prev > 0 ? prev - 1 : 0));
      }, 1000);
    }
    return () => clearInterval(countdown);
  }, [step]);

  const validationSchema = (step: number) => {
    return Yup.object().shape({
      phoneNumber:
        step === 1
          ? Yup.string()
              .required(t("forgotPassword.phoneRequired"))
              .test("is-valid-phone", t("forgotPassword.phoneInvalid"), (value) => {
                if (!value) return false;
                const formattedPhone = formatPhoneNumber(value);
                return isValidPhoneNumber(formattedPhone);
              })
              .matches(/^\d+$/, t("forgotPassword.phoneInvalid"))
          : Yup.string(),
      verificationCode:
        step === 2
          ? Yup.string()
              .required(t("forgotPassword.codeRequired"))
              .matches(/^\d{6}$/, t("forgotPassword.codeInvalid"))
          : Yup.string(),
      newPassword:
        step === 2 ? Yup.string().required(t("forgotPassword.newPasswordRequired")) : Yup.string(),
      confirmPassword:
        step === 2
          ? Yup.string()
              .required(t("forgotPassword.confirmPasswordRequired"))
              .oneOf([Yup.ref("newPassword"), null], t("forgotPassword.passwordsMustMatch"))
          : Yup.string(),
    });
  };

  const formik = useFormik({
    initialValues: {
      phoneNumber: "",
      verificationCode: "",
      newPassword: "",
      confirmPassword: "",
    },
    validationSchema: validationSchema(step),
    onSubmit: async (values) => {
      try {
        if (step === 1) {
          const formattedPhoneNumber = formatPhoneNumber(values.phoneNumber);
          const response = await checkPhoneNumber(formattedPhoneNumber);
          if (response?.data?.isCheck) {
            setIsShowError(false);
            await sendOtp(formattedPhoneNumber);
            setStep(2);
          } else {
            setIsShowError(true);
          }
        } else if (step === 2) {
          try {
            const otpResponse = await verifyOtp({
              typeOtp: "ForgotPass",
              phoneNumber: formatPhoneNumber(values.phoneNumber),
              email: "", // Assuming email is not used in this context
              codeOtp: values.verificationCode,
            });
            if (otpResponse) {
            } else {
              setIsShowError(true);
            }
          } catch (error) {
            setIsShowError(true);
          }
        }
      } catch (error) {
        setIsShowError(true);
      }
    },
  });

  const handlePhoneNumberChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = event.target;
    // Allow only digits
    if (/^\d*$/.test(value)) {
      formik.setFieldValue("phoneNumber", value);
      setIsShowError(false); // Reset error state
    }
  };

  const handleVerificationCodeChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = event.target;
    // Allow only digits
    if (/^\d*$/.test(value)) {
      formik.setFieldValue("verificationCode", value);
      setIsShowError(false); // Reset error state
    }
  };

  return (
    <>
      <Seo title={t("forgotPassword.title")} />
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          width: "100vw",
          height: "100vh",
          overflow: "hidden",
        }}
      >
        <Card
          elevation={16}
          sx={{
            width: "100%",
            maxWidth: "550px",
            height: "auto",
            display: "flex",
            flexDirection: "column",
            justifyContent: "center",
            alignItems: "center",
            mx: "auto",
          }}
        >
          <form noValidate onSubmit={formik.handleSubmit} style={{ width: "100%" }}>
            {step === 1 && (
              <CardContent sx={{ width: "100%" }}>
                <TitleTypography sx={{ mb: 2 }}>{t("forgotPassword.title")}</TitleTypography>
                <TextField
                  label={t("forgotPassword.phoneLabel")}
                  name="phoneNumber"
                  value={formik.values.phoneNumber}
                  onChange={handlePhoneNumberChange}
                  type="tel"
                  inputMode="numeric"
                  inputProps={{ maxLength: 15, pattern: "[0-9]*" }}
                  error={formik.touched.phoneNumber && Boolean(formik.errors.phoneNumber)}
                  helperText={formik.touched.phoneNumber && formik.errors.phoneNumber}
                  fullWidth
                />
                <ErrorAlert error={error} isShowError={isShowError} />
                <Button
                  type="submit"
                  fullWidth
                  size="large"
                  variant="contained"
                  disabled={loading}
                  sx={{ mt: 3, width: "100%" }}
                >
                  {loading ? <CircularProgress size={24} color="inherit" sx={{ mr: 2 }} /> : ""}
                  {t("forgotPassword.submitButton")}
                </Button>
              </CardContent>
            )}
            {step === 2 && (
              <CardContent>
                <Button
                  sx={{
                    mb: 2,
                  }}
                  onClick={() => setStep(1)}
                >
                  {t("forgotPassword.backButton")}
                </Button>
                <TitleTypography sx={{ mb: 2 }}>{t("forgotPassword.resetTitle")}</TitleTypography>
                <TextField
                  label={t("forgotPassword.codeLabel")}
                  name="verificationCode"
                  value={formik.values.verificationCode}
                  onChange={handleVerificationCodeChange}
                  inputProps={{ maxLength: 6, pattern: "[0-9]*" }}
                  error={formik.touched.verificationCode && Boolean(formik.errors.verificationCode)}
                  helperText={formik.touched.verificationCode && formik.errors.verificationCode}
                  fullWidth
                  sx={{ mb: 2 }}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        {Math.floor(timer / 60)}:{timer % 60 < 10 ? "0" : ""}
                        {timer % 60}
                      </InputAdornment>
                    ),
                  }}
                />
                <TextField
                  label={t("forgotPassword.newPasswordLabel")}
                  name="newPassword"
                  type={showPassword ? "text" : "password"}
                  value={formik.values.newPassword}
                  onChange={formik.handleChange}
                  error={formik.touched.newPassword && Boolean(formik.errors.newPassword)}
                  helperText={formik.touched.newPassword && formik.errors.newPassword}
                  fullWidth
                  sx={{ mb: 2 }}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton onClick={() => setShowPassword(!showPassword)}>
                          {showPassword ? <VisibilityOff /> : <Visibility />}
                        </IconButton>
                      </InputAdornment>
                    ),
                  }}
                />
                <TextField
                  label={t("forgotPassword.confirmPasswordLabel")}
                  name="confirmPassword"
                  type={showPassword ? "text" : "password"}
                  value={formik.values.confirmPassword}
                  onChange={formik.handleChange}
                  error={formik.touched.confirmPassword && Boolean(formik.errors.confirmPassword)}
                  helperText={formik.touched.confirmPassword && formik.errors.confirmPassword}
                  fullWidth
                  sx={{ mb: 2 }}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton onClick={() => setShowPassword(!showPassword)}>
                          {showPassword ? <VisibilityOff /> : <Visibility />}
                        </IconButton>
                      </InputAdornment>
                    ),
                  }}
                />
                <ErrorAlert error={error} isShowError={isShowError} />
                <Button
                  type="submit"
                  fullWidth
                  size="large"
                  sx={{ mt: 3 }}
                  variant="contained"
                  disabled={loading}
                >
                  {loading ? <CircularProgress size={24} color="inherit" sx={{ mr: 2 }} /> : ""}
                  {t("forgotPassword.verifyButton")}
                </Button>
              </CardContent>
            )}
            {step === 3 && (
              <CardContent>
                <Typography variant="h5">{t("forgotPassword.successMessage")}</Typography>
                <Button
                  onClick={() => (window.location.href = paths.auth.login)}
                  fullWidth
                  size="large"
                  sx={{ mt: 3 }}
                  variant="contained"
                >
                  {t("forgotPassword.loginButton")}
                </Button>
              </CardContent>
            )}
          </form>
        </Card>
      </Box>
    </>
  );
};

ForgotPasswordPage.getLayout = (page) => <AuthLayout>{page}</AuthLayout>;

export default ForgotPasswordPage;
