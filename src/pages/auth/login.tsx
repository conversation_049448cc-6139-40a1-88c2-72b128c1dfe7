import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>r,
  FormHelperText,
  InputAdornment,
  Link,
  Stack,
  TextField,
  CircularProgress,
} from "@mui/material";
import { useFormik } from "formik";
import { useRouter } from "next/router";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { Seo } from "src/components/seo";
import { useAuth } from "src/hooks/use-auth";
import { useMounted } from "src/hooks/use-mounted";
import { useSearchParams } from "src/hooks/use-search-params";
import { tokens } from "src/locales/tokens";
import { paths } from "src/paths";
import AuthLayout from "src/layouts/auth";
import * as Yup from "yup";
import { RouterLink } from "src/components/router-link";
import { Link as MuiLink } from "@mui/material";
import { useLogin } from "@/src/api/hooks";
import { isValidPhoneN<PERSON>ber } from "@/src/utils/format";
import { logger } from "@/src/utils/logger";
import { Visibility, VisibilityOff } from "@mui/icons-material";
import { IconButton } from "@mui/material";
import ErrorAlert from "src/components/error-alert";
import { formatPhoneNumber } from "@/src/utils/format";
import { TokenService } from "nextjs-api-lib";

type LoginType = "Email" | "Phone";

interface FormValues {
  email: string;
  phoneNumber: string;
  password: string;
  submit: string | null;
}

const LoginPage = () => {
  const router = useRouter();
  const { issuer, signIn } = useAuth();
  const isMounted = useMounted();
  const searchParams = useSearchParams();
  const returnTo = searchParams.get("returnTo");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isShowError, setIsShowError] = useState(true);
  const [loginType, setLoginType] = useState<LoginType>("Phone");
  const { t } = useTranslation();
  const [showPassword, setShowPassword] = useState(false);

  const initialValues: FormValues = {
    email: "",
    phoneNumber: "",
    password: "",
    submit: null,
  };

  const validationSchema = Yup.object({
    email:
      loginType === "Email"
        ? Yup.string()
            .email(t(tokens.auth.emailInvalid))
            .max(255)
            .required(t(tokens.auth.emailRequired))
        : Yup.string(),
    phoneNumber:
      loginType === "Phone"
        ? Yup.string()
            .required(t(tokens.auth.phoneRequired))
            .test("phone-format", t(tokens.auth.phoneInvalid), (value) => {
              if (!value) return false;
              const formattedPhone = formatPhoneNumber(value);
              return isValidPhoneNumber(formattedPhone);
            })
            .matches(/^\d+$/, t(tokens.auth.phoneInvalid))
            .max(15)
        : Yup.string(),
    password: Yup.string().max(255).required(t(tokens.auth.passwordRequired)),
  });

  const handleLoginTypeChange = (type: LoginType) => {
    setIsShowError(false);
    if (type !== loginType) {
      setLoginType(type);
      formik.resetForm();
    }
  };

  const { login, loading, error } = useLogin();

  const formik = useFormik<FormValues>({
    initialValues,
    validationSchema,
    validateOnBlur: false,
    validateOnChange: false,
    onSubmit: async (values, helpers) => {
      console.log("🚀 ~ onSubmit: ~ values:", values);
      try {
        let identifier =
          loginType === "Email" ? values.email : formatPhoneNumber(values.phoneNumber);

        if (loginType === "Phone") {
          const phoneWithoutPrefix = identifier.replace("+84", "");

          const cleanPhone = phoneWithoutPrefix.startsWith("0")
            ? phoneWithoutPrefix.slice(1)
            : phoneWithoutPrefix;

          identifier = `+84${cleanPhone}`;

          if (!isValidPhoneNumber(identifier)) {
            helpers.setFieldError("phoneNumber", t(tokens.auth.phoneInvalid));
            return;
          }
        }

        const response = await login({
          identifier,
          password: values.password,
          provider: loginType,
        });

        if (response?.data?.accessToken) {
          router.push(returnTo || paths.store.index);
        } else {
          helpers.setStatus({ success: false });
          setIsShowError(true);
          helpers.setErrors({
            submit: t(tokens.auth.loginFailed),
          });
        }
      } catch (err) {
        if (isMounted()) {
          const error = err as Error;
          helpers.setStatus({ success: false });
          setIsShowError(true);
          helpers.setErrors({
            submit: error.message || t(tokens.auth.loginFailed),
          });
          helpers.setSubmitting(false);
        }
      }
    },
  });

  const handleClickShowPassword = () => {
    setShowPassword((prev) => !prev);
  };

  return (
    <AuthLayout>
      <Seo title={t(tokens.nav.login)} />
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          minHeight: "100vh",
          overflow: "hidden",
        }}
      >
        <Card
          elevation={16}
          sx={{
            display: "flex",
            flexDirection: {
              xs: "column-reverse",
              md: "row",
            },
            maxWidth: 1000,
            mx: "auto",
            my: 3,
          }}
        >
          <Box
            sx={{
              flex: 1,
              display: "flex",
              flexDirection: "column",
              p: { xs: 3, sm: 4 },
            }}
          >
            <Box sx={{ mb: 3, textAlign: "center" }}>
              <Box
                component="img"
                src="/logo/logoevotech.png"
                alt="Evotech Logo"
                sx={{
                  display: { xs: "block", md: "none" },
                  height: "auto",
                  maxWidth: { xs: "250px", md: "180px" },
                }}
              />
            </Box>

            <CardHeader
              sx={{
                display: { xs: "none", md: "flex" },
                pb: 3,
                px: 0,
                textAlign: "center",
              }}
              title={
                loginType === "Email"
                  ? t(tokens.auth.loginWithEmail)
                  : t(tokens.auth.loginWithPhone)
              }
            />
            <CardContent
              sx={{
                flex: 1,
                px: 0,
                py: 0,
              }}
            >
              <form noValidate onSubmit={formik.handleSubmit}>
                <Stack spacing={3}>
                  {loginType === "Email" ? (
                    <TextField
                      autoFocus
                      fullWidth
                      error={!!(formik.touched.email && formik.errors.email)}
                      helperText={formik.touched.email && formik.errors.email}
                      label={t(tokens.auth.emailLabel)}
                      name="email"
                      onBlur={formik.handleBlur}
                      onChange={formik.handleChange}
                      type="email"
                      value={formik.values.email}
                      sx={{
                        border: "none",
                        "& .MuiInputBase-input": {
                          border: "none",
                        },
                        "& .MuiOutlinedInput-notchedOutline": {
                          display: "block",
                          border: "1px solid #979797 !important",
                        },
                        "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                          borderColor: "#1976d2 !important",
                          borderWidth: "2px !important",
                        },
                      }}
                    />
                  ) : (
                    <TextField
                      autoFocus={loginType === "Phone"}
                      error={!!(formik.touched.phoneNumber && formik.errors.phoneNumber)}
                      fullWidth
                      helperText={formik.touched.phoneNumber && formik.errors.phoneNumber}
                      label={t(tokens.auth.phoneLabel)}
                      name="phoneNumber"
                      onBlur={formik.handleBlur}
                      onChange={formik.handleChange}
                      type="tel"
                      value={formik.values.phoneNumber}
                      inputProps={{ maxLength: 15, pattern: "[0-9]*" }}
                      InputProps={{
                        startAdornment: <InputAdornment position="start">+84</InputAdornment>,
                      }}
                      sx={{
                        border: "none",
                        "& .MuiInputBase-input": {
                          border: "none",
                        },
                        "& .MuiOutlinedInput-notchedOutline": {
                          display: "block",
                          border: "1px solid #979797 !important",
                        },
                        "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                          borderColor: "#1976d2 !important",
                          borderWidth: "2px !important",
                        },
                      }}
                    />
                  )}

                  <TextField
                    error={!!(formik.touched.password && formik.errors.password)}
                    fullWidth
                    helperText={formik.touched.password && formik.errors.password}
                    label={t(tokens.auth.passwordLabel)}
                    name="password"
                    onBlur={formik.handleBlur}
                    onChange={formik.handleChange}
                    type={showPassword ? "text" : "password"}
                    value={formik.values.password}
                    InputProps={{
                      endAdornment: (
                        <InputAdornment position="end">
                          <IconButton onClick={handleClickShowPassword} edge="end">
                            {showPassword ? <VisibilityOff /> : <Visibility />}
                          </IconButton>
                        </InputAdornment>
                      ),
                    }}
                    sx={{
                      border: "none",
                      "& .MuiInputBase-input": {
                        border: "none",
                      },
                      "& .MuiOutlinedInput-notchedOutline": {
                        display: "block",
                        border: "1px solid #979797 !important",
                      },
                      "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                        borderColor: "#1976d2 !important",
                        borderWidth: "2px !important",
                      },
                    }}
                  />
                </Stack>

                {formik.errors.submit && (
                  <FormHelperText error sx={{ mt: 3 }}>
                    {formik.errors.submit as string}
                  </FormHelperText>
                )}

                <ErrorAlert error={error} isShowError={isShowError} />

                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "left",
                    mt: 3,
                  }}
                >
                  <RouterLink
                    href={paths.auth.forgotPassWordEmail}
                    component={MuiLink}
                    underline="hover"
                    variant="subtitle2"
                  >
                    {t(tokens.auth.forgotPassword)}
                  </RouterLink>
                </Box>

                <Button
                  disabled={loading || formik.isSubmitting}
                  fullWidth
                  size="large"
                  sx={{ mt: 3 }}
                  type="submit"
                  variant="contained"
                >
                  {loading ? <CircularProgress size={24} color="inherit" sx={{ mr: 2 }} /> : ""}
                  {t(tokens.auth.loginButton)}
                </Button>

                <Box
                  sx={{
                    mt: { xs: 2.5, sm: 3 },
                    mb: { xs: 2, sm: 3 },
                  }}
                >
                  <Stack
                    direction="row"
                    spacing={{ xs: 1, sm: 2 }}
                    sx={{
                      justifyContent: "center",
                      width: "100%",
                    }}
                  >
                    <Button
                      variant={loginType === "Phone" ? "contained" : "outlined"}
                      onClick={() => handleLoginTypeChange("Phone")}
                      sx={{
                        flex: 1,
                        fontSize: { xs: "0.75rem", sm: "1rem" },
                      }}
                    >
                      {t(tokens.auth.phoneButton)}
                    </Button>
                    <Button
                      variant={loginType === "Email" ? "contained" : "outlined"}
                      onClick={() => handleLoginTypeChange("Email")}
                      sx={{
                        flex: 1,
                        fontSize: { xs: "0.75rem", sm: "1rem" },
                      }}
                    >
                      {t(tokens.auth.emailButton)}
                    </Button>
                  </Stack>
                </Box>
              </form>
            </CardContent>
          </Box>

          <Divider
            orientation="vertical"
            flexItem
            sx={{
              display: {
                xs: "none",
                md: "block",
              },
              my: 3,
            }}
          />

          <Box
            sx={{
              flex: 1,
              display: { xs: "none", md: "flex" },
              alignItems: "center",
              justifyContent: "center",
              p: 3,
              height: "100%",
            }}
          >
            <Box
              sx={{
                position: "relative",
                width: "100%",
                height: "100%",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                overflow: "hidden",
              }}
            >
              <img
                src="/assets/logoEvo.jpg"
                alt="Login illustration"
                style={{
                  maxWidth: "100%",
                  maxHeight: "100%",
                  objectFit: "contain",
                  objectPosition: "center",
                }}
              />
            </Box>
          </Box>
        </Card>
      </Box>
    </AuthLayout>
  );
};
export default LoginPage;
