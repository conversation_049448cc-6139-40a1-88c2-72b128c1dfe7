import { useEffect, useState } from "react";
import { useFormik } from "formik";
import Head from "next/head";
import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import Card from "@mui/material/Card";
import CardContent from "@mui/material/CardContent";
import CardHeader from "@mui/material/CardHeader";
import Divider from "@mui/material/Divider";
import FormHelperText from "@mui/material/FormHelperText";
import Stack from "@mui/material/Stack";
import Typography from "@mui/material/Typography";
import { useRouter } from "next/router";
import { useSnackbar } from "@/src/hooks/use-snackbar";
import { useAppDispatch, useAppSelector } from "@/src/redux/hooks";
import { resetPassword, resetState } from "@/src/redux/slices/passwordResetSlice";
import { PasswordTextField } from "@/src/sections/account/password-text-field";
import {
  resetPasswordSchema,
  resetPasswordInitialValues,
} from "@/src/sections/account/validations/password-validations";
import {
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
} from "@mui/material";
import CheckCircleOutlineIcon from "@mui/icons-material/CheckCircleOutline";

const ResetPassword = () => {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const snackbar = useSnackbar();
  const { token } = router.query;
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [openSuccessDialog, setOpenSuccessDialog] = useState(false);

  useEffect(() => {
    if (!token && router.isReady) {
      router.replace("/forgot-password");
    }
    return () => {
      dispatch(resetState());
    };
  }, [router, token, dispatch]);

  const handleCloseSuccessDialog = () => {
    setOpenSuccessDialog(false);
    router.push("/auth/login");
  };

  const formik = useFormik({
    initialValues: resetPasswordInitialValues,
    validationSchema: resetPasswordSchema,
    onSubmit: async (values, helpers) => {
      if (!token) {
        return;
      }
      try {
        await dispatch(
          resetPassword({
            token: token as string,
            newPassword: values.newPassword,
          })
        ).unwrap();
        setOpenSuccessDialog(true);
      } catch (err: any) {
        const errorMessage =
          typeof err === "string" ? err : err.message || "Đã có lỗi xảy ra. Vui lòng thử lại.";
        helpers.setStatus({ success: false });
        helpers.setErrors({ submit: errorMessage });
      } finally {
        helpers.setSubmitting(false);
      }
    },
  });

  return (
    <>
      <Head>
        <title>Đặt lại mật khẩu</title>
      </Head>
      <Box
        sx={{
          backgroundColor: "background.default",
          display: "flex",
          flexDirection: "column",
          minHeight: "100vh",
        }}
      >
        <Box
          sx={{
            flex: "1 1 auto",
            alignItems: "center",
            display: "flex",
            justifyContent: "center",
            p: 3,
          }}
        >
          <Card sx={{ maxWidth: 550, width: "100%" }}>
            <CardHeader
              title="Đặt lại mật khẩu"
              subheader="Tạo mật khẩu mới cho tài khoản của bạn"
              subheaderTypographyProps={{ sx: { mt: 1 } }}
              sx={{ pb: 0 }}
            />
            <CardContent>
              <form noValidate onSubmit={formik.handleSubmit}>
                <Stack spacing={2}>
                  <PasswordTextField
                    label="Mật khẩu mới"
                    placeholder="Nhập mật khẩu mới"
                    name="newPassword"
                    showPassword={showNewPassword}
                    onTogglePassword={() => setShowNewPassword((prev) => !prev)}
                    onBlur={formik.handleBlur}
                    onChange={formik.handleChange}
                    value={formik.values.newPassword}
                    error={!!(formik.touched.newPassword && formik.errors.newPassword)}
                    helperText={formik.touched.newPassword && formik.errors.newPassword}
                  />

                  <PasswordTextField
                    label="Xác nhận mật khẩu mới"
                    placeholder="Nhập lại mật khẩu mới"
                    name="confirmPassword"
                    showPassword={showConfirmPassword}
                    onTogglePassword={() => setShowConfirmPassword((prev) => !prev)}
                    onBlur={formik.handleBlur}
                    onChange={formik.handleChange}
                    value={formik.values.confirmPassword}
                    error={!!(formik.touched.confirmPassword && formik.errors.confirmPassword)}
                    helperText={formik.touched.confirmPassword && formik.errors.confirmPassword}
                  />

                  <Typography variant="caption" sx={{ color: "text.secondary", pt: 1 }}>
                    Mật khẩu mới phải có ít nhất 8 ký tự, bao gồm chữ hoa, chữ thường, số và ký tự
                    đặc biệt.
                  </Typography>

                  {formik.errors.submit && (
                    <FormHelperText error>{String(formik.errors.submit)}</FormHelperText>
                  )}
                  <Button
                    disabled={formik.isSubmitting}
                    fullWidth
                    size="large"
                    type="submit"
                    variant="contained"
                  >
                    {formik.isSubmitting ? (
                      <CircularProgress size={24} color="inherit" />
                    ) : (
                      "Đặt lại mật khẩu"
                    )}
                  </Button>
                  <Divider />
                  <Button
                    fullWidth
                    size="large"
                    variant="outlined"
                    onClick={() => router.push("/auth/login")}
                  >
                    Quay lại đăng nhập
                  </Button>
                </Stack>
              </form>
            </CardContent>
          </Card>
        </Box>
      </Box>
      <Dialog
        open={openSuccessDialog}
        onClose={handleCloseSuccessDialog}
        PaperProps={{
          sx: {
            borderRadius: 1,
            p: { xs: 1, sm: 2 },
          },
        }}
      >
        <DialogContent sx={{ textAlign: "center", p: { xs: 2, sm: 4 } }}>
          <CheckCircleOutlineIcon sx={{ fontSize: 70, color: "success.main", mb: 2 }} />
          <DialogTitle sx={{ p: 0, mb: 1, fontWeight: "bold" }}>Thành công!</DialogTitle>
          <DialogContentText>Mật khẩu của bạn đã được đặt lại thành công.</DialogContentText>
        </DialogContent>
        <DialogActions sx={{ justifyContent: "center", p: { xs: 2, sm: 3 }, pt: 0 }}>
          <Button onClick={handleCloseSuccessDialog} variant="contained" size="large">
            Quay lại đăng nhập
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default ResetPassword;
