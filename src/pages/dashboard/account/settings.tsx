import { FC, useEffect, useState } from "react";
import { useFormik } from "formik";
import * as Yup from "yup";
import { useTranslation } from "react-i18next";
import { useSnackbar } from "@/src/hooks/use-snackbar";
import TitleTypography from "@/src/components/title-typography/title-typography";
import { tokens } from "@/src/locales/tokens";
import StoreLayout from "src/layouts/store/store-layout";
import { paths } from "src/paths";
import { useSearchParams } from "src/hooks/use-search-params";
import Box from "@mui/material/Box";
import Card from "@mui/material/Card";
import CardContent from "@mui/material/CardContent";
import CardHeader from "@mui/material/CardHeader";
import Divider from "@mui/material/Divider";
import MenuItem from "@mui/material/MenuItem";
import Stack from "@mui/material/Stack";
import TextField from "@mui/material/TextField";
import Typography from "@mui/material/Typography";
import Button from "@mui/material/Button";
import { useRouter } from "next/navigation";
import CircularProgress from "@mui/material/CircularProgress";
import { StorageService } from "nextjs-api-lib";
import DashboardLayout from "@/src/layouts/dashboard";
import { useAppDispatch, useAppSelector } from "@/src/redux/hooks";
import { updateProfile } from "@/src/redux/slices/profileSlice";
import { AccountPasswordChangeDialog } from "@/src/sections/account/account-password-change-dialog";
import { Padding } from "@/src/styles/CommonStyle";

const languageOptions = [
  { value: "en", label: "English" },
  { value: "vi", label: "Tiếng Việt" },
];

const Settings: FC = () => {
  const { t } = useTranslation();
  const snackbar = useSnackbar();
  const searchParams = useSearchParams();
  const returnTo = searchParams.get("returnTo");
  const router = useRouter();

  const dispatch = useAppDispatch();
  const { profile, loading } = useAppSelector((state) => state.profile);

  const [partnerId, setPartnerId] = useState("");
  const [openPasswordDialog, setOpenPasswordDialog] = useState(false);

  useEffect(() => {
    const storedPartnerId = StorageService.get("partnerId") || "";
    if (storedPartnerId) {
      setPartnerId(storedPartnerId as string);
    }
  }, []);

  const formik = useFormik({
    initialValues: {
      firstname: "",
      lastname: "",
      phoneNumber: "",
      email: "",
      notificationEmail: "",
      language: "vi",
    },
    validationSchema: Yup.object({
      firstname: Yup.string().required(t(tokens.settings.firstNameRequired)),
      lastname: Yup.string().required(t(tokens.settings.lastNameRequired)),
      notificationEmail: Yup.string().email(t(tokens.auth.emailInvalid)),
      language: Yup.string().required().oneOf(["en", "vi"]),
    }),
    onSubmit: async (values) => {
      try {
        const updatedProfile = {
          ...values,
          partnerId: partnerId,
          language: values.language.toUpperCase(),
          fullname: `${values.firstname} ${values.lastname}`.trim(),
          updated: new Date().toISOString(),
        };

        await dispatch(updateProfile(updatedProfile));
        snackbar.success(t(tokens.settings.updateSuccess));

        if (returnTo) {
          router.push(returnTo);
        }
      } catch (error) {
        //snackbar.error(t(tokens.settings.updateError));
      }
    },
  });

  useEffect(() => {
    formik.setValues({
      firstname: profile?.firstname || "",
      lastname: profile?.lastname || "",
      phoneNumber: profile?.phoneNumber || "",
      email: profile?.email || "",
      notificationEmail: profile?.notificationEmail || "",
      language: profile?.language?.toLowerCase() || "vi",
    });
  }, [profile]);

  const handleSaveChanges = () => {
    formik.handleSubmit();
  };

  if (loading && !formik.values.firstname) {
    return (
      <DashboardLayout>
        <Box
          sx={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            minHeight: "100vh",
          }}
        >
          <CircularProgress />
        </Box>
      </DashboardLayout>
    );
  }

  return (
    <>
      <DashboardLayout>
        <Typography variant="h5" sx={{ p: Padding }}>
          {t(tokens.settings.title)}
        </Typography>

        <Box
          sx={{
            flexGrow: 1,
            py: 4,
            px: 3,
          }}
        >
          <Stack spacing={4}>
            <Card elevation={16}>
              <Box
                className="p-3"
                sx={{
                  display: "flex",
                  flexDirection: { xs: "column", sm: "row" },
                  justifyContent: "space-between",
                  alignItems: { xs: "stretch", sm: "center" },
                  gap: { xs: 2, sm: 0 },
                  px: 2,
                }}
              >
                <Box sx={{ px: { xs: 0, sm: 3 } }}>
                  <CardHeader
                    title={t(tokens.settings.personalInfo)}
                    sx={{
                      p: 0,
                      "& .MuiCardHeader-content": { m: 0 },
                    }}
                  />
                </Box>
                <Button
                  onClick={() => setOpenPasswordDialog(true)}
                  variant="contained"
                  size="small"
                  sx={{
                    height: 40,
                    minHeight: 0,
                    padding: { xs: "2px 8px", sm: "4px 12px" },
                    alignSelf: { xs: "flex-start", sm: "auto" },
                  }}
                >
                  Đổi mật khẩu
                </Button>
              </Box>

              <CardContent>
                <form onSubmit={formik.handleSubmit}>
                  <Stack spacing={3}>
                    <Stack direction="row" spacing={2}>
                      <TextField
                        label={t(tokens.settings.firstName)}
                        fullWidth
                        name="firstname"
                        value={formik.values.firstname}
                        onChange={formik.handleChange}
                        error={!!(formik.touched.firstname && formik.errors.firstname)}
                        helperText={
                          formik.touched.firstname && t(formik.errors.firstname as string)
                        }
                      />
                      <TextField
                        label={t(tokens.settings.lastName)}
                        fullWidth
                        name="lastname"
                        value={formik.values.lastname}
                        onChange={formik.handleChange}
                        error={!!(formik.touched.lastname && formik.errors.lastname)}
                        helperText={formik.touched.lastname && t(formik.errors.lastname as string)}
                      />
                    </Stack>
                    <Stack direction="row" spacing={2}>
                      <TextField
                        label={t(tokens.settings.phone)}
                        fullWidth
                        name="phoneNumber"
                        value={formik.values.phoneNumber}
                        disabled={true}
                        InputProps={{
                          readOnly: true,
                        }}
                      />
                      <TextField
                        label={t(tokens.settings.email)}
                        fullWidth
                        name="email"
                        value={formik.values.email}
                        disabled={true}
                        InputProps={{
                          readOnly: true,
                        }}
                      />
                    </Stack>
                    <TextField
                      label={t(tokens.settings.notificationEmail)}
                      fullWidth
                      name="notificationEmail"
                      value={formik.values.notificationEmail}
                      onChange={formik.handleChange}
                      error={
                        !!(formik.touched.notificationEmail && formik.errors.notificationEmail)
                      }
                      helperText={
                        formik.touched.notificationEmail &&
                        t(formik.errors.notificationEmail as string)
                      }
                      placeholder="<EMAIL>"
                    />
                  </Stack>
                  <Divider sx={{ my: 4 }} />
                  <Typography variant="h6" gutterBottom sx={{ mb: 2 }}>
                    {t(tokens.settings.languageSection)}
                  </Typography>
                  <TextField
                    select
                    label={t(tokens.settings.language)}
                    fullWidth
                    name="language"
                    value={formik.values.language}
                    onChange={formik.handleChange}
                    error={!!(formik.touched.language && formik.errors.language)}
                    helperText={formik.touched.language && t(formik.errors.language as string)}
                  >
                    {languageOptions.map((option) => (
                      <MenuItem key={option.value} value={option.value}>
                        {option.label}
                      </MenuItem>
                    ))}
                  </TextField>
                  <Box sx={{ mt: 4, textAlign: "right" }}>
                    <Button variant="contained" color="primary" onClick={handleSaveChanges}>
                      {t(tokens.settings.saveButton)}
                    </Button>
                  </Box>
                </form>
              </CardContent>
            </Card>
          </Stack>
        </Box>
      </DashboardLayout>

      <AccountPasswordChangeDialog
        open={openPasswordDialog}
        onClose={() => setOpenPasswordDialog(false)}
      />
    </>
  );
};

export default Settings;
