import React, { useEffect, useState } from "react";
import DashboardLayout from "../../../../layouts/dashboard";
import { useGamification } from "@/src/api/hooks/gamification/use-gamification";
import { useStoreId } from "@/src/hooks/use-store-id";
import {
  Box,
  Tabs,
  Tab,
  Typography,
  Grid,
  Card,
  CardMedia,
  CardContent,
  CircularProgress,
  CardActions,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Select,
  MenuItem,
  FormControl,
  Switch,
  Chip,
  IconButton,
  Menu,
  ListItemIcon,
  ListItemText,
} from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import useSnackbar from "@/src/hooks/use-snackbar";
import { Settings, PowerOff, Edit, MoreVert } from "@mui/icons-material";
import { useRouter } from "next/router";
import { useAppSelector } from "@/src/redux/hooks";
import { paths } from "@/src/paths";
import { useTheme } from "@mui/material/styles";

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

interface NewCampaignData {
  name: string;
  game: string;
  startTime: string;
  endTime: string;
}

interface EditCampaignData extends NewCampaignData {
  id: string;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
      style={{ height: "100%", width: "100%" }}
    >
      {value === index && <Box sx={{ p: 3, height: "100%", width: "100%" }}>{children}</Box>}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `simple-tab-${index}`,
    "aria-controls": `simple-tabpanel-${index}`,
  };
}

const Gamification: React.FC = () => {
  const theme = useTheme();
  const snackbar = useSnackbar();
  const {
    createGameBrand,
    listCampaign,
    createCampaign,
    activeCampaign,
    deactivate,
    activate,
    getGames,
    getInfo,
    updateCampaign,
    loading,
  } = useGamification();
  const storeId = useStoreId();
  const [tabValue, setTabValue] = useState(0);
  const [campaigns, setCampaigns] = useState<any[]>([]);
  const router = useRouter();
  const [isAddCampaignDialogOpen, setIsAddCampaignDialogOpen] = useState(false);
  const [isEditCampaignDialogOpen, setIsEditCampaignDialogOpen] = useState(false);
  const [games, setGames] = useState<any[]>([]);
  const [newCampaignFormData, setNewCampaignFormData] = useState<Partial<NewCampaignData>>({
    name: "",
    game: "",
    startTime: "",
    endTime: "",
  });
  const [editCampaignFormData, setEditCampaignFormData] = useState<Partial<EditCampaignData>>({
    id: "",
    name: "",
    game: "",
    startTime: "",
    endTime: "",
  });
  const [campaignThumbnailFile, setCampaignThumbnailFile] = useState<File | null>(null);
  const [campaignThumbnailPreview, setCampaignThumbnailPreview] = useState<string | null>(null);
  const [campaignThumbnailError, setCampaignThumbnailError] = useState<string>("");
  const [editCampaignThumbnailFile, setEditCampaignThumbnailFile] = useState<File | null>(null);
  const [editCampaignThumbnailPreview, setEditCampaignThumbnailPreview] = useState<string | null>(
    null
  );
  const [editCampaignThumbnailError, setEditCampaignThumbnailError] = useState<string>("");
  const currentShop = useAppSelector((state) => state.shop.currentShop);
  const [showOaIdDialog, setShowOaIdDialog] = useState(false);
  const [showToggleDialog, setShowToggleDialog] = useState(false);
  const [isActived, setIsActived] = useState(false);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedCampaign, setSelectedCampaign] = useState<any>(null);
  const [isSavingEdit, setIsSavingEdit] = useState(false);

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleGoToCampaignDetail = (campaignId: string) => {
    router.push(`/dashboard/app/gamification/campaign/${campaignId}`);
  };

  const handleOpenEditCampaignDialog = (campaign: any) => {
    if (!currentShop?.oaId || currentShop?.oaId === "") {
      setShowOaIdDialog(true);
      return;
    }
    setEditCampaignFormData({
      id: campaign.id,
      name: campaign.name,
      game: campaign.game?.id || "",
      startTime: campaign.startTime ? new Date(campaign.startTime).toISOString().slice(0, 16) : "",
      endTime: campaign.endTime ? new Date(campaign.endTime).toISOString().slice(0, 16) : "",
    });
    setEditCampaignThumbnailPreview(campaign.thumbnailLink);
    setEditCampaignThumbnailFile(null);
    setEditCampaignThumbnailError("");
    setIsEditCampaignDialogOpen(true);
  };

  const handleCloseEditCampaignDialog = () => {
    setIsEditCampaignDialogOpen(false);
    setEditCampaignFormData({
      id: "",
      name: "",
      game: "",
      startTime: "",
      endTime: "",
    });
    setEditCampaignThumbnailFile(null);
    setEditCampaignThumbnailPreview(null);
    setEditCampaignThumbnailError("");
    setIsSavingEdit(false);
  };

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>, campaign: any) => {
    setAnchorEl(event.currentTarget);
    setSelectedCampaign(campaign);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedCampaign(null);
  };

  const handleEditFromMenu = () => {
    if (selectedCampaign) {
      handleOpenEditCampaignDialog(selectedCampaign);
    }
    handleMenuClose();
  };

  const handleSettingsFromMenu = () => {
    if (selectedCampaign) {
      handleGoToCampaignDetail(selectedCampaign.id);
    }
    handleMenuClose();
  };

  const handleToggleAllCampaigns = async () => {
    try {
      if (isActived) {
        // Tắt toàn bộ chiến dịch
        const response = await deactivate(storeId);
        if (response && response.data.result) {
          // snackbar.success("Đã tắt chiến dịch game thành công!");
          setShowToggleDialog(false);

          // Cập nhật trạng thái và clear data
          const newIsActive = await fetchGamificationInfo();
          if (!newIsActive) {
            setCampaigns([]);
            setGames([]);
          }
        } else {
          snackbar.error(response?.data?.message || "Có lỗi xảy ra khi tắt chiến dịch.");
        }
      } else {
        // Bật toàn bộ chiến dịch
        const response = await activate(storeId);
        if (response && response.data.result) {
          // snackbar.success("Đã bật toàn bộ chiến dịch game thành công!");
          setShowToggleDialog(false);

          // Cập nhật trạng thái và load data nếu active
          const newIsActive = await fetchGamificationInfo();
          if (newIsActive) {
            fetchCampaign();
            const gamesResponse = await getGames();
            if (gamesResponse && gamesResponse.data && Array.isArray(gamesResponse.data.result)) {
              setGames(gamesResponse.data.result);
              setNewCampaignFormData((prev) => ({
                ...prev,
                game: gamesResponse.data.result[0]?.id || "",
              }));
            }
          }
        } else {
          snackbar.error(response?.data?.message || "Có lỗi xảy ra khi bật chiến dịch.");
        }
      }
    } catch (error: any) {
      snackbar.error(error?.message || "Có lỗi xảy ra khi thực hiện thao tác.");
    }
  };

  const handleOpenAddCampaignDialog = () => {
    if (!currentShop?.oaId || currentShop?.oaId === "") {
      setShowOaIdDialog(true);
      return;
    }
    setIsAddCampaignDialogOpen(true);
    setNewCampaignFormData((prev) => ({
      ...prev,
      game: games[0]?.id || "",
    }));
  };

  const handleCloseAddCampaignDialog = () => {
    setIsAddCampaignDialogOpen(false);
    setNewCampaignFormData({
      name: "",
      game: games[0]?.id || "",
      startTime: "",
      endTime: "",
    });
    setCampaignThumbnailFile(null);
    setCampaignThumbnailPreview(null);
  };

  const handleAddCampaignFormChange = (
    event: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | { name?: string; value: unknown }
    >
  ) => {
    const { name, value } = event.target;
    setNewCampaignFormData((prev) => ({ ...prev, [name!]: value }));
  };

  const handleCampaignThumbnailChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setCampaignThumbnailFile(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setCampaignThumbnailPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
      setCampaignThumbnailError("");
    } else {
      setCampaignThumbnailFile(null);
      setCampaignThumbnailPreview(null);
    }
  };

  const handleEditCampaignFormChange = (
    event: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | { name?: string; value: unknown }
    >
  ) => {
    const { name, value } = event.target;
    setEditCampaignFormData((prev) => ({ ...prev, [name!]: value }));
  };

  const handleEditCampaignThumbnailChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setEditCampaignThumbnailFile(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setEditCampaignThumbnailPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
      setEditCampaignThumbnailError("");
    } else {
      setEditCampaignThumbnailFile(null);
      setEditCampaignThumbnailPreview(null);
    }
  };

  const handleSaveNewCampaign = async () => {
    if (!newCampaignFormData.name) {
      snackbar.error("Tên chiến dịch không được để trống.");
      return;
    }
    if (!campaignThumbnailFile) {
      setCampaignThumbnailError("Vui lòng chọn ảnh đại diện chiến dịch.");
      return;
    }
    if (!newCampaignFormData.startTime || !newCampaignFormData.endTime) {
      snackbar.error("Thời gian bắt đầu và kết thúc không được để trống.");
      return;
    }
    if (new Date(newCampaignFormData.startTime) >= new Date(newCampaignFormData.endTime)) {
      snackbar.error("Thời gian kết thúc phải sau thời gian bắt đầu.");
      return;
    }
    const formData = new FormData();
    formData.append("shopId", storeId);
    formData.append("name", newCampaignFormData.name || "");
    formData.append("startTime", newCampaignFormData.startTime || "");
    formData.append("endTime", newCampaignFormData.endTime || "");
    formData.append("gameId", newCampaignFormData.game || "");
    formData.append("thumbnail", campaignThumbnailFile);
    try {
      const response = await createCampaign(formData);
      if (response && response.data.result) {
        snackbar.success("Tạo chiến dịch thành công!");
        handleCloseAddCampaignDialog();
        fetchCampaign();
      } else {
        snackbar.error(response?.data?.message || "Có lỗi xảy ra khi tạo chiến dịch.");
      }
    } catch (error: any) {
      snackbar.error(error?.message || "Có lỗi xảy ra khi tạo chiến dịch.");
    }
  };

  const handleSaveEditCampaign = async () => {
    if (!editCampaignFormData.name) {
      snackbar.error("Tên chiến dịch không được để trống.");
      return;
    }
    if (!editCampaignFormData.startTime || !editCampaignFormData.endTime) {
      snackbar.error("Thời gian bắt đầu và kết thúc không được để trống.");
      return;
    }
    if (new Date(editCampaignFormData.startTime) >= new Date(editCampaignFormData.endTime)) {
      snackbar.error("Thời gian kết thúc phải sau thời gian bắt đầu.");
      return;
    }

    setIsSavingEdit(true);
    const formData = new FormData();
    formData.append("campaignId", editCampaignFormData.id || "");
    formData.append("shopId", storeId);
    formData.append("name", editCampaignFormData.name || "");
    formData.append("startTime", editCampaignFormData.startTime || "");
    formData.append("endTime", editCampaignFormData.endTime || "");
    formData.append("gameId", editCampaignFormData.game || "");
    if (editCampaignThumbnailFile) {
      formData.append("thumbnail", editCampaignThumbnailFile);
    }
    try {
      const response = await updateCampaign(formData);
      if (response && response.data.result) {
        snackbar.success("Cập nhật chiến dịch thành công!");
        handleCloseEditCampaignDialog();
        fetchCampaign();
      } else {
        snackbar.error(response?.data?.message || "Có lỗi xảy ra khi cập nhật chiến dịch.");
      }
    } catch (error: any) {
      snackbar.error(error?.message || "Có lỗi xảy ra khi cập nhật chiến dịch.");
    } finally {
      setIsSavingEdit(false);
    }
  };
  const fetchCampaign = async () => {
    const response = await listCampaign(storeId);
    if (response && response.data.result) {
      setCampaigns(response.data.result);
    } else {
      setCampaigns([]);
    }
  };

  const fetchGamificationInfo = async () => {
    try {
      const response = await getInfo(storeId);

      if (response && response.data.result) {
        const isActiveStatus = response.data.result.isActived || false;
        setIsActived(isActiveStatus);
        return isActiveStatus;
      } else {
        setIsActived(false);
        return false;
      }
    } catch (error) {
      console.error("Error fetching gamification info:", error);
      setIsActived(false);
      return false;
    }
  };
  useEffect(() => {
    if (!storeId) return;
    const fetchGamificationData = async () => {
      fetchGamificationInfo();
      await createGameBrand(storeId);

      // Kiểm tra isActive trước
      const isActiveStatus = await fetchGamificationInfo();

      // Chỉ load data khi isActive = true
      if (isActiveStatus) {
        fetchCampaign();
        const gamesResponse = await getGames();
        if (gamesResponse && gamesResponse.data && Array.isArray(gamesResponse.data.result)) {
          setGames(gamesResponse.data.result);
          setNewCampaignFormData((prev) => ({
            ...prev,
            game: gamesResponse.data.result[0]?.id || "",
          }));
        }
      } else {
        // Nếu isActive = false, clear data
        setCampaigns([]);
        setGames([]);
      }
    };

    fetchGamificationData();
  }, [storeId]);

  return (
    <DashboardLayout>
      <Box
        sx={{
          width: "100%",
          height: "100%",
          display: "flex",
          flexDirection: "column",
          background: "linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)",
          minHeight: "100vh",
        }}
      >
        {/* Header Section */}
        <Box
          sx={{
            borderBottom: "1px solid #e0e0e0",
            px: "24px",
            py: "10px",
            position: "relative",
          }}
        >
          <Box sx={{ mb: 2 }}>
            <Box sx={{ display: "flex", justifyContent: "start", alignItems: "center", gap: 1 }}>
              <Typography
                variant="h5"
                sx={{
                  fontWeight: 700,
                  color: "#1a1a1a",
                }}
              >
                Quản lý Game
              </Typography>
              <Switch checked={isActived} onChange={() => setShowToggleDialog(true)} />
            </Box>
            <Typography
              variant="body1"
              sx={{
                color: theme.palette.text.secondary,
                fontSize: "14px",
              }}
            >
              Tạo và quản lý các chiến dịch game tương tác với khách hàng
            </Typography>
          </Box>
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            aria-label="gamification tabs"
            sx={{
              "& .MuiTabs-indicator": {
                backgroundColor: "#1976d2",
                height: 3,
                borderRadius: "3px 3px 0 0",
              },
            }}
          >
            <Tab
              label="Chiến dịch Game"
              {...a11yProps(0)}
              sx={{
                color: "#666",
                fontSize: "16px",
                fontWeight: 600,
                textTransform: "none",
                px: 0,
                mr: 4,
                "&.Mui-selected": {
                  color: "#1976d2",
                  fontWeight: 700,
                },
              }}
            />
          </Tabs>
        </Box>
        <Box sx={{ flex: 1 }}>
          <TabPanel value={tabValue} index={0}>
            {loading && (
              <Box
                sx={{
                  display: "flex",
                  flexDirection: "column",
                  alignItems: "center",
                  justifyContent: "center",
                  height: "400px",
                  background: "white",
                  borderRadius: 3,
                  boxShadow: "0 2px 8px rgba(0, 0, 0, 0.1)",
                }}
              >
                <CircularProgress
                  size={48}
                  sx={{
                    color: "#1976d2",
                    mb: 2,
                  }}
                />
                <Typography
                  variant="h6"
                  sx={{
                    color: "#666",
                    fontWeight: 500,
                  }}
                >
                  Đang tải chiến dịch...
                </Typography>
              </Box>
            )}
            {!loading && !isActived && (
              <Box
                sx={{
                  display: "flex",
                  flexDirection: "column",
                  alignItems: "center",
                  justifyContent: "center",
                  height: "400px",
                  background: "white",
                  borderRadius: 3,
                  boxShadow: "0 2px 8px rgba(0, 0, 0, 0.1)",
                  textAlign: "center",
                  px: 4,
                }}
              >
                <Settings sx={{ fontSize: 64, color: theme.palette.text.secondary, mb: 2 }} />
                <Typography
                  variant="h6"
                  sx={{
                    color: theme.palette.text.secondary,
                    fontWeight: 600,
                    mb: 1,
                  }}
                >
                  Gamification đang tắt
                </Typography>
                <Typography variant="body2">
                  Bật chức năng Gamification để bắt đầu tạo và quản lý các chiến dịch game tương tác
                  với khách hàng.
                </Typography>
              </Box>
            )}
            {!loading && isActived && (
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6} md={4} lg={3}>
                  <Card
                    sx={{
                      height: 400, // Same height as campaign cards
                      display: "flex",
                      flexDirection: "column",
                      alignItems: "center",
                      justifyContent: "center",
                      cursor: "pointer",
                      border: "2px dashed #e0e0e0",
                      borderRadius: 3,
                      background: "linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)",
                      transition: "all 0.3s ease",
                      "&:hover": {
                        border: "2px dashed #1976d2",
                        background: "linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%)",
                        transform: "translateY(-4px)",
                        boxShadow: "0 8px 25px rgba(25, 118, 210, 0.15)",
                      },
                    }}
                    onClick={handleOpenAddCampaignDialog}
                  >
                    <Box
                      sx={{
                        width: 80,
                        height: 80,
                        borderRadius: "50%",
                        background: "linear-gradient(135deg, #1976d2 0%, #42a5f5 100%)",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        mb: 2,
                        boxShadow: "0 4px 15px rgba(25, 118, 210, 0.3)",
                      }}
                    >
                      <AddIcon sx={{ fontSize: 40, color: "white" }} />
                    </Box>
                    <Typography
                      variant="h6"
                      sx={{
                        color: "#1976d2",
                        fontWeight: 600,
                        textAlign: "center",
                        px: 2,
                      }}
                    >
                      Tạo chiến dịch mới
                    </Typography>
                    <Typography
                      variant="body2"
                      sx={{
                        color: "#666",
                        textAlign: "center",
                        mt: 1,
                        px: 2,
                      }}
                    >
                      Bắt đầu tạo game mới cho khách hàng
                    </Typography>
                  </Card>
                </Grid>
                {campaigns.map((campaign: any) => (
                  <Grid item xs={12} sm={6} md={4} lg={3} key={campaign.id}>
                    <Card
                      sx={{
                        position: "relative",
                        display: "flex",
                        flexDirection: "column",
                        height: 400, // Fixed height for all cards
                        borderRadius: 3,
                        border: campaign.active ? "2px solid #1976d2" : "1px solid #e0e0e0",
                        boxShadow: campaign.active
                          ? "0 8px 25px rgba(25, 118, 210, 0.2)"
                          : "0 2px 8px rgba(0, 0, 0, 0.1)",
                        background: campaign.active
                          ? "linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%)"
                          : "#fff",
                        transition: "all 0.3s ease",
                        overflow: "hidden",
                        "&:hover": {
                          transform: "translateY(-6px)",
                          boxShadow: campaign.active
                            ? "0 12px 35px rgba(25, 118, 210, 0.3)"
                            : "0 8px 25px rgba(0, 0, 0, 0.15)",
                        },
                      }}
                    >
                      {campaign.active && (
                        <Box sx={{ position: "absolute", top: 16, right: 16, zIndex: 2 }}>
                          <Chip
                            label="Đang hoạt động"
                            sx={{
                              background: "linear-gradient(135deg, #4caf50 0%, #66bb6a 100%)",
                              color: "white",
                              fontWeight: 600,
                              fontSize: "0.75rem",
                              boxShadow: "0 2px 8px rgba(76, 175, 80, 0.4)",
                              border: "2px solid rgba(255, 255, 255, 0.3)",
                            }}
                            size="small"
                          />
                        </Box>
                      )}
                      <Box sx={{ position: "relative", overflow: "hidden" }}>
                        <CardMedia
                          component="img"
                          image={campaign.thumbnailLink}
                          alt={campaign.name}
                          sx={{
                            width: "100%",
                            height: "180px",
                            objectFit: "cover",
                            transition: "transform 0.3s ease",
                            "&:hover": {
                              transform: "scale(1.05)",
                            },
                          }}
                        />
                        <Box
                          sx={{
                            position: "absolute",
                            bottom: 0,
                            left: 0,
                            right: 0,
                            height: "50%",
                            background: "linear-gradient(transparent, rgba(0, 0, 0, 0.1))",
                            pointerEvents: "none",
                          }}
                        />
                      </Box>
                      <CardContent
                        sx={{
                          display: "flex",
                          justifyContent: "space-between",
                          alignItems: "flex-start",
                          p: 3,
                          flexGrow: 1,
                        }}
                      >
                        <Box sx={{ flex: 1 }}>
                          <Typography
                            variant="h6"
                            sx={{
                              fontWeight: 700,
                              color: "#1a1a1a",
                              mb: 1,
                              lineHeight: 1.3,
                            }}
                          >
                            {campaign.name}
                          </Typography>
                          <Typography
                            variant="body2"
                            sx={{
                              color: "#666",
                              fontSize: "0.875rem",
                            }}
                          >
                            {campaign.game?.name}
                          </Typography>
                        </Box>
                        <IconButton
                          size="small"
                          onClick={(event) => handleMenuClick(event, campaign)}
                          sx={{
                            color: "#666",
                            "&:hover": {
                              color: "#1976d2",
                              background: "rgba(25, 118, 210, 0.04)",
                            },
                          }}
                        >
                          <MoreVert />
                        </IconButton>
                      </CardContent>
                      <CardActions sx={{ p: 3, pt: 0, mt: "auto" }}>
                        <Button
                          variant={campaign.active ? "contained" : "contained"}
                          disabled={campaign.active}
                          onClick={async () => {
                            if (!campaign.active && storeId) {
                              await activeCampaign(storeId, campaign.id);
                              fetchCampaign();
                            }
                          }}
                          sx={{
                            width: "100%",
                            py: 1.5,
                            borderRadius: 2,
                            fontWeight: 600,
                            textTransform: "none",
                            fontSize: "0.95rem",
                            ...(campaign.active
                              ? {
                                  background: "linear-gradient(135deg, #4caf50 0%, #66bb6a 100%)",
                                  color: "white",
                                  boxShadow: "0 4px 15px rgba(76, 175, 80, 0.3)",
                                  "&:disabled": {
                                    background: "linear-gradient(135deg, #4caf50 0%, #66bb6a 100%)",
                                    color: "white",
                                    opacity: 0.8,
                                  },
                                }
                              : {
                                  background: "linear-gradient(135deg, #1976d2 0%, #42a5f5 100%)",
                                  color: "white",
                                  boxShadow: "0 4px 15px rgba(25, 118, 210, 0.3)",
                                  "&:hover": {
                                    background: "linear-gradient(135deg, #1565c0 0%, #1976d2 100%)",
                                    boxShadow: "0 6px 20px rgba(25, 118, 210, 0.4)",
                                    transform: "translateY(-1px)",
                                  },
                                }),
                          }}
                        >
                          {campaign.active ? "✓ Đang hoạt động" : "Kích hoạt chiến dịch"}
                        </Button>
                      </CardActions>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            )}
          </TabPanel>
        </Box>
      </Box>

      {/* Campaign Actions Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "right",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "right",
        }}
      >
        <MenuItem onClick={handleEditFromMenu}>
          <ListItemIcon>
            <Edit fontSize="small" />
          </ListItemIcon>
          <ListItemText>Sửa chiến dịch</ListItemText>
        </MenuItem>
        <MenuItem onClick={handleSettingsFromMenu}>
          <ListItemIcon>
            <Settings fontSize="small" />
          </ListItemIcon>
          <ListItemText>Cài đặt</ListItemText>
        </MenuItem>
      </Menu>

      {/* Add Campaign Dialog */}
      <Dialog
        open={isAddCampaignDialogOpen}
        onClose={handleCloseAddCampaignDialog}
        fullWidth
        maxWidth="sm"
      >
        <DialogTitle>Tạo chiến dịch mới</DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 1 }}>
            <Typography variant="subtitle2" mb={1}>
              Tên chiến dịch <span style={{ color: "red" }}>*</span>
            </Typography>
            <TextField
              name="name"
              value={newCampaignFormData.name || ""}
              onChange={handleAddCampaignFormChange}
              fullWidth
              required
            />
            {/* Thumbnail upload */}
            <Box sx={{ mt: 2, mb: 1 }}>
              <Typography variant="subtitle2" gutterBottom>
                Ảnh đại diện chiến dịch <span style={{ color: "red" }}>*</span>
              </Typography>
              {campaignThumbnailPreview && (
                <Box
                  sx={{
                    mt: 1,
                    mb: 1,
                    border: "1px dashed #ccc",
                    borderRadius: "4px",
                    width: 150,
                    height: 150,
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    overflow: "hidden",
                  }}
                >
                  <img
                    src={campaignThumbnailPreview}
                    alt="Xem trước"
                    style={{ maxWidth: "100%", maxHeight: "100%", objectFit: "contain" }}
                  />
                </Box>
              )}
              <Button variant="outlined" component="label" size="small" sx={{ mr: 1 }}>
                {campaignThumbnailFile ? "Thay đổi ảnh" : "Tải ảnh lên"}
                <input
                  type="file"
                  hidden
                  accept="image/*"
                  onChange={handleCampaignThumbnailChange}
                />
              </Button>
              {campaignThumbnailError && (
                <Typography variant="caption" color="error" sx={{ mt: 1, display: "block" }}>
                  {campaignThumbnailError}
                </Typography>
              )}
            </Box>
            <FormControl fullWidth margin="normal" required>
              <Typography variant="subtitle2" gutterBottom>
                Game <span style={{ color: "red" }}>*</span>
              </Typography>
              <Select
                name="game"
                value={newCampaignFormData.game || ""}
                onChange={(e) => handleAddCampaignFormChange(e as any)}
              >
                {games.map((game) => (
                  <MenuItem key={game.id} value={game.id}>
                    {game.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <Box sx={{ mt: 2, mb: 1 }}>
              <Typography variant="subtitle2" gutterBottom>
                Thời gian bắt đầu <span style={{ color: "red" }}>*</span>
              </Typography>
              <TextField
                name="startTime"
                type="datetime-local"
                value={newCampaignFormData.startTime || ""}
                onChange={handleAddCampaignFormChange}
                fullWidth
                required
                InputLabelProps={{
                  shrink: true,
                }}
              />
            </Box>

            <Box sx={{ mt: 2, mb: 1 }}>
              <Typography variant="subtitle2" gutterBottom>
                Thời gian kết thúc <span style={{ color: "red" }}>*</span>
              </Typography>
              <TextField
                name="endTime"
                type="datetime-local"
                value={newCampaignFormData.endTime || ""}
                onChange={handleAddCampaignFormChange}
                fullWidth
                required
                InputLabelProps={{
                  shrink: true,
                }}
              />
            </Box>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseAddCampaignDialog}>Hủy</Button>
          <Button onClick={handleSaveNewCampaign} color="primary" variant="contained">
            Lưu
          </Button>
        </DialogActions>
      </Dialog>

      {/* Edit Campaign Dialog */}
      <Dialog
        open={isEditCampaignDialogOpen}
        onClose={handleCloseEditCampaignDialog}
        fullWidth
        maxWidth="sm"
      >
        <DialogTitle>Chỉnh sửa chiến dịch</DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 1 }}>
            <Typography variant="subtitle2" mb={1}>
              Tên chiến dịch <span style={{ color: "red" }}>*</span>
            </Typography>
            <TextField
              name="name"
              value={editCampaignFormData.name || ""}
              onChange={handleEditCampaignFormChange}
              fullWidth
              required
            />
            {/* Thumbnail upload */}
            <Box sx={{ mt: 2, mb: 1 }}>
              <Typography variant="subtitle2" gutterBottom>
                Ảnh đại diện chiến dịch
              </Typography>
              {editCampaignThumbnailPreview && (
                <Box
                  sx={{
                    mt: 1,
                    mb: 1,
                    border: "1px dashed #ccc",
                    borderRadius: "4px",
                    width: 150,
                    height: 150,
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    overflow: "hidden",
                  }}
                >
                  <img
                    src={editCampaignThumbnailPreview}
                    alt="Xem trước"
                    style={{ maxWidth: "100%", maxHeight: "100%", objectFit: "contain" }}
                  />
                </Box>
              )}
              <Button variant="outlined" component="label" size="small" sx={{ mr: 1 }}>
                {editCampaignThumbnailFile ? "Thay đổi ảnh" : "Thay đổi ảnh"}
                <input
                  type="file"
                  hidden
                  accept="image/*"
                  onChange={handleEditCampaignThumbnailChange}
                />
              </Button>
              {editCampaignThumbnailError && (
                <Typography variant="caption" color="error" sx={{ mt: 1, display: "block" }}>
                  {editCampaignThumbnailError}
                </Typography>
              )}
            </Box>
            <FormControl fullWidth margin="normal" required>
              <Typography variant="subtitle2" gutterBottom>
                Game <span style={{ color: "red" }}>*</span>
              </Typography>
              <Select
                name="game"
                value={editCampaignFormData.game || ""}
                onChange={(e) => handleEditCampaignFormChange(e as any)}
                disabled
              >
                {games.map((game) => (
                  <MenuItem key={game.id} value={game.id}>
                    {game.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <Box sx={{ mt: 2, mb: 1 }}>
              <Typography variant="subtitle2" gutterBottom>
                Thời gian bắt đầu <span style={{ color: "red" }}>*</span>
              </Typography>
              <TextField
                name="startTime"
                type="datetime-local"
                value={editCampaignFormData.startTime || ""}
                onChange={handleEditCampaignFormChange}
                fullWidth
                required
                InputLabelProps={{
                  shrink: true,
                }}
              />
            </Box>

            <Box sx={{ mt: 2, mb: 1 }}>
              <Typography variant="subtitle2" gutterBottom>
                Thời gian kết thúc <span style={{ color: "red" }}>*</span>
              </Typography>
              <TextField
                name="endTime"
                type="datetime-local"
                value={editCampaignFormData.endTime || ""}
                onChange={handleEditCampaignFormChange}
                fullWidth
                required
                InputLabelProps={{
                  shrink: true,
                }}
              />
            </Box>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseEditCampaignDialog}>Hủy</Button>
          <Button
            onClick={handleSaveEditCampaign}
            color="primary"
            variant="contained"
            disabled={isSavingEdit}
            startIcon={isSavingEdit ? <CircularProgress size={18} color="inherit" /> : null}
          >
            Cập nhật
          </Button>
        </DialogActions>
      </Dialog>

      <Dialog open={showOaIdDialog} onClose={() => setShowOaIdDialog(false)}>
        <DialogTitle>Thiếu thông tin Zalo OAID</DialogTitle>
        <DialogContent>
          <Typography>
            Vui lòng cập nhật thông tin Zalo OAID cho cửa hàng trước khi tạo chiến dịch mới.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowOaIdDialog(false)}>Đóng</Button>
          <Button
            onClick={() => {
              setShowOaIdDialog(false);
              router.push(paths.dashboard.store.generalSettings);
            }}
            color="primary"
            variant="contained"
          >
            Cập nhật ngay
          </Button>
        </DialogActions>
      </Dialog>

      {/* Toggle All Campaigns Dialog */}
      <Dialog
        open={showToggleDialog}
        onClose={() => setShowToggleDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle
          sx={{
            display: "flex",
            alignItems: "center",
            gap: 2,
            color: isActived ? theme.palette.error.main : theme.palette.primary.main,
            fontWeight: 600,
          }}
        >
          {isActived ? (
            <PowerOff sx={{ color: theme.palette.error.main }} />
          ) : (
            <Settings sx={{ color: theme.palette.primary.main }} />
          )}
          {isActived ? "Tắt chiến dịch game" : "Bật chiến dịch game"}
        </DialogTitle>
        <DialogContent>
          {isActived ? (
            <>
              <Typography sx={{ mb: 2 }}>
                Bạn có chắc chắn muốn tắt chiến dịch game không?
              </Typography>
              <Typography variant="body2" sx={{ color: theme.palette.text.secondary }}>
                Hành động này sẽ ngừng hoạt động chiến dịch game đang chạy. Bạn có thể kích hoạt lại
                sau này.
              </Typography>
            </>
          ) : (
            <>
              <Typography sx={{ mb: 2 }}>
                Bạn có chắc chắn muốn bật chiến dịch game không?
              </Typography>
              <Typography variant="body2" sx={{ color: theme.palette.text.secondary }}>
                Hành động này sẽ kích hoạt chiến dịch game có sẵn trong shop.
              </Typography>
            </>
          )}
        </DialogContent>
        <DialogActions sx={{ p: 3, gap: 1 }}>
          <Button
            onClick={() => setShowToggleDialog(false)}
            variant="outlined"
            sx={{
              color: theme.palette.text.secondary,
              "&:hover": {
                borderColor: theme.palette.primary.main,
                color: theme.palette.primary.main,
              },
            }}
          >
            Hủy
          </Button>
          <Button
            onClick={handleToggleAllCampaigns}
            variant="contained"
            sx={{
              backgroundColor: isActived ? theme.palette.error.main : theme.palette.primary.main,
              color: "white",
              fontWeight: 600,
              "&:hover": {
                backgroundColor: isActived ? theme.palette.error.dark : theme.palette.primary.dark,
              },
            }}
          >
            {isActived ? "Tắt" : "Bật"}
          </Button>
        </DialogActions>
      </Dialog>
    </DashboardLayout>
  );
};

export default Gamification;
