import React, { use<PERSON><PERSON>back, useRef, useState } from "react";
import {
  Add,
  CheckBox,
  Delete,
  Edit,
  Search,
  Upload,
  UploadFile,
  NavigateBefore as NavigateBeforeIcon,
  NavigateNext as NavigateNextIcon,
  IosShare,
  FileDownload,
  FileUpload,
  Close,
  PanoramaFishEye,
  CloudUpload,
  Download,
} from "@mui/icons-material";
import {
  Box,
  Button,
  Checkbox,
  CircularProgress,
  debounce,
  FormControl,
  FormControlLabel,
  FormLabel,
  Grid,
  IconButton,
  InputAdornment,
  InputLabel,
  MenuItem,
  Modal,
  Paper,
  Radio,
  RadioGroup,
  Select,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextareaAutosize,
  TextField,
  Tooltip,
  Typography,
} from "@mui/material";
import { DatePicker, DateTimePicker, LocalizationProvider } from "@mui/x-date-pickers";
import * as Yup from "yup";
import { useFormik } from "formik";
import dayjs from "dayjs";
import RichTextEditor from "@/src/components/rich-text-editor";
import { Colors } from "chart.js";
import { useStoreId } from "@/src/hooks/use-store-id";
import { useUserGroup } from "@/src/api/hooks/user-group/use-user-group";
import useSnackbar from "@/src/hooks/use-snackbar";
import { ParamImportExcelUserGroup } from "@/src/api/services/user-group/user-group.service";
import { EXCEL_MAX_SIZE } from "@/src/constants/constant";
// import * as XLSX from 'xlsx';
interface FormValues {
  GroupName: string;
  fileExcel: File | null;
  Description: string;
  submit: string | null;
}

const ModalImportExcel = ({ open, setOpen }) => {
  const { exportTemplateExcelUser, importExcelListUser } = useUserGroup();
  const [localDescription, setLocalDescription] = useState("");
  const [fileName, setFileName] = useState("");
  const [inputKey, setInputKey] = useState(0);
  const [loadingDownloadTemplate, setLoadingDownloadTemplate] = useState(false);
  const [file, setFile] = useState(null);
  const [error, setError] = useState("");
  const fileInputRef = useRef(null);
  const [loading, setLoading] = useState(false);
  const storeId = useStoreId();
  const snackbar = useSnackbar();
  const validationSchema = Yup.object({
    GroupName: Yup.string().required("Tên không được để trống"),
    fileExcel: Yup.mixed().required("Tệp tin không được để trống"),
  });

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.currentTarget.files?.[0] || null;
    validateAndSetFile(selectedFile);
  };

  const validateAndSetFile = (selectedFile) => {
    setError("");

    if (!selectedFile) {
      setFile(null);
      formik.setFieldValue("fileExcel", null);
      return;
    }

    const fileExt = selectedFile.name.split(".").pop().toLowerCase();
    if (fileExt !== "xlsx" && fileExt !== "xls") {
      setError("Chỉ chấp nhận file Excel (.xlsx, .xls)");
      return;
    }

    if (selectedFile.size > EXCEL_MAX_SIZE) {
      setError("Kích thước file không được vượt quá 10MB");
      return;
    }

    setFile(selectedFile);
    formik.setFieldValue("fileExcel", selectedFile);
    setFileName(selectedFile.name);
  };

  const formik = useFormik<FormValues>({
    initialValues: {
      GroupName: "",
      fileExcel: null,
      Description: "",
      submit: null,
    },
    validationSchema,
    validateOnBlur: true,
    validateOnChange: true,
    onSubmit: async (values, helpers) => {
      try {
        const data: ParamImportExcelUserGroup = {
          file: values.fileExcel,
          obj: {
            GroupName: values.GroupName,
            ShopId: storeId,
            Description: values.Description,
          },
        };
        console.log({ data });

        const res = await importExcelListUser(data);
        if (res.status === 200) {
          setOpen(false);
          formik.resetForm();
          setFileName(null);
        }
        console.log({ res });
      } catch (err) {}
    },
  });

  const handleDescriptionChange = (value: string) => {
    setLocalDescription(value);
    formik.setFieldValue("description", value);
  };

  const handleFileReset = () => {
    formik.setFieldValue("fileExcel", null);
    setFileName("");
    setInputKey((prevKey) => prevKey + 1);
  };

  const handleDownloadTemplate = async () => {
    try {
      setLoadingDownloadTemplate(true);

      const response = await exportTemplateExcelUser();

      if (response?.data?.data?.link) {
        const link = document.createElement("a");
        link.href = response.data.data.link;
        link.setAttribute("download", "FileMauNguoiDung.xlsx");
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      } else {
        console.error("Không tìm thấy link để tải file.");
      }
    } catch (error) {
      console.error("Lỗi khi tải template:", error);
      snackbar.error("Tải file mẫu thất bại");
    } finally {
      setLoadingDownloadTemplate(false);
      snackbar.success("Tải file mẫu thành công");
    }
  };
  const handleDragOver = (event) => {
    event.preventDefault();
    event.stopPropagation();
  };

  const handleDrop = (event) => {
    event.preventDefault();
    event.stopPropagation();

    if (event.dataTransfer.files && event.dataTransfer.files.length > 0) {
      validateAndSetFile(event.dataTransfer.files[0]);
    }
  };

  const handleDragAreaClick = () => {
    fileInputRef.current.click();
  };

  const handleCancel = () => {
    formik.resetForm();
    setFile(null);
    setFileName("");
    setError("");
    setOpen(false);
  };

  return (
    <>
      <Modal
        open={open}
        onClose={() => setOpen(false)}
        sx={{
          borderRadius: 8,
          backgroundColor: "transparent",
          margin: "auto",
        }}
      >
        <form onSubmit={formik.handleSubmit}>
          <Box
            sx={{
              position: "absolute",
              top: "50%",
              left: "50%",
              transform: "translate(-50%, -50%)",
              width: "800px",
              backgroundColor: "transparent",
              borderRadius: "8px",
            }}
          >
            <Box
              sx={{
                padding: 3,
                border: "1px solid #ddd",
                borderRadius: 2,
                backgroundColor: "#fff",
              }}
            >
              <Box>
                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    marginBottom: 2,
                    paddingBottom: 1.5,
                    borderBottom: 1,
                    borderBottomColor: "#ebebeb",
                  }}
                >
                  <Typography variant="h6">Tải lên danh sách</Typography>
                  <Button
                    sx={{
                      color: "#aaa",
                      padding: 0,
                      justifyContent: "flex-end",
                      width: 20,
                      height: 20,
                    }}
                    onClick={() => setOpen(false)}
                  >
                    <Close />
                  </Button>
                </Box>
                <Box mb={2}>
                  <Typography variant="subtitle1" gutterBottom>
                    Tải file mẫu
                  </Typography>
                  <Button
                    variant="outlined"
                    color="primary"
                    startIcon={
                      loadingDownloadTemplate ? (
                        <CircularProgress size={16} color="inherit" />
                      ) : (
                        <Download />
                      )
                    }
                    onClick={handleDownloadTemplate}
                    size="small"
                    disabled={loadingDownloadTemplate}
                  >
                    {loadingDownloadTemplate ? "ĐANG TẢI..." : "TẢI FILE MẪU"}
                  </Button>
                </Box>

                <Typography variant="subtitle1" gutterBottom>
                  Tải lên danh sách
                </Typography>

                <Paper
                  variant="outlined"
                  sx={{
                    border: "1px dashed #ccc",
                    borderRadius: 1,
                    p: 3,
                    textAlign: "center",
                    cursor: "pointer",
                    backgroundColor: file ? "#f5f5f5" : "transparent",
                    "&:hover": {
                      backgroundColor: "#f0f7ff",
                    },
                  }}
                  onDragOver={handleDragOver}
                  onDrop={handleDrop}
                  onClick={handleDragAreaClick}
                >
                  <input
                    type="file"
                    accept=".xlsx,.xls"
                    onChange={handleFileChange}
                    style={{ display: "none" }}
                    ref={fileInputRef}
                  />

                  <CloudUpload color="primary" sx={{ fontSize: 48, mb: 1 }} />

                  <Typography variant="body1" gutterBottom>
                    Kéo và thả file vào đây, hoặc click để chọn file
                  </Typography>

                  <Typography variant="body2" color="textSecondary" gutterBottom>
                    Định dạng tệp tải lên: XLSX
                  </Typography>

                  {file && (
                    <Box
                      mt={2}
                      p={1}
                      bgcolor="background.paper"
                      borderRadius={1}
                      position="relative"
                    >
                      <Typography variant="body2">Đã chọn: {file.name}</Typography>
                      <Close
                        sx={{
                          position: "absolute",
                          top: "23%",
                          right: 10,
                          fontSize: 18,
                          cursor: "pointer",
                          color: "#666",
                          "&:hover": {
                            color: "#000",
                          },
                        }}
                        onClick={(e) => {
                          e.stopPropagation();
                          setFile(null);
                          setLoading(false);
                          if (fileInputRef.current) {
                            fileInputRef.current.value = "";
                          }
                        }}
                      />
                    </Box>
                  )}
                </Paper>

                <Box sx={{ marginTop: 2, marginBottom: 2 }}>
                  <Typography sx={{ marginBottom: 0.5, fontSize: 15, fontWeight: 500 }}>
                    Tên nhóm
                  </Typography>
                  <TextField
                    name="GroupName"
                    sx={{ width: "100%", padding: 0 }}
                    onChange={formik.handleChange}
                    value={formik.values.GroupName}
                  >
                    {" "}
                  </TextField>
                  {formik.errors.GroupName && (
                    <Typography sx={{ fontSize: 14, color: "red" }}>
                      {formik.errors.GroupName}
                    </Typography>
                  )}
                </Box>
                <Box>
                  <Typography sx={{ marginBottom: 0.5, fontSize: 15, fontWeight: 500 }}>
                    Mô tả
                  </Typography>
                  <TextField
                    name="Description"
                    value={formik.values.Description}
                    onChange={formik.handleChange}
                    label=""
                    fullWidth
                    multiline
                    rows={5}
                  />
                </Box>
                <Box sx={{ display: "flex", justifyContent: "end", marginTop: 2 }}>
                  <Button variant="outlined" sx={{ marginRight: 2 }} onClick={handleCancel}>
                    Huỷ bỏ
                  </Button>
                  <Button
                    variant="contained"
                    type="submit"
                    disabled={!formik.isValid || formik.isSubmitting}
                  >
                    Thêm nhóm
                  </Button>
                </Box>
              </Box>
            </Box>
          </Box>
        </form>
      </Modal>
    </>
  );
};

export default ModalImportExcel;
