import React, { useEffect, useState } from "react";
import {
  Add,
  Delete,
  NavigateBefore as NavigateBeforeIcon,
  NavigateNext as NavigateNextIcon,
  KeyboardDoubleArrowRight,
  KeyboardDoubleArrowLeft,
  KeyboardArrowDown,
  KeyboardArrowUp,
} from "@mui/icons-material";
import {
  Box,
  Button,
  Checkbox,
  Divider,
  FormControl,
  Grid,
  IconButton,
  InputAdornment,
  InputLabel,
  MenuItem,
  Modal,
  Paper,
  Select,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Typography,
  useTheme,
  useMediaQuery,
  Collapse,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";

import { DatePicker } from "@mui/x-date-pickers";
import * as Yup from "yup";
import { useFormik } from "formik";
import dayjs from "dayjs";
import { useUserGroup } from "@/src/api/hooks/user-group/use-user-group";
import { useStoreId } from "@/src/hooks/use-store-id";
import type {
  FilterRequestDto,
  UserGroupAdvancedSearchDto,
} from "@/src/api/services/user-group/user-group.service";
import { formatNumberTypePrice } from "./modal";
import TruncatedText from "@/src/components/truncated-text/truncated-text";

interface FormValues {
  groupName: string;
  description: string;
  submit: string | null;
}

export interface FilterUserOfUserGroupDto {
  id: string;
  field: string;
  fieldName: string;
  operator: string[];
  valueType: string;
  orderNumber: number;
  createdBy: string;
  createdDate: string;
  modifiedBy: string;
  modifiedDate: string | null;
  isDeleted: boolean;
  deletedAt: string | null;
}

export interface CustomerAdvancedSearchDto {
  avatar: string;
  created: string;
  fullname: string;
  phoneNumber: string;
  shopId: string;
  status: string;
  birthdate: string;
  email: string;
  gender: string;
  userId: string;
  referralCode: string;
}

// Original searchConfig as a fallback
const staticSearchConfig = {
  fields: {
    ten: {
      label: "Tên",
      type: "text",
      operators: [
        { value: "equals", label: "Bằng" },
        { value: "notEquals", label: "Không bằng" },
        { value: "contains", label: "Có chứa" },
        { value: "notContains", label: "Không có chứa" },
        { value: "startsWith", label: "Bắt đầu có chứa" },
        { value: "notStartsWith", label: "Bắt đầu không có chứa" },
        { value: "endsWith", label: "Kết thúc có chứa" },
        { value: "notEndsWith", label: "Kết thúc không có chứa" },
      ],
    },
  },
};

export const mapOperatorToLabel = (operator: string): string => {
  const map: Record<string, string> = {
    Equals: "Bằng",
    NotEquals: "Không bằng",
    Contains: "Chứa",
    NotContains: "Không chứa",
    StartsWith: "Bắt đầu với",
    NotStartsWith: "Không bắt đầu với",
    EndsWith: "Kết thúc với",
    NotEndsWith: "Không kết thúc với",
    GreaterThan: "Lớn hơn",
    GreaterThanOrEqual: "Lớn hơn hoặc bằng",
    LessThan: "Nhỏ hơn",
    LessThanOrEqual: "Nhỏ hơn hoặc bằng",
    Between: "Nằm trong khoảng",
  };

  return map[operator] || operator;
};

const valueTypeToFieldType = {
  String: "text",
  Number: "number",
  boolean: "boolean",
  DateTime: "date",
  select: "select",
};

const getSelectOptions = (fieldName) => {
  const fieldOptionsMap = {
    hangThanhVien: [
      { value: "bronze", label: "Bronze" },
      { value: "silver", label: "Silver" },
      { value: "gold", label: "Gold" },
      { value: "diamond", label: "Diamond" },
    ],
    nhomDoiTac: [
      { value: "ctv", label: "CTV" },
      { value: "daily", label: "Đại lý" },
      { value: "dailyCap1", label: "Đại lý cấp 1" },
      { value: "dailyCap2", label: "Đại lý cấp 2" },
    ],
    nhanNguoiDung: [
      { value: "vip", label: "VIP" },
      { value: "thamKhao", label: "Tham khảo" },
      { value: "tiemNang", label: "Tiềm năng" },
      { value: "khongMuaHang", label: "Không mua hàng" },
    ],
    nguonKhachHang: [
      { value: "facebook", label: "Facebook" },
      { value: "google", label: "Google" },
      { value: "website", label: "Website" },
      { value: "gioiThieu", label: "Giới thiệu" },
    ],
  };

  return fieldOptionsMap[fieldName] || [];
};

const booleanOptions = [
  { value: true, label: "Có" },
  { value: false, label: "Không" },
];

const ModalUpdateGroupUser = ({ open, setOpen, idUpdate }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("md"));
  const isSmallMobile = useMediaQuery(theme.breakpoints.down("sm"));

  const [isOpenModalUploadFile, setIsOpenModalUploadFile] = React.useState(false);
  const [templateMessage, setTemplateMessage] = React.useState("");
  const {
    getListFilterUserGroup,
    filterListUserByAdvancedFilter,
    createUserGroupWithAdvancedSearch,
    updateUserGroupWithAdvancedSearch,
    getListUserGroupById,
  } = useUserGroup();

  const [availableItems, setAvailableItems] = useState<CustomerAdvancedSearchDto[]>([]);
  const [selectedItems, setSelectedItems] = useState<CustomerAdvancedSearchDto[]>([]);
  const [selected, setSelected] = useState<number[]>([]);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const shopId = useStoreId();
  const [listFilterUserOfUserGroup, setListFilterUserOfUserGroup] = useState<
    FilterUserOfUserGroupDto[]
  >([]);
  const [conditions, setConditions] = useState([
    {
      field: "",
      operator: "",
      value: null,
      valueEnd: null,
      logicOperator: "And",
      dateRange: [null, null],
    },
  ]);
  const [searchConfig, setSearchConfig] = useState<any>(staticSearchConfig);

  // Mobile-specific states
  const [showFilters, setShowFilters] = useState(!isMobile);
  const [activeTab, setActiveTab] = useState<"available" | "selected">("available");

  const validationSchema = Yup.object({
    groupName: Yup.string()
      .required("Tên nhóm người dùng không được để trống")
      .max(255, "Tên nhóm người dùng không được vượt quá 255 ký tự"),
  });

  const getDetailUserGroup = async () => {
    const res = await getListUserGroupById(shopId, idUpdate);
    if (res && res?.status === 200) {
      setSelectedItems(res?.data?.details);
      formik.setFieldValue("groupName", res?.data?.groupName);
      formik.setFieldValue("description", res?.data?.description);
      setConditions(
        res?.data?.conditions?.map((condition) => ({
          field: condition.field || "",
          operator: condition.operator || "",
          value: condition.values?.[0] || null,
          valueEnd: condition.values?.[1] || null,
          logicOperator: condition.logicOperator || "And",
          dateRange: condition.field === "Birthdate" ? condition.values : [null, null],
        })) || []
      );
    }
  };

  useEffect(() => {
    getDetailUserGroup();
  }, [shopId, idUpdate, open]);

  const transformApiDataToSearchConfig = (apiData) => {
    if (!apiData || !Array.isArray(apiData) || apiData.length === 0) {
      return staticSearchConfig;
    }

    const fields = {};

    apiData.forEach((item) => {
      const fieldKey = item.field || `field_${item.id}`;

      fields[fieldKey] = {
        label: item.fieldName || fieldKey,
        type: valueTypeToFieldType[item.valueType] || "text",
        operators:
          Array.isArray(item.operator) && item.operator.length > 0
            ? item.operator.map((op) => ({
                value: op,
                label: mapOperatorToLabel(op),
              }))
            : staticSearchConfig.fields.ten.operators,
      };

      if (fields[fieldKey].type === "select") {
        fields[fieldKey].options = getSelectOptions(fieldKey);
      } else if (fields[fieldKey].type === "boolean") {
        fields[fieldKey].options = booleanOptions;
      }
    });

    return { fields };
  };

  const fetchListFilterUserOfUserGroup = async () => {
    try {
      const res = await getListFilterUserGroup();
      if (res?.status === 200) {
        setListFilterUserOfUserGroup(res?.data);
        const newSearchConfig = transformApiDataToSearchConfig(res?.data);
        setSearchConfig(newSearchConfig);
      }
    } catch (error) {}
  };

  useEffect(() => {
    fetchListFilterUserOfUserGroup();
  }, [open]);

  const handleClick = (id: number) => {
    const selectedIndex = selected.indexOf(id);
    let newSelected: number[] = [];

    if (selectedIndex === -1) {
      newSelected = newSelected.concat(selected, id);
    } else if (selectedIndex === 0) {
      newSelected = newSelected.concat(selected.slice(1));
    } else if (selectedIndex === selected.length - 1) {
      newSelected = newSelected.concat(selected.slice(0, -1));
    } else if (selectedIndex > 0) {
      newSelected = newSelected.concat(
        selected.slice(0, selectedIndex),
        selected.slice(selectedIndex + 1)
      );
    }
    setSelected(newSelected);
  };

  const formik = useFormik<FormValues>({
    initialValues: {
      groupName: "",
      description: "",
      submit: null,
    },
    validationSchema,
    validateOnBlur: true,
    validateOnChange: true,
    onSubmit: async (values, helpers) => {
      try {
        const data: UserGroupAdvancedSearchDto = {
          shopId: shopId,
          groupName: values.groupName,
          groupId: idUpdate,
          description: values.description,
          isAuto: false,
          status: "Active",
          conditions: conditions.map((condition, index) => ({
            field: condition.field,
            operator: condition.operator,
            values:
              condition.operator === "Between"
                ? condition.field === "Birthdate"
                  ? condition.dateRange
                  : [condition.value, condition.valueEnd]
                : [condition.value],
            valueType: "String",
            logicOperator: condition.logicOperator,
          })),
          details: selectedItems.map((item) => ({
            source: "Manual",
            userId: item.userId,
            referralCode: item.referralCode,
            fullname: item.fullname,
            email: item.email,
            phoneNumber: item.phoneNumber,
          })),
        };
        const res = await updateUserGroupWithAdvancedSearch(data);
        setOpen(false);
        setAvailableItems([]);
        setSelectedItems([]);
      } catch (err) {}
    },
  });

  const isSelected = (id: number) => selected.indexOf(id) !== -1;

  const [searchResults, setSearchResults] = useState(null);

  // Get field options from searchConfig
  const fieldOptions = Object.keys(searchConfig?.fields).map((key) => ({
    value: key,
    label: searchConfig.fields[key].label,
  }));

  const handleLogicOperatorChange = (index, value) => {
    const updatedConditions = [...conditions];
    updatedConditions[index].logicOperator = value;
    setConditions(updatedConditions);
  };

  const handleFieldChange = (index, value) => {
    const updatedConditions = [...conditions];
    updatedConditions[index].field = value;
    updatedConditions[index].operator = "";
    updatedConditions[index].value = "";
    updatedConditions[index].valueEnd = "";
    setConditions(updatedConditions);
  };

  const handleOperatorChange = (index, value) => {
    const updatedConditions = [...conditions];
    updatedConditions[index].operator = value;
    setConditions(updatedConditions);
  };

  const handleValueChange = (index, value) => {
    const updatedConditions = [...conditions];
    updatedConditions[index].value = value;
    setConditions(updatedConditions);
  };

  const handleValueEndChange = (index, value) => {
    const updatedConditions = [...conditions];
    updatedConditions[index].valueEnd = value;
    setConditions(updatedConditions);
  };

  const addCondition = () => {
    setConditions([
      ...conditions,
      {
        field: "",
        operator: "",
        value: "",
        valueEnd: "",
        logicOperator: "And",
        dateRange: [null, null],
      },
    ]);
  };

  const removeCondition = (index) => {
    if (conditions.length > 1) {
      const updatedConditions = [...conditions];
      updatedConditions.splice(index, 1);
      setConditions(updatedConditions);
    }
  };

  const handleSearch = async () => {
    const query: FilterRequestDto = {
      shopId: shopId,
      groupId: idUpdate,
      conditions: conditions.map((condition) => {
        let values: string[] = [];

        if (condition.operator === "Between") {
          values =
            condition.field === "Birthdate"
              ? condition.dateRange
              : [condition.value, condition.valueEnd];
        } else {
          const phone =
            condition.field === "PhoneNumber"
              ? condition.value.startsWith("+84")
                ? condition.value
                : `+84${condition.value}`
              : condition.value;

          values = [phone];
        }

        return {
          field: condition.field,
          operator: condition.operator,
          values,
          valueType: "String",
          logicOperator: condition.logicOperator,
        };
      }),
    };

    const res = await filterListUserByAdvancedFilter(query);
    if (res?.status === 200) {
      if (Array.isArray(res?.data) && res?.data?.length > 0) {
        setAvailableItems(res?.data);
      }
    }
    setSearchResults(`Đã thực hiện tìm kiếm với ${conditions.length} điều kiện`);
  };

  const renderValueInput = (condition, index) => {
    if (!condition.field) return null;

    const fieldConfig = searchConfig.fields[condition.field];
    if (!fieldConfig) return null;

    const type = fieldConfig.type;
    switch (type) {
      case "text":
        return condition.field === "PhoneNumber" ? (
          <>
            <TextField
              fullWidth
              value={condition.value}
              onChange={(e) => handleValueChange(index, e.target.value)}
              placeholder="Nhập số điện thoại"
              size="small"
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Box
                      display="flex"
                      alignItems="center"
                      sx={{
                        paddingRight: 3,
                        marginTop: "1px",
                        fontWeight: 600,
                        color: "#000",
                      }}
                    >
                      <img
                        src="https://flagcdn.com/w40/vn.png"
                        alt="VN"
                        width={24}
                        style={{ marginRight: 5 }}
                      />
                      +84
                    </Box>
                  </InputAdornment>
                ),
              }}
            />
          </>
        ) : (
          <TextField
            fullWidth
            value={condition.value}
            onChange={(e) => handleValueChange(index, e.target.value)}
            placeholder="Nhập giá trị"
            size="small"
          />
        );

      case "number":
        return (
          <Box sx={{ display: "flex", gap: 1, flexDirection: { xs: "column", sm: "row" } }}>
            <TextField
              fullWidth
              type="text"
              value={condition.value}
              onChange={(e) => {
                const formatted = formatNumberTypePrice(e.target.value);
                handleValueChange(index, formatted);
              }}
              inputProps={{
                inputMode: "numeric",
                pattern: "[0-9]*",
                onKeyDown: (e) => {
                  if (["e", "E", "-", "+"].includes(e.key)) {
                    e.preventDefault();
                  }
                },
              }}
              sx={{ flex: 1 }}
              placeholder="Nhập số"
              size="small"
            />
            {condition.operator === "Between" && (
              <TextField
                fullWidth
                type="text"
                value={condition.valueEnd}
                onChange={(e) => {
                  const formatted = formatNumberTypePrice(e.target.value);
                  handleValueEndChange(index, formatted);
                }}
                inputProps={{
                  inputMode: "numeric",
                  pattern: "[0-9]*",
                  onKeyDown: (e) => {
                    if (["e", "E", "-", "+"].includes(e.key)) {
                      e.preventDefault();
                    }
                  },
                }}
                sx={{ flex: 1 }}
                placeholder="Nhập số"
                size="small"
              />
            )}
          </Box>
        );

      case "boolean":
        return (
          <FormControl fullWidth size="small">
            <Select
              value={condition.value}
              onChange={(e) => handleValueChange(index, e.target.value)}
              displayEmpty
            >
              <MenuItem value="" disabled>
                Chọn giá trị
              </MenuItem>
              {fieldConfig.options?.map((option) => (
                <MenuItem key={option.value.toString()} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        );

      case "select":
        return (
          <FormControl fullWidth size="small">
            <Select
              value={condition.value}
              onChange={(e) => handleValueChange(index, e.target.value)}
              displayEmpty
            >
              <MenuItem value="" disabled>
                Chọn giá trị
              </MenuItem>
              {fieldConfig.options?.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        );

      case "date":
        return (
          <Box sx={{ display: "flex", gap: 1, alignItems: "center", flexWrap: "wrap" }}>
            <DatePicker
              value={condition.dateRange[0] ? dayjs(condition.dateRange[0]) : null}
              onChange={(date) => {
                if (date) {
                  const startDate = dayjs(date);

                  if (startDate.isValid()) {
                    const formattedStartDate = startDate
                      .startOf("day")
                      .format("YYYY-MM-DD HH:mm:ss");
                    const endDate = condition.dateRange[1] ? dayjs(condition.dateRange[1]) : null;

                    if (!endDate || !endDate.isValid() || startDate.isAfter(endDate)) {
                      const formattedEndDate = startDate.endOf("day").format("YYYY-MM-DD HH:mm:ss");

                      setConditions((prev) => {
                        const updatedConditions = [...prev];
                        updatedConditions[index] = {
                          ...updatedConditions[index],
                          dateRange: [formattedStartDate, formattedEndDate],
                          value: startDate.format("DD/MM/YYYY"),
                        };
                        return updatedConditions;
                      });
                    } else {
                      const formattedEndDate = endDate.endOf("day").format("YYYY-MM-DD HH:mm:ss");

                      setConditions((prev) => {
                        const updatedConditions = [...prev];
                        updatedConditions[index] = {
                          ...updatedConditions[index],
                          dateRange: [formattedStartDate, formattedEndDate],
                          value: startDate.format("DD/MM/YYYY"),
                        };
                        return updatedConditions;
                      });
                    }
                  }
                }
              }}
              slotProps={{
                textField: {
                  sx: { width: { xs: "100%", sm: 150 } },
                  InputProps: { sx: { height: 40 } },
                  size: "small",
                },
              }}
            />

            {condition.operator === "Between" && (
              <>
                <span style={{ margin: "0 8px" }}>-</span>
                <DatePicker
                  value={condition.dateRange[1] ? dayjs(condition.dateRange[1]) : null}
                  onChange={(date) => {
                    if (date) {
                      const endDate = dayjs(date);

                      if (endDate.isValid()) {
                        const formattedEndDate = endDate.endOf("day").format("YYYY-MM-DD HH:mm:ss");
                        const startDateValue = condition.dateRange[0];
                        const startDate = startDateValue ? dayjs(startDateValue) : null;

                        if (startDate && startDate.isValid() && !endDate.isBefore(startDate)) {
                          const formattedStartDate = startDate
                            .startOf("day")
                            .format("YYYY-MM-DD HH:mm:ss");

                          setConditions((prev) => {
                            const updatedConditions = [...prev];
                            updatedConditions[index] = {
                              ...updatedConditions[index],
                              dateRange: [formattedStartDate, formattedEndDate],
                              valueEnd: endDate.format("DD/MM/YYYY"),
                            };
                            return updatedConditions;
                          });
                        } else if (!startDate || !startDate.isValid()) {
                          const formattedStartDate = endDate
                            .startOf("day")
                            .format("YYYY-MM-DD HH:mm:ss");

                          setConditions((prev) => {
                            const updatedConditions = [...prev];
                            updatedConditions[index] = {
                              ...updatedConditions[index],
                              dateRange: [formattedStartDate, formattedEndDate],
                              value: endDate.format("DD/MM/YYYY"),
                              valueEnd: endDate.format("DD/MM/YYYY"),
                            };
                            return updatedConditions;
                          });
                        }
                      }
                    }
                  }}
                  slotProps={{
                    textField: {
                      sx: { width: { xs: "100%", sm: 150 } },
                      InputProps: { sx: { height: 40 } },
                      size: "small",
                    },
                  }}
                />
              </>
            )}
          </Box>
        );

      default:
        return null;
    }
  };

  const [selectedAvailableIds, setSelectedAvailableIds] = useState([]);
  const [selectedSelectedIds, setSelectedSelectedIds] = useState([]);

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(Number.parseInt(event.target.value, 10));
    setPage(0);
  };

  const toggleSelectAvailable = (item: CustomerAdvancedSearchDto) => {
    setSelectedAvailableIds((prev) =>
      prev.includes(item.userId) ? prev.filter((id) => id !== item.userId) : [...prev, item.userId]
    );
  };

  const toggleSelectSelected = (item: CustomerAdvancedSearchDto) => {
    setSelectedSelectedIds((prev) =>
      prev.includes(item.userId) ? prev.filter((id) => id !== item.userId) : [...prev, item.userId]
    );
  };

  const handleSelect = (item: CustomerAdvancedSearchDto) => {
    setAvailableItems((prev) => prev.filter((i) => i.userId !== item.userId));
    setSelectedItems((prev) => [...prev, item]);
    setSelectedAvailableIds((prev) => prev.filter((id) => id !== item.userId));
  };

  const handleDeselect = (item) => {
    setSelectedItems((prev) => prev.filter((i) => i.userId !== item.id));
    setAvailableItems((prev) => [...prev, item]);
    setSelectedSelectedIds((prev) => prev.filter((id) => id !== item.id));
  };

  const handleSelectAll = () => {
    if (selectedAvailableIds.length > 0) {
      const itemsToMove = availableItems.filter((item) =>
        selectedAvailableIds.includes(item.userId)
      );
      setSelectedItems((prev) => [...prev, ...itemsToMove]);
      setAvailableItems((prev) =>
        prev.filter((item) => !selectedAvailableIds.includes(item.userId))
      );
      setSelectedAvailableIds([]);
    } else {
      setSelectedItems((prev) => [...prev, ...availableItems]);
      setAvailableItems([]);
    }
  };

  const handleDeselectAll = () => {
    if (selectedSelectedIds.length > 0) {
      const itemsToMove = selectedItems.filter((item) => selectedSelectedIds.includes(item.userId));
      setAvailableItems((prev) => [...prev, ...itemsToMove]);
      setSelectedItems((prev) => prev.filter((item) => !selectedSelectedIds.includes(item.userId)));
      setSelectedSelectedIds([]);
    } else {
      setAvailableItems((prev) => [...prev, ...selectedItems]);
      setSelectedItems([]);
    }
  };

  const handleSelectAllAvailable = (event) => {
    if (event.target.checked) {
      const allItemIds = availableItems.map((item) => item.userId);
      setSelectedAvailableIds(allItemIds);
    } else {
      setSelectedAvailableIds([]);
    }
  };

  const handleSelectAllSelected = (event) => {
    if (event.target.checked) {
      const allItemIds = selectedItems.map((item) => item.userId);
      setSelectedSelectedIds(allItemIds);
    } else {
      setSelectedSelectedIds([]);
    }
  };

  const currentPageAvailableItems = availableItems.slice(
    page * rowsPerPage,
    page * rowsPerPage + rowsPerPage
  );
  const isAllSelectedAvailable =
    currentPageAvailableItems.length > 0 &&
    currentPageAvailableItems.every((item) => selectedAvailableIds.includes(item.userId));

  const currentPageSelectedItems = selectedItems.slice(
    page * rowsPerPage,
    page * rowsPerPage + rowsPerPage
  );
  const isAllSelectedSelected =
    currentPageSelectedItems.length > 0 &&
    currentPageSelectedItems.every((item) => selectedSelectedIds.includes(item.userId));

  // Inline pagination props - single row
  const paginationBoxProps = {
    sx: {
      display: "flex",
      justifyContent: "space-between",
      alignItems: "center",
      px: 2,
      py: 1.5,
      flexWrap: "nowrap",
      gap: 1,
      minWidth: 0,
    },
  };

  return (
    <>
      <Modal
        open={open}
        onClose={() => setOpen(false)}
        sx={{
          display: "flex",
          alignItems: { xs: "flex-start", md: "center" },
          justifyContent: "center",
          pt: { xs: 1, md: 0 },
          pb: { xs: 1, md: 0 },
        }}
      >
        <Box
          sx={{
            background: "#fff",
            width: { xs: "98%", sm: "95%", md: "90%" },
            maxWidth: "1400px",
            p: { xs: 1, sm: 2, md: 3 },
            maxHeight: { xs: "98vh", md: "95vh" },
            overflowY: "auto",
            borderRadius: "8px",
            my: { xs: 1, md: 0 },
            position: "relative",
          }}
        >
          <IconButton
            onClick={() => setOpen(false)}
            sx={{
              position: "absolute",
              top: 8,
              right: 8,
              zIndex: 10,
              display: { xs: "block", sm: "none" },
            }}
          >
            <CloseIcon />
          </IconButton>

          <Box
            sx={{
              padding: { xs: 1, sm: 2, md: 3 },
              border: "1px solid #ddd",
              borderRadius: 2,
            }}
          >
            <form noValidate onSubmit={formik.handleSubmit}>
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                  marginBottom: 2,
                }}
              >
                <Typography
                  sx={{
                    fontSize: { xs: 18, sm: 20 },
                    fontWeight: 600,
                  }}
                >
                  Phân loại khách hàng
                </Typography>
              </Box>

              <Box
                sx={{
                  display: "flex",
                  flexDirection: { xs: "column", lg: "row" },
                  justifyContent: "space-between",
                  alignItems: "start",
                  gap: { xs: 2, lg: 3 },
                }}
              >
                <Box
                  sx={{
                    display: "flex",
                    flexDirection: "column",
                    justifyContent: "space-between",
                    alignItems: "start",
                    width: { xs: "100%", lg: "30%" },
                  }}
                >
                  <Box
                    sx={{
                      display: "flex",
                      flexDirection: "column",
                      justifyContent: "start",
                      width: "100%",
                    }}
                  >
                    <Typography
                      sx={{
                        fontSize: { xs: 14, sm: 16 },
                        fontWeight: 600,
                        marginBottom: 1,
                        marginTop: 1,
                      }}
                    >
                      Tên nhóm
                    </Typography>
                    <TextField
                      name="groupName"
                      fullWidth
                      size={isMobile ? "small" : "medium"}
                      value={formik.values.groupName}
                      onChange={formik.handleChange}
                      error={Boolean(formik.touched.groupName && formik.errors.groupName)}
                      helperText={formik.touched.groupName && formik.errors.groupName}
                    />
                  </Box>

                  <Box
                    sx={{
                      display: "flex",
                      flexDirection: "column",
                      justifyContent: "start",
                      width: "100%",
                      marginTop: 2,
                    }}
                  >
                    <Typography
                      sx={{
                        fontSize: { xs: 14, sm: 16 },
                        fontWeight: 600,
                        marginBottom: 1,
                        marginTop: 1,
                      }}
                    >
                      Giới thiệu
                    </Typography>
                    <TextField
                      name="description"
                      value={formik.values.description}
                      onChange={formik.handleChange}
                      fullWidth
                      multiline
                      rows={isMobile ? 3 : 5}
                      size={isMobile ? "small" : "medium"}
                    />
                  </Box>
                </Box>

                <Box
                  sx={{
                    width: { xs: "100%", lg: "70%" },
                    display: "flex",
                    flexDirection: "column",
                    justifyContent: "space-between",
                    alignItems: "start",
                    maxHeight: { xs: "none", lg: "500px" },
                    overflowY: { xs: "visible", lg: "auto" },
                    "&::-webkit-scrollbar": {
                      width: "6px",
                    },
                    "&::-webkit-scrollbar-track": {
                      background: "transparent",
                    },
                    "&::-webkit-scrollbar-thumb": {
                      background: "#888",
                      borderRadius: "3px",
                      display: "none",
                    },
                    "&:hover::-webkit-scrollbar-thumb": {
                      display: "block",
                    },
                  }}
                >
                  <Box
                    sx={{
                      display: "flex",
                      flexDirection: "column",
                      justifyContent: "start",
                      width: "100%",
                    }}
                  >
                    <Box
                      sx={{
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "space-between",
                        mb: 1,
                      }}
                    >
                      <Typography
                        sx={{
                          fontSize: { xs: 14, sm: 16 },
                          fontWeight: 600,
                          marginTop: 1,
                        }}
                      >
                        Điều kiện lọc
                      </Typography>
                      {isMobile && (
                        <IconButton
                          onClick={() => setShowFilters(!showFilters)}
                          size="small"
                          sx={{ ml: 1 }}
                        >
                          {showFilters ? <KeyboardArrowUp /> : <KeyboardArrowDown />}
                        </IconButton>
                      )}
                    </Box>

                    <Collapse in={!isMobile || (isMobile && showFilters)}>
                      <Box sx={{ border: "1px solid #ccc", borderRadius: 1 }}>
                        <Paper sx={{ p: { xs: 1, sm: 2 } }}>
                          {Array.isArray(conditions) &&
                            conditions.length > 0 &&
                            conditions.map((condition, index) => (
                              <React.Fragment key={index}>
                                {index > 0 && (
                                  <Box
                                    sx={{
                                      my: 1,
                                      display: "flex",
                                      justifyContent: "start",
                                      marginBottom: 2,
                                    }}
                                  >
                                    <FormControl size="small" sx={{ minWidth: 120 }}>
                                      <Select
                                        value={conditions[index - 1].logicOperator}
                                        onChange={(e) =>
                                          handleLogicOperatorChange(index - 1, e.target.value)
                                        }
                                        displayEmpty
                                      >
                                        <MenuItem value="And">Và</MenuItem>
                                        <MenuItem value="Or">Hoặc</MenuItem>
                                      </Select>
                                    </FormControl>
                                  </Box>
                                )}
                                <InputLabel
                                  sx={{
                                    marginBottom: 1,
                                    fontSize: { xs: "0.875rem", sm: "1rem" },
                                  }}
                                >
                                  Điều kiện {index + 1}
                                </InputLabel>
                                <Box sx={{ mb: 2 }}>
                                  <Paper variant="outlined">
                                    <Grid container spacing={2} alignItems="center" sx={{ p: 1 }}>
                                      <Grid item xs={12} sm={6} md={3}>
                                        <FormControl fullWidth size="small">
                                          <Select
                                            value={condition.field}
                                            onChange={(e) =>
                                              handleFieldChange(index, e.target.value)
                                            }
                                            displayEmpty
                                          >
                                            <MenuItem value="" disabled>
                                              Chọn trường dữ liệu
                                            </MenuItem>
                                            {fieldOptions.map((option) => (
                                              <MenuItem key={option.value} value={option.value}>
                                                {option.label}
                                              </MenuItem>
                                            ))}
                                          </Select>
                                        </FormControl>
                                      </Grid>

                                      <Grid item xs={12} sm={6} md={3}>
                                        <FormControl
                                          fullWidth
                                          size="small"
                                          disabled={!condition.field}
                                        >
                                          <Select
                                            value={condition.operator}
                                            onChange={(e) =>
                                              handleOperatorChange(index, e.target.value)
                                            }
                                            displayEmpty
                                          >
                                            <MenuItem value="" disabled>
                                              Chọn loại so sánh
                                            </MenuItem>
                                            {condition.field &&
                                              searchConfig.fields[condition.field].operators.map(
                                                (op) => (
                                                  <MenuItem key={op.value} value={op.value}>
                                                    {mapOperatorToLabel(op.label)}
                                                  </MenuItem>
                                                )
                                              )}
                                          </Select>
                                        </FormControl>
                                      </Grid>

                                      <Grid item xs={12} sm={10} md={5}>
                                        {condition.field &&
                                          condition.operator &&
                                          renderValueInput(condition, index)}
                                      </Grid>

                                      <Grid item xs={12} sm={2} md={1}>
                                        <IconButton
                                          color="error"
                                          onClick={() => removeCondition(index)}
                                          disabled={conditions.length === 1}
                                          size={isMobile ? "small" : "medium"}
                                        >
                                          <Delete />
                                        </IconButton>
                                      </Grid>
                                    </Grid>
                                  </Paper>
                                </Box>
                              </React.Fragment>
                            ))}

                          <Stack direction="row" spacing={2} sx={{ mt: 2 }}>
                            <Button
                              variant="outlined"
                              startIcon={<Add />}
                              onClick={addCondition}
                              size={isMobile ? "small" : "medium"}
                            >
                              Thêm điều kiện
                            </Button>
                          </Stack>
                        </Paper>
                      </Box>
                    </Collapse>
                  </Box>
                </Box>
              </Box>

              <Box
                sx={{
                  display: "flex",
                  justifyContent: { xs: "center", sm: "flex-start" },
                  alignItems: "center",
                  marginTop: 2,
                  marginBottom: 2,
                }}
              >
                <Button
                  onClick={handleSearch}
                  sx={{
                    bgcolor: "#2654FE",
                    color: "#fff",
                    width: { xs: "100%", sm: 120 },
                    maxWidth: 200,
                  }}
                  size={isMobile ? "medium" : "large"}
                >
                  Lọc
                </Button>
              </Box>

              {isMobile ? (
                <Box sx={{ width: "100%" }}>
                  <Box
                    sx={{
                      display: "flex",
                      borderBottom: 1,
                      borderColor: "divider",
                      mb: 2,
                    }}
                  >
                    <Button
                      onClick={() => setActiveTab("available")}
                      sx={{
                        flex: 1,
                        borderBottom: activeTab === "available" ? 2 : 0,
                        borderColor: "primary.main",
                        borderRadius: 0,
                        color: activeTab === "available" ? "primary.main" : "text.secondary",
                      }}
                    >
                      Danh sách khách hàng ({availableItems.length})
                    </Button>
                    <Button
                      onClick={() => setActiveTab("selected")}
                      sx={{
                        flex: 1,
                        borderBottom: activeTab === "selected" ? 2 : 0,
                        borderColor: "primary.main",
                        borderRadius: 0,
                        color: activeTab === "selected" ? "primary.main" : "text.secondary",
                      }}
                    >
                      Đã chọn ({selectedItems.length})
                    </Button>
                  </Box>

                  <Box
                    sx={{
                      display: "flex",
                      justifyContent: "center",
                      gap: 2,
                      mb: 2,
                      p: 1,
                      bgcolor: "grey.50",
                      borderRadius: 1,
                    }}
                  >
                    {activeTab === "available" ? (
                      <>
                        <Button
                          onClick={handleSelectAll}
                          variant="outlined"
                          size="small"
                          startIcon={<KeyboardDoubleArrowRight />}
                          disabled={availableItems.length === 0}
                          sx={{ minWidth: 120 }}
                        >
                          Tất cả
                        </Button>
                        <Button
                          onClick={() => {
                            const selectedItems = availableItems.filter((item) =>
                              selectedAvailableIds.includes(item.userId)
                            );
                            selectedItems.forEach(handleSelect);
                          }}
                          variant="outlined"
                          size="small"
                          startIcon={<NavigateNextIcon />}
                          disabled={selectedAvailableIds.length === 0}
                          sx={{ minWidth: 120 }}
                        >
                          Đã chọn
                        </Button>
                      </>
                    ) : (
                      <>
                        <Button
                          onClick={() => {
                            const itemsToMove = selectedItems.filter((item) =>
                              selectedSelectedIds.includes(item.userId)
                            );
                            itemsToMove.forEach((item) => handleDeselect(item));
                          }}
                          variant="outlined"
                          size="small"
                          startIcon={<NavigateBeforeIcon />}
                          disabled={selectedSelectedIds.length === 0}
                          sx={{ minWidth: 120 }}
                        >
                          Đã chọn
                        </Button>
                        <Button
                          onClick={handleDeselectAll}
                          variant="outlined"
                          size="small"
                          startIcon={<KeyboardDoubleArrowLeft />}
                          disabled={selectedItems.length === 0}
                          sx={{ minWidth: 120 }}
                        >
                          Tất cả
                        </Button>
                      </>
                    )}
                  </Box>

                  {/* Active Tab Content */}
                  <Paper
                    sx={{
                      padding: 2,
                      display: "flex",
                      flexDirection: "column",
                      border: "1px solid #c7c7c7",
                      minHeight: "400px",
                    }}
                  >
                    <Typography variant="h6" sx={{ fontSize: "1rem", mb: 1 }}>
                      {activeTab === "available"
                        ? `Danh sách khách hàng (${availableItems.length})`
                        : `Danh sách khách hàng đã chọn (${selectedItems.length})`}
                    </Typography>
                    <Divider sx={{ mb: 2 }} />

                    <TableContainer sx={{ flex: 1 }}>
                      <Table size="small">
                        <TableHead>
                          <TableRow>
                            <TableCell padding="checkbox">
                              <Checkbox
                                checked={
                                  activeTab === "available"
                                    ? isAllSelectedAvailable
                                    : isAllSelectedSelected
                                }
                                onChange={
                                  activeTab === "available"
                                    ? handleSelectAllAvailable
                                    : handleSelectAllSelected
                                }
                                size="small"
                                indeterminate={
                                  activeTab === "available"
                                    ? currentPageAvailableItems.some((item) =>
                                        selectedAvailableIds.includes(item.userId)
                                      ) && !isAllSelectedAvailable
                                    : currentPageSelectedItems.some((item) =>
                                        selectedSelectedIds.includes(item.userId)
                                      ) && !isAllSelectedSelected
                                }
                              />
                            </TableCell>
                            <TableCell sx={{ fontSize: "0.75rem" }}>Tên khách hàng</TableCell>
                            <TableCell sx={{ fontSize: "0.75rem" }}>Số điện thoại</TableCell>
                            <TableCell sx={{ fontSize: "0.75rem" }}>Thao tác</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {(activeTab === "available" ? availableItems : selectedItems)
                            .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                            .map((item) => (
                              <TableRow key={item.userId}>
                                <TableCell padding="checkbox">
                                  <Checkbox
                                    checked={
                                      activeTab === "available"
                                        ? selectedAvailableIds.includes(item.userId)
                                        : selectedSelectedIds.includes(item.userId)
                                    }
                                    onChange={() =>
                                      activeTab === "available"
                                        ? toggleSelectAvailable(item)
                                        : toggleSelectSelected(item)
                                    }
                                    size="small"
                                  />
                                </TableCell>
                                <TableCell sx={{ fontSize: "0.75rem", wordBreak: "break-word" }}>
                                  {item.fullname}
                                </TableCell>
                                <TableCell sx={{ fontSize: "0.75rem" }}>
                                  {item.phoneNumber}
                                </TableCell>
                                <TableCell sx={{ fontSize: "0.75rem" }}>
                                  {activeTab === "available" ? (
                                    <Button
                                      size="small"
                                      onClick={() => handleSelect(item)}
                                      startIcon={<Add />}
                                      sx={{ minWidth: 80 }}
                                    >
                                      Chọn
                                    </Button>
                                  ) : (
                                    <Button
                                      size="small"
                                      onClick={() => handleDeselect(item)}
                                      startIcon={<Delete />}
                                      color="error"
                                      sx={{ minWidth: 80 }}
                                    >
                                      Bỏ
                                    </Button>
                                  )}
                                </TableCell>
                              </TableRow>
                            ))}
                        </TableBody>
                      </Table>
                    </TableContainer>

                    {/* Mobile Pagination - Single Row */}
                    <Box {...paginationBoxProps}>
                      <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                        <Typography variant="body2" sx={{ fontSize: "0.75rem" }}>
                          Số dòng mỗi trang:
                        </Typography>
                        <Select
                          value={rowsPerPage}
                          onChange={handleChangeRowsPerPage}
                          size="small"
                          sx={{
                            minWidth: 60,
                            "& .MuiOutlinedInput-notchedOutline": { border: "none" },
                            "& .MuiSelect-select": { padding: "4px 8px", fontSize: "0.75rem" },
                          }}
                        >
                          <MenuItem value={10}>10</MenuItem>
                          <MenuItem value={25}>25</MenuItem>
                          <MenuItem value={50}>50</MenuItem>
                        </Select>
                        <Typography variant="body2" sx={{ fontSize: "0.75rem" }}>
                          {activeTab === "available"
                            ? `${availableItems.length > 0 ? page * rowsPerPage + 1 : 0}–${Math.min(
                                (page + 1) * rowsPerPage,
                                availableItems.length
                              )} của ${availableItems.length}`
                            : `${selectedItems.length > 0 ? page * rowsPerPage + 1 : 0}–${Math.min(
                                (page + 1) * rowsPerPage,
                                selectedItems.length
                              )} của ${selectedItems.length}`}
                        </Typography>
                      </Box>
                      <Box sx={{ display: "flex", alignItems: "center" }}>
                        <IconButton
                          disabled={page === 0}
                          onClick={() => handleChangePage(null, page - 1)}
                          size="small"
                        >
                          <NavigateBeforeIcon />
                        </IconButton>
                        <IconButton
                          disabled={
                            page >=
                            Math.ceil(
                              (activeTab === "available" ? availableItems : selectedItems).length /
                                rowsPerPage
                            ) -
                              1
                          }
                          onClick={() => handleChangePage(null, page + 1)}
                          size="small"
                        >
                          <NavigateNextIcon />
                        </IconButton>
                      </Box>
                    </Box>
                  </Paper>
                </Box>
              ) : (
                // Desktop: Side-by-side Layout
                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "space-between",
                    width: "100%",
                    position: "relative",
                    gap: 2,
                  }}
                >
                  <Paper
                    sx={{
                      width: "45%",
                      padding: 2,
                      display: "flex",
                      flexDirection: "column",
                      justifyContent: "space-between",
                      border: "1px solid #c7c7c7",
                      minHeight: "400px",
                    }}
                  >
                    <Box>
                      <Typography variant="h6">Danh sách khách hàng</Typography>
                      <Divider sx={{ mb: 2 }} />

                      <TableContainer>
                        <Table size="small">
                          <TableHead>
                            <TableRow>
                              <TableCell padding="checkbox">
                                <Checkbox
                                  checked={isAllSelectedAvailable}
                                  onChange={handleSelectAllAvailable}
                                  size="small"
                                  indeterminate={
                                    currentPageAvailableItems.some((item) =>
                                      selectedAvailableIds.includes(item.userId)
                                    ) && !isAllSelectedAvailable
                                  }
                                />
                              </TableCell>
                              <TableCell>Tên khách hàng</TableCell>
                              <TableCell>Số điện thoại</TableCell>
                              <TableCell>Email</TableCell>
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            {availableItems
                              .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                              .map((item) => (
                                <TableRow key={item.userId}>
                                  <TableCell padding="checkbox">
                                    <Checkbox
                                      checked={selectedAvailableIds.includes(item.userId)}
                                      onChange={() => toggleSelectAvailable(item)}
                                      size="small"
                                    />
                                  </TableCell>
                                  <TableCell>
                                    <TruncatedText
                                      text={item.fullname}
                                      typographyProps={{ width: 150, fontSize: 14 }}
                                    />
                                  </TableCell>
                                  <TableCell>{item.phoneNumber}</TableCell>
                                  <TableCell>{item.email}</TableCell>
                                </TableRow>
                              ))}
                          </TableBody>
                        </Table>
                      </TableContainer>
                    </Box>

                    <Box {...paginationBoxProps}>
                      <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                        <Typography variant="body2">Số dòng mỗi trang:</Typography>
                        <Select
                          value={rowsPerPage}
                          onChange={handleChangeRowsPerPage}
                          size="small"
                          sx={{
                            minWidth: 60,
                            "& .MuiOutlinedInput-notchedOutline": { border: "none" },
                            "& .MuiSelect-select": { padding: "4px 8px" },
                          }}
                        >
                          <MenuItem value={10}>10</MenuItem>
                          <MenuItem value={25}>25</MenuItem>
                          <MenuItem value={50}>50</MenuItem>
                        </Select>
                        <Typography variant="body2">
                          {`${availableItems.length > 0 ? page * rowsPerPage + 1 : 0}–${Math.min(
                            (page + 1) * rowsPerPage,
                            availableItems.length
                          )} của ${availableItems.length}`}
                        </Typography>
                      </Box>
                      <Box sx={{ display: "flex", alignItems: "center" }}>
                        <IconButton
                          disabled={page === 0}
                          onClick={() => handleChangePage(null, page - 1)}
                        >
                          <NavigateBeforeIcon />
                        </IconButton>
                        <IconButton
                          disabled={page >= Math.ceil(availableItems.length / rowsPerPage) - 1}
                          onClick={() => handleChangePage(null, page + 1)}
                        >
                          <NavigateNextIcon />
                        </IconButton>
                      </Box>
                    </Box>
                  </Paper>

                  <Box
                    sx={{
                      display: "flex",
                      flexDirection: "column",
                      alignItems: "center",
                      justifyContent: "center",
                    }}
                  >
                    <Button
                      onClick={handleSelectAll}
                      variant="outlined"
                      sx={{ marginBottom: 1 }}
                      title={selectedAvailableIds.length > 0 ? "Move selected" : "Move all"}
                      disabled={selectedAvailableIds.length === 0}
                    >
                      <KeyboardDoubleArrowRight />
                    </Button>
                    <Button
                      onClick={handleDeselectAll}
                      variant="outlined"
                      title={selectedSelectedIds.length > 0 ? "Move selected" : "Move all"}
                      disabled={selectedSelectedIds.length === 0}
                    >
                      <KeyboardDoubleArrowLeft />
                    </Button>
                  </Box>

                  <Paper
                    sx={{
                      width: "45%",
                      padding: 2,
                      display: "flex",
                      flexDirection: "column",
                      justifyContent: "space-between",
                      border: "1px solid #c7c7c7",
                      minHeight: "400px",
                    }}
                  >
                    <Box>
                      <Typography variant="h6">Danh sách khách hàng đã chọn</Typography>
                      <Divider sx={{ mb: 2 }} />

                      <TableContainer>
                        <Table size="small">
                          <TableHead>
                            <TableRow>
                              <TableCell padding="checkbox">
                                <Checkbox
                                  checked={isAllSelectedSelected}
                                  onChange={handleSelectAllSelected}
                                  size="small"
                                  indeterminate={
                                    currentPageSelectedItems.some((item) =>
                                      selectedSelectedIds.includes(item.userId)
                                    ) && !isAllSelectedSelected
                                  }
                                />
                              </TableCell>
                              <TableCell>Tên khách hàng</TableCell>
                              <TableCell>Số điện thoại</TableCell>
                              <TableCell>Email</TableCell>
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            {selectedItems
                              .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                              .map((item) => (
                                <TableRow key={item.userId}>
                                  <TableCell padding="checkbox">
                                    <Checkbox
                                      checked={selectedSelectedIds.includes(item.userId)}
                                      onChange={() => toggleSelectSelected(item)}
                                      size="small"
                                    />
                                  </TableCell>
                                  <TableCell>
                                    <TruncatedText
                                      text={item.fullname}
                                      typographyProps={{ width: 150, fontSize: 14 }}
                                    />
                                  </TableCell>
                                  <TableCell>{item.phoneNumber}</TableCell>
                                  <TableCell>{item.email}</TableCell>
                                </TableRow>
                              ))}
                          </TableBody>
                        </Table>
                      </TableContainer>
                    </Box>

                    <Box {...paginationBoxProps}>
                      <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                        <Typography variant="body2">Số dòng mỗi trang:</Typography>
                        <Select
                          value={rowsPerPage}
                          onChange={handleChangeRowsPerPage}
                          size="small"
                          sx={{
                            minWidth: 60,
                            "& .MuiOutlinedInput-notchedOutline": { border: "none" },
                            "& .MuiSelect-select": { padding: "4px 8px" },
                          }}
                        >
                          <MenuItem value={10}>10</MenuItem>
                          <MenuItem value={25}>25</MenuItem>
                          <MenuItem value={50}>50</MenuItem>
                        </Select>
                        <Typography variant="body2">
                          {`${selectedItems.length > 0 ? page * rowsPerPage + 1 : 0}–${Math.min(
                            (page + 1) * rowsPerPage,
                            selectedItems.length
                          )} của ${selectedItems.length}`}
                        </Typography>
                      </Box>
                      <Box sx={{ display: "flex", alignItems: "center" }}>
                        <IconButton
                          disabled={page === 0}
                          onClick={() => handleChangePage(null, page - 1)}
                        >
                          <NavigateBeforeIcon />
                        </IconButton>
                        <IconButton
                          disabled={page >= Math.ceil(selectedItems.length / rowsPerPage) - 1}
                          onClick={() => handleChangePage(null, page + 1)}
                        >
                          <NavigateNextIcon />
                        </IconButton>
                      </Box>
                    </Box>
                  </Paper>
                </Box>
              )}

              <Box
                sx={{
                  paddingBottom: 1,
                  marginTop: 2,
                  display: "flex",
                  justifyContent: { xs: "center", sm: "end" },
                  borderTop: 1,
                  borderColor: "#ebebeb",
                  gap: 2,
                  flexDirection: { xs: "row", sm: "row" },
                }}
              >
                <Button
                  variant="outlined"
                  onClick={() => setOpen(false)}
                  fullWidth={false}
                  size={isMobile ? "large" : "medium"}
                  sx={{
                    width: { xs: "50%", sm: "auto" },
                  }}
                >
                  Huỷ bỏ
                </Button>
                <Button
                  type="submit"
                  variant="contained"
                  fullWidth={false}
                  size={isMobile ? "large" : "medium"}
                  sx={{
                    width: { xs: "50%", sm: "auto" },
                  }}
                >
                  Xác nhận
                </Button>
              </Box>
            </form>
          </Box>
        </Box>
      </Modal>
    </>
  );
};

export default ModalUpdateGroupUser;
