import React, { useState } from 'react';
import { Box, Tab, Tabs, Typography, Button, Avatar, IconButton, useTheme } from '@mui/material';
import DashboardLayout from '../../../layouts/dashboard';
import AccountCircleIcon from '@mui/icons-material/AccountCircle';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import { useRouter } from 'next/router';
import Grid from '@mui/system/Grid';
import { paths } from '@/src/paths';

const tabs = [
  { label: 'Lịch sử thu nhập', index: 0 },
  { label: 'Lịch sử chi tiêu', index: 1 }
];

const tabPanels = [
  {
    index: 0,
    rows: [{ time: '2024-11-28 14:00', value: '+10', type: 'Mua sắm', detail: 'Cộng điểm đơn hàng #1234' }]
  },
  {
    index: 1,
    rows: [{ time: '2024-11-28 14:00', value: '-999999', type: 'Điều chỉnh', detail: 'Trừ điểm đơn hàng #1234' }]
  }
];

const userInfo = {
  name: 'OVNleaderstar',
  phone: '*********',
  points: [
    { label: 'Tổng điểm đã tích lũy', value: '100' },
    { label: 'Tổng điểm hiện có', value: '90' },
    { label: 'Điểm đã chi tiêu', value: '10' },
    { label: 'Doanh thu', value: '1.000.000' }
  ]
};

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel({ children, value, index, ...other }: TabPanelProps) {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const Header = ({ handleBackClick, theme }) => (
  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
      <IconButton onClick={handleBackClick}>
        <ArrowBackIcon />
      </IconButton>
      <Typography variant="h5">Chi tiết điểm người dùng</Typography>
    </Box>
    <Button
      variant="contained"
      sx={{ bgcolor: theme.palette.primary.main, '&:hover': { bgcolor: theme.palette.primary.dark } }}
    >
      Điều chỉnh điểm
    </Button>
  </Box>
);

const UserInfo = ({ userInfo, theme }) => (
  <Box
    sx={{
      mb: 4,
      p: 3,
      bgcolor: theme.palette.background.paper,
      borderRadius: 3,
      border: `1px solid ${theme.palette.divider}`
    }}
  >
    <Grid container spacing={2}>
      <Grid size={{ xs: 12, md: 6 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Avatar sx={{ width: 60, height: 60, color: theme.palette.text.primary }}>
            <AccountCircleIcon sx={{ width: '100%', height: '100%', color: theme.palette.text.secondary }} />
          </Avatar>
          <Box>
            <Typography variant="h6">{userInfo.name}</Typography>
            <Typography color="text.secondary">{userInfo.phone}</Typography>
          </Box>
        </Box>
      </Grid>
      <Grid size={{ xs: 12, md: 6 }}>
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1, textAlign: 'left' }}>
          {userInfo.points.map((item, index) => (
            <Box key={index} sx={{ display: 'flex', justifyContent: 'space-between' }}>
              <Typography flex={1}>{item.label}</Typography>
              <Typography fontWeight="bold" sx={{ flex: 0.3, textAlign: 'left' }}>
                {item.value}
              </Typography>
            </Box>
          ))}
        </Box>
      </Grid>
    </Grid>
  </Box>
);

const TabsSection = ({ tabValue, handleTabChange, theme }) => (
  <Box
    sx={{
      width: '100%',
      border: `1px solid ${theme.palette.divider}`,
      borderRadius: 3,
      flexGrow: 1,
      display: 'flex',
      flexDirection: 'column'
    }}
  >
    <Tabs
      value={tabValue}
      onChange={handleTabChange}
      scrollButtons="auto"
      sx={{ borderBottom: `1px solid ${theme.palette.divider}` }}
    >
      {tabs.map((tab) => (
        <Tab
          key={tab.index}
          label={tab.label}
          sx={{
            fontSize: { xs: '0.8rem', sm: '0.9rem', md: '1rem' },
            minWidth: { xs: 'auto', md: 160 },
            padding: { xs: '6px 12px', md: '12px 16px' },
            color: theme.palette.text.secondary,
            '&.Mui-selected': { color: theme.palette.text.primary, fontWeight: 'bold' }
          }}
        />
      ))}
    </Tabs>
    {tabPanels.map((tab) => (
      <TabPanel key={tab.index} value={tabValue} index={tab.index}>
        <Box sx={{ mt: 2 }}>
          <Grid container sx={{ p: 2, bgcolor: theme.palette.action.hover }}>
            {['Thời gian', 'Giá trị', 'Loại giao dịch', 'Chi tiết'].map((header, index) => (
              <Grid key={index} size={{ xs: 3 }}>
                <Typography fontWeight="medium">{header}</Typography>
              </Grid>
            ))}
          </Grid>
          {tab.rows.map((row, index) => (
            <Grid key={index} container sx={{ p: 2, borderBottom: `1px solid ${theme.palette.divider}` }}>
              <Grid size={{ xs: 3 }}>
                <Typography>{row.time}</Typography>
              </Grid>
              <Grid size={{ xs: 3 }}>
                <Typography>{row.value}</Typography>
              </Grid>
              <Grid size={{ xs: 3 }}>
                <Typography>{row.type}</Typography>
              </Grid>
              <Grid size={{ xs: 3 }}>
                <Typography>{row.detail}</Typography>
              </Grid>
            </Grid>
          ))}
        </Box>
      </TabPanel>
    ))}
  </Box>
);

const PointCustomer = () => {
  const [tabValue, setTabValue] = useState(0);
  const router = useRouter();
  const theme = useTheme();

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => setTabValue(newValue);
  const handleBackClick = () => router.push(paths.customers.setMembershipPolicy);

  return (
    <DashboardLayout>
      <Box sx={{ p: 3 }}>
        <Header handleBackClick={handleBackClick} theme={theme} />
        <UserInfo userInfo={userInfo} theme={theme} />
        <TabsSection tabValue={tabValue} handleTabChange={handleTabChange} theme={theme} />
      </Box>
    </DashboardLayout>
  );
};

export default PointCustomer;
