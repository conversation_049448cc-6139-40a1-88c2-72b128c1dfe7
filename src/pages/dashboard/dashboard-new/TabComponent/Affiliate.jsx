import React, { useState } from 'react';
import { Box, Tabs, Tab, Typography, Card, Grid, Button, Stack } from '@mui/material';
import Daily from './ComponentRank/Daily';
import Monthly from './ComponentRank/Monthly';
import Weekly from './ComponentRank/Weekly';
import { LocalizationProvider, DateRangePicker } from '@mui/x-date-pickers-pro';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';

const Ranking = () => {
  const [tabValue, setTabValue] = useState(0);
  const [open, setOpen] = useState(false);

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };
  const statisticsData = [
    { title: 'Doanh thu', value: '100,000,000 đ' },
    { title: 'Tổng đơn hàng đối tác', value: '1245' },
    { title: 'Hoa hồng đã duyệt', value: '20,000,000 đ' },
    { title: 'Đơn hàng thành công', value: '10000' },
    { title: 'Hoa hồng chờ duyệt', value: '10,000,000 đ' },
    { title: 'Tổng số đối tác', value: '2000' },
  ];

  return (
    <Box sx={{ p: 3, borderRadius: '20px', marginTop: '-15px', background: '#f5f5f5' }}>
      <Stack
        flexDirection={'row'}
        alignItems={'stretch'}
        gap={'20px'}
        sx={{
          '@media(max-width: 1024px)': {
            flexDirection: 'column',
          },
        }}
      >
        <Box>
          <Typography
            sx={{
              color: '#000000',
              fontWeight: '700',
              fontSize: '20px',
              flexDirection: 'column',
              justifyContent: 'space-between',
            }}
          >
            Bảng xếp hạng
          </Typography>
          <Box
            sx={{
              background: '#fff',
              borderRadius: '20px',
              padding: '15px',
              marginTop: '25px',
              minHeight: '670px',
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'space-between',
            }}
          >
            <Tabs
              value={tabValue}
              onChange={handleTabChange}
              sx={{
                mb: 3,
                width: '100%',
                display: 'flex',
                alignItems: 'center',
                '& .MuiTabs-indicator': {
                  backgroundColor: '#1976d2',
                },
              }}
            >
              <Tab
                label="Hằng ngày"
                disableRipple
                sx={{
                  textTransform: 'none',
                  fontSize: '20px',
                  fontWeight: '700',
                  color: '#fff',
                  fontWeight: tabValue === 0 ? 'bold' : 'bold',
                  color: tabValue === 0 ? '#fff !important' : '#000 !important',
                  background: tabValue === 0 ? '#2654FE' : '#fff',
                  borderRadius: tabValue === 0 ? '10px 10px 0 0' : '0',
                  width: '33.333%',
                  '&.Mui-selected': {
                    borderBottom: 'none',
                  },
                  '@media(max-width: 600px)': {
                    fontSize: '16px',
                    width: '30%',
                  },
                  '@media(max-width: 600px)': {
                    fontSize: '14px',
                    width: '30%',
                  },
                }}
              />
              <Tab
                label="Hằng tuần"
                disableRipple
                sx={{
                  textTransform: 'none',
                  fontSize: '20px',
                  fontWeight: '700',
                  color: '#fff',
                  fontWeight: tabValue === 1 ? 'bold' : 'bold',
                  color: tabValue === 1 ? '#fff !important' : '#000 !important',
                  background: tabValue === 1 ? '#2654FE' : '#fff',
                  borderRadius: tabValue === 1 ? '10px 10px 0 0' : '0',
                  width: '33.333%',
                  '&.Mui-selected': {
                    borderBottom: 'none',
                  },
                  '@media(max-width: 600px)': {
                    fontSize: '16px',
                    width: '30%',
                  },
                  '@media(max-width: 480px)': {
                    fontSize: '14px',
                    width: '30%',
                  },
                }}
              />
              <Tab
                label="Hằng tháng"
                disableRipple
                sx={{
                  textTransform: 'none',
                  fontSize: '20px',
                  fontWeight: '700',
                  color: '#fff',
                  fontWeight: tabValue === 2 ? 'bold' : 'bold',
                  color: tabValue === 2 ? '#fff !important' : '#000 !important',
                  background: tabValue === 2 ? '#2654FE' : '#fff',
                  borderRadius: tabValue === 2 ? '10px 10px 0 0' : '0',
                  width: '33.333%',
                  '&.Mui-selected': {
                    borderBottom: 'none',
                  },
                  '@media(max-width: 600px)': {
                    fontSize: '16px',
                    width: '30%',
                  },
                  '@media(max-width: 480px)': {
                    fontSize: '14px',
                    width: '30%',
                  },
                }}
              />
            </Tabs>
            {tabValue === 0 && <Daily />}
            {tabValue === 1 && <Weekly />}
            {tabValue === 2 && <Monthly />}
          </Box>
          <Box
            sx={{ background: '#fff', borderRadius: '20px', padding: '15px', marginTop: '25px' }}
          >
            <Typography
              sx={{
                color: '#000000',
                fontSize: '20px',
                fontWeight: '700',
                marginBottom: '8px',
              }}
            >
              Thêm đối tác mới thủ công
            </Typography>
            <Typography
              sx={{ color: '#000000', fontSize: '20', fontWeight: '400', marginBottom: '25px' }}
            >
              Nhạp dữ liệu đối tác thủ công và cài đặt hoa hồng cho đối tác của bạn
            </Typography>
            <Button
              sx={{
                textTransform: 'none',
                color: '#2654FE',
                fontSize: '20px',
                fontWeight: '400',
                padding: 0,
              }}
            >
              Thêm đối tác
            </Button>
          </Box>
        </Box>
        <Box display={'flex'} flexDirection={'column'} justifyContent={'space-between'}>
          <Stack
            flexDirection={'row'}
            alignItems={'start'}
            justifyContent={'space-between'}
            sx={{
              '@media(max-width: 1580px)': {
                flexDirection: 'column',
              },
            }}
          >
            <Stack flexDirection={'row'} alignItems={'center'} gap={'5px'}>
              <Typography color="#000000" fontSize={'20px'} fontWeight={'700'}>
                Dữ liệu tổng quan
              </Typography>
              <Button
                sx={{
                  textTransform: 'none',
                  color: '#2654FE',
                  fontSize: '16px',
                  fontWeight: '400',
                  padding: 0,
                }}
              >
                Chi tiết
              </Button>
            </Stack>
            <LocalizationProvider dateAdapter={AdapterDayjs}>
              <DateRangePicker
                open={open}
                onOpen={() => setOpen(true)}
                onClose={() => setOpen(false)}
                format="DD/MM/YYYY"
                localeText={{ start: 'Từ ngày', end: 'Đến ngày' }}
                slotProps={{
                  textField: { sx: { display: 'none' } },
                  popper: { placement: 'bottom-start' },
                }}
                slots={{
                  field: () => (
                    <Button
                      onClick={() => setOpen(true)}
                      sx={{
                        border: '1px solid #C4C4C4',
                        borderRadius: '12px',
                        padding: '10px 16px',
                        backgroundColor: '#F8F9FA',
                        fontSize: '16px',
                        fontWeight: 400,
                        color: '#000',
                        textTransform: 'none',
                        display: 'flex',
                        alignItems: 'center',
                        gap: '8px',
                        minWidth: '260px',
                        justifyContent: 'flex-start',
                        height: '40px',
                        '&:hover': { backgroundColor: '#ECECEC' },
                      }}
                    >
                      <Stack
                        direction="row"
                        alignItems="center"
                        gap="4px"
                        justifyContent="space-between"
                        width="100%"
                      >
                        <Stack direction="row" alignItems="center" gap="4px">
                          {'Bắt đầu'} - {'Kết thúc'}
                        </Stack>
                        <CalendarMonthIcon sx={{ fontSize: 22 }} />
                      </Stack>
                    </Button>
                  ),
                }}
              />
            </LocalizationProvider>
          </Stack>
          <Grid
            container
            spacing={2}
            sx={{ display: 'flex', marginTop: '-3px', minHeight: '670px' }}
          >
            {statisticsData.map((stat, index) => (
              <Grid
                item
                key={index}
                sx={{
                  width: '50%',
                  maxWidth: 'unset',
                  '& .MuiPaper-root': {
                    height: '100% !important',
                  },
                }}
              >
                <Card sx={{ p: 2, textAlign: 'left', borderRadius: '20px', height: '100%' }}>
                  <Typography color="#202224" fontSize={'16px'} fontWeight={'600'}>
                    {stat.title}
                  </Typography>
                  <Typography
                    color="#202224"
                    fontSize={'28px'}
                    mb={'50px'}
                    fontWeight={'700'}
                    sx={{ mt: 1 }}
                  >
                    {stat.value}
                  </Typography>
                </Card>
              </Grid>
            ))}
          </Grid>
          <Box
            sx={{ background: '#fff', borderRadius: '20px', padding: '15px', marginTop: '25px' }}
          >
            <Typography
              sx={{
                color: '#000000',
                fontSize: '20px',
                fontWeight: '700',
                marginBottom: '8px',
              }}
            >
              Sử dụng quảng cáo hoặc gửi link để tuyển dụng
            </Typography>
            <Typography
              sx={{ color: '#000000', fontSize: '20', fontWeight: '400', marginBottom: '25px' }}
            >
              Sử dụng quảng cáo hoặc gửi link đăng ký để đối tác của bạn tự nhập thông tin đăng ký{' '}
            </Typography>
            <Button
              sx={{
                textTransform: 'none',
                color: '#2654FE',
                fontSize: '20px',
                fontWeight: '400',
                padding: 0,
              }}
            >
              Chia sẻ
            </Button>
          </Box>
        </Box>
      </Stack>
    </Box>
  );
};

export default Ranking;
