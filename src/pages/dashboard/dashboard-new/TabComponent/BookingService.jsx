import React, { useState } from 'react';
import { Box, Select, MenuItem, Button, Stack, Typography, Tabs, Tab } from '@mui/material';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { LocalizationProvider, DateRangePicker } from '@mui/x-date-pickers-pro';
import { ExpandMore } from '@mui/icons-material';
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';
import MorningSchedule from './TimeComponent/AfternoonSchedule';
import AfternoonSchedule from './TimeComponent/MorningSchedule';

const BookingService = () => {
  const [filter, setFilter] = useState('Hôm nay');
  const [open, setOpen] = useState(false);
  const [tabValue, setTabValue] = useState(0);

  return (
    <Box
      sx={{
        p: 3,
        borderRadius: '20px',
        marginTop: '-15px',
        background: '#f5f5f5',
      }}
    >
      <Stack
        flexDirection={'row'}
        justifyContent={'space-between'}
        mb={'25px'}
        sx={{
          '@media(max-width: 600px)': {
            flexDirection: 'column',
            gap: '20px',
            alignItems: 'start',
          },
        }}
      >
        <Tabs
          value={tabValue}
          onChange={(e, newValue) => setTabValue(newValue)}
          sx={{
            '& .MuiTabs-indicator': {
              display: 'none',
            },
            '@media(max-width: 480px)': {
              flexDirection: 'column',
              display: 'flex',
            },
          }}
        >
          <Tab
            label="Sáng 00:00 - 12:00 (4)"
            sx={{
              backgroundColor: tabValue === 0 ? '#F4F5F9' : 'transparent',
              borderRadius: '8px',
              padding: '8px 16px',
              fontWeight: tabValue === 0 ? 'bold' : 'bold',
              color: tabValue === 0 ? 'black' : 'gray',
              border: tabValue === 0 ? '2px solid #1976d2' : 'none', // Blue border for active tab
              boxShadow: tabValue === 0 ? '0 0px 20px 0 #00000026' : 'none',
              textTransform: 'none',
              fontSize: '14px',
              marginRight: '8px',
              '&:hover': {
                backgroundColor: tabValue === 0 ? '#F4F5F9' : '#f5f5f5',
              },
              '@media(max-width: 480px)': {
                fontSize: '12px',
                padding: ' 10px',
              },
            }}
          />
          <Tab
            label="Chiều 12:00 - 24:00 (4)"
            sx={{
              backgroundColor: tabValue === 1 ? '#F4F5F9' : 'transparent',
              borderRadius: '8px',
              padding: '8px 16px',
              fontWeight: tabValue === 1 ? 'bold' : 'bold',
              color: tabValue === 1 ? 'black' : 'gray',
              border: tabValue === 1 ? '2px solid #1976d2' : 'none', // Blue border for active tab
              boxShadow: tabValue === 1 ? '0 0px 20px 0 #00000026' : 'none',
              textTransform: 'none',
              fontSize: '14px',
              marginRight: '8px',
              '&:hover': {
                backgroundColor: tabValue === 1 ? '#F4F5F9' : '#f5f5f5',
              },
              '@media(max-width: 480px)': {
                fontSize: '12px',
                padding: ' 10px',
              },
            }}
          />
        </Tabs>
        <Box
          display="flex"
          justifyContent="flex-end"
          gap={2}
          sx={{
            '@media(max-width: 480px)': {
              flexDirection: 'column',
            },
          }}
        >
          <Select
            value={filter}
            onChange={(e) => setFilter(e.target.value)}
            displayEmpty
            size="small"
            sx={{ minWidth: 120, height: 50, backgroundColor: '#fff' }}
            IconComponent={(props) => <ExpandMore {...props} sx={{ color: '#000' }} />}
          >
            <MenuItem value="Hôm nay">Hôm nay</MenuItem>
            <MenuItem value="Hôm qua">Hôm qua</MenuItem>
            <MenuItem value="7 ngày qua">7 ngày qua</MenuItem>
            <MenuItem value="1 tháng qua">1 tháng qua</MenuItem>
          </Select>
          <LocalizationProvider dateAdapter={AdapterDayjs}>
            <DateRangePicker
              open={open}
              onOpen={() => setOpen(true)}
              onClose={() => setOpen(false)}
              format="DD/MM/YYYY"
              localeText={{ start: 'Từ ngày', end: 'Đến ngày' }}
              slotProps={{
                textField: { sx: { display: 'none' } },
                popper: { placement: 'bottom-start' },
              }}
              slots={{
                field: () => (
                  <Button
                    onClick={() => setOpen(true)}
                    sx={{
                      border: '1px solid #C4C4C4',
                      borderRadius: '12px',
                      padding: '10px 16px',
                      backgroundColor: '#F8F9FA',
                      fontSize: '16px',
                      fontWeight: 400,
                      color: '#000',
                      textTransform: 'none',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px',
                      minWidth: '260px',
                      justifyContent: 'flex-start',
                      '&:hover': { backgroundColor: '#ECECEC' },
                    }}
                  >
                    <Stack
                      direction="row"
                      alignItems="center"
                      gap="4px"
                      justifyContent="space-between"
                      width="100%"
                    >
                      <Stack direction="row" alignItems="center" gap="4px">
                        {'Bắt đầu'} - {'Kết thúc'}
                      </Stack>
                      <CalendarMonthIcon sx={{ fontSize: 22 }} />
                    </Stack>
                  </Button>
                ),
              }}
            />
          </LocalizationProvider>
        </Box>
      </Stack>

      {tabValue === 1 ? <MorningSchedule /> : <AfternoonSchedule />}
    </Box>
  );
};

export default BookingService;
