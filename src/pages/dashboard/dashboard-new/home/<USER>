import React, { useState } from 'react';
import {
  Grid,
  Card,
  CardContent,
  Typography,
  Box,
  MenuItem,
  Select,
  Stack,
  FormControl,
  Tabs,
  Tab,
} from '@mui/material';
import { LineChart } from '@mui/x-charts';
import { styled } from '@mui/material/styles';
import DashboardLayout from '../../../../layouts/dashboard';
import Affiliate from '../TabComponent/Affiliate';
import BookingService from '../TabComponent/BookingService';
import SellingPoints from '../TabComponent/SellingPoints';
import StoreOnline from '../TabComponent/StoreOnline';
import BestSelling from './BestSelling';
import Conver from './convert';

const StatCard = styled(Card)(({ theme }) => ({
  padding: theme.spacing(2),
  textAlign: 'center',
  borderRadius: '10px',
  boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
}));

const Dashboard = () => {
  const [tabValue, setTabValue] = useState(0);

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const [selectedMonth, setSelectedMonth] = useState('12');

  const getDaysInMonth = (month) => {
    const monthIndex = parseInt(month, 10);
    const daysInMonth = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
    return daysInMonth[monthIndex - 1];
  };

  const daysInMonth = getDaysInMonth(selectedMonth);
  const days = Array.from({ length: daysInMonth }, (_, i) => i + 1);

  const generateRevenueData = (numDays) => {
    const baseData = [
      20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 85, 70, 60, 50, 55, 60, 65, 70, 60, 50, 40, 45, 50,
      55, 60, 65, 50, 45, 40, 35, 40,
    ];
    return baseData.slice(0, numDays);
  };

  const revenueData = generateRevenueData(daysInMonth);

  const handleMonthChange = (event) => {
    setSelectedMonth(event.target.value);
  };

  return (
    <DashboardLayout>
      <Box sx={{ padding: '12px', borderRadius: '10px' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <Typography color="#202224" fontSize={'32px'} fontWeight={'700'}>
            Dashboard
          </Typography>
        </Box>

        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={3} sx={{ padding: 0 }}>
            <StatCard sx={{ boxShadow: '0 0px 20px 0 #00000026 !important;', background: '#fff' }}>
              <CardContent sx={{ padding: '0 !important' }}>
                <Stack
                  flexDirection={'row'}
                  justifyContent={'space-between'}
                  alignItems={'start'}
                  marginBottom={'30px'}
                >
                  <Stack>
                    <Typography
                      color="#202224"
                      fontSize={'18px'}
                      fontWeight={'500'}
                      marginBottom={'15px'}
                      textAlign={'left'}
                    >
                      Khách hàng
                    </Typography>
                    <Typography color="#202224" fontSize={'28px'} fontWeight={'700'}>
                      40,689
                    </Typography>
                  </Stack>
                  <img
                    src="/logo/logo-dashboard/Icon.png"
                    alt="Logo"
                    style={{ width: '60px', height: '60px' }}
                  />
                </Stack>
                <Typography
                  fontSize={'16px'}
                  fontWeight={'500'}
                  color="#606060"
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '5px',
                    justifyContent: 'center',
                  }}
                >
                  <Stack flexDirection={'row'} alignItems={'center'} gap={'5px'}>
                    <img
                      src="/logo/logo-dashboard/Path.svg"
                      alt="Logo"
                      style={{ width: '20px', height: '12px' }}
                    />
                    <Typography>8.5%</Typography>
                  </Stack>
                  Hôm qua: 40,000
                </Typography>
              </CardContent>
            </StatCard>
          </Grid>
          <Grid item xs={12} sm={6} md={3} sx={{ padding: 0 }}>
            <StatCard sx={{ boxShadow: '0 0px 20px 0 #00000026 !important;' }}>
              <CardContent sx={{ padding: '0 !important' }}>
                <Stack
                  flexDirection={'row'}
                  justifyContent={'space-between'}
                  alignItems={'start'}
                  marginBottom={'30px'}
                >
                  <Stack>
                    <Typography
                      color="#202224"
                      fontSize={'18px'}
                      fontWeight={'500'}
                      marginBottom={'15px'}
                      textAlign={'left'}
                    >
                      Đơn hàng
                    </Typography>
                    <Typography color="#202224" fontSize={'28px'} fontWeight={'700'}>
                      10,293
                    </Typography>
                  </Stack>
                  <img
                    src="/logo/logo-dashboard/Icon (1).png"
                    alt="Logo"
                    style={{ width: '60px', height: '60px' }}
                  />
                </Stack>
                <Typography
                  fontSize={'16px'}
                  fontWeight={'500'}
                  color="#606060"
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '5px',
                    justifyContent: 'center',
                  }}
                >
                  <Stack flexDirection={'row'} alignItems={'center'} gap={'5px'}>
                    <img
                      src="/logo/logo-dashboard/Path.svg"
                      alt="Logo"
                      style={{ width: '20px', height: '12px' }}
                    />
                    <Typography>1.3%</Typography>
                  </Stack>
                  Hôm qua: 10,000
                </Typography>
              </CardContent>
            </StatCard>
          </Grid>
          <Grid item xs={12} sm={6} md={3} sx={{ padding: 0 }}>
            <StatCard sx={{ boxShadow: '0 0px 20px 0 #00000026 !important;' }}>
              <CardContent sx={{ padding: '0 !important' }}>
                <Stack
                  flexDirection={'row'}
                  justifyContent={'space-between'}
                  alignItems={'start'}
                  marginBottom={'30px'}
                >
                  <Stack>
                    <Typography
                      color="#202224"
                      fontSize={'18px'}
                      fontWeight={'500'}
                      marginBottom={'15px'}
                      textAlign={'left'}
                    >
                      Doanh thu
                    </Typography>
                    <Typography color="#202224" fontSize={'28px'} fontWeight={'700'}>
                      9,989,000
                    </Typography>
                  </Stack>
                  <img
                    src="/logo/logo-dashboard/Icon (2).png"
                    alt="Logo"
                    style={{ width: '60px', height: '60px' }}
                  />
                </Stack>
                <Typography
                  fontSize={'16px'}
                  fontWeight={'500'}
                  color="#606060"
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '5px',
                    justifyContent: 'center',
                  }}
                >
                  <Stack flexDirection={'row'} alignItems={'center'} gap={'5px'}>
                    <img
                      src="/logo/logo-dashboard/Path (1).svg"
                      alt="Logo"
                      style={{ width: '20px', height: '12px' }}
                    />
                    <Typography>4.3%</Typography>
                  </Stack>
                  Hôm qua: 10,203,000
                </Typography>
              </CardContent>
            </StatCard>
          </Grid>
          <Grid item xs={12} sm={6} md={3} sx={{ padding: 0 }}>
            <StatCard sx={{ boxShadow: '0 0px 20px 0 #00000026 !important;' }}>
              <CardContent sx={{ padding: '0 !important' }}>
                <Stack
                  flexDirection={'row'}
                  justifyContent={'space-between'}
                  alignItems={'start'}
                  marginBottom={'30px'}
                >
                  <Stack>
                    <Typography
                      color="#202224"
                      fontSize={'18px'}
                      fontWeight={'500'}
                      marginBottom={'15px'}
                      textAlign={'left'}
                    >
                      Đơn hàng đang xử lý
                    </Typography>
                    <Typography
                      color="#202224"
                      fontSize={'28px'}
                      fontWeight={'700'}
                      textAlign={'left'}
                    >
                      2,040
                    </Typography>
                  </Stack>
                  <img
                    src="/logo/logo-dashboard/Icon (3).png"
                    alt="Logo"
                    style={{ width: '60px', height: '60px' }}
                  />
                </Stack>
                <Typography
                  fontSize={'16px'}
                  fontWeight={'500'}
                  color="#606060"
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '5px',
                    justifyContent: 'center',
                  }}
                >
                  <Stack flexDirection={'row'} alignItems={'center'} gap={'5px'}>
                    <img
                      src="/logo/Path (1).svg"
                      alt="Logo"
                      style={{ width: '20px', height: '12px' }}
                    />
                    <Typography>1.8%</Typography>
                  </Stack>
                  Hôm qua: 2000
                </Typography>
              </CardContent>
            </StatCard>
          </Grid>
        </Grid>

        <Card sx={{ mb: 3, p: 2, boxShadow: '0 0px 20px 0 #00000026 !important;' }}>
          <Box
            sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}
          >
            <Typography
              color="#202224"
              fontSize={'24px'}
              fontWeight={'700'}
              sx={{
                '@media(max-width: 480px)': {
                  fontSize: '20px',
                },
              }}
            >
              Chi tiết doanh thu
            </Typography>
            <FormControl sx={{ minWidth: '120' }}>
              <Select value={selectedMonth} onChange={handleMonthChange} displayEmpty>
                <MenuItem value="1">Tháng 1</MenuItem>
                <MenuItem value="2">Tháng 2</MenuItem>
                <MenuItem value="3">Tháng 3</MenuItem>
                <MenuItem value="4">Tháng 4</MenuItem>
                <MenuItem value="5">Tháng 5</MenuItem>
                <MenuItem value="6">Tháng 6</MenuItem>
                <MenuItem value="7">Tháng 7</MenuItem>
                <MenuItem value="8">Tháng 8</MenuItem>
                <MenuItem value="9">Tháng 9</MenuItem>
                <MenuItem value="10">Tháng 10</MenuItem>
                <MenuItem value="11">Tháng 11</MenuItem>
                <MenuItem value="12">Tháng 12</MenuItem>
              </Select>
            </FormControl>
          </Box>
          <LineChart
            xAxis={[
              {
                data: days,
                label: '',
                scaleType: 'point',
              },
            ]}
            yAxis={[
              {
                min: 0,
                max: 100,
                tickInterval: 20,
                tickFormatter: (value) => `${value}tr`,
                labelStyle: { fill: '#A0A0A0', fontSize: 14 },
              },
            ]}
            series={[
              {
                data: revenueData,
                color: '#1976d2',
                showMark: true,
                area: true,
                fillOpacity: 0.3,
                valueFormatter: (value) => `${(value * 1000000).toLocaleString('vi-VN')}`,
              },
            ]}
            height={300}
            tooltip={{ trigger: 'item' }}
          />
        </Card>

        <Box sx={{}}>
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            sx={{
              overflow: 'unset !important',
              '& .MuiTabs-indicator': { display: 'none' },
              '& .MuiTabs-scroller': {
                overflow: 'unset !important',
                padding: '30px 0 0 0',
              },
            }}
          >
            <Tab
              sx={{
                padding: '25px 25px',
                color: '#000',
                fontWeight: tabValue === 0 ? '700' : '700',
                background: tabValue === 0 ? '#f5f5f5' : 'unset',
                textTransform: 'none',
                '&.Mui-selected': {
                  color: '#000',
                  background: '#f5f5f5',
                  borderRadius: '20px 20px 0 0 ',
                },
                '@media(max-width: 600px)': {
                  padding: '25px 10px',
                  fontSize: '12px',
                },
              }}
              label="Store Online"
            />
            <Tab
              sx={{
                color: '#000',
                padding: '25px 25px',
                fontWeight: tabValue === 1 ? '700' : '700',
                textTransform: 'none',

                '&.Mui-selected': {
                  color: '#000',
                  borderRadius: '20px 20px 0 0 ',
                  background: '#f5f5f5',
                },
                '@media(max-width: 600px)': {
                  padding: '25px 10px',
                  fontSize: '12px',
                },
              }}
              label="Đặt dịch vụ"
            />
            <Tab
              sx={{
                color: '#000',
                padding: '25px 25px',
                fontWeight: tabValue === 2 ? '700' : '700',
                textTransform: 'none',

                '&.Mui-selected': {
                  color: '#000',
                  borderRadius: '20px 20px 0 0 ',
                  background: '#f5f5f5',
                },
                '@media(max-width: 600px)': {
                  fontSize: '12px',
                  padding: '25px 10px',
                },
              }}
              label="Affiliate"
            />
            <Tab
              sx={{
                color: '#000',
                padding: '25px 25px',
                fontWeight: tabValue === 3 ? '700' : '700',
                textTransform: 'none',

                '&.Mui-selected': {
                  color: '#000',
                  borderRadius: '20px 20px 0 0 ',
                  background: '#f5f5f5',
                },
                '@media(max-width: 600px)': {
                  fontSize: '12px',
                  padding: '25px 10px',
                },
              }}
              label="Điểm bán"
            />
          </Tabs>
          <Box>
            {tabValue === 0 && <StoreOnline />}
            {tabValue === 1 && <BookingService />}
            {tabValue === 2 && <Affiliate />}
            {tabValue === 3 && <SellingPoints />}
          </Box>
        </Box>
        <BestSelling />
        <Conver />
      </Box>
    </DashboardLayout>
  );
};

export default Dashboard;
