import React, { useState, useEffect } from "react";
import type { ReactElement } from "react";
import type { NextPage } from "next";
import {
  Grid,
  Card,
  CardContent,
  Typography,
  Box,
  MenuItem,
  Select,
  Stack,
  FormControl,
  Tabs,
  Tab,
  Button,
} from "@mui/material";
import { LineChart } from "@mui/x-charts";
import { styled } from "@mui/material/styles";
import ContentLayout from "../../layouts/dashboard";
import Affiliate from "../../components/TabComponent/Affiliate";
import BookingService from "../../components/TabComponent/BookingService";
import SellingPoints from "../../components/TabComponent/SellingPoints";
import StoreOnline from "../../components/TabComponent/StoreOnline";
import BestSelling from "../../components/BestSelling";
import ConversionRate from "../../components/ConversionRate";
import { dashboardPartnerService } from "src/api/services/dashboard/partner/dashboardpartner.service";
import { IDashboarPartner, ParamDashboard } from "@/src/api/types/dashboard.types";
import { useStoreId } from "@/src/hooks/use-store-id";
import { formatDateTimeDisplay } from "@/src/utils/date-utils";
import { branchService } from "@/src/api/services/branch/branch.service";
import { formatPrice } from "@/src/api/types/membership.types";
import { Padding } from "@/src/styles/CommonStyle";
import dayjs from "dayjs";

const StatCard = styled(Card)(({ theme }) => ({
  padding: theme.spacing(2),
  textAlign: "center",
  borderRadius: "10px",
  boxShadow: "0 2px 10px rgba(0,0,0,0.1)",
}));

// Define custom type for Next.js pages with getLayout
type NextPageWithLayout = NextPage & {
  getLayout?: (page: ReactElement) => ReactElement;
  isDashboardPage?: boolean;
};

const Dashboard = () => {
  const [tabValue, setTabValue] = useState(0);
  const [dashboardPartner, setDashboardPartner] = useState<IDashboarPartner>();
  const storeId = useStoreId();

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };
  const currentMonth = new Date().getMonth() + 1; // January is 0

  const [selectedMonth, setSelectedMonth] = useState(currentMonth.toString());
  const [selectedDay, setSelectedDay] = useState<string>("TODAY");
  const [startDate, setStartDate] = useState<string>();
  const [endDate, setEndDate] = useState<string>();
  const [selectedBranch, setSelectedBranch] = useState<string>();
  const [listBranch, setListBranch] = useState([]);
  const [tabRank, setTabRank] = useState<string>("Day");

  const fetchBranch = async () => {
    try {
      const params = { skip: 0, limit: 99 };
      const res = await branchService.getBranchs(storeId, params);
      setListBranch(res?.data?.data);
    } catch (error) {
      console.error("Error fetching branches:", error);
    }
  };

  useEffect(() => {
    if (storeId) {
      fetchBranch();
    }
  }, [storeId]);

  useEffect(() => {
    if (listBranch.length > 0) {
      setSelectedBranch(listBranch[0].branchId);
    }
  }, [listBranch]);

  const handleMonthChange = (event) => {
    setSelectedMonth(event.target.value);
  };

  const data: ParamDashboard = {
    shopId: storeId,
    paramMonth: selectedMonth,
    paramDate: selectedDay,
    branchId: selectedBranch || "",
    typeSortRank: tabRank,
    ...(startDate && endDate ? { startDate, endDate } : {}),
  };

  const fetchDashboardPartner = async () => {
    const res = await dashboardPartnerService.getDashboardPartnerV2(data);
    setDashboardPartner(res?.data?.data);
  };
  useEffect(() => {
    fetchDashboardPartner();

    const interval = setInterval(() => {
      fetchDashboardPartner();
    }, 60000);

    return () => clearInterval(interval);
  }, [storeId, selectedMonth, selectedDay, startDate, endDate, selectedBranch, tabRank]);

  return (
    <ContentLayout>
      <Box sx={{ padding: Padding, borderRadius: "10px" }}>
        <Box sx={{ display: "flex", alignItems: "center", mb: 3, justifyContent: "space-between" }}>
          <Typography color="#202224" fontSize={"32px"} fontWeight={"700"}>
            Dashboard
          </Typography>
          <Stack
            color={"#797A7C"}
            fontSize={"18px"}
            fontWeight={"400"}
            flexDirection={"row"}
            alignItems={"center"}
            gap={"10px"}
          >
            Cập nhật {dayjs(dashboardPartner?.today).format("DD/MM/YYYY HH:mm:ss")}
            <Button sx={{ minWidth: "unset", padding: 0 }}>
              <img width={16} height={16} src="/assets/reset.svg" alt="Icon reset" />
            </Button>
          </Stack>
        </Box>

        <Grid container spacing={3} sx={{ mb: 3, display: "flex", alignItems: "stretch" }}>
          {dashboardPartner?.infoCount.map((item, index) => (
            <Grid item xs={12} sm={6} md={3} sx={{ padding: 0 }} key={item.title}>
              <StatCard
                sx={{
                  boxShadow: "6px 6px 54px 0 #0000000D !important;",
                  background: "#fff",
                  height: "100%",
                }}
              >
                <CardContent
                  sx={{
                    padding: "0 !important",
                    height: "100%",
                    display: "flex",
                    flexDirection: "column",
                    justifyContent: "space-between",
                  }}
                >
                  <Stack
                    flexDirection={"row"}
                    justifyContent={"space-between"}
                    alignItems={"start"}
                    marginBottom={"30px"}
                  >
                    <Stack>
                      <Typography
                        color="#202224"
                        fontSize={"18px"}
                        fontWeight={"500"}
                        marginBottom={"15px"}
                        textAlign={"left"}
                      >
                        {item.title}
                      </Typography>
                      <Typography
                        textAlign={"left"}
                        color="#202224"
                        fontSize={"28px"}
                        fontWeight={"700"}
                      >
                        {index === 2 ? `${formatPrice(item.valueToday)} VNĐ` : item.valueToday}
                      </Typography>
                    </Stack>
                    <img src={item.iconName} alt="Logo" style={{ width: "60px", height: "60px" }} />
                  </Stack>
                  <Stack
                    direction={"row"}
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      gap: "5px",
                      justifyContent: "start",
                    }}
                  >
                    <Stack flexDirection={"row"} alignItems={"center"} gap={"5px"}>
                      <img
                        src={
                          Number(item.valueToday) > Number(item.valueYesterday)
                            ? "/logo/logo-dashboard/IncreaseIcon.svg"
                            : "/logo/logo-dashboard/DecreaseIcon.svg"
                        }
                        alt="Logo"
                        style={{ width: "20px", height: "12px" }}
                      />
                      <Typography
                        sx={{
                          color:
                            Number(item.valueToday) > Number(item.valueYesterday)
                              ? "#00b69b"
                              : "#f93c65",
                          fontWeight: 600,
                        }}
                      >
                        {item.valuePercent}
                      </Typography>
                    </Stack>
                    <Typography
                      fontSize={"16px"}
                      fontWeight={"500"}
                      color="#606060"
                      sx={{
                        display: "flex",
                        alignItems: "center",
                        gap: "5px",
                        justifyContent: "start",
                      }}
                    >
                      Hôm qua:{" "}
                      {index === 2
                        ? `${formatPrice(item.valueYesterday)} VNĐ`
                        : item.valueYesterday}
                    </Typography>
                  </Stack>
                </CardContent>
              </StatCard>
            </Grid>
          ))}
        </Grid>

        <Card sx={{ mb: 3, p: 2, boxShadow: "6px 6px 54px 0 #0000000D !important;" }}>
          <Box
            sx={{ display: "flex", justifyContent: "space-between", alignItems: "center", mb: 2 }}
          >
            <Typography
              color="#202224"
              fontSize={"24px"}
              fontWeight={"700"}
              sx={{
                "@media(max-width: 480px)": {
                  fontSize: "20px",
                },
              }}
            >
              Chi tiết doanh thu
            </Typography>
            <FormControl sx={{ minWidth: "120" }}>
              <Select
                sx={{ height: "36px" }}
                value={selectedMonth}
                onChange={handleMonthChange}
                displayEmpty
              >
                <MenuItem value="1">Tháng 1</MenuItem>
                <MenuItem value="2">Tháng 2</MenuItem>
                <MenuItem value="3">Tháng 3</MenuItem>
                <MenuItem value="4">Tháng 4</MenuItem>
                <MenuItem value="5">Tháng 5</MenuItem>
                <MenuItem value="6">Tháng 6</MenuItem>
                <MenuItem value="7">Tháng 7</MenuItem>
                <MenuItem value="8">Tháng 8</MenuItem>
                <MenuItem value="9">Tháng 9</MenuItem>
                <MenuItem value="10">Tháng 10</MenuItem>
                <MenuItem value="11">Tháng 11</MenuItem>
                <MenuItem value="12">Tháng 12</MenuItem>
              </Select>
            </FormControl>
          </Box>
          <LineChart
            xAxis={[
              {
                data: dashboardPartner?.dataChartDetailRevenue?.listDayOfMonth || [],
                label: "",
                scaleType: "point",
              },
            ]}
            yAxis={[
              {
                labelStyle: { fill: "#A0A0A0", fontSize: 14 },
                valueFormatter: (value) => {
                  if (value >= 1_000_000_000) return `${value / 1_000_000_000}tỉ`;
                  if (value >= 1_000_000) return `${value / 1_000_000}tr`;
                  if (value >= 1_000) return `${value / 1_000}k`;
                  return value;
                },
              },
            ]}
            series={[
              {
                data: dashboardPartner?.dataChartDetailRevenue?.listRevenueOfDay || [],
                color: "#1976d2",
                showMark: true,
                area: true,
                // fillOpacity: 0.3,
                valueFormatter: (value) => `${value.toLocaleString("vi-VN")}`,
              },
            ]}
            height={300}
            tooltip={{ trigger: "item" }}
            grid={{
              horizontal: true,
              vertical: true,
            }}
            sx={{
              "& .MuiChartsAxis-gridLine": {
                stroke: "#1976d2",
                strokeDasharray: "3 3",
                strokeWidth: 1,
              },
            }}
          />
        </Card>

        <Box sx={{}}>
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            sx={{
              overflow: "unset !important",
              "& .MuiTabs-indicator": { display: "none" },
              "& .MuiTabs-scroller": {
                overflow: "unset !important",
                padding: "30px 0 0 0",
              },
            }}
          >
            <Tab
              sx={{
                padding: "25px 25px",
                color: "#000",
                fontWeight: tabValue === 0 ? "700" : "700",
                background: tabValue === 0 ? "#fff" : "unset",
                textTransform: "none",
                "&.Mui-selected": {
                  color: "#000",
                  background: "#fff",
                  borderRadius: "20px 20px 0 0 ",
                },
                "@media(max-width: 600px)": {
                  padding: "25px 10px",
                  fontSize: "12px",
                },
              }}
              label="Store Online"
            />
            {/* <Tab
              sx={{
                color: '#000',
                padding: '25px 25px',
                fontWeight: tabValue === 1 ? '700' : '700',
                textTransform: 'none',

                '&.Mui-selected': {
                  color: '#000',
                  borderRadius: '20px 20px 0 0 ',
                  background: '#fff',
                },
                '@media(max-width: 600px)': {
                  padding: '25px 10px',
                  fontSize: '12px',
                },
              }}
              label="Đặt dịch vụ"
            /> */}
            {/* <Tab
              sx={{
                color: '#000',
                padding: '25px 25px',
                fontWeight: tabValue === 2 ? '700' : '700',
                textTransform: 'none',

                '&.Mui-selected': {
                  color: '#000',
                  borderRadius: '20px 20px 0 0 ',
                  background: '#fff',
                },
                '@media(max-width: 600px)': {
                  fontSize: '12px',
                  padding: '25px 10px',
                },
              }}
              label="Affiliate"
            /> */}
            <Tab
              sx={{
                color: "#000",
                padding: "25px 25px",
                fontWeight: tabValue === 3 ? "700" : "700",
                textTransform: "none",

                "&.Mui-selected": {
                  color: "#000",
                  borderRadius: "20px 20px 0 0 ",
                  background: "#fff",
                },
                "@media(max-width: 600px)": {
                  fontSize: "12px",
                  padding: "25px 10px",
                },
              }}
              label="Điểm bán"
            />
          </Tabs>
          <Box>
            {tabValue === 0 && (
              <StoreOnline
                selectedDay={selectedDay}
                setSelectedDay={setSelectedDay}
                startDate={startDate}
                setStartDate={setStartDate}
                endDate={endDate}
                setEndDate={setEndDate}
                dataOrder={dashboardPartner?.dataShopOnline || []}
              />
            )}
            {/* {tabValue === 1 && <BookingService />} */}
            {/* {tabValue === 1 && <Affiliate
              selectedDay={selectedDay}
              setSelectedDay={setSelectedDay}
              startDate={startDate}
              setStartDate={setStartDate}
              endDate={endDate}
              setEndDate={setEndDate}
              tabRank={tabRank}
              setTabRank={setTabRank}
              dataOrder={dashboardParter?.dataShopOnline || []}
              topRank={dashboardParter.rankUsers || []}
            />} */}
            {tabValue === 1 && (
              <SellingPoints
                selectedDay={selectedDay}
                setSelectedDay={setSelectedDay}
                startDate={startDate}
                setStartDate={setStartDate}
                endDate={endDate}
                setEndDate={setEndDate}
                selectedBranch={selectedBranch}
                setSelectedBranch={setSelectedBranch}
                listBranch={listBranch}
                setListBranch={setListBranch}
                dataOrder={dashboardPartner?.dataShopOffline || []}
              />
            )}
          </Box>
        </Box>
        <BestSelling
          selectedMonth={selectedMonth}
          setSelectedMonth={setSelectedMonth}
          handleMonthChange={handleMonthChange}
          top10Items={dashboardPartner?.top10ItemPopulars || []}
        />
        <ConversionRate
          selectedMonth={selectedMonth}
          setSelectedMonth={setSelectedMonth}
          handleMonthChange={handleMonthChange}
          dataChart={dashboardPartner?.dataChartConvertCustomer}
        />
      </Box>
    </ContentLayout>
  );
};

// Mark this page as a dashboard page for _app.js to detect
(Dashboard as NextPageWithLayout).isDashboardPage = true;

// Use the new ContentLayout instead of DashboardLayout
(Dashboard as NextPageWithLayout).getLayout = (page) => <ContentLayout>{page}</ContentLayout>;

export default Dashboard as NextPageWithLayout;
