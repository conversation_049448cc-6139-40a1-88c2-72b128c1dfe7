import React, { useEffect, useState } from "react";
import {
  Box,
  <PERSON>po<PERSON>,
  Button,
  TextField,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Checkbox,
  Paper,
  InputAdornment,
  Tooltip,
  IconButton,
  Dialog,
  DialogActions,
  DialogContentText,
  DialogTitle,
  DialogContent,
  TablePagination,
} from "@mui/material";
import SearchIcon from "@mui/icons-material/Search";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import AddIcon from "@mui/icons-material/Add";
import { useRouter } from "next/router";
import { paths } from "@/src/paths";
import { useStoreId } from "@/src/hooks/use-store-id";
import { useShop } from "@/src/api/hooks/shop/use-shop";
import { shopPolicyService } from "@/src/api/services/shop-policy/shop-policy.service";
import { tokens } from "@/src/locales/tokens";
import { useTranslation } from "react-i18next";
import DashboardLayout from "@/src/layouts/dashboard";
import { itemOptionGroupService } from "@/src/api/services/item-option-group/item-option-group.service";
import { Padding } from "@/src/styles/CommonStyle";
import { useAllPermissions } from "@/src/api/hooks/auth/use-all-permissions";
import { usePathname } from "next/navigation";
import { PERMISSION_TYPE_ENUM } from "@/src/constants/constant";
import TruncatedText from "@/src/components/truncated-text/truncated-text";
import { rowPerPageOptionsDefault } from "@/src/types/store/enum-type";
import TitleDialog from "@/src/components/dialog/TitleDialog";
import { formatTruncatedText } from "@/src/utils/format";

const Header = ({
  onAddNew,
  selected,
  isDeleteMany,
  setIsDeleteMany,
  handleDeleteMany,
  transformedData,
  isGranted,
}) => {
  const { t } = useTranslation();
  const router = useRouter();
  const pathname = usePathname();
  const [listDelete, setListDelete] = useState([]);

  const onDeleteClick = () => {
    setIsDeleteMany(true);
  };

  useEffect(() => {
    setListDelete(transformedData?.filter((x: any) => selected?.includes(x.id)));
  }, [selected]);
  return (
    <>
      <Box
        sx={{
          display: "flex",
          justifyContent: "flex-end",
          alignItems: "right",
          mb: 3,
          gap: 2,
        }}
      >
        {selected && selected?.length > 0 && (
          <Button
            variant="outlined"
            color="error"
            startIcon={<DeleteIcon />}
            onClick={onDeleteClick}
            sx={{
              height: "40px",
              borderRadius: "8px",
              textTransform: "none",
            }}
          >
            {`Xoá${selected?.length > 0 ? ` (${selected?.length})` : ""}`}
          </Button>
        )}
        <Tooltip
          title={!isGranted(pathname, PERMISSION_TYPE_ENUM.Add) ? "Bạn không có quyền thêm" : ""}
        >
          <span>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              sx={{
                backgroundColor: "#2654FE",
                textTransform: "none",
                "&:hover": {
                  backgroundColor: "#1565c0",
                },
              }}
              onClick={onAddNew}
              disabled={!isGranted(pathname, PERMISSION_TYPE_ENUM.Add)}
            >
              Thêm mới
            </Button>
          </span>
        </Tooltip>
      </Box>
      <Dialog open={isDeleteMany} onClose={() => setIsDeleteMany(false)} maxWidth="sm" fullWidth>
        <DialogTitle>{t(tokens.contentManagement.category.delete.confirmTitle)}</DialogTitle>
        <DialogContent>
          <DialogContentText>
            {t(tokens.contentManagement.category.delete.confirmMessage, {
              name: listDelete.map((item) => item.title).join(", "),
            })}
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setIsDeleteMany(false)} variant="outlined">
            {t(tokens.common.cancel)}
          </Button>
          <Button onClick={() => handleDeleteMany()} color="error" variant="contained" autoFocus>
            {t(tokens.common.delete)}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};
const SearchBar = ({
  searchText,
  setSearchText,
  transformedData,
  setTransformedData,
  transformedDataClone,
  setTransformedDataClone,
}) => {
  const handleChange = (e: any) => {
    const searchValue = e?.target?.value.toLowerCase();
    setSearchText(searchValue);
    if (searchValue?.length > 0) {
      const filteredData = transformedData?.filter((item: any) =>
        item.title.toLowerCase().includes(searchValue)
      );
      setTransformedData(filteredData);
    } else {
      setTransformedData((prev) => [...transformedDataClone]);
    }
  };

  return (
    <Box sx={{ mb: 3 }}>
      <TextField
        placeholder="Tìm kiếm"
        variant="outlined"
        size="small"
        sx={{ width: "300px" }}
        onChange={handleChange}
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <SearchIcon sx={{ color: "#666" }} />
            </InputAdornment>
          ),
        }}
      />
    </Box>
  );
};
const ItemOptionGroupTable = ({
  data,
  setData,
  transformedData,
  setTransformedData,
  selected,
  setSelected,
  handleSelectAll,
  handleSelect,
  fetchListItemOptionGroup,
  isGranted,
  page,
  setPage,
  rowsPerPage,
  setRowsPerPage,
  totalCount,
  setTotalCount,
}) => {
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedItemOptionGroup, setSelectedItemOptionGroup] = useState<any>();
  const router = useRouter();
  const pathname = usePathname();
  const handleDeletePolicy = (item) => {
    setOpenDialog(true);
    setSelectedItemOptionGroup(item);
  };
  const confirmDelete = async (policy) => {
    const res = await itemOptionGroupService.deleteItemOptionGroup(
      selectedItemOptionGroup?.itemOptionGroupId
    );
    fetchListItemOptionGroup();
    setOpenDialog(false);
  };

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  return (
    <TableContainer component={Paper} sx={{ boxShadow: "none" }}>
      <Table>
        <TableHead>
          <TableRow>
            {/* <TableCell padding="checkbox">
              <Checkbox
                checked={selected?.length === data?.length}
                indeterminate={selected?.length > 0 && selected.length < data?.length}
                onChange={handleSelectAll}
              />
            </TableCell> */}
            <TableCell>STT</TableCell>
            <TableCell sx={{ width: 600 }}>Tên nhóm tuỳ chọn</TableCell>
            <TableCell align="center">Yêu cầu</TableCell>
            <TableCell align="center">Được chọn nhiều</TableCell>
            <TableCell>Quản lý</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {data?.map((row: any, index: number) => (
            <TableRow key={index} hover>
              {/* <TableCell padding="checkbox">
                <Checkbox checked={selected?.includes(row.id)} onChange={() => handleSelect(row.id)} />
              </TableCell> */}
              <TableCell>{page * rowsPerPage + index + 1}</TableCell>
              <TableCell>
                <TruncatedText
                  {...formatTruncatedText({
                    text: row.name,
                    isGranted: isGranted(pathname, PERMISSION_TYPE_ENUM.Edit),
                    openInNewTab: false,
                    width: "600px",
                    actionNeed: () =>
                      router.push(paths.itemGroups.update.replace(":id", row?.itemOptionGroupId)),
                  })}
                />
              </TableCell>
              <TableCell sx={{ minWidth: 100 }}>
                <Box sx={{ display: "flex", justifyContent: "center" }}>
                  <Checkbox checked={row?.require} />
                </Box>
              </TableCell>
              <TableCell sx={{ minWidth: 150, display: "flex", justifyContent: "center" }}>
                <Checkbox checked={row?.isMultiSelect} />
              </TableCell>
              <TableCell sx={{ minWidth: 100 }}>
                <Box sx={{ display: "flex", gap: 1 }}>
                  <Tooltip
                    title={
                      !isGranted(pathname, PERMISSION_TYPE_ENUM.Edit)
                        ? "Bạn không có quyền sửa"
                        : "Sửa"
                    }
                  >
                    <span>
                      <IconButton
                        size="small"
                        sx={{ color: "primary.main" }}
                        onClick={() =>
                          router.push(
                            paths.itemGroups.update.replace(":id", row?.itemOptionGroupId)
                          )
                        }
                        disabled={!isGranted(pathname, PERMISSION_TYPE_ENUM.Edit)}
                      >
                        <EditIcon fontSize="small" />
                      </IconButton>
                    </span>
                  </Tooltip>
                  <Tooltip
                    title={
                      !isGranted(pathname, PERMISSION_TYPE_ENUM.Delete)
                        ? "Bạn không có quyền xoá"
                        : "Xoá"
                    }
                  >
                    <span>
                      <IconButton
                        onClick={() => handleDeletePolicy(row)}
                        size="small"
                        sx={{ color: "error.main" }}
                        disabled={!isGranted(pathname, PERMISSION_TYPE_ENUM.Delete)}
                      >
                        <DeleteIcon fontSize="small" />
                      </IconButton>
                    </span>
                  </Tooltip>
                </Box>
                <TitleDialog
                  title="Xóa nhóm tuỳ chọn"
                  open={openDialog}
                  handleClose={() => setOpenDialog(false)}
                  handleSubmit={() => confirmDelete(row)}
                  submitBtnTitle="Xác nhận"
                  color="error"
                >
                  <Box>
                    <Typography>Bạn có chắc muốn xóa nhóm tuỳ chọn này?</Typography>
                  </Box>
                </TitleDialog>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
      <TablePagination
        component="div"
        count={totalCount}
        page={page}
        onPageChange={handleChangePage}
        rowsPerPage={rowsPerPage}
        onRowsPerPageChange={handleChangeRowsPerPage}
        rowsPerPageOptions={rowPerPageOptionsDefault}
        labelRowsPerPage="Số dòng mỗi trang" // Thay đổi văn bản ở phần này
      />
    </TableContainer>
  );
};

const ItemOptionGroup = () => {
  const [transformedData, setTransformedData] = useState([]);
  const [totalCount, setTotalCount] = useState(0);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [selected, setSelected] = useState<number[]>([]);
  const [shopId, setShopId] = useState<string>("");
  const [isDeleteMany, setIsDeleteMany] = useState<boolean>(false);
  const router = useRouter();
  const storeId = useStoreId();
  const { detailShop } = useShop();
  const { permissions } = useAllPermissions();
  const isGranted = (url, permission) => {
    if (url.endsWith("/")) {
      url = url.slice(0, -1);
    }
    const matchingUrl = Object.keys(permissions).find((key) => key === url);
    if (!matchingUrl) return false;
    return permissions[matchingUrl]?.includes(permission) || false;
  };
  const fetchData = async () => {
    try {
      if (storeId) {
        const detail = await detailShop(storeId);
        setShopId(detail?.data?.shopId);
      }
    } catch (error) {
      console.log({ error });
    }
  };
  useEffect(() => {
    fetchData();
  }, [storeId]);

  const handleSelect = (id: number) => {
    const selectedIndex = selected?.indexOf(id);
    let newSelected: number[] = [];

    if (selectedIndex === -1) {
      newSelected = [...selected, id];
    } else {
      newSelected = selected?.filter((item) => item !== id);
    }
    setSelected(newSelected);
  };

  const handleAddNew = () => {
    router.push(paths.itemGroups.new);
  };

  const [listItemOptionGroup, setListItemOptionGroup] = useState<any[]>([]);

  const fetchListItemOptionGroup = async () => {
    if (shopId) {
      const param = {
        skip: rowsPerPage * page,
        limit: rowsPerPage,
        data: {
          shopId: shopId,
        },
      };
      const res = await itemOptionGroupService.listItemOptionGroup(param);
      setTotalCount(res?.data?.total);
      setListItemOptionGroup(res?.data?.data);
    }
  };
  useEffect(() => {
    fetchListItemOptionGroup();
  }, [shopId, page, rowsPerPage]);

  const handleSelectAll = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.checked) {
      const list = listItemOptionGroup?.map((item) => item.id);
      setSelected(list);
    } else {
      setSelected([]);
    }
  };

  const handleDeleteMany = async () => {
    const transformedDataX = transformedData?.filter((item) => !selected.includes(item.id));
    var data = {
      shopId: storeId,
      shopPolicy: JSON.stringify(transformedDataX),
    };
    setSelected(transformedDataX?.map((item) => item.id));
    const res = await shopPolicyService.createShopPolicy(data);
    fetchListItemOptionGroup();
    setIsDeleteMany(false);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };
  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  return (
    <DashboardLayout>
      <Box sx={{ backgroundColor: "unset", p: Padding }}>
        <Box sx={{ display: "flex", justifyContent: "space-between" }}>
          {/* <SearchBar
            searchText={searchText}
            setSearchText={setSearchText}
            transformedData={transformedData}
            setTransformedData={setTransformedData}
            transformedDataClone={transformedDataClone}
            setTransformedDataClone={transformedDataClone}
          /> */}
          <Header
            onAddNew={handleAddNew}
            selected={selected}
            isDeleteMany={isDeleteMany}
            setIsDeleteMany={setIsDeleteMany}
            handleDeleteMany={handleDeleteMany}
            transformedData={transformedData}
            isGranted={isGranted}
          />
        </Box>
        <Box sx={{ background: "white", boxShadow: 3, borderRadius: 2, p: 2 }}>
          <ItemOptionGroupTable
            data={listItemOptionGroup}
            setData={setListItemOptionGroup}
            transformedData={transformedData}
            setTransformedData={setTransformedData}
            selected={selected}
            setSelected={setSelected}
            handleSelectAll={handleSelectAll}
            handleSelect={handleSelect}
            fetchListItemOptionGroup={fetchListItemOptionGroup}
            isGranted={isGranted}
            page={page}
            setPage={setPage}
            rowsPerPage={rowsPerPage}
            setRowsPerPage={setRowsPerPage}
            totalCount={totalCount}
            setTotalCount={setTotalCount}

            // onEdit={handleEdit}
          />
        </Box>
        {/* <TablePagination
          component="div"
          count={totalCount}
          page={page}
          onPageChange={handleChangePage}
          rowsPerPage={rowsPerPage}
          onRowsPerPageChange={handleChangeRowsPerPage}
          rowsPerPageOptions={[5, 10, 20]}
          labelRowsPerPage="Số dòng mỗi trang" // Thay đổi văn bản ở phần này
        /> */}
      </Box>
    </DashboardLayout>
  );
};

export default ItemOptionGroup;
