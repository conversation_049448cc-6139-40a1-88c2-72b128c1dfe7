import React, { useEffect } from "react";
import DashboardLayout from "@/src/layouts/dashboard";
import PageTitleWithBackBtn from "@/src/layouts/dashboard/page-title-with-back-btn/page-title";
import { Box } from "@mui/material";
import ItemOptionGroupForm from "@/src/components/item-option-groups/ItemOptionGroupForm";
import { paths } from "@/src/paths";
import { Padding } from "@/src/styles/CommonStyle";

export default function NewItemGroup() {
  return (
    <DashboardLayout>
      <Box sx={{ p: Padding }}>
        <PageTitleWithBackBtn title="Thêm nhóm tùy chọn" backPath={paths.itemGroups.list} />
        <ItemOptionGroupForm />
      </Box>
    </DashboardLayout>
  );
}
