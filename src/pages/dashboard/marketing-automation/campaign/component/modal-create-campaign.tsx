"use client";

import { useEffect, useState } from "react";
import {
  Box,
  Button,
  Dialog,
  DialogContent,
  TextField,
  Typography,
  Radio,
  RadioGroup,
  FormControlLabel,
  FormControl,
  Select,
  MenuItem,
  Paper,
  Grid,
  IconButton,
  Divider,
  Autocomplete,
} from "@mui/material";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { Upload, CalendarToday, UploadFile } from "@mui/icons-material";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import dayjs from "dayjs";
import ModalImportCustomer from "./modal-import-customer";
import { useZaloAutomation } from "@/src/api/hooks/zalo-automation/zalo-automation";
import { useStoreId } from "@/src/hooks/use-store-id";
import { ZnsTemplateDto } from "@/src/api/services/zalo-automation/zalo-automation.service";
import * as yup from "yup";
import { Controller, useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { useCampaign } from "@/src/api/hooks/campaign/campaign";
import {
  CampaignDto,
  IBodyCreateCampaign,
  IBodyImportExcelListUserCampaign,
} from "@/src/api/services/campaign/campaign.service";
import { DateTimePicker } from "@mui/x-date-pickers";
import useSnackbar from "@/src/hooks/use-snackbar";

export interface IParamsZns {
  name: string;
  type?: string;
  require: boolean;
  maxLength: number;
  minLength: number;
  acceptNull: boolean;
}

export const campaignSchema = yup.object().shape({
  campaignName: yup
    .string()
    .required("Vui lòng nhập tên chiến dịch")
    .max(100, "Tên chiến dịch không được vượt quá 100 ký tự"),
  messageTemplate: yup.object().nullable().required("Vui lòng chọn mẫu tin nhắn"),
  campaignDesc: yup.string().max(500, "Mô tả không được vượt quá 500 ký tự"),
  runTime: yup.string().required("Vui lòng chọn thời gian chạy"),
  scheduledDate: yup
    .date()
    .nullable()
    .when("runTime", {
      is: "SpecificDateTime",
      then: (schema) => schema.required("Vui lòng chọn ngày giờ chạy"),
    }),
});

export default function ModalCreateCampaignV2({
  campaignType,
  setCampaignType,
  open,
  setOpen,
  fetchListCampaign,
}) {
  const { getListTemplateZns, getDetailTemplateZns, exportFileTemplateExcelZns } =
    useZaloAutomation();
  const { importFileExcelListUserCampaign, createCampaign, updateCampaign } = useCampaign();
  const [chartDateRange, setChartDateRange] = useState(dayjs());
  const [startDate, setStartDate] = useState<any>();
  const [isOpenModalUploadFile, setIsOpenModalUploadFile] = useState<boolean>(false);
  const storeId = useStoreId();
  const [listTemplateMessage, setListTemplateMessage] = useState<ZnsTemplateDto[]>([]);
  const [selectedMessageTemplate, setSelectedMessageTemplate] = useState<ZnsTemplateDto | null>(
    null
  );
  const [listParam, setListParam] = useState<IParamsZns[]>([]);
  const [file, setFile] = useState<File>(null);
  const [campaignCreated, setCampaignCreated] = useState<CampaignDto>();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isLoadingImportFile, setIsLoadingImportFile] = useState<boolean>(false);
  const snackbar = useSnackbar();
  const fetchListTemplateZns = async () => {
    const res = await getListTemplateZns(storeId, "", "");
    if (res && res?.status === 200) {
      if (Array.isArray(res?.data?.data) && res?.data?.data.length > 0) {
        setListTemplateMessage(res?.data?.data);
      }
    }
  };
  const getDetaiDataTemplateZns = async () => {
    const res = await getDetailTemplateZns(storeId, selectedMessageTemplate?.znsId);
    if (res && res?.status === 200) {
      if (Array.isArray(res?.data?.data?.listParams)) {
        setListParam(res?.data?.data?.listParams);
      }
    }
  };
  useEffect(() => {
    fetchListTemplateZns();
  }, [storeId]);
  useEffect(() => {
    getDetaiDataTemplateZns();
  }, [selectedMessageTemplate]);

  const handleClose = () => {
    reset(defaultValues);
    setSelectedMessageTemplate(null);
    setCampaignCreated(null);
    setOpen(false);
  };

  const handleOpen = () => {
    setOpen(true);
  };

  const handleDownloadTemplateFileExcelCustomer = async () => {
    try {
      const res = await exportFileTemplateExcelZns(storeId, selectedMessageTemplate.znsId);
      if (res && res?.data?.data?.link) {
        const response = await fetch(res?.data?.data?.link);
        const blob = await response.blob();

        const url = window.URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = "TemplateZNS.xlsx";
        document.body.appendChild(a);
        a.click();
        a.remove();
        window.URL.revokeObjectURL(url);
      } else {
      }
    } catch (error) {}
  };
  const defaultValues = {
    campaignName: "",
    messageTemplate: null,
    campaignDesc: "",
    runTime: "RunNow",
    scheduledDate: null,
  };
  const handleImportExcelListCustomer = async () => {
    setIsLoadingImportFile(true);
    const data: IBodyImportExcelListUserCampaign = {
      file: file,
      CampaignId: campaignCreated.campaignId,
      ShopId: storeId,
      TemplateId: campaignCreated.templateId,
    };

    try {
      const res = await importFileExcelListUserCampaign(data);
      if (res && res?.status === 200) {
        reset(defaultValues);

        setFile(null);
        setIsOpenModalUploadFile(false);
        setOpen(false);
        setSelectedMessageTemplate(null);
        setListParam([]);
        setCampaignCreated(undefined);
        setIsLoadingImportFile(false);
        snackbar.success("Thêm mới dữ liệu thành công");

        fetchListCampaign();
      } else {
        setFile(null);
        setIsLoadingImportFile(false);
      }
    } catch (error) {
      snackbar.error("Có lỗi xảy ra khi import dữ liệu");
    }
  };

  const {
    control,
    handleSubmit,
    watch,
    formState: { errors },
    reset,
  } = useForm({
    resolver: yupResolver(campaignSchema),
    defaultValues: {
      campaignName: "",
      messageTemplate: null,
      campaignDesc: "",
      runTime: "RunNow",
      scheduledDate: null,
    },
  });

  const runTime = watch("runTime");

  const onSubmit = async (data) => {
    setIsLoading(true);
    const body: IBodyCreateCampaign = {
      campaignName: data.campaignName,
      description: data.campaignDesc,
      templateId: data.messageTemplate.znsId,
      shopId: storeId,
      status: data.runTime === "RunNow" ? "Draft" : "Scheduled",
      runTimeType: data.runTime,
      templateType: "ZNS",
      runTime:
        data.runTime !== "RunNow"
          ? dayjs(data.scheduledDate).format("YYYY-MM-DDTHH:mm:ss")
          : undefined,
    };
    try {
      const res = campaignCreated
        ? await updateCampaign({ ...body, campaignId: campaignCreated.campaignId })
        : await createCampaign(body);

      if (res && res?.status === 200) {
        setIsLoading(false);
        fetchListCampaign();
        setCampaignCreated(res?.data?.data);
        snackbar.success(
          campaignCreated ? "Cập nhật chiến dịch thành công" : "Thêm mới chiến dịch thành công"
        );
      } else {
        setIsLoading(false);
      }
    } catch (error) {}
  };
  const handleUpdateCampaign = () => {};

  return (
    <Box>
      <Dialog
        open={open}
        onClose={handleClose}
        maxWidth="lg"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 2,
            p: 1,
            maxHeight: "90vh",
          },
        }}
      >
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            px: 2,
            py: 1,
          }}
        >
          <Typography variant="h6" fontWeight="bold">
            Cấu hình chiến dịch
          </Typography>
          <Button
            variant="contained"
            onClick={handleSubmit(onSubmit)}
            sx={{
              bgcolor: "#2563eb",
              color: "white",
              "&:hover": {
                bgcolor: "#1d4ed8",
              },
            }}
            disabled={isLoading}
          >
            {campaignCreated ? "Cập nhật chiến dịch" : "Tạo chiến dịch"}
          </Button>
        </Box>

        <DialogContent sx={{ p: 2 }}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={7}>
              <Box sx={{ mb: 1.5 }}>
                <Typography variant="body1" fontWeight="bold" sx={{ display: "flex", mb: 1 }}>
                  Tên chiến dịch <Typography sx={{ color: "red", ml: 0.5 }}>*</Typography>
                </Typography>
                <Controller
                  name="campaignName"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      variant="outlined"
                      size="small"
                      error={!!errors.campaignName}
                      helperText={errors.campaignName?.message}
                    />
                  )}
                />
              </Box>

              <Box sx={{ mb: 1.5 }}>
                <Typography variant="body1" fontWeight="bold" sx={{ mb: 1, display: "flex" }}>
                  Mẫu tin nhắn <Typography sx={{ color: "red", ml: 0.5 }}>*</Typography>
                </Typography>
                <Controller
                  name="messageTemplate"
                  control={control}
                  render={({ field }) => (
                    <Autocomplete
                      {...field}
                      fullWidth
                      size="small"
                      options={listTemplateMessage}
                      getOptionLabel={(option: any) => option?.templateName || ""}
                      onChange={(_, newValue) => {
                        field.onChange(newValue);
                        setSelectedMessageTemplate(newValue);
                      }}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          placeholder="Chọn mẫu tin"
                          error={!!errors.messageTemplate}
                          helperText={errors.messageTemplate?.message}
                        />
                      )}
                      noOptionsText="Không có mẫu tin"
                      isOptionEqualToValue={(option, value) =>
                        option.templateId === value?.templateId
                      }
                    />
                  )}
                />
              </Box>

              <Box sx={{ mb: 1.5 }}>
                <Typography variant="body1" fontWeight="bold" sx={{ mb: 1 }}>
                  Mô tả chiến dịch
                </Typography>
                <Controller
                  name="campaignDesc"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      multiline
                      rows={4}
                      variant="outlined"
                      error={!!errors.campaignDesc}
                      helperText={errors.campaignDesc?.message}
                    />
                  )}
                />
              </Box>

              <Box sx={{ mb: 1.5 }}>
                <Typography variant="body1" fontWeight="bold" sx={{ mb: 1, display: "flex" }}>
                  Thời gian chạy <Typography sx={{ color: "red", ml: 0.5 }}>*</Typography>
                </Typography>
                <Controller
                  name="runTime"
                  control={control}
                  render={({ field }) => (
                    <RadioGroup {...field}>
                      <FormControlLabel value="RunNow" control={<Radio />} label="Ngay lập tức" />
                      <FormControlLabel
                        value="SpecificDateTime"
                        control={<Radio />}
                        label="Chọn ngày giờ"
                      />
                    </RadioGroup>
                  )}
                />

                {runTime === "SpecificDateTime" && (
                  <Controller
                    name="scheduledDate"
                    control={control}
                    render={({ field }) => (
                      <LocalizationProvider dateAdapter={AdapterDayjs} adapterLocale="vi">
                        <DateTimePicker
                          value={field.value}
                          onChange={field.onChange}
                          format="DD/MM/YYYY HH:mm:ss"
                          ampm={false} // Use 24-hour format
                          minDateTime={dayjs()}
                          slotProps={{
                            textField: {
                              error: !!errors.scheduledDate,
                              helperText: errors.scheduledDate?.message,
                              sx: {
                                width: "250px",
                                "& .MuiInputBase-root": {
                                  height: 36,
                                },
                              },
                            },
                          }}
                        />
                      </LocalizationProvider>
                    )}
                  />
                )}
              </Box>
              <Typography variant="body1" fontWeight="bold" sx={{ mb: 1, display: "flex" }}>
                Nhập danh sách khách hàng <Typography sx={{ color: "red", ml: 0.5 }}>*</Typography>
              </Typography>
              <Box
                sx={{
                  width: "100%",
                  display: "flex",
                  justifyContent: "center",
                  border: "1px solid #ddd",
                  borderRadius: 2,
                  cursor: campaignCreated ? "pointer" : "not-allowed",
                  padding: "25px 0",
                  opacity: campaignCreated ? 1 : 0.5,
                  backgroundColor: campaignCreated ? "transparent" : "#f5f5f5",
                  pointerEvents: campaignCreated ? "auto" : "none",
                }}
                onClick={() => campaignCreated && setIsOpenModalUploadFile(true)}
              >
                <Upload sx={{ color: campaignCreated ? "inherit" : "#9e9e9e" }} />
                <Typography
                  sx={{
                    ml: 1,
                    color: campaignCreated ? "inherit" : "#9e9e9e",
                  }}
                >
                  {campaignCreated ? "Tải lên" : "Vui lòng tạo chiến dịch trước"}
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} md={5}>
              <Box sx={{ bgcolor: "#f1f5f9", borderRadius: 2, height: "100%" }}>
                <iframe
                  src={selectedMessageTemplate?.previewUrl}
                  title="ZNS Preview"
                  width="100%"
                  height="100%"
                  style={{ border: "none" }}
                />{" "}
              </Box>
            </Grid>
          </Grid>
        </DialogContent>
      </Dialog>
      <ModalImportCustomer
        isOpenModalUploadFile={isOpenModalUploadFile}
        setIsOpenModalUploadFile={setIsOpenModalUploadFile}
        downloadTemplateFile={handleDownloadTemplateFileExcelCustomer}
        importListUserExcel={handleImportExcelListCustomer}
        campaignCreated={campaignCreated}
        listParam={listParam}
        file={file}
        setFile={setFile}
        isLoading={isLoadingImportFile}
      />
    </Box>
  );
}
