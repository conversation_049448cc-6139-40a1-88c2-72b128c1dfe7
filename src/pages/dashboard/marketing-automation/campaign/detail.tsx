import { paths } from "@/src/paths";
import {
  Box,
  Button,
  Checkbox,
  IconButton,
  InputAdornment,
  MenuItem,
  Modal,
  Paper,
  Select,
  Switch,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Typography,
} from "@mui/material";
import Link from "next/link";
import React, { useState } from "react";
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  NavigateBefore as NavigateBeforeIcon,
  NavigateNext as NavigateNextIcon,
  Search as SearchIcon,
  FileDownload as FileDownloadIcon,
  Label,
  IosShare,
} from "@mui/icons-material";
import ZaloAutomationPage from "..";
const DetailCampaign = ({ isOpenModalDetail, setIsOpenModalDetail }) => {
  const initialRows = [
    {
      id: 1,
      name: "Tuấn Evo",
      typeMessage: "UID",
      typeCompaign: "<PERSON><PERSON><PERSON> lần",
      startDate: "01-01-2025",
      endDate: "11-03-2025",
      status: false,
    },
    {
      id: 2,
      name: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
      typeMessage: "ZNS",
      typeCompaign: "<PERSON><PERSON><PERSON><PERSON> lần",
      startDate: "01-01-2025",
      endDate: "11-11-2025",
      status: true,
    },
    {
      id: 3,
      name: "Sơn Evo",
      typeMessage: "UID",
      typeCompaign: "Định kỳ",
      startDate: "01-02-2025",
      endDate: "11-11-2025",
      status: true,
    },
  ];
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [rows, setRows] = useState(initialRows);

  const tableContainerProps = {
    sx: {
      overflowX: "auto",
      maxWidth: "100%",
    },
  };

  const paginationBoxProps = {
    sx: {
      display: "flex",
      justifyContent: "flex-end",
      alignItems: "center",
      px: 2,
      py: 1.5,
      flexDirection: { xs: "column", sm: "row" },
      gap: 1,
    },
  };

  const searchFieldProps = {
    placeholder: "Tìm kiếm",
    variant: "outlined" as "outlined",
    size: "small" as "small",
    sx: {
      width: { xs: "100%", sm: 240 },
      "& .MuiOutlinedInput-root": {
        borderRadius: 30,
        backgroundColor: "#fff",
      },
    },
    InputProps: {
      startAdornment: (
        <InputAdornment position="start">
          <SearchIcon fontSize="small" />
        </InputAdornment>
      ),
    },
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleChangePage = (event, newPage: number) => {
    setPage(newPage);
  };
  return (
    <Modal
      open={isOpenModalDetail}
      onClose={() => setIsOpenModalDetail(false)}
      sx={{ overflowY: "scroll" }}
    >
      <Box
        sx={{
          position: "absolute",
          top: "60%",
          left: "50%",
          transform: "translate(-50%, -50%)",
          width: "70%",
          bgcolor: "background.paper",
          borderRadius: "8px",
          boxShadow: 24,
          p: 3,
        }}
      >
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            marginBottom: 2,
          }}
        >
          <Typography sx={{ fontSize: 22, fontWeight: 600 }}>Chi tiết chiến dịch</Typography>
          <Box>
            <TextField {...searchFieldProps} />
            <Button>
              <IosShare />
              <Typography>Export</Typography>
            </Button>
          </Box>
        </Box>
        <Paper elevation={3} sx={{ borderRadius: 2 }}>
          <TableContainer {...tableContainerProps}>
            <Table stickyHeader>
              <TableHead>
                <TableRow>
                  <TableCell>ID</TableCell>
                  <TableCell>OA</TableCell>
                  <TableCell>SĐT người nhận</TableCell>
                  <TableCell>Mẫu ZNS ID</TableCell>
                  <TableCell>Tên mẫu ZNS</TableCell>
                  <TableCell>Ngày tạo</TableCell>
                  <TableCell>Đơn giá</TableCell>
                  <TableCell>Trạng thái</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {rows.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage).map((row) => {
                  // const isItemSelected = isSelected(row.id);
                  return (
                    <TableRow hover role="checkbox" tabIndex={-1} key={row.id}>
                      <TableCell component="th" scope="row" sx={{ color: "#1a73e8" }}>
                        {row.name}
                      </TableCell>
                      <TableCell>{row.typeMessage}</TableCell>
                      <TableCell>{row.typeCompaign}</TableCell>
                      <TableCell>{row.startDate}</TableCell>
                      <TableCell>{row.endDate}</TableCell>
                      <TableCell>{row.endDate}</TableCell>
                      <TableCell>{row.endDate}</TableCell>
                      <TableCell>{row.endDate}</TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </TableContainer>
        </Paper>
        <Box {...paginationBoxProps}>
          <Box sx={{ display: "flex", alignItems: "center", mr: { sm: 2 }, gap: 1 }}>
            <Typography variant="body2" sx={{ mr: 2 }}>
              Số dòng mỗi trang
            </Typography>
            <Select
              value={rowsPerPage}
              onChange={handleChangeRowsPerPage}
              size="small"
              sx={{
                minWidth: 60,
                "& .MuiOutlinedInput-notchedOutline": { border: "none" },
                "& .MuiSelect-select": { padding: "4px 8px" },
              }}
            >
              <MenuItem value={10}>10</MenuItem>
              <MenuItem value={25}>25</MenuItem>
              <MenuItem value={50}>50</MenuItem>
            </Select>
            <Typography variant="body2">
              {`${page * rowsPerPage + 1}–${Math.min((page + 1) * rowsPerPage, rows.length)} của ${
                rows.length
              }`}
            </Typography>
          </Box>
          <Box sx={{ display: "flex", alignItems: "center" }}>
            <IconButton disabled={page === 0} onClick={() => handleChangePage(null, page - 1)}>
              <NavigateBeforeIcon />
            </IconButton>
            <IconButton
              disabled={page >= Math.ceil(rows.length / rowsPerPage) - 1}
              onClick={() => handleChangePage(null, page + 1)}
            >
              <NavigateNextIcon />
            </IconButton>
          </Box>
        </Box>
      </Box>
    </Modal>
  );
};

export default DetailCampaign;
