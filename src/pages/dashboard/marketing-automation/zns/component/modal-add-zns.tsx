import React, { useState } from "react";
import {
  Add,
  CheckBox,
  Delete,
  Edit,
  Search,
  Upload,
  UploadFile,
  NavigateBefore as NavigateBeforeIcon,
  NavigateNext as NavigateNextIcon,
} from "@mui/icons-material";
import {
  Box,
  Button,
  Checkbox,
  FormControl,
  FormControlLabel,
  FormHelperText,
  FormLabel,
  Grid,
  IconButton,
  InputAdornment,
  InputLabel,
  MenuItem,
  Modal,
  Paper,
  Radio,
  RadioGroup,
  Select,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Typography,
} from "@mui/material";
import * as Yup from "yup";
import { useFormik } from "formik";
import dayjs from "dayjs";
import { useZaloAutomation } from "@/src/api/hooks/zalo-automation/zalo-automation";
import { IBodyTemplateZns } from "@/src/api/services/zalo-automation/zalo-automation.service";
import { useStoreId } from "@/src/hooks/use-store-id";
import useSnackbar from "@/src/hooks/use-snackbar";
interface FormValues {
  templateId: string;
  templateType: string;
  submit: string | null;
}

const ModalAddZns = ({ isOpenModalCreate, setIsOpenModalCreate, fetchListTemplateZns }) => {
  const { createTemplateZns } = useZaloAutomation();
  const snackbar = useSnackbar();
  const shopId = useStoreId();
  const validationSchema = Yup.object({
    templateId: Yup.string().required("Template Id không được để trống"),
    templateType: Yup.string().required("Hãy chọn loại mẫu"),
  });

  const formik = useFormik<FormValues>({
    initialValues: {
      // oaId: "",
      templateId: "",
      templateType: "",
      submit: null,
    },
    validationSchema,
    validateOnBlur: true,
    validateOnChange: true,
    onSubmit: async (values, { setSubmitting, setErrors }) => {
      try {
        const data: IBodyTemplateZns = {
          shopId: shopId,
          templateId: values.templateId,
          templateType: values.templateType,
        };
        const res = await createTemplateZns(data);
        if (res && res?.status === 200) {
          snackbar.success("Thêm mới thành công");
          fetchListTemplateZns();
        } else {
          snackbar.error("Thêm mới thất bại");
        }
        setIsOpenModalCreate(false);
      } catch (err) {
        setErrors({ submit: "Có lỗi xảy ra" });
      } finally {
        setSubmitting(false);
      }
    },
  });

  return (
    <>
      <Modal
        open={isOpenModalCreate}
        onClose={() => setIsOpenModalCreate(false)}
        aria-labelledby="template-modal-title"
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        <Box
          sx={{
            backgroundColor: "white",
            borderRadius: "12px",
            boxShadow: "0px 4px 20px rgba(0, 0, 0, 0.1)",
            width: "100%",
            maxWidth: "550px",
            outline: "none",
            overflow: "hidden",
          }}
        >
          <Box
            sx={{
              p: 4,
            }}
          >
            <form noValidate onSubmit={formik.handleSubmit}>
              <Typography
                id="template-modal-title"
                // variant="h5"
                // component="h2"
                sx={{
                  fontWeight: 500,
                  fontSize: "18px",
                  color: "#111827",
                  mb: 2,
                }}
              >
                Thêm Template
              </Typography>

              <Box sx={{ mb: 1 }}>
                <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
                  <Typography
                    sx={{
                      fontSize: "0.9rem",
                      fontWeight: 500,
                      color: "#374151",
                    }}
                  >
                    Template ID
                  </Typography>
                  <Typography sx={{ color: "#EF4444", ml: 0.5 }}>*</Typography>
                </Box>

                <TextField
                  placeholder="Nhập Template Id"
                  fullWidth
                  name="templateId"
                  error={!!(formik.touched.templateId && formik.errors.templateId)}
                  helperText={formik.touched.templateId && formik.errors.templateId}
                  onBlur={formik.handleBlur}
                  onChange={formik.handleChange}
                  value={formik.values.templateId}
                  size="small"
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      borderRadius: "8px",
                      "& fieldset": {
                        borderColor: "#E5E7EB",
                      },
                      "&:hover fieldset": {
                        borderColor: "#D1D5DB",
                      },
                      "&.Mui-focused fieldset": {
                        borderColor: "#6366F1",
                      },
                      "& input": {
                        padding: "8px 14px",
                      },
                    },
                    "& .MuiFormHelperText-root.Mui-error": {
                      color: "#EF4444",
                    },
                  }}
                />
              </Box>

              <Box sx={{ mb: 1 }}>
                <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
                  <Typography
                    sx={{
                      fontSize: "0.9rem",
                      fontWeight: 500,
                      color: "#374151",
                    }}
                  >
                    Loại mẫu
                  </Typography>
                  <Typography sx={{ color: "#EF4444", ml: 0.5 }}>*</Typography>
                </Box>

                <Select
                  fullWidth
                  name="templateType"
                  displayEmpty
                  error={!!(formik.touched.templateType && formik.errors.templateType)}
                  onBlur={formik.handleBlur}
                  onChange={formik.handleChange}
                  value={formik.values.templateType}
                  renderValue={(selected) => {
                    if (!selected) {
                      return <Typography sx={{ color: "#9CA3AF" }}>Chọn loại mẫu</Typography>;
                    }

                    const options = {
                      Unknown: "Không xác định",
                      Text: "Văn bản",
                      OTP: "OTP",
                      Table: "Bảng",
                      Rating: "Đánh giá",
                    };

                    return options[selected] || selected;
                  }}
                  sx={{
                    borderRadius: "8px",
                    height: "40px",
                    "& .MuiOutlinedInput-notchedOutline": {
                      borderColor: "#E5E7EB",
                    },
                    "&:hover .MuiOutlinedInput-notchedOutline": {
                      borderColor: "#D1D5DB",
                    },
                    "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                      borderColor: "#6366F1",
                    },
                    "& .MuiSelect-select": {
                      padding: "8px 14px",
                    },
                  }}
                  MenuProps={{
                    PaperProps: {
                      sx: {
                        borderRadius: "8px",
                        boxShadow: "0px 4px 20px rgba(0, 0, 0, 0.1)",
                      },
                    },
                  }}
                >
                  <MenuItem value="Unknown">Không xác định</MenuItem>
                  <MenuItem value="Text">Văn bản</MenuItem>
                  <MenuItem value="OTP">OTP</MenuItem>
                  <MenuItem value="Table">Bảng</MenuItem>
                  <MenuItem value="Rating">Đánh giá</MenuItem>
                </Select>

                {formik.touched.templateType && formik.errors.templateType && (
                  <FormHelperText error sx={{ color: "#EF4444", ml: 1.5 }}>
                    {formik.errors.templateType}
                  </FormHelperText>
                )}
              </Box>

              <Box
                sx={{
                  display: "flex",
                  justifyContent: "flex-end",
                  gap: 2,
                  mt: 2,
                }}
              >
                <Button
                  variant="outlined"
                  onClick={() => setIsOpenModalCreate(false)}
                  sx={{
                    borderRadius: "8px",
                    padding: "0px 24px",
                    textTransform: "uppercase",
                    fontWeight: 600,
                    borderColor: "#E5E7EB",
                    color: "#6B7280",
                    "&:hover": {
                      borderColor: "#D1D5DB",
                      backgroundColor: "#F9FAFB",
                    },
                  }}
                >
                  Huỷ
                </Button>

                <Button
                  type="submit"
                  variant="contained"
                  disabled={formik.isSubmitting}
                  sx={{
                    borderRadius: "8px",
                    padding: "10px 24px",
                    textTransform: "uppercase",
                    fontWeight: 600,
                    backgroundColor: "#6366F1",
                    "&:hover": {
                      backgroundColor: "#4F46E5",
                    },
                    "&.Mui-disabled": {
                      backgroundColor: "#A5B4FC",
                      color: "white",
                    },
                  }}
                >
                  Thêm
                </Button>
              </Box>
            </form>
          </Box>
        </Box>
      </Modal>
    </>
  );
};

export default ModalAddZns;
