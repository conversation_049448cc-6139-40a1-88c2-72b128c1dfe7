import React, { useCallback, useEffect, useState } from "react";
import {
  Box,
  Button,
  Typography,
  InputAdornment,
  TextField,
  Modal,
  List,
  ListItem,
  ListItemText,
  Checkbox,
  Stack,
  IconButton,
} from "@mui/material";
import TableCommissionGroups from "./TableCommissionGroups";
import { CommissionsConfigData, UserGroupCommissionConfig } from "@/src/api/types/affiliation.type";
import { useUser } from "@/src/api/hooks/user/use-user";
import { useStoreId } from "@/src/hooks/use-store-id";

type CommissionGroupsProps = {
  onBack: () => void;
  commissionsConfigData?: CommissionsConfigData; // Kiểu dữ liệu từ interface đã tạo trước
  saveUserGroupCommissionsConfigs: (userGroup) => void;
};

const CommissionGroups: React.FC<CommissionGroupsProps> = ({
  onBack,
  saveUserGroupCommissionsConfigs,
  commissionsConfigData,
}) => {
  const [isAddingGroup, setIsAddingGroup] = useState(false);
  const [groups, setGroups] = useState<UserGroupCommissionConfig[]>([]);
  const [newGroup, setNewGroup] = useState({
    name: "",
    members: [],
    commissionLevel1: "",
    commissionLevel2: "",
  });

  const availableMembers = ["F2 Henry", "F1 Henry", "Tuấn Ngọc", "Thành viên 4"];

  useEffect(() => {
    if (
      commissionsConfigData &&
      commissionsConfigData.basicCommissionsConfig?.userGroupCommissionsConfigs?.length > 0
    ) {
      setGroups(commissionsConfigData.basicCommissionsConfig?.userGroupCommissionsConfigs);
    }
  }, [commissionsConfigData]);

  const handleSaveGroup = (userGroupConfigs) => {
    saveUserGroupCommissionsConfigs(userGroupConfigs);
  };

  return (
    <Box sx={{ width: "100%", padding: "70px 15px" }}>
      <Box>
        <TableCommissionGroups onBack={onBack} groups={groups} handleSaveGroup={handleSaveGroup} />
      </Box>
    </Box>
  );
};

export default CommissionGroups;
