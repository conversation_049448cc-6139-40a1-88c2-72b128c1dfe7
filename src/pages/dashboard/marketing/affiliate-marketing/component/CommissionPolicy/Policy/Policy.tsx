import React, { createContext, useContext, useEffect, useState } from "react";
import {
  Box,
  Paper,
  Typography,
  Button,
  Switch,
  Dialog,
  DialogTitle,
  DialogContent,
  FormControlLabel,
  Checkbox,
  DialogActions,
} from "@mui/material";
import { styled } from "@mui/material/styles";
import SetupPopup from "./PopupPolicy/SetupPopup";
import CommissionGroups from "./CommisssionGroup/CommissionGroups";
import ProductCommission from "./ProductCommission/ProductCommission";
import { useStoreId } from "@/src/hooks/use-store-id";
import { useAffiliation } from "@/src/api/hooks/affiliation/use-affiliation";
import { CommissionsConfigData } from "@/src/api/types/affiliation.type";
import useSnackbar from "@/src/hooks/use-snackbar";
import { AffiliatePolicyContext, initConfigPolicyData } from "../CommissionPolicy";

const CustomSwitch = styled(Switch)(() => ({
  "& .MuiSwitch-switchBase.Mui-checked": {
    color: "#2654FE",
  },
  "& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track": {
    backgroundColor: "#2654FE",
  },
  "& .MuiSwitch-track": {
    backgroundColor: "#ddd",
    borderRadius: 20,
  },
}));

const Policy = () => {
  const [isEnabled, setIsEnabled] = useState(true);
  const [openPopup, setOpenPopup] = useState(false);
  const [popupType, setPopupType] = useState("");
  const [showCommissionGroups, setShowCommissionGroups] = useState(false);
  const [showProductCommission, setShowProductCommission] = useState(false);
  const storeId = useStoreId();
  const { getCommissionsConfig, updateCommissionsConfig, updateLevelTwoStatus } = useAffiliation();
  const { commissionsConfigData, setCommissionsConfigData } = useContext(AffiliatePolicyContext);
  const [openConfirm, setOpenConfirm] = useState(false);
  const [checked, setChecked] = useState(false);
  const snackbar = useSnackbar();

  const handleOpenPopup = (type) => {
    setPopupType(type);
    setOpenPopup(true);
  };

  const handleClosePopup = () => {
    setOpenPopup(false);
    setPopupType("");
  };

  const handleShowCommissionGroups = () => {
    setShowCommissionGroups(true);
  };

  const handleBackToPolicy = () => {
    setShowCommissionGroups(false);
    setShowProductCommission(false);
  };
  const handleShowProductCommission = () => {
    setShowProductCommission(true);
  };

  const fetchCommissionsConfig = async (shopId) => {
    const response = await getCommissionsConfig({ shopId });
    if (response?.data) {
      const { commissionsConfigId, shopId, ...filteredData } = response.data.data; // Loại bỏ field
      setCommissionsConfigData(filteredData);
    }
  };

  const saveCommissionsConfig = async (configData: CommissionsConfigData) => {
    const response = await updateCommissionsConfig({ shopId: storeId }, configData);
    if (response?.data) {
      snackbar.success("Cập nhật thông tin thành công");
      fetchCommissionsConfig(storeId);
    }
  };

  const handleSaveLevelCommission = async (levelOnePercent, levelTwoPercent) => {
    let levelTwoValue = levelTwoPercent;
    if (!commissionsConfigData.basicCommissionsConfig.isActiveLevelTwo) {
      levelTwoValue = null;
    }
    const newData: CommissionsConfigData = {
      ...commissionsConfigData,
      basicCommissionsConfig: {
        ...commissionsConfigData.basicCommissionsConfig,
        levelOneCommissionPercentage: levelOnePercent,
        levelTwoCommissionPercentage: levelTwoValue,
      },
    };
    await saveCommissionsConfig(newData);
    handleClosePopup();
  };

  const handleSaveUserGroupCommissionsConfigs = async (userGroupCommissionsConfigs) => {
    const newData: CommissionsConfigData = {
      ...commissionsConfigData,
      basicCommissionsConfig: {
        ...commissionsConfigData.basicCommissionsConfig,
        userGroupCommissionsConfigs: userGroupCommissionsConfigs,
      },
    };
    await saveCommissionsConfig(newData);
    // await saveCommissionsConfig(newData);
    // handleClosePopup();
  };

  useEffect(() => {
    if (storeId) {
      fetchCommissionsConfig(storeId);
    }
  }, [storeId]);

  const handleSaveItemsGroup = async (itemGroupCommissionsConfigs) => {
    const newData: CommissionsConfigData = {
      ...commissionsConfigData,
      basicCommissionsConfig: {
        ...commissionsConfigData.basicCommissionsConfig,
        itemGroupCommissionsConfigs: itemGroupCommissionsConfigs,
      },
    };
    await saveCommissionsConfig(newData);
  };

  const changeActiveLevelTwo = async (event) => {
    const isActiveLevelTwo = event.target.checked;
    if (
      isActiveLevelTwo &&
      commissionsConfigData.advancedCommissionsConfig.isSelfReferralCommission
    ) {
      setOpenConfirm(true);
    } else {
      let newData = {
        ...commissionsConfigData,
        basicCommissionsConfig: {
          ...commissionsConfigData.basicCommissionsConfig,
          isActiveLevelTwo,
          levelTwoCommissionPercentage: 0,
        },
      };

      if (!isActiveLevelTwo) {
        // Nếu không hoạt động, set levelTwoCommissionPercentage = null cho cả cấp cơ bản và userGroup
        newData.basicCommissionsConfig.levelTwoCommissionPercentage = null;

        // Cập nhật các userGroupCommissionsConfigs
        newData.basicCommissionsConfig.userGroupCommissionsConfigs =
          newData.basicCommissionsConfig.userGroupCommissionsConfigs.map((group) => ({
            ...group,
            levelTwoCommissionPercentage: null, // Set null cho levelTwo của mỗi userGroup
          }));

        newData.basicCommissionsConfig.itemGroupCommissionsConfigs =
          newData.basicCommissionsConfig.itemGroupCommissionsConfigs.map((group) => ({
            ...group,
            levelTwoCommissionPercentage: null, // Set null cho levelTwo của mỗi userGroup
          }));
      }
      await saveCommissionsConfig(newData);
    }
  };

  const handleConfirm = async () => {
    let newData = {
      ...commissionsConfigData,
      basicCommissionsConfig: {
        ...commissionsConfigData.basicCommissionsConfig,
        isActiveLevelTwo: true,
        levelTwoCommissionPercentage: 0,
      },
    };

    await saveCommissionsConfig(newData);

    setOpenConfirm(false);
    setChecked(false);
  };

  const handleCancel = () => {
    setOpenConfirm(false);
    setChecked(false);
  };

  return (
    <Box mt={2} sx={{ boxShadow: "none" }}>
      {showCommissionGroups ? (
        <CommissionGroups
          onBack={handleBackToPolicy}
          saveUserGroupCommissionsConfigs={handleSaveUserGroupCommissionsConfigs}
          commissionsConfigData={commissionsConfigData}
        />
      ) : showProductCommission ? (
        <ProductCommission onBack={handleBackToPolicy} submitItemsGroup={handleSaveItemsGroup} />
      ) : (
        <Box>
          <Paper
            sx={{
              p: 2,
              mb: 2,
              boxShadow: "none",
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <Box width={"55%"}>
              <Typography fontWeight={700}>Chính sách hoa hồng mặc định</Typography>
              <Typography variant="body2" color="textSecondary">
                Đặt mức hoa hồng phù hợp với ngân sách tiếp thị của bạn, có thể hỗ trợ hoa hồng cấp
                2
              </Typography>
            </Box>
            <Box
              display="flex"
              justifyContent="end"
              alignItems="center"
              gap={2}
              mt={1}
              width={"43%"}
              sx={{
                "@media (max-width: 767px)": {
                  display: "flex",
                  flexDirection: "column",
                  alignItems: "end",
                },
              }}
            >
              <Typography
                sx={{
                  color: "#787878",
                  fontSize: "14px",
                  fontWeight: "500",
                  "@media (max-width: 767px)": { textAlign: "right" },
                }}
              >
                Hoa hồng hiện tại:{" "}
                {commissionsConfigData?.basicCommissionsConfig?.levelOneCommissionPercentage}%
              </Typography>
              <Button
                variant="contained"
                sx={{
                  bgcolor: "#2654FE",
                  color: "#fff",
                  fontWeight: "400",
                  fontSize: "14px",
                  textTransform: "none",
                  borderRadius: "10px",
                }}
                onClick={() => handleOpenPopup("Chính sách hoa hồng mặc định")}
              >
                Thiết lập
              </Button>
            </Box>
          </Paper>

          <Paper
            sx={{
              p: 2,
              mb: 2,
              boxShadow: "none",
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <Box width={"55%"}>
              <Typography fontWeight={700}>Hoa hồng theo nhóm đối tác</Typography>
              <Typography variant="body2" color="textSecondary">
                Phân các đối tác thành các nhóm khác nhau và cung cấp mức hoa hồng riêng cho từng
                đối tác
              </Typography>
            </Box>
            <Box
              display="flex"
              justifyContent="end"
              alignItems="center"
              gap={2}
              mt={1}
              width={"43%"}
            >
              <Button
                variant="contained"
                sx={{
                  bgcolor: "#2654FE",
                  color: "#fff",
                  fontWeight: "400",
                  fontSize: "14px",
                  textTransform: "none",
                  borderRadius: "10px",
                }}
                onClick={handleShowCommissionGroups} // Hiển thị CommissionGroups
              >
                Thiết lập
              </Button>
            </Box>
          </Paper>

          <Paper
            sx={{
              p: 2,
              mb: 2,
              boxShadow: "none",
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <Box width={"55%"}>
              <Typography fontWeight={700}>Hoa hồng riêng cho từng sản phẩm</Typography>
              <Typography variant="body2" color="textSecondary">
                Đặt mức hoa hồng khác nhau cho các sản phẩm có mức hoa hồng riêng, điều này chỉ áp
                dụng đối với các sản phẩm được chọn các sản phẩm khác áp dụng theo cơ chế chung
              </Typography>
            </Box>
            <Box
              display="flex"
              justifyContent="end"
              alignItems="center"
              gap={2}
              mt={1}
              width={"43%"}
            >
              <Button
                variant="contained"
                sx={{
                  bgcolor: "#2654FE",
                  color: "#fff",
                  fontWeight: "400",
                  fontSize: "14px",
                  textTransform: "none",
                  borderRadius: "10px",
                }}
                onClick={handleShowProductCommission}
              >
                Thiết lập
              </Button>
            </Box>
          </Paper>

          <Paper
            sx={{
              p: 2,
              boxShadow: "none",
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <Box width={"55%"}>
              <Typography fontWeight={700}>Hoa hồng cấp 2</Typography>
              <Typography variant="body2" color="textSecondary">
                Sau khi kích hoạt, các đối tác có thể phát triển thêm các đối tác cấp 2 và hưởng hoa
                hồng cấp 2 theo chính sách thiết lập
              </Typography>
            </Box>
            <Box
              display="flex"
              justifyContent="end"
              alignItems="center"
              gap={2}
              mt={1}
              width={"43%"}
            >
              <CustomSwitch
                checked={commissionsConfigData.basicCommissionsConfig.isActiveLevelTwo}
                onChange={changeActiveLevelTwo}
              />

              <Dialog open={openConfirm} onClose={handleCancel} fullWidth maxWidth="sm">
                <DialogTitle>Cho phép hoa hồng tự giới thiệu</DialogTitle>

                <DialogContent dividers>
                  <Typography>
                    Việc kích hoạt tính năng này sẽ khiến mô hình kinh doanh của bạn được quy vào
                    hình thức kinh doanh đa cấp theo quy định pháp luật. Bạn có trách nhiệm đăng ký
                    hoạt động với Bộ Công Thương nếu áp dụng.
                  </Typography>
                  <br />
                  <Typography>
                    Bằng việc sử dụng tính năng này, bạn xác nhận rằng Evotech sẽ không chịu bất kỳ
                    trách nhiệm nào liên quan đến hoạt động kinh doanh của bạn.
                  </Typography>

                  <FormControlLabel
                    control={
                      <Checkbox checked={checked} onChange={(e) => setChecked(e.target.checked)} />
                    }
                    label="Tôi đồng ý với điều khoản sử dụng"
                  />
                </DialogContent>

                <DialogActions sx={{ padding: 2 }}>
                  <Button onClick={handleCancel}>Hủy</Button>
                  <Button variant="contained" onClick={handleConfirm} disabled={!checked}>
                    Đồng ý
                  </Button>
                </DialogActions>
              </Dialog>
            </Box>
          </Paper>

          <SetupPopup
            title={popupType}
            open={openPopup}
            onClose={handleClosePopup}
            saveLevelCommission={handleSaveLevelCommission}
          />
        </Box>
      )}
    </Box>
  );
};

export default Policy;
