import React, { useContext, useEffect, useState } from "react";
import { Box, Typography, TextField, Paper, Avatar, Link, Button } from "@mui/material";
import { styled } from "@mui/material/styles";
import { Dialog, DialogTitle, DialogContent } from "@mui/material";
import { CommissionsConfigData } from "@/src/api/types/affiliation.type";
import { AffiliatePolicyContext } from "../../CommissionPolicy";

const CustomBox = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  borderRadius: 12,
  boxShadow: "0 4px 10px rgba(0,0,0,0.1)",
}));

const ExampleBox = styled(Box)(({ theme }) => ({
  padding: theme.spacing(2),
  borderRadius: 12,
  background: "#F9F9F9",
  display: "flex",
  flexDirection: "column",
  gap: theme.spacing(2),
}));

const Row = styled(Box)({
  display: "flex",
  alignItems: "center",
  gap: 10,
});

type SetupPopupProps = {
  title: string;
  open: boolean;
  onClose: () => void;
  saveLevelCommission: (level1, level2) => void;
};

const SetupPopup: React.FC<SetupPopupProps> = ({ title, open, onClose, saveLevelCommission }) => {
  const { commissionsConfigData } = useContext(AffiliatePolicyContext);
  const [level1, setLevel1] = useState(
    commissionsConfigData?.basicCommissionsConfig?.levelOneCommissionPercentage
  );
  const [level2, setLevel2] = useState(
    commissionsConfigData?.basicCommissionsConfig?.levelTwoCommissionPercentage
  );

  useEffect(() => {
    if (commissionsConfigData) {
      setLevel1(commissionsConfigData?.basicCommissionsConfig?.levelOneCommissionPercentage);
      setLevel2(commissionsConfigData?.basicCommissionsConfig?.levelTwoCommissionPercentage);
    }
  }, [commissionsConfigData]);

  const totalCommission = ((level1 / 100) * 100000 + (level2 / 100) * 100000).toLocaleString();

  return (
    <Dialog open={open} onClose={onClose} maxWidth="xl" fullWidth>
      <DialogTitle>{title}</DialogTitle>
      <DialogContent>
        <Box sx={{ width: "100%" }}>
          <Box
            display="flex"
            gap={4}
            marginTop={"30px"}
            alignItems={"start"}
            sx={{ "@media (max-width: 1180px)": { flexDirection: "column" } }}
          >
            <CustomBox
              sx={{
                width: "70%",
                boxShadow: "none",
                padding: "0",
                "@media (max-width: 1180px)": { width: "100%" },
              }}
            >
              <Typography
                sx={{ color: "#000", fontSize: "20px", fontWeight: "700", marginBottom: "10px" }}
              >
                Thiết lập chính sách hoa hồng
              </Typography>
              <Typography variant="body2" color="textSecondary" mb={2}>
                Đặt mức hoa hồng phù hợp với ngân sách tiếp thị của bạn, có thể hỗ trợ hoa hồng cấp
                2
              </Typography>

              <Box
                sx={{
                  border: "1px solid #D8D8D8",
                  borderRadius: "5px",
                  padding: "30px 15px",
                }}
              >
                <Row
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "space-between",
                    marginBottom: commissionsConfigData.basicCommissionsConfig.isActiveLevelTwo
                      ? "30px"
                      : "none",
                    padding: "0 20px 30px 20px",
                    borderBottom: commissionsConfigData.basicCommissionsConfig.isActiveLevelTwo
                      ? "1px solid #D8D8D8"
                      : "none",
                    "@media(max-width: 440px)": {
                      padding: "0 0 30px 0",
                    },
                  }}
                >
                  <Typography sx={{ fontSize: "16px", fontWeight: "700", color: "#000" }}>
                    Hoa hồng bậc 1
                  </Typography>
                  <TextField
                    value={level1}
                    onChange={(e) => {
                      const value = Number(e.target.value);
                      if (value >= 0 && value <= 100) {
                        setLevel1(value);
                      } else if (e.target.value === "") {
                        setLevel1(0);
                      }
                    }}
                    type="number"
                    size="small"
                    sx={{
                      width: "20%",
                      padding: "0px !important",
                      overflow: "hidden",
                      border: "1px solid #D8D8D8",
                      borderRadius: "10px",
                      "@media (max-width: 880px)": { width: "30%" },
                      "& .MuiInputBase-root": { padding: 0, overflow: "hidden" },
                      "& input": { padding: "0 0 0 10px !important", overflow: "hidden" },
                    }}
                    InputProps={{
                      endAdornment: (
                        <Typography
                          sx={{
                            background: "#efefef",
                            width: "20%",
                            padding: "5px 10px",
                            height: "50px",
                            display: "flex",
                            alignItems: "center",
                            "@media (max-width: 880px)": { width: "50%" },
                          }}
                        >
                          %
                        </Typography>
                      ),
                    }}
                    inputProps={{
                      min: 0,
                      max: 100,
                      step: 1,
                    }}
                  />
                </Row>
                {commissionsConfigData.basicCommissionsConfig.isActiveLevelTwo && (
                  <Row
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "space-between",
                      padding: "0 20px",
                      "@media(max-width: 440px)": {
                        padding: "0 0 30px 0",
                      },
                    }}
                  >
                    <Typography sx={{ fontSize: "16px", fontWeight: "700", color: "#000" }}>
                      Hoa hồng bậc 2
                    </Typography>
                    <TextField
                      value={level2}
                      onChange={(e) => {
                        const value = Number(e.target.value);
                        if (value >= 0 && value <= 100) {
                          setLevel2(value);
                        } else if (e.target.value === "") {
                          setLevel2(0);
                        }
                      }}
                      type="number"
                      size="small"
                      sx={{
                        width: "20%",
                        padding: "0px !important",
                        overflow: "hidden",
                        border: "1px solid #D8D8D8",
                        borderRadius: "10px",
                        "@media (max-width: 880px)": { width: "30%" },
                        "& .MuiInputBase-root": { padding: 0, overflow: "hidden" },
                        "& input": { padding: "0 0 0 10px !important", overflow: "hidden" },
                      }}
                      InputProps={{
                        endAdornment: (
                          <Typography
                            sx={{
                              background: "#efefef",
                              width: "20%",
                              padding: "5px 10px",
                              height: "50px",
                              display: "flex",
                              alignItems: "center",

                              "@media (max-width: 880px)": { width: "50%" },
                            }}
                          >
                            %
                          </Typography>
                        ),
                      }}
                      inputProps={{
                        min: 0,
                        max: 100,
                        step: 1,
                      }}
                    />
                  </Row>
                )}
              </Box>

              <Box sx={{ display: "flex", justifyContent: "flex-end", marginTop: 2 }}>
                <Button
                  variant="contained"
                  sx={{
                    bgcolor: "#2654FE",
                    color: "#fff",
                    fontWeight: "400",
                    fontSize: "14px",
                    textTransform: "none",
                    borderRadius: "10px",
                  }}
                  onClick={() => saveLevelCommission(level1, level2)}
                >
                  Lưu
                </Button>
              </Box>
            </CustomBox>

            <ExampleBox
              flex={1}
              sx={{
                width: "30%",
                background: "#fff",
                "@media (max-width: 1180px)": { width: "100%" },
              }}
            >
              <Typography
                variant="h6"
                fontWeight="400"
                sx={{
                  fontSize: "20px",
                  color: "#2654FE",
                  padding: "10px 10px",
                  background: "#F7F8FA",
                  borderRadius: "10px",
                  width: "fit-content",
                }}
              >
                Ví dụ minh họa
              </Typography>
              <Box sx={{ display: "flex", flexDirection: "column", gap: "10px" }}>
                <Row sx={{ display: "flex", alignItems: "start", justifyContent: "start" }}>
                  <Box
                    sx={{
                      width: "20%",
                      display: "flex",
                      alignItems: "center",
                      gap: "10px",
                      flexDirection: "column",
                    }}
                  >
                    <Avatar sx={{ width: 32, height: 32 }} src="https://i.pravatar.cc/32?img=1" />
                    <Typography
                      variant="body2"
                      sx={{
                        fontSize: "14px",
                        fontWeight: "400",
                        color: "#000",
                        textAlign: "center",
                      }}
                    >
                      Người tiêu dùng
                    </Typography>
                    <Box>
                      <svg
                        width="20"
                        height="35"
                        viewBox="0 0 24 48"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <polygon points="12,0 0,12 24,12" fill="#2654FE" />{" "}
                        <line x1="12" y1="14" x2="12" y2="46" stroke="#A0A0A0" />{" "}
                      </svg>
                    </Box>
                  </Box>
                  <Box
                    sx={{
                      width: "80%",
                      background: "#F7F8FA",
                      padding: "10px",
                      borderRadius: "15px 15px 15px 0px",
                    }}
                  >
                    <Typography
                      variant="body2"
                      sx={{
                        fontSize: "14px",
                        fontWeight: "400",
                        color: "#000",
                      }}
                    >
                      Sau khi bạn mua sắm số tiền thực tế bạn trả khi đã trừ chiết khấu, phiếu giảm
                      giá, phí vận chuyển là 100.000đ
                    </Typography>
                  </Box>
                </Row>

                <Row sx={{ display: "flex", alignItems: "start", justifyContent: "start" }}>
                  <Box
                    sx={{
                      width: "20%",
                      display: "flex",
                      alignItems: "center",
                      gap: "10px",
                      flexDirection: "column",
                    }}
                  >
                    <Avatar sx={{ width: 28, height: 28 }} src="https://i.pravatar.cc/28?img=2" />
                    <Typography variant="body2" textAlign={"center"}>
                      Cấp độ 1:
                    </Typography>
                    {commissionsConfigData.basicCommissionsConfig.isActiveLevelTwo && (
                      <Box>
                        <svg
                          width="20"
                          height="35"
                          viewBox="0 0 24 48"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <polygon points="12,0 0,12 24,12" fill="#2654FE" />{" "}
                          <line x1="12" y1="14" x2="12" y2="46" stroke="#A0A0A0" />{" "}
                        </svg>
                      </Box>
                    )}
                  </Box>
                  <Box
                    sx={{
                      width: "fit-content",
                      background: "#F7F8FA",
                      padding: "10px",
                      borderRadius: "15px 15px 15px 0px",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "space-between",
                    }}
                  >
                    <Typography
                      variant="body2"
                      sx={{
                        display: "flex",
                        alignItems: "center",
                        gap: "10px",
                        paddingRight: "15px",
                        borderRight: "1px solid #D9D9D9",
                      }}
                    >
                      Cấp độ 1:{" "}
                      <Typography
                        variant="body2"
                        sx={{ fontSize: "14px", fontWeight: "400", color: "#000" }}
                      >
                        {level1}%
                      </Typography>
                    </Typography>
                    <Typography
                      variant="body2"
                      sx={{
                        marginLeft: "10px",
                        color: "#2654FE",
                        borderRadius: "50px",
                        padding: "5px 10px",
                        background: "#2654FE1A",
                        fontWeight: "500",
                      }}
                    >
                      {((level1 / 100) * 100000).toLocaleString()}đ
                    </Typography>
                  </Box>
                </Row>
                {commissionsConfigData.basicCommissionsConfig.isActiveLevelTwo && (
                  <Row sx={{ display: "flex", alignItems: "start", justifyContent: "start" }}>
                    <Box
                      sx={{
                        width: "20%",
                        display: "flex",
                        alignItems: "center",
                        gap: "10px",
                        flexDirection: "column",
                      }}
                    >
                      <Avatar sx={{ width: 28, height: 28 }} src="https://i.pravatar.cc/28?img=3" />
                      <Typography variant="body2" textAlign={"center"}>
                        Cấp độ 2:
                      </Typography>
                    </Box>
                    <Box
                      sx={{
                        width: "fit-content",
                        background: "#F7F8FA",
                        padding: "10px",
                        borderRadius: "15px 15px 15px 0px",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "space-between",
                      }}
                    >
                      <Typography
                        variant="body2"
                        sx={{
                          display: "flex",
                          alignItems: "center",
                          gap: "10px",
                          paddingRight: "15px",
                          borderRight: "1px solid #D9D9D9",
                        }}
                      >
                        Cấp độ 2:{" "}
                        <Typography
                          variant="body2"
                          sx={{ fontSize: "14px", fontWeight: "400", color: "#000" }}
                        >
                          {level2}%
                        </Typography>
                      </Typography>
                      <Typography
                        variant="body2"
                        sx={{
                          marginLeft: "10px",
                          color: "#2654FE",
                          borderRadius: "50px",
                          padding: "5px 10px",
                          background: "#2654FE1A",
                          fontWeight: "500",
                        }}
                      >
                        {((level2 / 100) * 100000).toLocaleString()}đ
                      </Typography>
                    </Box>
                  </Row>
                )}
              </Box>

              <Row
                sx={{
                  paddingTop: "20px",
                  borderTop: "1px solid #D9D9D9",
                  textAlign: "center",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                <Typography
                  sx={{
                    fontWeight: "400",
                    gap: "10px",
                    textAlign: "center",
                  }}
                >
                  Tổng hoa hồng phải trả cho mỗi 100.000 doanh thu:{" "}
                  <Typography
                    component="span"
                    variant="body2"
                    sx={{ fontSize: "14px", fontWeight: "700", color: "#2654FE" }}
                  >
                    {totalCommission} đ
                  </Typography>
                </Typography>
              </Row>
            </ExampleBox>
          </Box>
        </Box>
      </DialogContent>
    </Dialog>
  );
};

export default SetupPopup;
