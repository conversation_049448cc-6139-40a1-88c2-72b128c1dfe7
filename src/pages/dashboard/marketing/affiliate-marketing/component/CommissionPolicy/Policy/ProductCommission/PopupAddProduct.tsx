import React, { useCallback, useEffect, useState } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Box,
  Checkbox,
  Typography,
  IconButton,
  List,
  ListItem,
  Button,
  ListItemIcon,
  ListItemText,
  Stack,
  TableContainer,
  TableHead,
  Table,
  TableRow,
  TableCell,
  TableBody,
  TablePagination,
  Collapse,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import ArrowBackIosIcon from "@mui/icons-material/ArrowBackIos";
import ArrowForwardIosIcon from "@mui/icons-material/ArrowForwardIos";
import { useCart } from "@/src/api/hooks/cart/use-cart";
import { useStoreId } from "@/src/hooks/use-store-id";
import _ from "lodash";
import { formatMoney } from "@/src/utils/format-money";
import { KeyboardArrowDown, KeyboardArrowUp } from "@mui/icons-material";
import {
  formatResult,
  isAllSelected,
  isSomeSelected,
} from "@/src/components/orders/draft/SearchItemOrder";

const PopupAddProduct = ({ open, onClose, onAddProducts, selectedProductIds = [] }) => {
  const [search, setSearch] = useState("");
  const [selectedProducts, setSelectedProducts] = useState([]);
  const [page, setPage] = useState(0);
  const [itemsRowsPerPage, setItemsRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const [selectAll, setSelectAll] = useState(false);
  const storeId = useStoreId();
  const [items, setItems] = useState([]);
  const [selected, setSelected] = useState([]);
  const { searchItems, loading: productLoading } = useCart();

  // Pre-select products when editing
  useEffect(() => {
    if (selectedProductIds && selectedProductIds.length > 0) {
      const preSelectedItems = items.filter((item) => selectedProductIds.includes(item.itemsId));
      setSelected(preSelectedItems);
    }
  }, [selectedProductIds, items]);

  const handleChangeRowsPerPage = (event) => {
    setItemsRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleSelectedItem = (event, item, parentItem = null) => {
    let formattedItems = [item];

    if (!event.target.checked) {
      // Bỏ chọn
      setSelected((prevSelected) =>
        prevSelected.filter((selectedItem) =>
          formattedItems.every((updatedItem) => selectedItem.itemsId !== updatedItem.itemsId)
        )
      );
    } else {
      // Chọn
      setSelected((prevSelected) => [...prevSelected, ...formattedItems]);
    }
  };

  const fetchItemList = async (currentPage, pageSize, searchQuery, shopId) => {
    const skip = currentPage * pageSize;
    const limit = pageSize;
    const response = await searchItems(skip, limit, shopId, searchQuery);

    if (response?.data) {
      setItems(response.data.data || []);
      // handleFetchItems(response.data.data || []);
      setTotalCount(response.data.total || 0);
    }
  };

  // Wrap fetchUserList with debounce
  const debouncedFetchItemList = useCallback(
    _.debounce((currentPage, pageSize, searchQuery, shopId) => {
      fetchItemList(currentPage, pageSize, searchQuery, shopId);
    }, 400), // Delay 1s
    []
  );

  useEffect(() => {
    if (!storeId) return;
    debouncedFetchItemList(page, itemsRowsPerPage, search, storeId);
    return () => {
      debouncedFetchItemList.cancel(); // Cancel debounce if component unmounts or searchText changes
    };
  }, [storeId, page, itemsRowsPerPage, search]);

  const rowsPerPage = 3;

  const handleAdd = () => {
    console.log("selected is", selected);
    // const selected = items.filter((product) => selectedProducts.includes(product.id));
    onAddProducts(selected);
    setSelected([]);
    onClose();
  };

  const checkIsAllItemSelected = (selectedItems, items) => {
    // Kiểm tra nếu mỗi phần tử trong items đều có itemsId trong selectedItems
    const allSelected = items.every((item) =>
      selectedItems.some((selectedItem) => selectedItem.itemsId === item.itemsId)
    );

    return allSelected;
  };

  const checkIsSomeItemSelected = (selectedItems, items) => {
    // Kiểm tra nếu có ít nhất một phần tử trong items có itemsId trong selectedItems
    const someSelected = items.some((item) =>
      selectedItems.some((selectedItem) => selectedItem.itemsId === item.itemsId)
    );

    return someSelected;
  };

  const isAllItemSelected = checkIsAllItemSelected(selected, items);

  const isSomeItemSelected = checkIsSomeItemSelected(selected, items);
  const handleSelectAll = (event) => {
    const isIndeterminate = !checkIsAllItemSelected && isSomeItemSelected;
    const isChecked = event.target.checked;

    if (isIndeterminate) {
      // Nếu checkbox đang ở trạng thái Indeterminate, bỏ chọn tất cả
      setSelected((prevSelected) => {
        // Xóa các item hiện tại trong selected
        const filteredItems = prevSelected.filter(
          (item) => !items.some((i) => i.itemsId === item.itemsId)
        );
        return filteredItems;
      });
    } else if (isChecked) {
      // Nếu checkbox được đánh dấu là checked, chọn tất cả các sản phẩm và biến thể
      const allItems = items;

      // Thêm tất cả các sản phẩm vào selected, đảm bảo không có các sản phẩm trùng lặp
      setSelected((prevSelected) => {
        const newSelectedItems = [...prevSelected, ...allItems];
        return _.uniqBy(newSelectedItems, "itemsId"); // Đảm bảo không có các sản phẩm trùng lặp
      });
    } else {
      // Nếu checkbox bị bỏ chọn (unchecked), bỏ chọn tất cả các sản phẩm
      setSelected((prevSelected) => {
        // Xóa tất cả các sản phẩm hiện tại trong selected
        const filteredItems = prevSelected.filter(
          (item) => !items.some((i) => i.itemsId === item.itemsId)
        );
        return filteredItems;
      });
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle sx={{ borderBottom: "1px solid #979797" }}>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Typography variant="h6">Chọn sản phẩm</Typography>
          <IconButton onClick={onClose}>
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>
      <DialogContent sx={{ marginTop: "25px" }}>
        <TextField
          fullWidth
          variant="outlined"
          placeholder="Tìm kiếm tên sản phẩm"
          value={search}
          onChange={(e) => setSearch(e.target.value)}
          sx={{ mb: 2 }}
        />

        <TableContainer sx={{ maxHeight: 500, overflowY: "auto" }}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell padding="checkbox">
                  <Checkbox
                    checked={isAllItemSelected}
                    indeterminate={!isAllItemSelected && isSomeItemSelected}
                    onChange={handleSelectAll}
                  />
                </TableCell>
                <TableCell>Tên</TableCell>
                <TableCell>Số lượng tồn kho</TableCell>
                <TableCell>Giá bán</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {items.map((item, index) => (
                <SubtableRow
                  row={item}
                  key={index}
                  selected={selected}
                  handleSelected={handleSelectedItem}
                />
              ))}
            </TableBody>
          </Table>
        </TableContainer>
        <TablePagination
          component="div"
          count={totalCount}
          page={page}
          onPageChange={handleChangePage}
          rowsPerPage={itemsRowsPerPage}
          onRowsPerPageChange={handleChangeRowsPerPage}
          rowsPerPageOptions={[5, 10, 20]}
          labelRowsPerPage="Số dòng mỗi trang" // Thay đổi văn bản ở phần này
        />

        {/* <Box display="flex" justifyContent="end" alignItems="center" mt={2}>
          <IconButton onClick={handlePreviousPage} disabled={page === 1}>
            <ArrowBackIosIcon fontSize="small" />
          </IconButton>
          <Typography sx={{ color: "#000", display: "flex", alignItems: "center", gap: "15px" }}>
            <Typography
              sx={{
                padding: "3px 15px",
                textAlign: "center",
                border: "1px solid #D5D5D5",
                borderRadius: "5px",
              }}
            >
              {page}
            </Typography>{" "}
            / <Typography sx={{ padding: "3px 8px" }}>{totalPages}</Typography>
          </Typography>
          <IconButton onClick={handleNextPage} disabled={page === totalPages}>
            <ArrowForwardIosIcon fontSize="small" />
          </IconButton>
        </Box> */}
      </DialogContent>
      <DialogActions
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          padding: "20px 20px",
        }}
      >
        <Box>
          <Typography variant="body2" color="textSecondary" mt={1}>
            Đã chọn ({selected.length})
          </Typography>
        </Box>
        <Stack flexDirection={"row"} alignItems={"center"} gap={"5px"}>
          <Button onClick={onClose} color="primary">
            Đóng
          </Button>
          <Button onClick={handleAdd} variant="contained" color="primary">
            Xác nhận
          </Button>
        </Stack>
      </DialogActions>
    </Dialog>
  );
};

const SubtableRow = ({ row, selected, handleSelected }) => {
  const isVariant = row.isVariant;
  const selectedSet = new Set(selected.map((s) => s.itemsId));
  const [open, setOpen] = useState(false);
  const setListVariantItemsId = new Set(row.itemsId);
  const checkIfAllInSet = () => {
    return [row.itemsId].some((item) => selectedSet.has(item));
  };

  const checkIfSomeInSet = () => {
    return [...setListVariantItemsId].some((item) => selectedSet.has(item));
  };

  return (
    <>
      <TableRow>
        <TableCell padding="checkbox">
          <Checkbox checked={checkIfAllInSet()} onChange={(e) => handleSelected(e, row)} />
        </TableCell>
        <TableCell>
          <Box display="flex" gap={1} alignItems="center">
            <Box
              sx={{
                width: 40, // Kích thước chiều ngang
                height: 40, // Kích thước chiều dọc (vuông)
                overflow: "hidden", // Ẩn phần hình ảnh vượt ra ngoài
                borderRadius: "8px", // Bo góc, nếu cần
                boxShadow: 3,
                flexShrink: 0, // Đổ bóng
              }}
            >
              <img
                src={row.images?.[0]?.link} // Thay đường dẫn hình ảnh tại đây
                alt={row.itemsName}
                style={{
                  width: "100%",
                  height: "100%",
                  objectFit: "cover", // Đảm bảo hình ảnh vừa khít khung mà không bị méo
                }}
              />
            </Box>
            <Typography> {row.itemsName}</Typography>
          </Box>
        </TableCell>
        <TableCell>{!isVariant && row.listVariant?.[0]?.quantity}</TableCell>
        <TableCell>
          {isVariant ? (
            <IconButton sx={{ fontSize: "2rem" }} color="primary" onClick={() => setOpen(!open)}>
              {open ? (
                <KeyboardArrowUp sx={{ fontSize: "2rem" }} />
              ) : (
                <KeyboardArrowDown sx={{ fontSize: "2rem" }} />
              )}
            </IconButton>
          ) : (
            `${formatMoney(row.listVariant?.[0]?.price || 0)}đ`
          )}
        </TableCell>
      </TableRow>

      {/* Subtable */}
      {isVariant && (
        <TableRow>
          <TableCell colSpan={4} style={{ padding: 0 }}>
            <Collapse in={open} timeout="auto" unmountOnExit>
              <Box sx={{ paddingLeft: 2 }}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell padding="checkbox"></TableCell>
                      <TableCell>Tên</TableCell>
                      <TableCell>Số lượng tồn kho</TableCell>
                      <TableCell>Giá bán</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {row.listVariant?.map((variant) => (
                      <TableRow key={variant.itemsId}>
                        <TableCell />
                        <TableCell>
                          <Box sx={{ display: "flex", alignItems: "center" }}>
                            <Box display="flex" gap={1} alignItems="center">
                              <Box
                                sx={{
                                  width: 40, // Kích thước chiều ngang
                                  height: 40, // Kích thước chiều dọc (vuông)
                                  overflow: "hidden", // Ẩn phần hình ảnh vượt ra ngoài
                                  borderRadius: 1, // Bo góc, nếu cần
                                  boxShadow: 3,
                                  flexShrink: 0, // Đổ bóng
                                }}
                              >
                                <img
                                  src={variant.variantImage?.link} // Thay đường dẫn hình ảnh tại đây
                                  alt={row.itemsName}
                                  style={{
                                    width: "100%",
                                    height: "100%",
                                    objectFit: "cover", // Đảm bảo hình ảnh vừa khít khung mà không bị méo
                                  }}
                                />
                              </Box>
                              <Typography>
                                {[
                                  variant.variantValueOne,
                                  variant.variantValueTwo,
                                  variant.variantValueThree,
                                ]
                                  .filter((value) => value != null && value !== "")
                                  .join("/")}
                              </Typography>
                            </Box>
                          </Box>
                        </TableCell>
                        <TableCell>{variant.quantity}</TableCell>
                        <TableCell>{formatMoney(variant.price)}đ</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </Box>
            </Collapse>
          </TableCell>
        </TableRow>
      )}
    </>
  );
};

export default PopupAddProduct;
