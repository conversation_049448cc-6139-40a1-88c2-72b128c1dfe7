import React, { useContext, useEffect, useState } from "react";
import {
  Box,
  Button,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Checkbox,
  TextField,
  TablePagination,
  IconButton,
} from "@mui/material";
import DeleteIcon from "@mui/icons-material/Delete";
import PopupAddProduct from "./PopupAddProduct";
import { CommissionsConfigData } from "@/src/api/types/affiliation.type";
import { useVoucher } from "@/src/api/hooks/voucher/use-voucher";
import { useStoreId } from "@/src/hooks/use-store-id";
import { useProduct } from "@/src/api/hooks/dashboard/product/use-product";
import { AffiliatePolicyContext } from "../../CommissionPolicy";

const ProductCommission = ({ onBack, submitItemsGroup }) => {
  const [products, setProducts] = useState([]);

  const { commissionsConfigData }: { commissionsConfigData: CommissionsConfigData } =
    useContext(AffiliatePolicyContext);

  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(5);
  const [selectAll, setSelectAll] = useState(false);
  const [openPopup, setOpenPopup] = useState(false);
  const { listItemsByItemsCode } = useProduct();
  const storeId = useStoreId();
  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleDelete = (itemsCode) => {
    setProducts(products.filter((product) => product.itemsCode !== itemsCode));
  };

  const handleAddProduct = () => {
    setOpenPopup(true);
  };

  const handleAddProductsFromPopup = (selectedProducts) => {
    const newProducts = selectedProducts
      .filter((product) => {
        return !products.some((existingProduct) => existingProduct.itemsCode === product.itemsCode);
      })
      .map((product) => ({
        itemsCode: product.itemsCode,
        name: product.itemsName,
        levelOneCommissionPercentage: "0",
        levelTwoCommissionPercentage: "0",
        image: product.images?.[0].link,
        checked: false,
      }));

    // Chỉ thêm vào products nếu có sản phẩm mới
    if (newProducts.length > 0) {
      setProducts((prevProducts) => [...prevProducts, ...newProducts]);
    }
  };

  const handleCommissionChange = (itemsCode, field, value) => {
    const updatedValue = Math.max(0, Math.min(100, Number(value))).toString();
    setProducts(
      products.map((product) =>
        product.itemsCode === itemsCode ? { ...product, [field]: updatedValue } : product
      )
    );
  };

  const handleSelectAllChange = (event) => {
    const checked = event.target.checked;
    setSelectAll(checked);

    const itemsCodesInPage = paginatedProducts.map((p) => p.itemsCode);

    const updatedProducts = products.map((product) => {
      if (itemsCodesInPage.includes(product.itemsCode)) {
        return { ...product, checked };
      }
      return product;
    });

    setProducts(updatedProducts);
  };

  const handleRowCheckboxChange = (itemsCode) => {
    const updatedProducts = products.map((product) =>
      product.itemsCode === itemsCode ? { ...product, checked: !product.checked } : product
    );
    setProducts(updatedProducts);

    // const allChecked = updatedProducts.every((product) => product.checked);
    // const someChecked = updatedProducts.some((product) => product.checked);
    // setSelectAll(allChecked);
    // if (!someChecked) setSelectAll(false);
  };

  const fetchProductByItemsIds = async (productIds, shopId) => {
    const response = await listItemsByItemsCode({ shopId: shopId, itemsCodes: productIds });
    if (response?.data?.data) {
      const { data } = response.data;
      if (Array.isArray(data)) {
        const groupedData = Object.groupBy(data, (item) => item.itemsCode);
        const mappedProducts = mapProductsWithCommission(
          groupedData,
          commissionsConfigData?.basicCommissionsConfig.itemGroupCommissionsConfigs
        );
        setProducts(mappedProducts);
      } else {
        console.error("Data is not an array:", data);
      }

      // setSelectedProducts(response?.data?.data);
    }
  };

  const mapProductsWithCommission = (itemsRaw, itemGroupCommissionsConfigs) => {
    return itemGroupCommissionsConfigs.map(
      ({ itemsCode, levelOneCommissionPercentage, levelTwoCommissionPercentage }) => {
        const product = itemsRaw[itemsCode]?.[0]; // Lấy sản phẩm đầu tiên từ danh sách theo mã itemsCode

        return {
          itemsCode,
          name: product?.itemsName || "", // Lấy tên sản phẩm hoặc chuỗi rỗng nếu không có
          levelOneCommissionPercentage: levelOneCommissionPercentage || "",
          levelTwoCommissionPercentage: levelTwoCommissionPercentage || "",
          image: product?.images?.[0]?.link || "", // Lấy ảnh đầu tiên hoặc chuỗi rỗng
          checked: false, // Mặc định là false
        };
      }
    );
  };

  useEffect(() => {
    if (
      storeId &&
      commissionsConfigData?.basicCommissionsConfig.itemGroupCommissionsConfigs?.length > 0
    ) {
      fetchProductByItemsIds(
        commissionsConfigData?.basicCommissionsConfig.itemGroupCommissionsConfigs.map(
          (item) => item.itemsCode
        ),
        storeId
      );
    }
  }, [commissionsConfigData, storeId]);

  const handleDeleteSelected = () => {
    const filtered = products.filter((p) => !p.checked);

    setProducts(filtered);
  };

  useEffect(() => {
    const allChecked = paginatedProducts.length > 0 && paginatedProducts.every((p) => p.checked);

    setSelectAll(allChecked);
  }, [products, page, rowsPerPage]);

  const paginatedProducts = products.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage);
  const selectedProducts = products.filter((product) => product.checked);

  return (
    <Box mt={2}>
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          "@media(max-width: 600px)": {
            flexDirection: "column",
          },
        }}
      >
        <Box display="flex" alignItems="center" mb={2}>
          <IconButton onClick={onBack}>
            <svg
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 18L9 12L15 6"
                stroke="#000"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </IconButton>
          <Typography variant="h6" fontWeight={700} ml={1}>
            Hoa hồng riêng cho từng sản phẩm
          </Typography>
        </Box>
        <Box
          display="flex"
          justifyContent="space-between"
          mb={2}
          gap={2}
          sx={{
            "@media(max-width: 600px)": {
              flexDirection: "column",
              justifyContent: "start",
            },
          }}
        >
          <Button
            variant="contained"
            sx={{
              bgcolor: "#2654FE",
              color: "#fff",
              textTransform: "none",
              borderRadius: "10px",
              "@media(max-width: 600px)": {
                width: "fit-content",
              },
            }}
            onClick={handleAddProduct}
          >
            Thêm sản phẩm
          </Button>
          <Button
            variant="contained"
            sx={{
              bgcolor: "#2654FE",
              color: "#fff",
              textTransform: "none",
              borderRadius: "10px",
              "@media(max-width: 600px)": {
                width: "fit-content",
              },
            }}
            onClick={() => {
              // Handle save logic here
              // Lọc các trường cần thiết từ mỗi sản phẩm
              const filteredProducts = products.map((product) => ({
                itemsCode: product.itemsCode, // Lấy itemsCode
                levelOneCommissionPercentage: product.levelOneCommissionPercentage, // Lấy levelOneCommissionPercentage
                levelTwoCommissionPercentage: product.levelTwoCommissionPercentage, // Lấy levelTwoCommissionPercentage
              }));

              // Gửi dữ liệu filteredProducts
              submitItemsGroup(filteredProducts);
            }}
          >
            Lưu
          </Button>
        </Box>
      </Box>
      {selectedProducts.length > 0 && (
        <Box sx={{ px: 2, mb: 1 }} display="flex" gap="8px">
          <Button
            sx={{
              color: "red",
              fontSize: "0.8rem", // hoặc "12px" tùy bạn
              textTransform: "none", // giữ nguyên kiểu chữ
              p: 0,
            }}
            onClick={handleDeleteSelected}
          >
            Xóa {products.filter((p) => p.checked).length} sản phẩm
          </Button>{" "}
        </Box>
      )}

      <TableContainer component={Paper} sx={{ boxShadow: "none" }}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>
                <Checkbox checked={selectAll} onChange={handleSelectAllChange} />
              </TableCell>
              <TableCell sx={{ minWidth: "200px" }}>Tên sản phẩm</TableCell>
              <TableCell sx={{ minWidth: "150px" }}>Hoa hồng bậc 1</TableCell>
              {commissionsConfigData.basicCommissionsConfig.isActiveLevelTwo && (
                <TableCell sx={{ minWidth: "150px" }}>Hoa hồng bậc 2</TableCell>
              )}

              <TableCell sx={{ minWidth: "150px" }}>Quản lý</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {paginatedProducts.map((product) => (
              <TableRow key={product.id}>
                <TableCell>
                  <Checkbox
                    checked={product.checked}
                    onChange={() => handleRowCheckboxChange(product.itemsCode)}
                  />
                </TableCell>
                <TableCell>
                  <Box display="flex" alignItems="center">
                    <img
                      src={product.image}
                      alt={product.name}
                      style={{
                        width: 45,
                        height: 45,
                        borderRadius: "50%",
                        marginRight: 8,
                      }}
                    />
                    {product.name}
                  </Box>
                </TableCell>
                <TableCell>
                  <TextField
                    value={product.levelOneCommissionPercentage}
                    type="number"
                    onChange={(e) =>
                      handleCommissionChange(
                        product.itemsCode,
                        "levelOneCommissionPercentage",
                        e.target.value
                      )
                    }
                    variant="outlined"
                    size="small"
                    sx={{
                      width: "100px",
                      padding: 0,
                      "& input": {
                        padding: "0",
                      },
                      "& .MuiInputBase-root": {
                        padding: 0,
                      },
                    }}
                    InputProps={{
                      endAdornment: (
                        <Typography
                          padding={"5px 10px"}
                          bgcolor={"#F7F8FA"}
                          borderLeft={"1px solid #D8D8D8"}
                          color="#979797"
                        >
                          %
                        </Typography>
                      ),
                    }}
                    inputProps={{
                      min: 0,
                      max: 100,
                      step: 1,
                    }}
                  />
                </TableCell>
                {commissionsConfigData.basicCommissionsConfig.isActiveLevelTwo && (
                  <TableCell>
                    <TextField
                      value={product.levelTwoCommissionPercentage}
                      type="number"
                      onChange={(e) =>
                        handleCommissionChange(
                          product.itemsCode,
                          "levelTwoCommissionPercentage",
                          e.target.value
                        )
                      }
                      variant="outlined"
                      size="small"
                      sx={{
                        width: "100px",
                        padding: 0,
                        "& input": {
                          padding: "0",
                        },
                        "& .MuiInputBase-root": {
                          padding: 0,
                        },
                      }}
                      InputProps={{
                        endAdornment: (
                          <Typography
                            padding={"5px 10px"}
                            bgcolor={"#F7F8FA"}
                            borderLeft={"1px solid #D8D8D8"}
                            color="#979797"
                          >
                            %
                          </Typography>
                        ),
                      }}
                      inputProps={{
                        min: 0,
                        max: 100,
                        step: 1,
                      }}
                    />
                  </TableCell>
                )}

                <TableCell>
                  <IconButton onClick={() => handleDelete(product.itemsCode)}>
                    <DeleteIcon sx={{ color: "red" }} />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
      <TablePagination
        rowsPerPageOptions={[5, 10, 25]}
        component="div"
        count={products.length}
        rowsPerPage={rowsPerPage}
        page={page}
        onPageChange={handleChangePage}
        onRowsPerPageChange={handleChangeRowsPerPage}
        labelRowsPerPage="Rows per page:"
        labelDisplayedRows={({ from, to, count }) => `${from}-${to} of ${count}`}
      />
      <PopupAddProduct
        open={openPopup}
        onClose={() => setOpenPopup(false)}
        onAddProducts={handleAddProductsFromPopup}
      />
    </Box>
  );
};

export default ProductCommission;
