import React, { use<PERSON><PERSON>back, useEffect, useState } from "react";
import {
  Box,
  Button,
  Container,
  TextField,
  InputAdornment,
  Typography,
  ToggleButton,
  ToggleButtonGroup,
  Stack,
  Select,
  MenuItem,
} from "@mui/material";
import SearchIcon from "@mui/icons-material/Search";
import DownloadIcon from "@mui/icons-material/Download";
import { LocalizationProvider, DateRangePicker, DateRange } from "@mui/x-date-pickers-pro";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import CalendarMonthIcon from "@mui/icons-material/CalendarMonth";
import UnpaidTab from "./UnpaidCommissionTab";
import PaidTab from "./PaidTab";
import { useAffiliation } from "@/src/api/hooks/affiliation/use-affiliation";
import _ from "lodash";
import { useStoreId } from "@/src/hooks/use-store-id";

interface CommissionReportDto {
  ShopId: string;
  Search: string;
  Month: string;
  Year: string;
}
const months = [
  { value: 1, label: "Tháng 1" },
  { value: 2, label: "Tháng 2" },
  { value: 3, label: "Tháng 3" },
  { value: 4, label: "Tháng 4" },
  { value: 5, label: "Tháng 5" },
  { value: 6, label: "Tháng 6" },
  { value: 7, label: "Tháng 7" },
  { value: 8, label: "Tháng 8" },
  { value: 9, label: "Tháng 9" },
  { value: 10, label: "Tháng 10" },
  { value: 11, label: "Tháng 11" },
  { value: 12, label: "Tháng 12" },
];
const currentYear = new Date().getFullYear();
const years = Array.from({ length: 10 }, (_, i) => {
  const year = currentYear - i;
  return { value: year, label: `Năm ${year}` };
});

const PaymentStatusTable = () => {
  const storeId = useStoreId();
  const [selectedTab, setSelectedTab] = useState("Unpaid");
  const [searchQuery, setSearchQuery] = useState("");
  const [chartDateRange, setChartDateRange] = useState<DateRange<any>>([null, null]);
  const [open, setOpen] = useState(false);
  const [selected, setSelected] = useState([]);
  const { getCommissionReport, exportExcelCommissionReport } = useAffiliation();
  const currentMonth = new Date().getMonth() + 1;
  const [filterData, setFilterData] = useState<CommissionReportDto>({
    ShopId: storeId,
    Search: "",
    Month: currentMonth.toString(),
    Year: currentYear.toString(),
  });
  const [commissionReport, setCommissionReport] = useState([]);
  const [totalCount, setTotalCount] = useState<number>(0);

  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const fetchCommissionReport = async (currentPage, pageSize, data: any) => {
    data.PageNumber = currentPage;
    data.PageSize = pageSize;

    const response = await getCommissionReport(data);
    if (response && response.data) {
      setCommissionReport(response?.data.data?.result);
      setTotalCount(response.data.data.total || 0);
    }
  };

  const debouncedFetchCommissionReport = useCallback(
    _.debounce((currentPage, pageSize, data) => {
      fetchCommissionReport(currentPage, pageSize, data);
    }, 400), // Delay 400ms
    []
  );

  useEffect(() => {
    if (filterData?.ShopId) {
      debouncedFetchCommissionReport(page, rowsPerPage, filterData);
    }

    return () => {
      debouncedFetchCommissionReport.cancel(); // Cancel debounce if component unmounts or searchText changes
    };
  }, [page, rowsPerPage, debouncedFetchCommissionReport, filterData]);
  useEffect(() => {
    if (storeId) {
      setFilterData((prevState) => {
        return {
          ...prevState,
          ShopId: storeId,
        };
      });
    }
  }, [storeId]);

  const handleExportExcel = async () => {
    try {
      const response = await exportExcelCommissionReport(filterData);
      if (response) {
        const blob = new Blob([response.data], {
          type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        });

        const url = window.URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.setAttribute("download", "BaoCaoHoaHong.xlsx"); // tên file
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      }
    } catch (error) {
      console.error("Export Excel failed:", error);
    }
  };
  return (
    <Container
      maxWidth={false}
      sx={{
        boxShadow: "0px 0px 10px 0px rgba(0, 0, 0, 0.1)",
        borderRadius: "15px",
        padding: "20px",
        margin: "20px 0",
        background: "#fff",
        width: "100%",
      }}
    >
      <Typography sx={{ fontSize: "20px", fontWeight: 700, marginBottom: 1.5 }}>
        Báo cáo hoa hồng
      </Typography>

      <Stack
        direction={{ xs: "column", sm: "row" }}
        justifyContent="space-between"
        alignItems={{ xs: "stretch", sm: "center" }}
        gap={2}
        sx={{ width: "100%", mb: 2 }}
      >
        <Stack
          direction={{ xs: "column", sm: "row" }}
          alignItems={{ xs: "stretch", sm: "center" }}
          gap={1}
          sx={{ flex: 1 }}
        >
          <TextField
            variant="outlined"
            placeholder="Tìm tên, mã đối tác"
            value={searchQuery}
            onChange={(e) => {
              setSearchQuery(e.target.value);
              setFilterData((prev) => {
                return { ...prev, Search: e.target.value };
              });
            }}
            size="small"
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
            sx={{
              width: { xs: "100%", sm: 280 },
              "& .MuiOutlinedInput-root": {
                borderRadius: "8px",
                height: 36,
                fontSize: 14,
              },
            }}
          />

          <Box
            sx={{
              display: "flex",
              flexDirection: "row",
              gap: 1,
              width: { xs: "100%", sm: "auto" },
            }}
          >
            <Select
              value={filterData?.Month}
              size="small"
              sx={{
                height: 36,
                borderRadius: "8px",
                width: { xs: "100%", sm: 130 },
                flex: { xs: 1, sm: "none" },
                fontSize: 14,
              }}
              onChange={(e) =>
                setFilterData((prev) => {
                  return { ...prev, Month: e.target.value };
                })
              }
            >
              {months.map((month, index) => (
                <MenuItem key={index} value={month.value}>
                  {month.label}
                </MenuItem>
              ))}
            </Select>

            <Select
              value={filterData?.Year}
              size="small"
              sx={{
                height: 36,
                borderRadius: "8px",
                width: { xs: "100%", sm: 130 },
                flex: { xs: 1, sm: "none" },
                fontSize: 14,
              }}
              onChange={(e) =>
                setFilterData((prev) => {
                  return { ...prev, Year: e.target.value };
                })
              }
            >
              {years.map((year, index) => (
                <MenuItem key={index} value={year.value}>
                  {year.label}
                </MenuItem>
              ))}
            </Select>
          </Box>
        </Stack>

        <Button
          variant="outlined"
          startIcon={<DownloadIcon />}
          onClick={handleExportExcel}
          size="small"
          sx={{
            textTransform: "none",
            borderRadius: "8px",
            backgroundColor: "#fff",
            color: "#2654FE",
            border: "1.5px solid #2654FE",
            fontSize: "14px",
            fontWeight: 600,
            minWidth: 60,
            width: { xs: "calc(50% - 2px)", sm: 150 },
            height: 36,
            boxShadow: "none",
            transition: "all 0.2s",
            "&:hover": {
              backgroundColor: "#F0F6FF",
              borderColor: "#2654FE",
              color: "#2654FE",
            },
            "& .MuiButton-startIcon": {
              marginRight: 1,
            },
          }}
        >
          Export
        </Button>
      </Stack>

      <UnpaidTab
        commissionReport={commissionReport}
        setCommissionReport={setCommissionReport}
        totalCount={totalCount}
        setTotalCount={setTotalCount}
        page={page}
        setPage={setPage}
        rowsPerPage={rowsPerPage}
        setRowsPerPage={setRowsPerPage}
        selected={selected}
        setSelected={setSelected}
      />
    </Container>
  );
};

export default PaymentStatusTable;
