import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>alogContent,
  DialogTitle,
  Button,
  TextField,
  Switch,
  FormControlLabel,
  MenuItem,
  Grid,
  FormControl,
  Stack,
  InputLabel,
  Typography,
  Select,
  Autocomplete,
  FormHelperText,
} from "@mui/material";
import * as Yup from "yup";
import { useForm, Controller } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { UpdateAffiliationPartnerRequest } from "@/src/api/types/affiliation.type";
import { tokens } from "@/src/locales/tokens";
import { useTranslation } from "react-i18next";
import { formatPhoneNumber, isValidPhoneNumber } from "@/src/utils/format";
import { useUser } from "@/src/api/hooks/user/use-user";
import { useStoreId } from "@/src/hooks/use-store-id";
import { useAffiliation } from "@/src/api/hooks/affiliation/use-affiliation";
import useSnackbar from "@/src/hooks/use-snackbar";
import { getValue } from "@mui/system";
import ReferrerSearchInput from "@/src/components/ReferrerSearchInput";

interface FormValues {
  affiliationStatus: "Actived" | "InActived";
  fullname: string;
  email: string;
  phoneNumber: string;
  bankName: string;
  paymentType: string;
  bankAccountNumber: string;
  identityCardNumber: string;
  taxCode: string;
  referrerCode: string;
  affiliationFullName: string;
  affiliationEmail?: string;
  affiliationPhoneNumber?: string;
}

const EditPartnerModal = ({
  open,
  onClose,
  partner,
  fetchAffiliationPartner,
  page,
  rowsPerPage,
}) => {
  const { t } = useTranslation();
  const { getListBank, updateAffiliationPartner } = useAffiliation();
  const [banks, setBanks] = useState([]);
  const storeId = useStoreId();
  const [defaultReferrer, setDefaultReferrer] = useState<any>(null);
  const [defaultBank, setDefaultBank] = useState(null);
  const [isShowError, setIsShowError] = useState(false);

  const validationSchema = Yup.object().shape({
    affiliationStatus: Yup.string()
      .oneOf(["Actived", "InActived"] as const)
      .required("Trạng thái hoạt động là bắt buộc"),
    fullname: Yup.string().required("Họ tên không được bỏ trống"),
    affiliationFullName: Yup.string()
      .required("Họ tên đối tác không được bỏ trống")
      .max(255, "Họ tên đối tác không được vượt quá 255 ký tự")
      .matches(/^[A-Za-zÀ-ỹ\s]+$/, "Họ tên chỉ được chứa chữ cái"),
    phoneNumber: Yup.string().required(t(tokens.auth.phoneRequired)),
    affiliationEmail: Yup.string()
      .email("Email không đúng định dạng")
      .max(255, "Email không được vượt quá 255 ký tự")
      .required("Email không được bỏ trống"),
    affiliationPhoneNumber: Yup.string()
      .required("Số điện thoại không được bỏ trống")
      .matches(/^0\d{9}$/, "Số điện thoại phải bắt đầu bằng số 0 và có đúng 10 chữ số"),

    bankName: Yup.string().required("Hãy chọn ngân hàng"),
    paymentType: Yup.string().required("Hãy chọn phương thức thanh toán"),
    bankAccountNumber: Yup.string().required("Hãy nhập số tài khoản ngân hàng"),
    identityCardNumber: Yup.string()
      .nullable()
      .notRequired()
      .test(
        "identityCardNumber-is-number",
        "Số CCCD phải là số",
        (value) => !value || /^\d+$/.test(value)
      ),

    taxCode: Yup.string()
      .nullable()
      .notRequired()
      .test("taxCode-is-number", "Mã số thuế phải là số", (value) => !value || /^\d+$/.test(value)),
  });
  const snackbar = useSnackbar();

  const {
    control,
    handleSubmit,
    reset,
    setValue,
    watch,
    formState: { errors },
  } = useForm<any>({
    resolver: yupResolver(validationSchema),
    defaultValues: {
      affiliationStatus: "InActived",
      fullname: "",
      referrerCode: "",
      email: "",
      identityCardNumber: "",
      phoneNumber: "",
      taxCode: "",
      bankName: "",
      paymentType: "Transfer",
      bankAccountNumber: "",
      affiliationFullName: "",
      affiliationEmail: "",
      affiliationPhoneNumber: "",
    },
  });

  // fetchUserList removed - using ReferrerSearchInput component instead

  const fetchBankList = async () => {
    const response = await getListBank();
    if (response && response.data) {
      setBanks(response.data || []);
    }
  };

  useEffect(() => {
    if (open) {
      fetchBankList();
    }
  }, [open]);

  useEffect(() => {
    if (partner) {
      reset({
        affiliationStatus: partner.affiliationStatus,
        affiliationEmail: partner.affiliationEmail || "",
        affiliationFullName: partner.affiliationFullName || "",
        affiliationPhoneNumber: partner.affiliationPhoneNumber || "",
        fullname: partner.fullname || "",
        referrerCode: partner.referrerCode || "",
        email: partner.email || "",
        identityCardNumber: partner.identityCardNumber || "",
        phoneNumber: partner.phoneNumber || "",
        taxCode: partner.taxCode || "",
        bankName: partner.bankName || "",
        paymentType: partner.paymentType || "Transfer",
        bankAccountNumber: partner.bankAccountNumber || "",
      });
    } else {
      reset({
        affiliationStatus: "InActived",
        fullname: "",
        referrerCode: "",
        email: "",
        identityCardNumber: "",
        phoneNumber: "",
        taxCode: "",
        bankName: "",
        paymentType: "Transfer",
        bankAccountNumber: "",
        affiliationFullName: "",
        affiliationEmail: "",
        affiliationPhoneNumber: "",
      });
    }
  }, [partner, open, reset]);

  useEffect(() => {
    if (!open) {
      setDefaultReferrer(null);
    }
  }, [open]);

  useEffect(() => {
    const bankF = banks.length > 0 && banks.find((x) => partner?.bankName?.includes(x.shortName));
    if (bankF) {
      setDefaultBank(bankF);
    }
  }, [banks, partner?.bankName]);

  // convertPhoneNumber moved to ReferrerSearchInput component

  const onSubmit = async (data: any) => {
    if (defaultBank === null) {
      setIsShowError(true);
    } else {
      setIsShowError(false);
      if (partner?.userId) {
        const res = await updateAffiliationPartner(partner.userId, data);
        if (res?.status === 200) {
          fetchAffiliationPartner(page, rowsPerPage, "active", { ShopId: storeId });
          snackbar.success("Cập nhật thông tin thành công");
          onClose();
        }
      }
    }
  };

  const handleClose = () => {
    reset({
      affiliationStatus: "InActived",
      fullname: "",
      referrerCode: "",
      email: "",
      identityCardNumber: "",
      phoneNumber: "",
      taxCode: "",
      bankName: "",
      paymentType: "Transfer",
      bankAccountNumber: "",
      affiliationFullName: "",
      affiliationEmail: "",
      affiliationPhoneNumber: "",
    });
    onClose();
  };

  if (!partner) return null;

  return (
    <Dialog open={open} onClose={handleClose} fullWidth maxWidth="md">
      <form onSubmit={handleSubmit(onSubmit)}>
        <DialogTitle
          sx={{ padding: "20px", borderBottom: "1px solid #E0E0E0", marginBottom: "20px" }}
        >
          Chỉnh sửa thông tin
        </DialogTitle>
        <DialogContent
          sx={{
            "@media (max-width: 600px)": {
              flexDirection: "column",
              alignItems: "center",
            },
          }}
        >
          <Grid
            container
            spacing={2}
            sx={{
              display: "flex",
              marginTop: "30px",
              "@media (max-width: 600px)": {
                width: "100%",
                flexDirection: "column",
              },
            }}
          >
            <Stack
              width={"100%"}
              flexDirection={"row"}
              gap={"16px"}
              paddingLeft={"16px"}
              sx={{
                "@media (max-width: 600px)": {
                  width: "100%",
                  flexDirection: "column",
                },
              }}
            >
              <Grid
                sx={{
                  width: "50%",
                  "@media (max-width: 600px)": {
                    width: "100%",
                  },
                }}
              >
                <Stack direction={"column"} gap={0}>
                  <Typography sx={{ fontSize: "16px", fontWeight: "600" }}>
                    Người giới thiệu
                  </Typography>

                  <ReferrerSearchInput
                    value={defaultReferrer}
                    onChange={(newValue) => {
                      setValue("referrerCode", newValue?.referralCode);
                      setDefaultReferrer(newValue);
                    }}
                    storeId={storeId}
                    error={!!errors.referrerCode}
                    helperText={errors.referrerCode && String(errors.referrerCode.message)}
                    placeholder="Nhập tên hoặc số điện thoại để tìm kiếm..."
                    initialReferrerCode={partner?.referrerCode}
                  />
                </Stack>
              </Grid>
              <Grid
                sx={{
                  width: "50%",
                  display: "flex",
                  alignItems: "start",
                  flexDirection: "column",
                  "@media (max-width: 600px)": {
                    width: "100%",
                  },
                }}
              >
                <Typography>Trạng thái hoạt động</Typography>
                <FormControlLabel
                  control={
                    <Switch
                      sx={{ marginTop: "16px" }}
                      onChange={(event) => {
                        const isChecked = event.target.checked;
                        setValue("affiliationStatus", isChecked ? "Actived" : "InActived");
                      }}
                      checked={watch("affiliationStatus") === "Actived"}
                    />
                  }
                  label=""
                />
              </Grid>
            </Stack>

            <Stack flexDirection={"column"} width={"100%"} paddingLeft={"16px"}>
              <Typography sx={{ fontSize: "16px", fontWeight: "600", margin: "20px 0" }}>
                Thông tin tài khoản
              </Typography>
              <Stack
                flexDirection={"row"}
                gap={"16px"}
                sx={{
                  "@media (max-width: 600px)": {
                    width: "100%",
                    flexDirection: "column",
                  },
                }}
              >
                <Grid
                  item
                  sx={{
                    width: "50%",
                    display: "flex",
                    alignItems: "start",
                    flexDirection: "column",
                    "@media (max-width: 600px)": {
                      width: "100%",
                    },
                  }}
                >
                  <Typography sx={{ fontSize: "15px" }}>Tên tài khoản</Typography>
                  <TextField
                    fullWidth
                    margin="dense"
                    label=""
                    defaultValue={partner.name}
                    value={watch("fullname")}
                    InputProps={{ readOnly: true }}
                    sx={{ background: "#F7F8FA" }}
                  />
                </Grid>
                <Grid
                  item
                  sx={{
                    width: "50%",
                    display: "flex",
                    alignItems: "start",
                    flexDirection: "column",
                    "@media (max-width: 600px)": {
                      width: "100%",
                    },
                  }}
                >
                  <Typography sx={{ fontSize: "15px" }}>Số điện thoại</Typography>

                  <TextField
                    fullWidth
                    margin="dense"
                    label=""
                    defaultValue={partner.phoneNumber}
                    value={watch("phoneNumber")}
                    InputProps={{ readOnly: true }}
                    sx={{ background: "#F7F8FA" }}
                  />
                </Grid>
              </Stack>
            </Stack>

            <Stack flexDirection={"column"} width={"100%"} paddingLeft={"16px"}>
              <Typography sx={{ fontSize: "16px", fontWeight: "600", margin: "20px 0" }}>
                Thông tin cơ bản
              </Typography>
              <Stack
                flexDirection={"row"}
                gap={"16px"}
                sx={{
                  "@media (max-width: 600px)": {
                    width: "100%",
                  },
                }}
              >
                <Grid
                  item
                  sx={{
                    width: "50%",
                    display: "flex",
                    alignItems: "start",
                    flexDirection: "column",
                    paddingRight: "8px",
                    "@media (max-width: 600px)": {
                      width: "100%",
                    },
                  }}
                >
                  <Typography
                    sx={{
                      fontSize: "15px",
                      flexDirection: "row",
                      alignItems: "center",
                      display: "flex",
                      gap: "5px",
                    }}
                  >
                    Họ tên<Typography sx={{ color: "#F60A0A" }}>*</Typography>
                  </Typography>

                  <TextField
                    error={!!errors.affiliationFullName}
                    helperText={
                      errors.affiliationFullName && String(errors.affiliationFullName.message)
                    }
                    onChange={(event) => {
                      setValue("affiliationFullName", event.target.value);
                    }}
                    value={watch("affiliationFullName")}
                    name="affiliationFullName"
                    fullWidth
                    margin="dense"
                    label=""
                  />
                </Grid>
              </Stack>
            </Stack>

            <Grid
              item
              sx={{
                width: "50%",
                display: "flex",
                alignItems: "start",
                flexDirection: "column",
                "@media (max-width: 600px)": {
                  width: "100%",
                },
              }}
            >
              <Typography
                sx={{
                  fontSize: "15px",
                  flexDirection: "row",
                  alignItems: "center",
                  display: "flex",
                  gap: "5px",
                }}
              >
                Email liên hệ <Typography sx={{ color: "#F60A0A" }}>*</Typography>
              </Typography>

              <TextField
                error={!!errors.affiliationEmail}
                helperText={errors.affiliationEmail && String(errors.affiliationEmail.message)}
                onChange={(event) => {
                  setValue("affiliationEmail", event.target.value);
                }}
                value={watch("affiliationEmail")}
                name="affiliationEmail"
                fullWidth
                margin="dense"
                label=""
              />
            </Grid>
            <Grid
              item
              sx={{
                width: "50%",
                display: "flex",
                alignItems: "start",
                flexDirection: "column",
                "@media (max-width: 600px)": {
                  width: "100%",
                },
              }}
            >
              <Typography
                sx={{
                  fontSize: "15px",
                  flexDirection: "row",
                  alignItems: "center",
                  display: "flex",
                  gap: "5px",
                }}
              >
                Số điện thoại liên lạc <Typography sx={{ color: "#F60A0A" }}>*</Typography>
              </Typography>

              <TextField
                error={!!errors.affiliationPhoneNumber}
                helperText={
                  errors.affiliationPhoneNumber && String(errors.affiliationPhoneNumber.message)
                }
                onChange={(event) => {
                  setValue("affiliationPhoneNumber", event.target.value);
                }}
                value={watch("affiliationPhoneNumber")}
                name="affiliationPhoneNumber"
                fullWidth
                margin="dense"
                label=""
              />
            </Grid>

            <Grid
              item
              sx={{
                width: "50%",
                display: "flex",
                alignItems: "start",
                flexDirection: "column",
                "@media (max-width: 600px)": {
                  width: "100%",
                },
              }}
            >
              <Typography sx={{ fontSize: "15px" }}>Số CCCD</Typography>

              <TextField
                error={!!errors.identityCardNumber}
                helperText={errors.identityCardNumber && String(errors.identityCardNumber.message)}
                onChange={(event) => {
                  setValue("identityCardNumber", event.target.value);
                }}
                name="identityCardNumber"
                value={watch("identityCardNumber")}
                fullWidth
                margin="dense"
                label=""
                // defaultValue={partner.identityCardNumber}
              />
            </Grid>
            <Grid
              item
              sx={{
                width: "50%",
                display: "flex",
                alignItems: "start",
                flexDirection: "column",
                "@media (max-width: 600px)": {
                  width: "100%",
                },
              }}
            >
              <Typography sx={{ fontSize: "15px" }}>Mã số thuế</Typography>

              <TextField
                name="taxCode"
                error={!!errors.taxCode}
                helperText={errors.taxCode && String(errors.taxCode.message)}
                onChange={(event) => {
                  setValue("taxCode", event.target.value);
                }}
                value={watch("taxCode")}
                fullWidth
                margin="dense"
                label=""
              />
            </Grid>

            <Stack flexDirection={"column"} width={"100%"} paddingLeft={"16px"}>
              <Typography sx={{ fontSize: "16px", fontWeight: "600", margin: "20px 0" }}>
                Tài khoản thanh toán
              </Typography>
              <Grid
                item
                sx={{
                  width: "50%",
                  display: "flex",
                  alignItems: "start",
                  flexDirection: "column",
                  paddingRight: "8px",
                  "@media (max-width: 600px)": {
                    width: "100%",
                  },
                }}
              >
                <Typography
                  sx={{
                    fontSize: "15px",
                    display: "flex",
                    alignItems: "center",
                    gap: "5px",
                  }}
                >
                  Phương thức thanh toán <Typography sx={{ color: "#F60A0A" }}>*</Typography>
                </Typography>

                <Controller
                  name="paymentType"
                  control={control}
                  render={({ field }) => (
                    <FormControl fullWidth margin="dense" error={!!errors.paymentType}>
                      <InputLabel id="payment-type-label">Phương thức thanh toán</InputLabel>
                      <Select
                        labelId="payment-type-label"
                        value={field.value || "Transfer"}
                        onChange={field.onChange}
                        onBlur={field.onBlur}
                        label="Phương thức thanh toán"
                      >
                        <MenuItem value="Transfer">Chuyển khoản ngân hàng</MenuItem>
                      </Select>
                      {errors.paymentType && (
                        <FormHelperText>{String(errors.paymentType.message)}</FormHelperText>
                      )}
                    </FormControl>
                  )}
                />
              </Grid>
            </Stack>
            <Grid
              item
              sx={{
                width: "50%",
                display: "flex",
                alignItems: "start",
                flexDirection: "column",
                "@media (max-width: 600px)": {
                  width: "100%",
                },
              }}
            >
              <Typography
                sx={{ fontSize: "15px", display: "flex", alignItems: "center", gap: "5px" }}
              >
                Thông tin thanh toán <Typography sx={{ color: "#F60A0A" }}>*</Typography>
              </Typography>

              <FormControl fullWidth margin="dense">
                <Autocomplete
                  options={banks}
                  getOptionLabel={(option) => `${option?.vn_name} (${option?.shortName})`}
                  value={defaultBank}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      error={!!errors.bankName}
                      helperText={errors.bankName && String(errors.bankName.message)}
                      variant="outlined"
                      fullWidth
                      margin="dense"
                    />
                  )}
                  onChange={(event, newValue) => {
                    setValue("bankName", `${newValue?.shortName}`);

                    setDefaultBank(newValue);
                    if (newValue !== null) {
                      setIsShowError(false);
                    } else {
                      setIsShowError(true);
                    }
                  }}
                />
              </FormControl>
            </Grid>
            <Grid
              item
              sx={{
                width: "50%",
                display: "flex",
                alignItems: "start",
                flexDirection: "column",
                "@media (max-width: 600px)": {
                  width: "100%",
                },
              }}
            >
              <Typography
                sx={{ fontSize: "15px", display: "flex", alignItems: "center", gap: "5px" }}
              >
                Tài khoản thanh toán <Typography sx={{ color: "#F60A0A" }}>*</Typography>
              </Typography>

              <TextField
                name="bankAccountNumber"
                error={!!errors.bankAccountNumber}
                helperText={errors.bankAccountNumber && String(errors.bankAccountNumber.message)}
                onChange={(event) => {
                  setValue("bankAccountNumber", event.target.value);
                }}
                value={watch("bankAccountNumber")}
                fullWidth
                margin="dense"
                label=""
                sx={{ marginTop: "16px" }}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={handleClose}
            color="secondary"
            sx={{
              color: "#797A7C",
              fontSize: "14px",
              fontWeight: "700",
              padding: "10px 25px",
              border: "1px solid #797A7C",
              borderRadius: "5px",
            }}
          >
            Hủy
          </Button>
          <Button
            color="primary"
            variant="contained"
            type="submit"
            sx={{
              color: "#FFFFFF",
              fontSize: "14px",
              fontWeight: "700",
              padding: "10px 25px",
              borderRadius: "5px",
              background: "#2654FE",
            }}
          >
            Lưu
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  );
};

export default EditPartnerModal;
