import React, { useEffect, useState } from "react";
import { Container, Typography, Box, Tabs, Tab, Stack, Modal, Card } from "@mui/material";
import DashboardLayout from "../../../../layouts/dashboard";
import Overview from "./component/Overview";
import PartnersManagement from "./component/PartnersManagement/PartnersManagement";
import OrderManagement from "./component/OrderManagement/OrderManagement";
import PaymentStatusTable from "./component/CommissionReport/PaymentStatusTable";
import Recruitment from "./component/Recruitment/Recruitment";
import CommissionPolicy from "./component/CommissionPolicy/CommissionPolicy";
import toast from "react-hot-toast";
import { Padding } from "@/src/styles/CommonStyle";
import TitleTypography from "@/src/components/title-typography/title-typography";

const AffiliateMarketing = () => {
  const [tabIndex, setTabIndex] = useState(0);

  const tabItems = [
    { label: "Tổng quan", value: 0 },
    { label: "Quản lý đối tác", value: 1 },
    { label: "Quản lý đơn hàng", value: 2 },
    { label: "Báo cáo hoa hồng", value: 3 },
    { label: "Trang tuyển dụng", value: 4 },
    { label: "Chính sách hoa hồng", value: 5 },
  ];

  return (
    <DashboardLayout>
      <Box sx={{ p: Padding, paddingTop: "20px" }}>
        {/* Title outside */}
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            borderBottom: "1px solid #bdbdbd",
            paddingBottom: "8px",
            mb: 2,
          }}
        >
          <TitleTypography
            sx={{
              fontSize: "20px",
              fontWeight: "700",
              color: "#000",
            }}
          >
            Tiếp thị liên kết
          </TitleTypography>
        </Box>

        {/* White box container */}
        <Card sx={{ p: 2, borderRadius: "8px" }}>
          {/* Tabs inside white box */}
          <Box sx={{ mb: 3 }}>
            <Tabs
              value={tabIndex}
              onChange={(e, newIndex) => setTabIndex(newIndex)}
              sx={{
                "& .MuiTabs-indicator": {
                  backgroundColor: "#2654FE",
                },
              }}
              variant="scrollable"
              scrollButtons={false}
            >
              {tabItems.map((tab) => (
                <Tab
                  key={tab.value}
                  label={tab.label}
                  sx={{
                    textTransform: "none",
                    color: "black",
                    minWidth: { xs: "80px", md: "auto" },
                    fontSize: { xs: "14px", md: "14px" },
                    padding: { xs: "6px 8px", md: "6px 12px" },
                    minHeight: "40px",
                    "&.Mui-selected": {
                      color: "#2654FE",
                    },
                  }}
                />
              ))}
            </Tabs>
          </Box>

          {/* Content area */}
          <Box sx={{ width: "100%" }}>
            {tabIndex === 0 && <Overview />}
            {tabIndex === 1 && <PartnersManagement />}
            {tabIndex === 2 && <OrderManagement />}
            {tabIndex === 3 && <PaymentStatusTable />}
            {tabIndex === 4 && <Recruitment />}
            {tabIndex === 5 && <CommissionPolicy />}
          </Box>
        </Card>
      </Box>
    </DashboardLayout>
  );
};

export default AffiliateMarketing;
