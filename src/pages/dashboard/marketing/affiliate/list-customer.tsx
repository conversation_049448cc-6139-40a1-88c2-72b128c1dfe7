import React, { useState } from 'react';
import {
  Box,
  Button,
  Paper,
  TextField,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  Select,
  MenuItem,
  FormControl,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Stack,
  Divider,
  InputAdornment,
} from '@mui/material';
import { DateRange, CalendarToday, ArrowDropDown } from '@mui/icons-material';
import { Sidebar } from './index';
import SearchIcon from '@mui/icons-material/Search';
import DashboardLayout from '../../../../layouts/dashboard';
import { Container } from '@mui/system';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { DateRangePicker } from '@mui/x-date-pickers-pro/DateRangePicker';

const Dashboard = () => {
  const [, setOpenDatePicker] = useState(false);
  const [dateRange, setDateRange] = useState('Full-time');

  const [activeTab, setActiveTab] = useState('danh-sach-dang-ky');

  const [openPopup, setOpenPopup] = useState(false);
  const [openPopup2, setOpenPopup2] = useState(false);
  const [selectedRow, setSelectedRow] = useState(null);
  const [open, setOpen] = useState(false);
  const [inputValueName, setInputValueName] = useState('');
  const [inputValueEmail, setInputValueEmail] = useState('');
  const [inputValueTel, setInputValueTel] = useState('');
  const [inputValueCccd, setInputValueCccd] = useState('');
  const [inputValueTaxCode, setInputValueTaxCode] = useState('');
  const [inputValuePaymentMethod, setInputValuePaymentMethod] = useState('');
  const [inputValuePaymentAccount, setInputValuePaymentAccount] = useState('');

  const handleOpenPopup = (row) => {
    setSelectedRow(row);
    setInputValueName(row?.name || '');
    setInputValueEmail(row?.email || '');
    setInputValueTel(row?.tel || '');
    setInputValueCccd(row?.cccd || '');
    setInputValueTaxCode(row?.tax_code || '');
    setInputValuePaymentMethod(row?.paymentMethod || '');
    setInputValuePaymentAccount(row?.paymentAccount || '');
    setOpenPopup(true);
  };

  const handleClosePopup = () => {
    setOpenPopup(false);
    setSelectedRow(null);
  };

  const handleUpdate = () => {
    if (selectedRow) {
      const updatedRows = rows.map((row) =>
        row.id === selectedRow.id
          ? {
              ...row,
              name: inputValueName,
              email: inputValueEmail,
              tel: inputValueTel,
              cccd: inputValueCccd,
              tax_code: inputValueTaxCode,
              paymentMethod: inputValuePaymentMethod,
              paymentAccount: inputValuePaymentAccount,
            }
          : row
      );
      setRows(updatedRows);
    }
    handleClosePopup();
  };

  const handleOpenPopup2 = (row) => {
    setSelectedRow(row);
    setOpenPopup2(true);
  };
  const handleClosePopup2 = () => {
    setOpenPopup2(false);
    setSelectedRow(null);
  };

  const handleDateRangeChange = (event) => {
    setDateRange(event.target.value);
    if (event.target.value === 'DateRange') {
      handleOpenDatePicker();
    }
  };

  const handleOpenDatePicker = () => {
    setOpenDatePicker(true);
  };

  const stats = [
    { value: '10', label: 'Tổng khách hàng' },
    { value: '5', label: 'Khách đã mua' },
    { value: '4', label: 'Đơn hàng thành công' },
  ];

  const revenue = [
    { value: '10.000.000đ', label: 'Doanh thu' },
    { value: '1.000.000đ', label: 'Hoa hồng chờ duyệt' },
    { value: '5.000.000đ', label: 'Hoa hồng đã duyệt' },
  ];

  const [rows, setRows] = useState([
    {
      id: 'ABC123',
      name: 'Ngô Văn Tuấn',
      referral_id: 'YYY333',
      introducer: 'Tâm Văn',
      account: '*********0',
      email: '<EMAIL>',
      tel: '**********',
      cccd: '*********',
      tax_code: 'TAX123',
      paymentMethod: 'Chuyển khoản',
      paymentAccount: '*********',
    },
  ]);

  const pendingRows = [
    {
      id: 'XYZ789',
      name: 'Trần Thị Hồng',
      referral_id: 'XYZ456',
      introducer: 'Lan Anh',
      account: '*********',
      tel: '0*********',
      email: '<EMAIL>',
      cccd: '***********',
      joinDate: '10/03/2025',
      paymentMethod: 'TPBANK **********',
    },
  ];

  return (
    <DashboardLayout>
      <Container
        maxWidth={false}
        sx={{
          display: 'flex',
          gap: { xs: '10px', md: '15px' },
          p: { xs: 1, md: 0 },
          m: 0,
          flexDirection: { xs: 'column', lg: 'row' },
        }}
      >
        <Sidebar />
        <Box sx={{ flexGrow: 1, padding: { xs: '15px' }, width: { xs: '100%', lg: '70%' } }}>
          <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2, borderRadius: 'none' }}>
            <Button
              sx={{
                mx: 1,
                borderBottom: activeTab === 'danh-sach-dang-ky' ? '2px solid #2654FE' : 'none',
                borderRadius: '0 !important',
                textTransform: 'none',
                fontSize: '16px',
                fontWeight: '700',
                fontFamily: 'Inter',
                color: '#000',
              }}
              color={activeTab === 'danh-sach-dang-ky' ? 'inherit' : 'inherit'}
              onClick={() => setActiveTab('danh-sach-dang-ky')}
            >
              Danh sách đăng ký
            </Button>
            <Button
              sx={{
                mx: 1,
                borderBottom: activeTab === 'cho-duyet' ? '2px solid #2654FE' : 'none',
                borderRadius: '0 !important',
                textTransform: 'none',
                fontSize: '16px',
                fontWeight: '700',
                fontFamily: 'Inter',
                color: '#000',
              }}
              color={activeTab === 'cho-duyet' ? 'inherit' : 'inherit'}
              onClick={() => setActiveTab('cho-duyet')}
            >
              Chờ duyệt
            </Button>
          </Box>

          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              mb: 2,
              gap: 2,
              '@media (max-width: 600px)': { flexDirection: 'column' },
            }}
          >
            <TextField
              variant="outlined"
              placeholder=""
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon sx={{ color: '#000000', fontSize: '32px' }} />
                  </InputAdornment>
                ),
              }}
              sx={{ flexGrow: 1, '@media (max-width: 600px)': { width: '100%' } }}
            />
            <FormControl sx={{ minWidth: 200, '@media (max-width: 600px)': { width: '100%' } }}>
              <Select
                value={dateRange}
                onChange={handleDateRangeChange}
                displayEmpty
                renderValue={(selected) => (
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <DateRange sx={{ fontSize: 20 }} />
                    <Typography>
                      {selected === 'Full-time' ? 'Toàn thời gian' : selected}
                    </Typography>
                    {selected === 'Full-time' && (
                      <Typography variant="caption" color="text.secondary"></Typography>
                    )}
                  </Box>
                )}
                inputProps={{ 'aria-label': 'Select date range' }}
              >
                <MenuItem value="Full-time">
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Typography>Toàn thời gian</Typography>
                    <Typography variant="caption" color="text.secondary"></Typography>
                  </Box>
                </MenuItem>
                <MenuItem value="Today">
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Typography>Hôm nay</Typography>
                  </Box>
                </MenuItem>
                <MenuItem value="Yesterday">
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Typography>Hôm qua</Typography>
                  </Box>
                </MenuItem>
                <MenuItem value="7 days">
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Typography>7 ngày qua</Typography>
                  </Box>
                </MenuItem>
                <MenuItem value="30 days">
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Typography>30 ngày qua</Typography>
                  </Box>
                </MenuItem>
                <MenuItem value="DateRange" onClick={handleOpenDatePicker}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Typography>Date Range</Typography>
                  </Box>
                </MenuItem>
              </Select>
            </FormControl>
          </Box>

          <Box>
            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell sx={{ minWidth: '100px' }}>Mã đối tác</TableCell>
                    <TableCell sx={{ minWidth: '100px' }}>Tên đối tác</TableCell>
                    <TableCell sx={{ minWidth: '100px' }}>Mã người giới thiệu</TableCell>
                    <TableCell sx={{ minWidth: '100px' }}>Người giới thiệu</TableCell>
                    <TableCell sx={{ minWidth: '100px' }}>Tài khoản</TableCell>
                    <TableCell sx={{ minWidth: '100px' }}>Số điện thoại</TableCell>
                    <TableCell sx={{ minWidth: '100px' }}>Email</TableCell>
                    <TableCell sx={{ minWidth: '100px' }}>CCCD</TableCell>
                    <TableCell sx={{ minWidth: '100px' }}>Ngày tham gia</TableCell>
                    <TableCell sx={{ minWidth: '100px' }}>Phương thức thanh toán</TableCell>
                    <TableCell sx={{ minWidth: '100px' }}>Quản lý</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {(activeTab === 'danh-sach-dang-ky' ? rows : pendingRows).map((row) => (
                    <TableRow key={row.id}>
                      <TableCell>{row.id}</TableCell>
                      <TableCell>{row.name}</TableCell>
                      <TableCell>{row.referral_id}</TableCell>
                      <TableCell>{row.introducer}</TableCell>
                      <TableCell>{row.account}</TableCell>
                      <TableCell>{row.tel}</TableCell>
                      <TableCell>{row.email}</TableCell>
                      <TableCell>{row.cccd}</TableCell>
                      <TableCell>{row.joinDate}</TableCell>
                      <TableCell>{row.paymentMethod}</TableCell>
                      <TableCell>
                        <Button
                          sx={{
                            color: '#000',
                            fontSize: '14px',
                            fontWeight: '400',
                            fontFamily: 'Inter',
                            textAlign: 'right',
                            width: '100%',
                            justifyContent: 'flex-end',
                          }}
                          onClick={() => handleOpenPopup(row)}
                        >
                          Sửa
                        </Button>
                        <Button
                          sx={{
                            color: '#000',
                            fontSize: '14px',
                            fontWeight: '400',
                            fontFamily: 'Inter',
                            textAlign: 'right',
                            width: '100%',
                            justifyContent: 'flex-end',
                          }}
                          onClick={() => handleOpenPopup2(row)}
                        >
                          Báo cáo nhanh
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>

            <Dialog open={openPopup} onClose={handleClosePopup} fullWidth>
              <DialogTitle>Chỉnh sửa thông tin</DialogTitle>
              <DialogContent>
                <FormControl fullWidth sx={{ mb: 2 }}>
                  <Typography
                    color="#000"
                    fontWeight="700"
                    fontSize="12px"
                    fontFamily="Inter"
                    marginBottom={'15px'}
                  >
                    Người giới thiệu
                  </Typography>
                  <Select
                    defaultValue=""
                    sx={{ width: '49%', '@media (max-width: 600px)': { width: '100%' } }}
                  >
                    <MenuItem value="">Lựa chọn</MenuItem>
                    <MenuItem value="Ngô Văn Tuấn">Ngô Văn Tuấn</MenuItem>
                    <MenuItem value="Ngô Văn A">Ngô Văn A</MenuItem>
                    <MenuItem value="Ngô Văn B">Ngô Văn B</MenuItem>
                    <MenuItem value="Ngô Văn C">Ngô Văn C</MenuItem>
                  </Select>
                </FormControl>
                <Box>
                  <Typography
                    sx={{
                      fontSize: '16px',
                      fontWeight: '700',
                      fontFamily: 'Inter',
                      marginBottom: '15px',
                    }}
                  >
                    Thông tin tài khoản
                  </Typography>
                  <FormControl
                    sx={{ width: '49%', '@media (max-width: 600px)': { width: '100%' } }}
                  >
                    <Typography
                      color="#000"
                      fontWeight="400"
                      fontSize="14px"
                      fontFamily="Inter"
                      marginBottom={'10px'}
                    >
                      Chọn loại tài khoản
                    </Typography>
                    <TextField
                      fullWidth
                      value={selectedRow?.name}
                      disabled
                      sx={{ mb: 2, backgroundColor: 'rgba(17, 25, 39, 0.04)' }}
                    />
                  </FormControl>
                  <FormControl
                    sx={{
                      width: '49%',
                      float: 'right',
                      '@media (max-width: 600px)': { width: '100%' },
                    }}
                  >
                    <Typography
                      color="#000"
                      fontWeight="400"
                      fontSize="14px"
                      fontFamily="Inter"
                      marginBottom={'10px'}
                    >
                      Số điện thoại
                    </Typography>
                    <TextField
                      fullWidth
                      value={selectedRow?.account}
                      disabled
                      sx={{ mb: 2, backgroundColor: 'rgba(17, 25, 39, 0.04)', border: 'none' }}
                    />
                  </FormControl>
                </Box>
                <Box>
                  <Typography
                    sx={{
                      fontSize: '16px',
                      fontWeight: '700',
                      fontFamily: 'Inter',
                      marginBottom: '15px',
                    }}
                  >
                    Thông tin cơ bản
                  </Typography>
                  <FormControl
                    sx={{ width: '49%', '@media (max-width: 600px)': { width: '100%' } }}
                  >
                    <Typography
                      color="#000"
                      fontWeight="400"
                      fontSize="14px"
                      fontFamily="Inter"
                      marginBottom={'10px'}
                      display={'flex'}
                      alignItems={'center'}
                      gap={'5px'}
                    >
                      Họ tên{' '}
                      <Typography color="red" display={'inline'}>
                        *
                      </Typography>
                    </Typography>
                    <TextField
                      required
                      fullWidth
                      value={inputValueName}
                      onChange={(e) => setInputValueName(e.target.value)}
                      placeholder={selectedRow?.name}
                      sx={{ mb: 2 }}
                    />
                  </FormControl>
                  <Box>
                    <FormControl
                      sx={{ width: '49%', '@media (max-width: 600px)': { width: '100%' } }}
                    >
                      <Typography
                        color="#000"
                        fontWeight="400"
                        fontSize="14px"
                        fontFamily="Inter"
                        marginBottom={'10px'}
                      >
                        Email liên hệ
                      </Typography>
                      <TextField
                        fullWidth
                        value={inputValueEmail}
                        onChange={(e) => setInputValueEmail(e.target.value)}
                        placeholder={selectedRow?.email}
                        sx={{ mb: 2 }}
                      />
                    </FormControl>
                    <FormControl
                      sx={{
                        width: '49%',
                        float: 'right',
                        '@media (max-width: 600px)': { width: '100%' },
                      }}
                    >
                      <Typography
                        color="#000"
                        fontWeight="400"
                        fontSize="14px"
                        fontFamily="Inter"
                        marginBottom={'10px'}
                      >
                        Số điện thoại liên lạc
                      </Typography>
                      <TextField
                        fullWidth
                        value={inputValueTel}
                        onChange={(e) => setInputValueTel(e.target.value)}
                        placeholder={selectedRow?.tel}
                        sx={{ mb: 2 }}
                      />
                    </FormControl>
                  </Box>
                  <FormControl
                    sx={{ width: '49%', '@media (max-width: 600px)': { width: '100%' } }}
                  >
                    <Typography
                      color="#000"
                      fontWeight="400"
                      fontSize="14px"
                      fontFamily="Inter"
                      marginBottom={'10px'}
                    >
                      Số CCCD
                    </Typography>
                    <TextField
                      fullWidth
                      value={inputValueCccd}
                      onChange={(e) => setInputValueCccd(e.target.value)}
                      placeholder={selectedRow?.cccd}
                      sx={{ mb: 2 }}
                    />
                  </FormControl>
                  <FormControl
                    sx={{
                      width: '49%',
                      float: 'right',
                      '@media (max-width: 600px)': { width: '100%' },
                    }}
                  >
                    <Typography
                      color="#000"
                      fontWeight="400"
                      fontSize="14px"
                      fontFamily="Inter"
                      marginBottom={'10px'}
                    >
                      Mã số thuế
                    </Typography>
                    <TextField
                      fullWidth
                      value={inputValueTaxCode}
                      onChange={(e) => setInputValueTaxCode(e.target.value)}
                      placeholder={selectedRow?.tax_code}
                      sx={{ mb: 2 }}
                    />
                  </FormControl>
                </Box>
                <Box>
                  <Typography
                    sx={{
                      fontSize: '16px',
                      fontWeight: '700',
                      fontFamily: 'Inter',
                      marginBottom: '15px',
                    }}
                  >
                    Tài khoản thanh toán
                  </Typography>
                  <FormControl
                    sx={{ width: '49%', '@media (max-width: 600px)': { width: '100%' } }}
                  >
                    <Typography
                      color="#000"
                      fontWeight="400"
                      fontSize="14px"
                      fontFamily="Inter"
                      marginBottom={'10px'}
                    >
                      Phương thức thanh toán
                      <Typography display={'inline'} color="red">
                        *
                      </Typography>
                    </Typography>
                    <TextField
                      fullWidth
                      required
                      value={inputValuePaymentMethod}
                      onChange={(e) => setInputValuePaymentMethod(e.target.value)}
                      placeholder={selectedRow?.paymentMethod}
                      sx={{ mb: 2 }}
                    />
                  </FormControl>
                  <FormControl
                    sx={{
                      width: '49%',
                      float: 'right',
                      '@media (max-width: 600px)': { width: '100%' },
                    }}
                  >
                    <Typography
                      color="#000"
                      fontWeight="400"
                      fontSize="14px"
                      fontFamily="Inter"
                      marginBottom={'10px'}
                    >
                      Tài khoản thanh toán
                      <Typography display={'inline'} color="red">
                        *
                      </Typography>
                    </Typography>
                    <TextField
                      fullWidth
                      required
                      value={inputValuePaymentAccount}
                      onChange={(e) => setInputValuePaymentAccount(e.target.value)}
                      placeholder={selectedRow?.paymentAccount}
                      sx={{ mb: 2 }}
                    />
                  </FormControl>
                </Box>
              </DialogContent>
              <DialogActions>
                <Button onClick={handleClosePopup}>Hủy bỏ</Button>
                <Button variant="contained" color="primary" onClick={handleUpdate}>
                  Cập nhật
                </Button>
              </DialogActions>
            </Dialog>

            <Dialog open={openPopup2} onClose={handleClosePopup2}>
              <DialogTitle
                sx={{
                  fontSize: '20px',
                  fontWeight: '700',
                  fontFamily: 'Inter',
                  color: '#000',
                  mb: '32px',
                }}
              >
                Báo cáo nhanh
              </DialogTitle>
              <DialogContent sx={{ display: 'flex', flexDirection: 'column', gap: '20px' }}>
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'start',
                    justifyContent: 'space-between',
                    backgroundColor: '#f8f9fa',
                    p: 2,
                    borderRadius: 2,
                    '@media (max-width: 600px)': { flexDirection: 'column' },
                  }}
                >
                  {stats.map((stat, index) => (
                    <Box key={stat.value} sx={{ textAlign: 'center', flex: 1 }}>
                      <Typography
                        sx={{
                          fontSize: '20px',
                          fontWeight: '700',
                          fontFamily: 'Inter',
                          color: '#000',
                        }}
                      >
                        {stat.value}
                      </Typography>
                      <Typography
                        sx={{
                          fontSize: '16px',
                          fontWeight: '400',
                          fontFamily: 'Inter',
                          color: '#000',
                        }}
                      >
                        {stat.label}
                      </Typography>
                      {index < stats.length - 1 && (
                        <Divider orientation="vertical" flexItem sx={{ height: '40px', mx: 2 }} />
                      )}
                    </Box>
                  ))}
                </Box>
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'start',
                    justifyContent: 'space-between',
                    backgroundColor: '#f8f9fa',
                    p: 2,
                    borderRadius: 2,
                    '@media (max-width: 600px)': { flexDirection: 'column' },
                  }}
                >
                  {revenue.map((revenues, index) => (
                    <Box key={revenues.value} sx={{ textAlign: 'center', flex: 1 }}>
                      <Typography
                        sx={{
                          fontSize: '20px',
                          fontWeight: '700',
                          fontFamily: 'Inter',
                          color: '#000',
                        }}
                      >
                        {revenues.value}
                      </Typography>
                      <Typography
                        sx={{
                          fontSize: '16px',
                          fontWeight: '400',
                          fontFamily: 'Inter',
                          color: '#000',
                        }}
                      >
                        {revenues.label}
                      </Typography>
                      {index < revenue.length - 1 && (
                        <Divider orientation="vertical" flexItem sx={{ height: '40px', mx: 2 }} />
                      )}
                    </Box>
                  ))}
                </Box>
              </DialogContent>
            </Dialog>
          </Box>

          <LocalizationProvider dateAdapter={AdapterDayjs}>
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'flex-end',
                marginTop: '45px',
                flexDirection: 'column',
                gap: '15px',
                alignItems: 'flex-end',
              }}
            >
              <Button
                sx={{ width: 'fit-content' }}
                variant="outlined"
                onClick={() => setOpen(!open)}
              >
                <Stack
                  direction="row"
                  sx={{
                    color: '#000',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '15px',
                    textTransform: 'none',
                  }}
                  alignItems="center"
                  spacing={1}
                >
                  <CalendarToday sx={{ fontSize: '16px', color: '#787878', marginRight: '15px' }} />
                  Today
                  <ArrowDropDown sx={{ fontSize: '16px', color: '#787878' }} />
                </Stack>
              </Button>
              {open && <DateRangePicker localeText={{ start: 'Start', end: 'End' }} />}
            </Box>
          </LocalizationProvider>
        </Box>
      </Container>
    </DashboardLayout>
  );
};

export default Dashboard;
