import React from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Button,
  Typography,
  Box,
  Container,
} from '@mui/material';
import FilterAltIcon from '@mui/icons-material/FilterAlt';
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';
import DashboardLayout from '../../../../layouts/dashboard';
import { Sidebar } from './index';

const orders = [
  {
    id: '#12344',
    time: '8:00 22/12/2024',
    quantity: 2,
    revenue: '1.000.000đ',
    customer: 'Thanh Tùng',
    referrer: 'Tâm Văn',
    commission: '100.000đ',
    status: 'Phê duyệt',
  },
];

const OrderTable = () => {
  return (
    <DashboardLayout>
      <Container
        maxWidth={false}
        sx={{
          display: 'flex',
          gap: { xs: '10px', md: '15px' },
          p: { xs: 1, md: 0 },
          m: 0,
          flexDirection: { xs: 'column', lg: 'row' },
          boxShadow: 'none',
        }}
      >
        <Sidebar />
        <Paper
          sx={{
            flexGrow: 1,
            padding: { xs: '15px' },
            width: { xs: '100%', lg: '70%' },
            boxShadow: 'none',
          }}
        >
          <Typography style={{ fontWeight: 'bold', marginBottom: '25px' }}>
            Danh sách đơn hàng thành công
          </Typography>
          <Box
            style={{
              display: 'flex',
              justifyContent: 'flex-end',
              gap: '10px',
              marginBottom: '10px',
              padding: '15px 0 30px 0',
              borderTop: '1px solid #787878',
            }}
          >
            <Button
              variant="outlined"
              startIcon={<CalendarMonthIcon />}
              sx={{
                color: '#000',
                fontWeight: '400',
                borderColor: '#000',
                fontFamily: 'Inter',
                fontSize: '16px',
              }}
            >
              Toàn thời gian
            </Button>
            <Button
              variant="outlined"
              startIcon={<FilterAltIcon />}
              sx={{
                display: 'flex',
                alignItems: 'center',
                gap: '10px',
                flexDirection: 'row-reverse',
                fontWeight: '400',
                fontFamily: 'Inter',
                fontSize: '16px',
                color: '#000',
                borderColor: '#000',
              }}
            >
              Tất cả
            </Button>
          </Box>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell sx={{ minWidth: '100px' }}>Mã đơn hàng</TableCell>
                  <TableCell sx={{ minWidth: '100px' }}>Thời gian đặt</TableCell>
                  <TableCell sx={{ minWidth: '100px' }}>Số lượng</TableCell>
                  <TableCell sx={{ minWidth: '100px' }}>Doanh thu</TableCell>
                  <TableCell sx={{ minWidth: '100px' }}>Khách hàng</TableCell>
                  <TableCell sx={{ minWidth: '100px' }}>Người giới thiệu</TableCell>
                  <TableCell sx={{ minWidth: '100px' }}>Hoa hồng</TableCell>
                  <TableCell sx={{ minWidth: '100px' }}>Quản lý</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {orders.map((order) => (
                  <TableRow key={order.id}>
                    <TableCell sx={{ borderBottom: '1px solid #787878' }}>{order.id}</TableCell>
                    <TableCell sx={{ borderBottom: '1px solid #787878' }}>{order.time}</TableCell>
                    <TableCell sx={{ borderBottom: '1px solid #787878' }}>
                      {order.quantity}
                    </TableCell>
                    <TableCell sx={{ borderBottom: '1px solid #787878' }}>
                      {order.revenue}
                    </TableCell>
                    <TableCell sx={{ borderBottom: '1px solid #787878' }}>
                      {order.customer}
                    </TableCell>
                    <TableCell sx={{ borderBottom: '1px solid #787878' }}>
                      {order.referrer}
                    </TableCell>
                    <TableCell sx={{ borderBottom: '1px solid #787878' }}>
                      {order.commission}
                    </TableCell>
                    <TableCell sx={{ borderBottom: '1px solid #787878' }}>{order.status}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </Paper>
      </Container>
    </DashboardLayout>
  );
};

export default OrderTable;
