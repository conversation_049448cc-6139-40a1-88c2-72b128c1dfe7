import React from "react";
import {
  Box,
  Typography,
  Paper,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Stack,
  Grid,
} from "@mui/material";
import { Upload } from "@mui/icons-material";
import { useMembershipLevel } from "@/src/api/hooks/membership-level/use-membership-level";
import { useStoreId } from "@/src/hooks/use-store-id";
import { useSnackbar } from "notistack";
import { MembershipImageUpload } from "./SetUpPoint";
import { useFormik } from "formik";
import * as yup from "yup";
import { FILE_SIZE_2MB } from "@/src/constants/constant";
const SUPPORTED_FORMATS = ["image/jpg", "image/jpeg", "image/png"];

const initialValues = {
  ranks: [{ id: 1, logo: null, name: "", minSpend: "", rate: "" }],
};

const validateFileFormat = (value) => {
  if (typeof value === "string" && value.startsWith("http")) {
    return true;
  }
  return (
    value &&
    (value instanceof File || value instanceof Blob) &&
    SUPPORTED_FORMATS.includes(value.type)
  );
};

const validateFileSize = (value) => {
  if (typeof value === "string" && value.startsWith("http")) {
    return true;
  }
  return value && (value instanceof File || value instanceof Blob) && value.size <= FILE_SIZE_2MB;
};

const validateMinSpend = (value) => {
  const numeric = Number((value || "").replace(/[^0-9]/g, ""));
  return numeric <= 1000000000;
};

const validateRateRange = (value) => {
  const numeric = Number(value);
  return numeric >= 0 && numeric <= 100;
};

export const schemaMembershipPoint = yup.object({
  ranks: yup.array().of(
    yup.object({
      logo: yup
        .mixed()
        .required("Logo hạng là bắt buộc")
        .test(
          "fileFormat",
          "Định dạng file không hợp lệ (chỉ chấp nhận jpg, jpeg, png)",
          validateFileFormat
        )
        .test(
          "fileSize",
          `Kích thước file không được vượt quá ${FILE_SIZE_2MB / (1024 * 1024)}MB`,
          validateFileSize
        ),
      name: yup
        .string()
        .required("Tên hạng là bắt buộc")
        .max(255, "Tên hạng phải có tối đa 255 ký tự"),
      minSpend: yup
        .string()
        .required("Tổng chi tiêu là bắt buộc")
        .test("min-spend", "Tổng chi tiêu không lớn hơn 1 tỉ", validateMinSpend),
      rate: yup
        .string()
        .required("Tỉ lệ là bắt buộc")
        .test("rate-range", "Tỉ lệ phải nằm trong khoảng 0 đến 100", validateRateRange),
    })
  ),
});

const AddRank = ({ goBack, onSuccess }) => {
  const { createMembershipLevel, updateMembershipLevelImage } = useMembershipLevel();
  const storeId = useStoreId();
  const { enqueueSnackbar } = useSnackbar();

  const handleSubmit = async (values, { setSubmitting }) => {
    try {
      const fileImg = formik.values.ranks[0].logo;
      const rank = values.ranks[0];

      const pointRate = parseInt(rank.rate, 10);
      const spendingThreshold = parseInt(rank.minSpend, 10);

      const createData = {
        shopId: storeId,
        levelName: rank.name,
        image: {
          type: "FILE",
          link: "",
        },
        pointRate,
        spendingThreshold,
      };

      setSubmitting(true);

      const response = await createMembershipLevel(createData);

      if (response?.data?.levelId && formik.values.ranks?.[0].logo) {
        await updateMembershipLevelImage(response.data.levelId, fileImg);
      }

      enqueueSnackbar("Thêm mới hạng thành viên thành công", { variant: "success" });
      onSuccess?.();
      goBack();
    } catch (error) {
      enqueueSnackbar("Lỗi khi tạo hạng thành viên", { variant: "error" });
    } finally {
      setSubmitting(false);
    }
  };

  const formik = useFormik({
    initialValues,
    validationSchema: schemaMembershipPoint,
    onSubmit: handleSubmit,
  });

  const handleLogoChange = (file) => {
    formik.setFieldValue("ranks[0].logo", file);
  };

  const formatNumberWithCommas = (value) => {
    if (!value) return "";
    const rawValue = value.replace(/\D/g, "");
    if (!rawValue) return "";
    return rawValue.replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  };

  return (
    <form onSubmit={formik.handleSubmit}>
      <Stack direction={"row"} gap={3} flexDirection={"column"}>
        <Stack
          direction={"row"}
          gap={3}
          sx={{ "@media (max-width: 880px)": { flexDirection: "column" } }}
        >
          <Box flex={2} sx={{ width: "70%", "@media (max-width: 880px)": { width: "100%" } }}>
            <Typography variant="h6" sx={{ fontWeight: "bold", mb: 2 }}>
              Thiết lập tích điểm
            </Typography>

            <TableContainer component={Paper}>
              <Table sx={{ minWidth: "800px" }}>
                <TableHead>
                  <TableRow>
                    <TableCell align="left" sx={{ minWidth: 200 }}>
                      Logo hạng
                    </TableCell>
                    <TableCell>Tên hạng</TableCell>
                    <TableCell>Tổng chi tiêu tối thiểu (VNĐ)</TableCell>
                    <TableCell>Tỉ lệ tích điểm (%)</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {formik.values.ranks.map((row, index) => (
                    <TableRow key={row.id || index}>
                      <TableCell sx={{ position: "relative" }}>
                        <MembershipImageUpload
                          onLogoChange={handleLogoChange}
                          uploadIcon={<Upload />}
                          currentLogo={formik.values.ranks[0].logo}
                        />
                        <Typography
                          sx={{
                            position: "absolute",
                            bottom: 0,
                            color: "red",
                            fontSize: 12,
                          }}
                        >
                          {formik.touched.ranks?.[index] &&
                          (formik.errors.ranks?.[index] as { [key: string]: string })?.logo
                            ? (formik.errors.ranks?.[index] as { [key: string]: string })?.logo
                            : " "}
                        </Typography>
                      </TableCell>
                      <TableCell sx={{ position: "relative" }}>
                        <TextField
                          fullWidth
                          size="small"
                          name={`ranks[${index}].name`}
                          variant="outlined"
                          value={formik.values.ranks[index].name}
                          onChange={formik.handleChange}
                          onBlur={formik.handleBlur}
                        />
                        <Typography
                          sx={{ position: "absolute", bottom: 0, color: "red", fontSize: 12 }}
                        >
                          {formik.touched.ranks?.[index]?.name &&
                          (formik.errors.ranks?.[index] as { [key: string]: string })?.name
                            ? (formik.errors.ranks?.[index] as { [key: string]: string })?.name
                            : " "}
                        </Typography>
                      </TableCell>
                      <TableCell sx={{ position: "relative" }}>
                        <TextField
                          fullWidth
                          size="small"
                          variant="outlined"
                          name={`ranks[${index}].minSpend`}
                          value={formatNumberWithCommas(formik.values.ranks[index].minSpend)}
                          onChange={(e) => {
                            const val = e.target.value.replace(/\D/g, "");
                            formik.setFieldValue(`ranks[${index}].minSpend`, val);
                          }}
                          onBlur={formik.handleBlur}
                          inputProps={{ min: 0 }}
                        />
                        <Typography
                          sx={{ position: "absolute", bottom: 0, color: "red", fontSize: 12 }}
                        >
                          {formik.touched.ranks?.[index]?.minSpend &&
                          (formik.errors.ranks?.[index] as { [key: string]: string })?.minSpend
                            ? (formik.errors.ranks?.[index] as { [key: string]: string })?.minSpend
                            : " "}
                        </Typography>
                      </TableCell>

                      <TableCell sx={{ position: "relative" }}>
                        <TextField
                          fullWidth
                          size="small"
                          variant="outlined"
                          name={`ranks[${index}].rate`}
                          value={formik.values.ranks[index].rate}
                          onChange={(e) => {
                            const value = e.target.value;
                            if (/^\d*\.?\d*$/.test(value)) {
                              const decimalCount = (value.match(/\./g) || []).length;
                              if (decimalCount <= 1) {
                                const parts = value.split(".");
                                if (parts[1]?.length > 2) {
                                  return;
                                }
                                formik.setFieldValue(`ranks[${index}].rate`, value);
                              }
                            }
                          }}
                          onBlur={formik.handleBlur}
                          onKeyDown={(e) => {
                            if (["e", "E", "-", "+"].includes(e.key)) {
                              e.preventDefault();
                            }
                          }}
                          inputProps={{
                            inputMode: "decimal",
                            style: { textAlign: "left" },
                          }}
                          onWheel={(e) => e.target instanceof HTMLElement && e.target.blur()}
                        />
                        <Typography
                          sx={{ position: "absolute", bottom: 0, color: "red", fontSize: 12 }}
                        >
                          {formik.touched.ranks?.[index]?.rate &&
                          (formik.errors.ranks?.[index] as { [key: string]: string })?.rate
                            ? (formik.errors.ranks?.[index] as { [key: string]: string })?.rate
                            : " "}
                        </Typography>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Box>
        </Stack>

        <Grid container justifyContent="flex-end" sx={{ mt: 3 }}>
          <Button
            onClick={goBack}
            sx={{
              mr: 2,
              px: 2,
              py: 1,
            }}
            variant="outlined"
            disabled={formik.isSubmitting}
          >
            Hủy bỏ
          </Button>
          <Button variant="contained" color="primary" type="submit" disabled={formik.isSubmitting}>
            {formik.isSubmitting ? "Đang xử lý..." : "Xác nhận"}
          </Button>
        </Grid>
      </Stack>
    </form>
  );
};

export default AddRank;
