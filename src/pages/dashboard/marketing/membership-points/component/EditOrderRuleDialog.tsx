import React, { useState, useEffect } from "react";
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  RadioGroup,
  FormControlLabel,
  Radio,
  Typography,
  Stack,
  Box,
  InputAdornment,
} from "@mui/material";
import { TypeRewardCondition, TypePointCalculation } from "src/api/types/membership.types";

const EditOrderRuleDialog = ({ open, onClose, onSave, initialData }) => {
  const [conversionRate, setConversionRate] = useState(initialData?.conversionRate || "1000");
  const [pointGainMethod, setPointGainMethod] = useState(
    initialData?.isPointAdd || TypeRewardCondition.PaymentSuccess
  );
  const [calculationMethod, setCalculationMethod] = useState(
    initialData?.isScore || TypePointCalculation.TotalOrderValue
  );
  const [amount, setAmount] = useState(initialData?.amount || 1000);

  const handleSave = () => {
    onSave({
      isPointAdd: pointGainMethod,
      isScore: calculationMethod,
    });
  };

  useEffect(() => {
    if (initialData?.isPointAdd !== undefined) {
      setPointGainMethod(initialData.isPointAdd);
      setCalculationMethod(initialData.isScore);
    }
  }, [initialData]);

  const handleAmountChange = (event) => {
    const value = event.target.value.replace(/[^0-9]/g, "");
    setAmount(value ? parseInt(value, 10) : "");
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Typography fontSize="20px" fontWeight="700" sx={{ display: "flex", alignItems: "center" }}>
          Kiếm điểm{" "}
          <Typography fontSize="18px" color="textSecondary" sx={{ marginLeft: "10px" }}>
            (Đặt hàng)
          </Typography>
        </Typography>
      </DialogTitle>

      <DialogContent>
        <Typography fontSize="16px" fontWeight="700" mb={1}>
          Quy tắc đổi điểm
        </Typography>
        <Stack
          direction={"row"}
          alignItems="center"
          gap={1}
          sx={{ "@media (max-width: 600px)": { flexDirection: "column" } }}
        >
          <TextField
            variant="outlined"
            size="small"
            value={amount.toLocaleString()}
            onChange={handleAmountChange}
            sx={{
              width: "20%",
              padding: "0 !important",
              backgroundColor: "#F5F5F5",
              borderRadius: "8px",
              pointerEvents: "none",
              "& .MuiInputBase-input": {
                textAlign: "center",
                padding: "5px 15px",
              },
              "& .MuiOutlinedInput-root": {
                padding: "0 !important",
              },
              "@media (max-width: 600px)": {
                width: "100%",
              },
            }}
            InputProps={{
              endAdornment: (
                <InputAdornment
                  position="end"
                  sx={{ borderLeft: "1px solid #ccc", padding: "2px 15px" }}
                >
                  đ
                </InputAdornment>
              ),
            }}
          />

          <Typography>=</Typography>

          <Stack
            direction={"row"}
            alignItems="center"
            border="1px solid #979797"
            borderRadius="8px"
            overflow="hidden"
            bgcolor="#f5f5f5"
            sx={{
              "@media (max-width: 600px)": {
                width: "100%",
              },
            }}
          >
            <Box
              sx={{
                padding: "5px 10px",
                minWidth: "60px",
                textAlign: "center",
                "@media (max-width: 600px)": {
                  width: "100%",
                },
              }}
            >
              <Typography>{amount ? Math.floor(amount / 1000) : 0}</Typography>
            </Box>
            <Box
              sx={{
                background: "#f5f5f5",
                padding: "5px 10px",
                borderLeft: "1px solid #ccc",
              }}
            >
              <Typography variant="body2">Điểm</Typography>
            </Box>
          </Stack>
        </Stack>
        <Typography fontSize="14px" color="textSecondary" mb={2} marginTop={2}>
          Với mỗi {conversionRate} đ người dùng chi tiêu sẽ tương đương với 1 điểm.
        </Typography>

        <Stack
          direction={"row"}
          alignItems="center"
          gap={2}
          sx={{
            "@media (max-width: 600px)": { flexDirection: "column" },
          }}
        >
          <Box sx={{ width: "50%", "@media (max-width: 600px)": { width: "100%" } }}>
            <Typography fontSize="16px" fontWeight="700" mb={1} marginTop={2}>
              Cộng điểm khi
            </Typography>
            <RadioGroup
              value={pointGainMethod}
              onChange={(e) => setPointGainMethod(e.target.value)}
            >
              <FormControlLabel
                value={TypeRewardCondition.PaymentSuccess}
                control={<Radio color="primary" />}
                label="Cộng điểm khi thanh toán thành công"
                sx={{
                  ".MuiFormControlLabel-label": {
                    fontSize: "14px",
                  },
                  "@media (max-width: 600px)": {
                    marginBottom: "20px",
                  },
                }}
              />
              <FormControlLabel
                value={TypeRewardCondition.DeliverySuccess}
                control={<Radio color="primary" />}
                label="Cộng điểm khi giao hàng thành công"
                sx={{
                  ".MuiFormControlLabel-label": {
                    fontSize: "14px",
                  },
                }}
              />
            </RadioGroup>
          </Box>

          <Box
            sx={{
              width: "50%",
              "@media (max-width: 600px)": { width: "100%" },
            }}
          >
            <Typography fontSize="16px" fontWeight="700" mt={2} mb={1}>
              Cách tính điểm
            </Typography>
            <RadioGroup
              value={calculationMethod}
              onChange={(e) => setCalculationMethod(e.target.value)}
            >
              <FormControlLabel
                value={TypePointCalculation.TotalOrderValue}
                control={<Radio color="primary" />}
                label="Tổng giá trị thanh toán đơn hàng"
                sx={{
                  ".MuiFormControlLabel-label": {
                    fontSize: "14px",
                  },
                }}
              />
              <FormControlLabel
                value={TypePointCalculation.TotalExcludingShipping}
                control={<Radio color="primary" />}
                label="Tổng giá trị thanh toán đơn hàng trừ phí vận chuyển"
                sx={{
                  ".MuiFormControlLabel-label": {
                    fontSize: "14px",
                  },
                  "@media (max-width: 600px)": {
                    marginBottom: "20px",
                  },
                }}
              />
            </RadioGroup>
          </Box>
        </Stack>
      </DialogContent>

      <DialogActions>
        <Button
          onClick={onClose}
          variant="outlined"
          sx={{
            textTransform: "none",
            color: "#797A7C",
            border: "1px solid #797A7C",
            padding: "10px 30px ",
          }}
        >
          Hủy bỏ
        </Button>
        <Button
          onClick={handleSave}
          variant="contained"
          sx={{
            backgroundColor: "#2654FE",
            textTransform: "none",
            padding: "10px 30px ",
          }}
        >
          Xác nhận
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default EditOrderRuleDialog;
