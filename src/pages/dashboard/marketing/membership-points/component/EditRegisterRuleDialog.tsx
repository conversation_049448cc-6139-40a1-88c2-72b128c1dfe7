import React, { useState, useEffect } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  TextField,
  Stack,
  IconButton,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import { useSnackbar } from "notistack";

const EditRegisterRuleDialog = ({ open, onClose, onSave, initialData }) => {
  const [registerPoints, setRegisterPoints] = useState(initialData?.rule || "10");
  const { enqueueSnackbar } = useSnackbar();
  const handleSave = () => {
    if (Number(registerPoints) < 0) {
      enqueueSnackbar("Điểm đăng ký phải lớn hơn 0! Vui lòng nhập lại", { variant: "error" });
      return;
    }

    onSave({
      ...initialData,
      rule: registerPoints,
    });
  };

  useEffect(() => {
    if (initialData?.rule !== undefined) {
      setRegisterPoints(initialData.rule);
    }
  }, [initialData]);

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>
        <Stack direction={"row"} justifyContent="space-between" alignItems="center">
          <Typography fontSize="20px" fontWeight={700}>
            Kiếm điểm{" "}
            <Typography component="span" variant="body2" color="textSecondary">
              {" "}
              (Đăng ký mới){" "}
            </Typography>
          </Typography>
          <IconButton onClick={onClose}>
            <CloseIcon />
          </IconButton>
        </Stack>
      </DialogTitle>

      <DialogContent>
        <Typography fontSize="16px" fontWeight="700" mb={1}>
          Quy tắc
        </Typography>
        <Stack
          direction={"row"}
          alignItems="center"
          gap={1}
          mt={2}
          sx={{ "@media (max-width: 600px)": { flexDirection: "column" } }}
        >
          <Typography>Kiếm điểm khi đăng ký thành công :</Typography>
          <TextField
            value={registerPoints}
            type="number"
            onChange={(e) => {
              const value = e.target.value;
              if (Number(value) >= 0) {
                setRegisterPoints(value);
              }
            }}
            onKeyDown={(e) => {
              if (e.key === "e" || e.key === "E" || e.key === "-" || e.key === "+") {
                e.preventDefault();
              }
            }}
            inputProps={{
              min: 0,
              step: 1,
            }}
            // variant="outlined"
            size="small"
            sx={{ paddingRight: 0 }}
            InputProps={{
              endAdornment: (
                <Typography
                  sx={{
                    color: "#000",
                    fontSize: "14px",
                    padding: "10px",
                    borderRadius: "0 8px 8px 0",
                    // background: "#eeeeee",
                  }}
                >
                  điểm
                </Typography>
              ),
            }}
          />
        </Stack>
      </DialogContent>

      <DialogActions sx={{ padding: "16px" }}>
        <Button onClick={onClose} variant="outlined" sx={{ textTransform: "none" }}>
          Hủy bỏ
        </Button>
        <Button
          onClick={handleSave}
          variant="contained"
          sx={{ backgroundColor: "#2654FE", textTransform: "none" }}
        >
          Xác nhận
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default EditRegisterRuleDialog;
