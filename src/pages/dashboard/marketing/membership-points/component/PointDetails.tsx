import React, { useState, useEffect } from "react";
import {
  Box,
  Button,
  Paper,
  Typography,
  Tabs,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Stack,
  Avatar,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  IconButton,
  Radio,
  RadioGroup,
  FormControlLabel,
  Tooltip,
  InputAdornment,
} from "@mui/material";
import { Close } from "@mui/icons-material";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import { useMembershipLevel } from "@/src/api/hooks/membership-level/use-membership-level";
import { useSnackbar } from "notistack";
import dayjs from "dayjs";
import { ExchangeHistoryType } from "@/src/api/types/membership.types";
import { useStoreId } from "@/src/hooks/use-store-id";
import { usePathname } from "next/navigation";
import { useAllPermissions } from "@/src/api/hooks/auth/use-all-permissions";
import { PERMISSION_TYPE_ENUM } from "@/src/constants/constant";
import ModalChangePoint from "./ModalChangePoint";
import { AdjustPointState } from "./UserPointsTab";
import TruncatedText from "@/src/components/truncated-text/truncated-text";
interface UserPointDetailProps {
  user?: any;
  onBack?: () => void;
  setUser?: any;
  updateUser?: (userId: string) => Promise<void>;
}
const UserPointDetail: React.FC<UserPointDetailProps> = ({ user, onBack, setUser, updateUser }) => {
  const pathname = usePathname();
  const [tabIndex, setTabIndex] = useState(0);
  const [pointHistory, setPointHistory] = useState([]);
  const [spendHistory, setSpendHistory] = useState([]);
  const [loading, setLoading] = useState(false);

  const { getIncomeHistory, getSpendingHistory, updateUserPoints, getDetailUserPoint } =
    useMembershipLevel();
  const { enqueueSnackbar } = useSnackbar();
  const handleTabChange = (_, newIndex) => setTabIndex(newIndex);
  const storeId = useStoreId();
  const [openDialog, setOpenDialog] = useState(false);
  const [userHistory, setUserHistory] = useState(null);
  const [adjustPointData, setAdjustPointData] = useState<AdjustPointState>({
    adjustType: "add",
    points: "",
    reason: "",
    isSubmitting: false,
  });
  const { permissions } = useAllPermissions();
  const isGranted = (url, permission) => {
    if (url.endsWith("/")) {
      url = url.slice(0, -1);
    }
    const matchingUrl = Object.keys(permissions).find((key) => key === url);
    if (!matchingUrl) return false;
    return permissions[matchingUrl]?.includes(permission) || false;
  };

  const handleOpenDialog = () => {
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setAdjustPointData((prev) => ({
      ...prev,
      points: "",
      reason: "",
    }));
  };

  const getTypeDisplay = (type) => {
    return ExchangeHistoryType[type] || type;
  };

  const fetchIncomeHistory = async () => {
    try {
      setLoading(true);
      const response = await getIncomeHistory(
        user?.shopId || userHistory?.shopId || storeId,
        user.id
      );

      if (response?.data) {
        const formattedData = response.data.data.map((item) => ({
          time: dayjs(item.created).format("DD/MM/YYYY HH:mm:ss"),
          value: `+${item.pointsEarned}`,
          type: getTypeDisplay(item.type),
          detail: item.note,
        }));
        setPointHistory(formattedData);
      }
    } catch (error) {
      console.error("Error fetching income history:", error);
      enqueueSnackbar("Có lỗi khi tải lịch sử tích điểm", { variant: "error" });
    } finally {
      setLoading(false);
    }
  };

  const fetchSpendingHistory = async () => {
    try {
      setLoading(true);
      const response = await getSpendingHistory(
        user?.shopId || userHistory?.shopId || storeId,
        user.id
      );

      if (response?.data) {
        const formattedData = response.data.data.map((item) => ({
          time: dayjs(item.created).format("DD/MM/YYYY HH:mm:ss"),
          value: `${item.pointsEarned}`,
          type: getTypeDisplay(item.type),
          detail: item.note,
        }));
        setSpendHistory(formattedData);
      }
    } catch (error) {
      console.error("Error fetching spending history:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleUpdatePoints = async () => {
    try {
      setAdjustPointData((prev) => ({ ...prev, isSubmitting: true }));

      // Validate
      if (!adjustPointData?.points || !adjustPointData?.reason) {
        enqueueSnackbar("Vui lòng nhập đầy đủ thông tin", { variant: "error" });
        return;
      }

      const updateData = {
        shopId: storeId,
        userId: user.id,
        note: adjustPointData?.reason,
        point:
          adjustPointData?.adjustType === "add"
            ? adjustPointData?.points
            : -adjustPointData?.points,
        isAdd: adjustPointData?.adjustType === "add",
        type: "Adjust",
      };

      const response = await updateUserPoints(updateData);
      if (response?.data.status === true) {
        enqueueSnackbar("Điều chỉnh điểm thành công", { variant: "success" });
        handleCloseDialog();
        fetchIncomeHistory();
        fetchSpendingHistory();
        updateUser(user?.id);
      }
    } catch (error) {
      console.error("Error updating points:", error);
      enqueueSnackbar(error?.response?.data?.message, { variant: "error" });
    } finally {
      setAdjustPointData((prev) => ({ ...prev, isSubmitting: false }));
    }
  };
  const fetchDetailUserPoint = async () => {
    if (!user?.points || !user?.spentPoints || !user?.currentPoints) {
      const res = await getDetailUserPoint(storeId, user.id);
      if (res?.status === 200) {
        setUserHistory(res?.data?.data);
      }
    }
  };

  useEffect(() => {
    if (user?.id) {
      fetchIncomeHistory();
      fetchSpendingHistory();
    }
  }, [user?.id]);

  useEffect(() => {
    if (user?.id && storeId) {
      fetchDetailUserPoint();
    }
  }, [user?.id, storeId]);

  return (
    <Box sx={{ padding: 2 }}>
      <Stack
        direction={"row"}
        justifyContent={"space-between"}
        alignItems={"center"}
        sx={{
          "@media (max-width: 600px)": {
            flexDirection: "column",
            alignItems: "start",
          },
        }}
      >
        <Button
          startIcon={<ArrowBackIcon />}
          onClick={onBack}
          sx={{ mb: 0, color: "#000", fontWeight: "bold", textTransform: "none", fontSize: "20px" }}
        >
          Chi tiết điểm người dùng
        </Button>

        {isGranted(pathname, PERMISSION_TYPE_ENUM.Edit) ? (
          <Button
            variant="contained"
            onClick={handleOpenDialog}
            sx={{
              height: "40px",
              borderRadius: "8px",
              textTransform: "none",
              px: 2,
              background: "#2654FE !important",
              minWidth: "auto",
              color: "white",
            }}
          >
            Điều chỉnh điểm
          </Button>
        ) : (
          <Tooltip title="Bạn không có quyền điều chỉnh điểm">
            <span>
              <Button
                variant="contained"
                disabled={true}
                sx={{
                  height: "40px",
                  borderRadius: "8px",
                  textTransform: "none",
                  px: 2,
                  minWidth: "auto",
                  color: "white",
                }}
              >
                Điều chỉnh điểm
              </Button>
            </span>
          </Tooltip>
        )}
      </Stack>

      <Stack
        direction={"row"}
        alignItems={"center"}
        sx={{
          padding: 2,
          borderRadius: 2,
          justifyContent: "space-between",
          boxShadow: "0 0 10px 0 rgba(0, 0, 0, 0.1)",
          margin: "10px 0 30px 0",
          "@media (max-width: 600px)": {
            flexDirection: "column",
            alignItems: "start",
          },
        }}
      >
        <Stack
          sx={{ "@media (max-width: 600px)": { width: "100%" } }}
          width="50%"
          direction="row"
          spacing={2}
          alignItems="center"
        >
          <Box sx={{ display: "flex", flexDirection: "column", padding: "16px" }}>
            <Box sx={{ display: "flex", justifyContent: "space-between" }}>
              <Avatar
                src={user.avatar || userHistory?.avatar}
                alt={user.name}
                sx={{ width: 50, height: 50 }}
              />
              <Box sx={{ display: "flex", flexDirection: "column", marginLeft: 2 }}>
                <TruncatedText
                  typographyProps={{ width: 250, fontSize: 18, fontWeight: 500 }}
                  text={user.name || userHistory?.fullName}
                />
                <Typography sx={{ color: "#000", my: 0.5 }}>
                  {user.phone || userHistory?.phone}
                </Typography>
                <Typography></Typography>
                <TruncatedText
                  typographyProps={{ width: 250 }}
                  text={user?.membershipLevel?.levelName || userHistory?.membershipLevel?.levelName}
                />
              </Box>
            </Box>
          </Box>
        </Stack>
        <Box
          sx={{
            mt: 2,
            padding: 2,
            borderRadius: 2,
            width: "50%",
            "@media (max-width: 600px)": { width: "100%" },
          }}
        >
          <Stack direction={"column"} justifyContent={"space-between"}>
            <Stack direction={"row"} justifyContent={"space-between"}>
              <Typography sx={{ color: "#000", width: "70%" }}>Tổng điểm đã tích lũy</Typography>
              <Typography sx={{ fontWeight: "bold", width: "30%" }}>
                {user.currentPoints || userHistory?.totalPoint}
              </Typography>
            </Stack>
            <Stack direction={"row"} justifyContent={"space-between"}>
              <Typography sx={{ color: "#000", width: "70%" }}>Tổng điểm hiện có</Typography>
              <Typography sx={{ fontWeight: "bold", width: "30%" }}>
                {user.points || userHistory?.currentPoint}
              </Typography>
            </Stack>
            <Stack direction={"row"} justifyContent={"space-between"}>
              <Typography sx={{ color: "#000", width: "70%" }}>Điểm đã chi tiêu</Typography>
              <Typography sx={{ fontWeight: "bold", width: "30%" }}>
                {user.spentPoints || userHistory?.spentPoint}
              </Typography>
            </Stack>
            <Stack direction={"row"} justifyContent={"space-between"}>
              <Typography sx={{ color: "#000", width: "70%" }}>Doanh thu</Typography>
              <Typography sx={{ fontWeight: "bold", width: "30%" }}>
                {user.totalSpent || userHistory?.spentAmount}
              </Typography>
            </Stack>
          </Stack>
        </Box>
      </Stack>

      <Box
        sx={{
          padding: 2,
          borderRadius: 2,
          boxShadow: "0 0 10px 0 rgba(0, 0, 0, 0.1)",
          mt: 2,
          overflow: "hidden",
        }}
      >
        <Tabs value={tabIndex} onChange={handleTabChange} sx={{ mt: 2 }}>
          <Tab label="Lịch sử thu thập" />
          <Tab label="Lịch sử chi tiêu" />
        </Tabs>

        <TableContainer
          // variant="scrollable"
          // scrollButtons="auto"
          component={Paper}
          sx={{ mt: 2 }}
        >
          <Table>
            <TableHead>
              <TableRow>
                <TableCell sx={{ minWidth: "150px" }}>Thời gian</TableCell>
                <TableCell sx={{ minWidth: "150px" }}>Giá trị</TableCell>
                <TableCell sx={{ minWidth: "150px" }}>Loại giao dịch</TableCell>
                <TableCell sx={{ minWidth: "150px" }}>Chi tiết</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={4} align="center">
                    <Typography>Đang tải...</Typography>
                  </TableCell>
                </TableRow>
              ) : (tabIndex === 0 ? pointHistory : spendHistory).length > 0 ? (
                (tabIndex === 0 ? pointHistory : spendHistory).map((item, index) => (
                  <TableRow key={index}>
                    <TableCell>{item.time}</TableCell>
                    <TableCell
                      sx={{
                        color: item.value.includes("+") ? "#22A163" : "red",
                        fontWeight: "bold",
                      }}
                    >
                      {item.value}
                    </TableCell>
                    <TableCell>{item.type}</TableCell>
                    <TableCell>{item.detail}</TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={4} align="center">
                    <Typography>Không có dữ liệu</Typography>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
      </Box>

      <ModalChangePoint
        openDialog={openDialog}
        user={user}
        handleCloseDialog={handleCloseDialog}
        adjustPointData={adjustPointData}
        setAdjustPointData={setAdjustPointData}
        handleUpdatePoints={handleUpdatePoints}
      />
    </Box>
  );
};

export default UserPointDetail;
