import React, { useState, useEffect } from "react";
import {
  Switch,
  FormControlLabel,
  Typography,
  Box,
  Paper,
  Button,
  Select,
  MenuItem,
  Stack,
  IconButton,
  Tooltip,
} from "@mui/material";
import EditIcon from "@mui/icons-material/Edit";
import EditOrderRuleDialog from "./EditOrderRuleDialog";
import EditRegisterRuleDialog from "./EditRegisterRuleDialog";
import EditReferralRuleDialog from "./EditReferralRuleDialog";
import DiscountShoppingDialog from "./EditDiscountShoppingDialog";
import { useMembershipLevel } from "@/src/api/hooks/membership-level/use-membership-level";
import { useStoreId } from "@/src/hooks/use-store-id";
import { useSnackbar } from "notistack";
import {
  DEFAULT_EARN_POINT_CONFIG,
  DEFAULT_EXCHANGE_POINTS_CONFIG,
  TypeValidUntil,
  TYPE_VALID_UNTIL_DISPLAY,
} from "src/api/types/membership.types";
import { config } from "process";
import { usePathname } from "next/navigation";
import { useAllPermissions } from "@/src/api/hooks/auth/use-all-permissions";
import { PERMISSION_TYPE_ENUM } from "@/src/constants/constant";
const Section = (props) => (
  <Paper sx={{ padding: 2, marginBottom: 2, backgroundColor: "#fff" }} {...props} />
);

const RuleItem = ({ title, description, enabled, onToggle, onEdit, hideEdit, isGranted }) => (
  <Stack direction={"row"} alignItems="center" justifyContent="space-between" mb={2}>
    <Box paddingRight={2}>
      <Typography variant="subtitle1" fontWeight={600}>
        {title}
      </Typography>
      <Typography variant="body2" color="textSecondary">
        {description}
      </Typography>
    </Box>
    <Stack direction={"row"} alignItems="center" gap={0}>
      {!hideEdit && (
        <>
          {isGranted ? (
            <IconButton onClick={onEdit} size="small">
              <EditIcon sx={{ color: "#2654FE" }} />
            </IconButton>
          ) : (
            <Tooltip title="Bạn không có quyền sửa">
              <span>
                <IconButton sx={{ color: "primary.main" }} disabled={!isGranted} size="small">
                  <EditIcon />
                </IconButton>
              </span>
            </Tooltip>
          )}
        </>
      )}
      <Switch checked={enabled} onChange={onToggle} color="primary" disabled={!isGranted} />
    </Stack>
  </Stack>
);

const RulesTab = () => {
  const pathname = usePathname();
  const [loading, setLoading] = useState(false);
  const { getConfig, updateConfig } = useMembershipLevel();
  const storeId = useStoreId();
  const { enqueueSnackbar } = useSnackbar();
  const [openEditOrder, setOpenEditOrder] = useState(false);
  const [openEditRegister, setOpenEditRegister] = useState(false);
  const [openDiscountShoppingDialog, setOpenDiscountShoppingDialog] = useState(false);
  const [openReferralDialog, setOpenReferralDialog] = useState(false);
  const [expiry, setExpiry] = useState("Unlimited");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [configDataUpdate, setConfigDataUpdate] = useState({
    shopId: "",
    status: false,
    exchangePoints: DEFAULT_EXCHANGE_POINTS_CONFIG,
    earnPoint: DEFAULT_EARN_POINT_CONFIG,
    validUntil: "Unlimited",
  });
  const { permissions } = useAllPermissions();
  const isGranted = (url, permission) => {
    if (url.endsWith("/")) {
      url = url.slice(0, -1);
    }
    const matchingUrl = Object.keys(permissions).find((key) => key === url);
    if (!matchingUrl) return false;
    return permissions[matchingUrl]?.includes(permission) || false;
  };

  const fetchConfig = async () => {
    try {
      setLoading(true);
      const response = await getConfig(storeId);

      if (response?.data) {
        const earnPoint = JSON.parse(response.data.earnPoint);
        const exchangePoints = JSON.parse(response.data.exchangePoints);

        setConfigDataUpdate({
          ...configDataUpdate,
          shopId: response.data?.shopId,
          earnPoint,
          exchangePoints,
          validUntil: response.data?.validUntil,
          status: response.data?.status,
        });
        // Cập nhật state
        // setProgramEnabled(response.data.status);
        // setEarnPointConfig(earnPoint);
        // setExchangePointsConfig(exchangePoints);
        setExpiry(response.data.validUntil || TypeValidUntil.Unlimited);

        // // Cập nhật rules từ config
        // setRules({
        //   order: earnPoint.orderJson?.status || false,
        //   register: earnPoint.registerJson?.status || false,
        //   referral: earnPoint.shareJson?.status || false,
        //   discount: exchangePoints.discountJson?.status || false,
        //   voucher: exchangePoints.voucherJson?.status || false,
        // });
      } else if (response?.status === 204) {
        enqueueSnackbar(
          "Cửa hàng đang chưa có Thiết lập quy tắc điểm. Vui lòng tạo Hạng thành viên !",
          { variant: "info" }
        );
      }
    } catch (error) {
      enqueueSnackbar("Có lỗi khi tải cấu hình", { variant: "error" });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (storeId) {
      setConfigDataUpdate((prev) => ({ ...prev, shopId: storeId }));
      fetchConfig();
    }
  }, [storeId]);

  const handleSaveExpiry = async () => {
    setIsSubmitting(true);

    const newConfigData = { ...configDataUpdate, validUntil: expiry };

    await handleSaveConfigNew(newConfigData);
    setIsSubmitting(false);
  };

  const handleEdit = (rule) => {
    if (rule === "order") setOpenEditOrder(true);
    if (rule === "register") setOpenEditRegister(true);
    if (rule === "referral") setOpenReferralDialog(true);
    if (rule === "discount") setOpenDiscountShoppingDialog(true);
  };

  const handleRuleToggle = (rule) => {
    let newConfigData = { ...configDataUpdate };
    if (rule === "order" || rule === "register" || rule === "referral") {
      newConfigData = {
        ...newConfigData,
        earnPoint: {
          ...newConfigData.earnPoint,
          orderJson:
            rule === "order"
              ? {
                  ...newConfigData.earnPoint.orderJson,
                  status: !newConfigData.earnPoint.orderJson.status,
                }
              : newConfigData.earnPoint.orderJson,
          registerJson:
            rule === "register"
              ? {
                  ...newConfigData.earnPoint.registerJson,
                  status: !newConfigData.earnPoint.registerJson.status,
                }
              : newConfigData.earnPoint.registerJson,
          shareJson:
            rule === "referral"
              ? {
                  ...newConfigData.earnPoint.shareJson,
                  status: !newConfigData.earnPoint.shareJson.status,
                }
              : newConfigData.earnPoint.shareJson,
        },
      };
    }

    if (rule === "discount" || rule === "voucher") {
      newConfigData = {
        ...newConfigData,
        exchangePoints: {
          ...newConfigData.exchangePoints,
          discountJson:
            rule === "discount"
              ? {
                  ...newConfigData.exchangePoints.discountJson,
                  status: !newConfigData.exchangePoints.discountJson.status,
                }
              : newConfigData.exchangePoints.discountJson,
          voucherJson:
            rule === "voucher"
              ? {
                  ...newConfigData.exchangePoints.voucherJson,
                  status: !newConfigData.exchangePoints.voucherJson.status,
                }
              : newConfigData.exchangePoints.voucherJson,
        },
      };
    }

    setConfigDataUpdate(newConfigData);
    handleSaveConfigNew(newConfigData);
  };

  const handleOrderRuleChange = async (orderData) => {
    const newConfigData = {
      ...configDataUpdate,
      earnPoint: {
        ...configDataUpdate.earnPoint,
        orderJson: {
          ...configDataUpdate.earnPoint.orderJson,
          isPointAdd: Number(orderData.isPointAdd),
          isScore: Number(orderData.isScore),
        },
      },
    };

    setConfigDataUpdate(newConfigData);
    await handleSaveConfigNew(newConfigData);

    setOpenEditOrder(false);
  };

  const handleRegisterRuleChange = async (registerData) => {
    const newConfigData = {
      ...configDataUpdate,
      earnPoint: {
        ...configDataUpdate.earnPoint,
        registerJson: {
          ...configDataUpdate.earnPoint.registerJson,
          ...registerData,
        },
      },
    };
    setConfigDataUpdate(newConfigData);
    await handleSaveConfigNew(newConfigData);
    setOpenEditRegister(false);
  };

  const handleReferralRuleChange = async (referralData) => {
    const newConfigData = {
      ...configDataUpdate,
      earnPoint: {
        ...configDataUpdate.earnPoint,
        shareJson: {
          ...configDataUpdate.earnPoint.shareJson,
          ...referralData,
        },
      },
    };

    setConfigDataUpdate(newConfigData);
    await handleSaveConfigNew(newConfigData);
    setOpenReferralDialog(false);
  };

  const handleDiscountRuleChange = async (discountData) => {
    const newConfigData = {
      ...configDataUpdate,
      exchangePoints: {
        ...configDataUpdate.exchangePoints,
        discountJson: {
          ...configDataUpdate.exchangePoints.discountJson,
          ...discountData,
        },
      },
    };

    setConfigDataUpdate(newConfigData);
    await handleSaveConfigNew(newConfigData);
    setOpenDiscountShoppingDialog(false);
  };

  const handleChangeStatusProgram = async () => {
    const newStatus = !configDataUpdate.status;

    const newConfigUpdate = {
      ...configDataUpdate,
      status: newStatus,
    };
    setConfigDataUpdate(newConfigUpdate);
    handleSaveConfigNew(newConfigUpdate);
  };

  const handleSaveConfigNew = async (config) => {
    try {
      const configData = {
        shopId: config.shopId,
        status: config.status,
        validUntil: config.validUntil,
        earnPoint: JSON.stringify(config.earnPoint),
        exchangePoints: JSON.stringify(config.exchangePoints),
      };
      await updateConfig(configData, storeId);
      enqueueSnackbar("Cập nhật cấu hình thành công", { variant: "success" });
      await fetchConfig();
    } catch (error) {
      console.error("Error updating config:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Box sx={{ marginBottom: "100px" }}>
      <Section sx={{ padding: "0px", boxShadow: "none" }}>
        <Stack
          direction={"row"}
          alignItems="center"
          justifyContent="space-between"
          gap={2}
          sx={{
            "@media (max-width: 767px)": {
              flexDirection: "column",
            },
          }}
        >
          <Box sx={{ width: "30%", "@media (max-width: 767px)": { width: "100%" } }}>
            <Typography sx={{ fontSize: "16px", fontWeight: "bold", marginBottom: "10px" }}>
              Trạng thái chương trình
            </Typography>
            <Typography sx={{ fontSize: "14px", color: "#6d747f" }}>
              Bật gói, người dùng có thể bắt đầu sử dụng điểm. Sau khi đóng, việc tích lũy điểm sẽ
              dừng lại.
            </Typography>
          </Box>
          <FormControlLabel
            sx={{
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
              width: "70%",
              "@media (max-width: 767px)": {
                width: "100%",
              },
              boxShadow: "0px 0px 10px 0px rgba(0, 0, 0, 0.1)",
              borderRadius: "10px",
              padding: "10px 20px",
              margin: "0px",
            }}
            label={
              <Typography variant="h6" fontWeight={600}>
                Trạng thái
              </Typography>
            }
            control={
              <Switch
                checked={configDataUpdate.status}
                onChange={handleChangeStatusProgram}
                disabled={!isGranted(pathname, PERMISSION_TYPE_ENUM.Edit)}
              />
            }
            labelPlacement="start"
          />
        </Stack>
      </Section>

      {configDataUpdate.status && (
        <>
          <Stack
            direction={"row"}
            gap={2}
            padding={"0px"}
            boxShadow={"none"}
            marginTop={"50px"}
            sx={{
              "@media (max-width: 767px)": {
                flexDirection: "column",
              },
            }}
          >
            <Box sx={{ width: "30%", "@media (max-width: 767px)": { width: "100%" } }}>
              <Typography variant="h6" fontWeight={600} gutterBottom>
                Kiếm điểm
              </Typography>
              <Typography variant="body2" color="textSecondary">
                Đặt nhiệm vụ cho người dùng để kiếm điểm
              </Typography>
            </Box>
            <Stack
              direction={"column"}
              sx={{
                width: "70%",
                boxShadow: "0px 0px 10px 0px rgba(0, 0, 0, 0.1)",
                borderRadius: "10px",
                padding: "10px 20px",
                gap: 2,
                "@media (max-width: 767px)": {
                  width: "100%",
                },
              }}
            >
              <RuleItem
                title="Đặt hàng"
                description="Khách hàng được tích điểm theo quy tắc đã đặt khi mua hàng thành công"
                enabled={configDataUpdate.earnPoint.orderJson.status}
                onToggle={() => handleRuleToggle("order")}
                onEdit={() => handleEdit("order")}
                hideEdit={false}
                isGranted={isGranted(pathname, PERMISSION_TYPE_ENUM.Edit)}
              />
              <RuleItem
                title="Đăng ký mới"
                description={`Đăng ký thành công để nhận ${configDataUpdate.earnPoint.registerJson.rule} điểm`}
                enabled={configDataUpdate.earnPoint.registerJson.status}
                onToggle={() => handleRuleToggle("register")}
                hideEdit={false}
                onEdit={() => handleEdit("register")}
                isGranted={isGranted(pathname, PERMISSION_TYPE_ENUM.Edit)}
              />
              <RuleItem
                title="Giới thiệu khách hàng"
                description="Mời bạn bè truy cập cửa hàng và kích hoạt tài khoản thành công để được thưởng điểm"
                enabled={configDataUpdate.earnPoint.shareJson.status}
                onToggle={() => handleRuleToggle("referral")}
                hideEdit={false}
                onEdit={() => handleEdit("referral")}
                isGranted={isGranted(pathname, PERMISSION_TYPE_ENUM.Edit)}
              />
            </Stack>
          </Stack>

          <EditOrderRuleDialog
            open={openEditOrder}
            onClose={() => setOpenEditOrder(false)}
            onSave={handleOrderRuleChange}
            initialData={configDataUpdate.earnPoint.orderJson}
          />
          <EditRegisterRuleDialog
            open={openEditRegister}
            onClose={() => setOpenEditRegister(false)}
            onSave={handleRegisterRuleChange}
            initialData={configDataUpdate.earnPoint.registerJson}
          />
          <EditReferralRuleDialog
            open={openReferralDialog}
            onClose={() => setOpenReferralDialog(false)}
            onSave={handleReferralRuleChange}
            initialData={configDataUpdate.earnPoint.shareJson}
          />
          <Stack
            direction={"row"}
            gap={2}
            padding={"0px"}
            boxShadow={"none"}
            marginTop={"50px"}
            sx={{
              "@media (max-width: 767px)": {
                flexDirection: "column",
              },
            }}
          >
            <Box sx={{ width: "30%", "@media (max-width: 767px)": { width: "100%" } }}>
              <Typography variant="h6" fontWeight={600} gutterBottom>
                Đổi điểm
              </Typography>
              <Typography variant="body2" color="textSecondary">
                Thiết lập cách người sử dụng điểm và đổi điểm để nhận lợi ích
              </Typography>
            </Box>
            <Stack
              direction={"column"}
              sx={{
                width: "70%",
                boxShadow: "0px 0px 10px 0px rgba(0, 0, 0, 0.1)",
                borderRadius: "10px",
                padding: "10px 20px",
                gap: 2,
                "@media (max-width: 767px)": {
                  width: "100%",
                },
              }}
            >
              <RuleItem
                title="Giảm giá mua sắm"
                description={`1 điểm = ${configDataUpdate.exchangePoints.discountJson.rule} đ`}
                enabled={configDataUpdate.exchangePoints.discountJson.status}
                onToggle={() => handleRuleToggle("discount")}
                onEdit={() => handleEdit("discount")}
                hideEdit={false}
                isGranted={isGranted(pathname, PERMISSION_TYPE_ENUM.Edit)}
              />
              <RuleItem
                title="Quy đổi voucher"
                description="Người tiêu dùng sử dụng điểm để đổi lấy voucher theo tỉ lệ của cửa hàng"
                enabled={configDataUpdate.exchangePoints.voucherJson.status}
                onToggle={() => handleRuleToggle("voucher")}
                hideEdit={true}
                onEdit={() => {}}
                isGranted={isGranted(pathname, PERMISSION_TYPE_ENUM.Edit)}
              />
            </Stack>
          </Stack>

          <DiscountShoppingDialog
            open={openDiscountShoppingDialog}
            onClose={() => setOpenDiscountShoppingDialog(false)}
            onSave={handleDiscountRuleChange}
            initialData={configDataUpdate.exchangePoints.discountJson}
          />

          <Stack
            direction={"row"}
            gap={2}
            padding={"0px"}
            boxShadow={"none"}
            marginTop={"50px"}
            sx={{
              "@media (max-width: 767px)": {
                flexDirection: "column",
              },
            }}
          >
            <Box sx={{ width: "30%", "@media (max-width: 767px)": { width: "100%" } }}>
              <Typography variant="h6" fontWeight={600} gutterBottom>
                Điểm hết hạn
              </Typography>
              <Typography variant="body2" color="textSecondary">
                Xác định thời điểm có thể sử dụng điểm sau khi nhận được. Điểm được trả lại do hoàn
                tiền và các lý do khác vẫn giữ nguyên thời hạn hiệu lực ban đầu
              </Typography>
            </Box>
            <Stack
              direction={"column"}
              sx={{
                width: "70%",
                boxShadow: "0px 0px 10px 0px rgba(0, 0, 0, 0.1)",
                borderRadius: "10px",
                padding: "10px 20px",
                gap: 2,
                "@media (max-width: 767px)": {
                  width: "100%",
                },
              }}
            >
              <Typography sx={{ fontSize: "16px", fontWeight: "500", marginBottom: "5px" }}>
                Thời hạn có hiệu lực
              </Typography>
              <Select
                sx={{ width: "70%", height: "40px" }}
                value={expiry}
                onChange={(e) => setExpiry(e.target.value)}
                displayEmpty
                disabled={!isGranted(pathname, PERMISSION_TYPE_ENUM.Edit)}
              >
                {Object.entries(TYPE_VALID_UNTIL_DISPLAY).map(([value, label]) => (
                  <MenuItem key={value} value={value}>
                    {label}
                  </MenuItem>
                ))}
              </Select>
              <Typography variant="body2" color="textSecondary">
                Việc tính toán sẽ bắt đầu sau khi điểm được thu thập thành công
              </Typography>
              {isGranted(pathname, PERMISSION_TYPE_ENUM.Edit) && (
                <Stack direction={"row"} justifyContent="flex-end" mt={2} mb={1}>
                  <Button
                    variant="outlined"
                    disabled={isSubmitting}
                    onClick={() => fetchConfig()}
                    sx={{
                      mr: 2,
                      color: "#000",
                      borderColor: "#000",
                      textTransform: "none",
                      padding: "10px 30px",
                    }}
                  >
                    Hủy bỏ
                  </Button>
                  <Button
                    sx={{
                      backgroundColor: "#2654FE",
                      color: "#fff",
                      textTransform: "none",
                      padding: "10px 30px",
                      "&:hover": {
                        backgroundColor: "#1c3fcc",
                      },
                      "&:disabled": {
                        backgroundColor: "#ccc",
                      },
                    }}
                    disabled={isSubmitting}
                    onClick={handleSaveExpiry}
                  >
                    {isSubmitting ? "Đang lưu" : " Xác nhận"}
                  </Button>
                </Stack>
              )}
            </Stack>
          </Stack>
        </>
      )}
    </Box>
  );
};

export default RulesTab;
