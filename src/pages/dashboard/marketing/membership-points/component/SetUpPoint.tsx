import React, { useEffect, useState } from "react";
import {
  Box,
  Typography,
  Paper,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Stack,
  Grid,
  List,
  ListItem,
  Avatar,
} from "@mui/material";
import { Upload } from "@mui/icons-material";
import { useMembershipLevel } from "@/src/api/hooks/membership-level/use-membership-level";
import { useStoreId } from "@/src/hooks/use-store-id";
import { useSnackbar } from "notistack";
import { useFormik } from "formik";
import { logger } from "@/src/utils/logger";
import { ImageProcessor } from "@/src/utils/image-processor";
import { ImageType } from "nextjs-api-lib";
import { schemaMembershipPoint } from "./AddRank";
const getUpdateInitialValues = (editData) => ({
  ranks: [
    {
      id: editData?.levelId || null,
      logo: editData?.image?.link || null,
      name: editData?.levelName || "",
      minSpend: editData?.spendingThreshold?.toString() || "",
      rate: editData?.pointRate?.toString() || "",
      logoFile: null,
    },
  ],
});
const SetupPoint = ({ goBack, editData, onSuccess }) => {
  const { updateMembershipLevel, updateMembershipLevelImage } = useMembershipLevel();
  const storeId = useStoreId();
  const { enqueueSnackbar } = useSnackbar();

  const handleUpdateSubmit = async (values, { setSubmitting }) => {
    try {
      setSubmitting(true);
      const rank = values.ranks[0];

      const pointRate = parseFloat(rank.rate);
      const spendingThreshold = parseInt(rank.minSpend, 10);

      const updateData = {
        levelId: rank.id,
        levelName: rank.name,
        spendingThreshold: spendingThreshold,
        pointRate: pointRate,
        shopId: storeId,
      };

      await updateMembershipLevel(updateData);

      if (rank.logoFile instanceof File) {
        await updateMembershipLevelImage(rank.id, rank.logoFile);
      }

      enqueueSnackbar("Cập nhật hạng thành viên thành công", { variant: "success" });
      onSuccess?.();
      goBack();
    } catch (error) {
      enqueueSnackbar("Lỗi khi cập nhật hạng thành viên: " + error.message, { variant: "error" });
    } finally {
      setSubmitting(false);
    }
  };

  const formik = useFormik({
    initialValues: getUpdateInitialValues(editData),
    validationSchema: schemaMembershipPoint,
    onSubmit: handleUpdateSubmit,
  });

  const handleLogoChange = (file) => {
    formik.setFieldValue("ranks[0].logo", file);
    formik.setFieldValue("ranks[0].logoFile", file);
  };

  const formatNumberWithCommas = (value) => {
    if (!value) return "";
    const rawValue = value.replace(/\D/g, "");
    if (!rawValue) return "";
    return rawValue.replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  };

  return (
    <form onSubmit={formik.handleSubmit}>
      <Stack direction={"row"} gap={3} flexDirection={"column"}>
        <Stack
          direction={"row"}
          gap={3}
          sx={{ "@media (max-width: 880px)": { flexDirection: "column" } }}
        >
          <Box flex={2} sx={{ width: "70%", "@media (max-width: 880px)": { width: "100%" } }}>
            <Typography variant="h6" sx={{ fontWeight: "bold", mb: 2 }}>
              Thiết lập tích điểm
            </Typography>

            <TableContainer component={Paper}>
              <Table sx={{ minWidth: "800px" }}>
                <TableHead>
                  <TableRow>
                    <TableCell align="left" sx={{ minWidth: 120 }}>
                      Logo hạng
                    </TableCell>
                    <TableCell sx={{ minWidth: 150 }}>Tên hạng</TableCell>
                    <TableCell>Tổng chi tiêu tối thiểu (VNĐ)</TableCell>
                    <TableCell>Tỉ lệ tích điểm (%)</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {formik.values.ranks.map((row, index) => (
                    <TableRow key={row.id || index}>
                      <TableCell sx={{ position: "relative" }} align="center">
                        <MembershipImageUpload
                          onLogoChange={handleLogoChange}
                          uploadIcon={<Upload />}
                          currentLogo={row?.logo}
                        />
                        <Typography
                          sx={{ position: "absolute", bottom: 0, color: "red", fontSize: 12 }}
                        >
                          {formik.touched.ranks?.[index] &&
                          (formik.errors.ranks?.[index] as { [key: string]: string })?.logo
                            ? (formik.errors.ranks?.[index] as { [key: string]: string })?.logo
                            : " "}
                        </Typography>
                      </TableCell>
                      <TableCell sx={{ position: "relative" }}>
                        <TextField
                          fullWidth
                          size="small"
                          name={`ranks[${index}].name`}
                          variant="outlined"
                          value={formik.values.ranks[index].name}
                          onChange={formik.handleChange}
                          onBlur={formik.handleBlur}
                        />
                        <Typography
                          sx={{ position: "absolute", bottom: 0, color: "red", fontSize: 12 }}
                        >
                          {formik.touched.ranks?.[index]?.name &&
                          (formik.errors.ranks?.[index] as { [key: string]: string })?.name
                            ? (formik.errors.ranks?.[index] as { [key: string]: string })?.name
                            : " "}
                        </Typography>
                      </TableCell>
                      <TableCell sx={{ position: "relative" }}>
                        <TextField
                          fullWidth
                          size="small"
                          variant="outlined"
                          name={`ranks[${index}].minSpend`}
                          value={formatNumberWithCommas(formik.values.ranks[index].minSpend)}
                          onChange={(e) => {
                            const val = e.target.value.replace(/\D/g, "");
                            formik.setFieldValue(`ranks[${index}].minSpend`, val);
                          }}
                          onBlur={formik.handleBlur}
                          inputProps={{ min: 0 }}
                        />
                        <Typography
                          sx={{ position: "absolute", bottom: 0, color: "red", fontSize: 12 }}
                        >
                          {formik.touched.ranks?.[index]?.minSpend &&
                          (formik.errors.ranks?.[index] as { [key: string]: string })?.minSpend
                            ? (formik.errors.ranks?.[index] as { [key: string]: string })?.minSpend
                            : " "}
                        </Typography>
                      </TableCell>
                      <TableCell sx={{ position: "relative" }}>
                        <TextField
                          fullWidth
                          size="small"
                          variant="outlined"
                          name={`ranks[${index}].rate`}
                          value={formik.values.ranks[index].rate}
                          onChange={(e) => {
                            const value = e.target.value;
                            if (/^\d*(\.\d{0,2})?$/.test(value)) {
                              formik.setFieldValue(`ranks[${index}].rate`, value);
                            }
                          }}
                          onBlur={formik.handleBlur}
                          onKeyDown={(e) => {
                            if (
                              !/^\d$/.test(e.key) &&
                              e.key !== "." &&
                              e.key !== "Backspace" &&
                              e.key !== "Delete" &&
                              e.key !== "ArrowLeft" &&
                              e.key !== "ArrowRight" &&
                              e.key !== "Tab"
                            ) {
                              e.preventDefault();
                            }
                          }}
                          inputProps={{
                            inputMode: "decimal",
                            style: { textAlign: "left" },
                          }}
                          onWheel={(e) => e.target instanceof HTMLElement && e.target.blur()}
                        />
                        <Typography
                          sx={{ position: "absolute", bottom: 0, color: "red", fontSize: 12 }}
                        >
                          {formik.touched.ranks?.[index]?.rate &&
                          (formik.errors.ranks?.[index] as { [key: string]: string })?.rate
                            ? (formik.errors.ranks?.[index] as { [key: string]: string })?.rate
                            : " "}
                        </Typography>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Box>
          {/* Phần còn lại của UI giữ nguyên */}
          {/* ... Phần ví dụ minh họa và ảnh ở cuối ... */}
          <Paper
            sx={{
              p: 3,
              bgcolor: "#fef9e7",
              boxShadow: " 0 4px 4px 0 #00000040",
              marginBottom: "150px",
              width: "30%",
              "@media (max-width: 880px)": { width: "100%" },
            }}
          >
            <Stack direction={"row"} gap={2} justifyContent={"space-between"}>
              <Stack direction={"column"} gap={1} width={"70%"}>
                <Typography
                  sx={{
                    color: "#000000",
                    fontSize: "24px",
                    fontWeight: "400",
                    fontStyle: "italic",
                  }}
                >
                  Ví dụ minh họa
                </Typography>
                <Typography sx={{ color: "#000000", fontSize: "16px", fontWeight: "400" }}>
                  Điểm được tính tích lũy dữ trên tổng giá trị đơn hàng đã trừ giảm giá và phí vận
                  chuyển
                </Typography>
              </Stack>
              <Stack
                direction={"row"}
                alignItems={"start"}
                justifyContent={"center"}
                sx={{
                  backgroundColor: "#FFF7E6",
                  borderRadius: "8px",
                  padding: "8px",
                }}
              >
                <Typography
                  sx={{
                    fontSize: "24px",
                    fontWeight: "bold",
                    background: "linear-gradient(90deg, #22A163 0%, #E58E26 100%)",
                    WebkitBackgroundClip: "text",
                    WebkitTextFillColor: "transparent",
                  }}
                >
                  10%
                </Typography>
              </Stack>
            </Stack>

            <Box sx={{ position: "relative" }}>
              <List sx={{ marginTop: "30px" }}>
                <ListItem sx={{ padding: 0, gap: "15px", marginBottom: "30px" }}>
                  <img src="/assets/customerlist/Vector.png" alt="icon" />
                  <Typography
                    sx={{
                      color: "#000000",
                      fontSize: "16px",
                      fontWeight: "400",
                      fontStyle: "italic",
                    }}
                  >
                    Người tiêu dùng mua đơn hàng 1.000.000đ
                  </Typography>
                </ListItem>
                <ListItem sx={{ padding: 0, gap: "15px", marginBottom: "30px" }}>
                  <img src="/assets/customerlist/Vector.png" alt="icon" />
                  <Typography
                    sx={{
                      color: "#000000",
                      fontSize: "16px",
                      fontWeight: "400",
                      fontStyle: "italic",
                    }}
                  >
                    Người tiêu dùng được tích lũy 100 điểm
                  </Typography>
                </ListItem>
                <ListItem sx={{ padding: 0, gap: "15px", marginBottom: "30px" }}>
                  <img src="/assets/customerlist/Vector.png" alt="icon" />
                  <Typography
                    sx={{
                      color: "#000000",
                      fontSize: "16px",
                      fontWeight: "400",
                      fontStyle: "italic",
                    }}
                  >
                    Khi người tiêu dùng mua sắm trong tương lai 100 điểm = 100.000đ
                  </Typography>
                </ListItem>
                <ListItem sx={{ padding: 0, gap: "15px", marginBottom: "30px" }}>
                  <img src="/assets/customerlist/Vector.png" alt="icon" />
                  <Typography
                    sx={{
                      color: "#000000",
                      fontSize: "16px",
                      fontWeight: "400",
                      fontStyle: "italic",
                    }}
                  >
                    Người tiêu dùng cũng có thể đổi điểm thành voucher theo chương trình của cửa
                    hàng
                  </Typography>
                </ListItem>
                <ListItem sx={{ padding: 0, gap: "15px", marginBottom: "30px" }}>
                  <img src="/assets/customerlist/Vector.png" alt="icon" />
                  <Typography
                    sx={{
                      color: "#000000",
                      fontSize: "16px",
                      fontWeight: "400",
                      fontStyle: "italic",
                    }}
                  >
                    Điểm được sử dụng vô thời hạn
                  </Typography>
                </ListItem>
              </List>
              <img
                src="/assets/customerlist/noto-v1_money-bag.png"
                width={150}
                height={150}
                alt="Group Icon"
                style={{ position: "absolute", bottom: "-100px", right: "-50px" }}
              />
            </Box>
          </Paper>
        </Stack>

        <Grid container justifyContent="flex-end" sx={{ mt: 3 }}>
          <Button
            onClick={goBack}
            variant="outlined"
            sx={{
              mr: 2,
              px: 2,
              py: 1,
            }}
            disabled={formik.isSubmitting}
          >
            Hủy bỏ
          </Button>
          <Button variant="contained" color="primary" type="submit" disabled={formik.isSubmitting}>
            {formik.isSubmitting ? "Đang xử lý..." : "Xác nhận"}
          </Button>
        </Grid>
      </Stack>
    </form>
  );
};

export default SetupPoint;

export const MembershipImageUpload = ({ onLogoChange, currentLogo, uploadIcon }) => {
  const [logo, setLogo] = useState(null);
  const [previewUrl, setPreviewUrl] = useState(currentLogo);

  useEffect(() => {
    if (currentLogo && currentLogo instanceof Blob) {
      const url = URL.createObjectURL(currentLogo);
      setPreviewUrl(url);

      return () => URL.revokeObjectURL(url);
    } else if (typeof currentLogo === "string") {
      setPreviewUrl(currentLogo);
    } else {
      setPreviewUrl(null);
    }
  }, [currentLogo]);

  const handleFileChange = async (event) => {
    const file = event.target.files?.[0];

    if (file && file.type.startsWith("image/")) {
      try {
        logger.debug("Processing logo image:", { fileName: file.name, fileSize: file.size });

        const processedFile = await ImageProcessor.processImage(file, {
          maxSizeMB: 0.5,
          maxWidth: 512,
          maxHeight: 512,
          quality: 0.9,
          maintainAspectRatio: true,
          allowedTypes: [ImageType.JPEG, ImageType.PNG, ImageType.WEBP],
        });

        const newPreviewUrl = URL.createObjectURL(processedFile);

        logger.debug("Logo processed successfully", {
          originalSize: file.size,
          processedSize: processedFile.size,
          fileName: processedFile.name,
        });

        if (previewUrl && previewUrl !== currentLogo) {
          URL.revokeObjectURL(previewUrl);
        }

        setLogo(processedFile);
        setPreviewUrl(newPreviewUrl);
        onLogoChange(processedFile);
      } catch (error) {
        logger.error("Error processing logo:", error);
        setLogo(null);
        setPreviewUrl(currentLogo);
        onLogoChange(null);
      }
    } else {
      logger.warn("Invalid file type selected for logo");
      setLogo(null);
      setPreviewUrl(currentLogo);
      onLogoChange(null);
    }
  };

  useEffect(() => {
    return () => {
      if (previewUrl && previewUrl !== currentLogo) {
        URL.revokeObjectURL(previewUrl);
      }
    };
  }, [previewUrl, currentLogo]);

  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        alignItems: "flex-start",
        justifyContent: "left",
        cursor: "pointer",
      }}
      onClick={() => {
        document.getElementById("logo-input")?.click();
      }}
    >
      <input
        id="logo-input"
        type="file"
        accept="image/*"
        style={{ display: "none" }}
        onChange={handleFileChange}
      />
      <Avatar
        src={previewUrl}
        sx={{
          width: 50,
          height: 50,
          borderRadius: "100%",
          mb: 1,
          objectFit: "cover",
          transition: "border-color 0.3s ease",
          backgroundColor: "#f5f5f5",
          "& img": {
            objectFit: "cover",
            p: 1,
          },
        }}
      >
        {uploadIcon}
      </Avatar>
    </Box>
  );
};
