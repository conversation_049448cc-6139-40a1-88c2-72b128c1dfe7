import React, { useState } from "react";
import { Container, Typography, Box, Tabs, Tab } from "@mui/material";
import OverviewTab from "./component/OverviewTab";
import UserPointsTab from "./component/UserPointsTab";
import RedeemProfileTab from "./component/HistoryPoint";
import RulesTab from "./component/RulesTab";
import DashboardLayout from "../../../../layouts/dashboard";
import { Padding } from "@/src/styles/CommonStyle";

const MemberPoints = () => {
  const [tabIndex, setTabIndex] = useState(0);
  const [isAddRank, setIsAddRank] = useState(false);

  return (
    <DashboardLayout>
      <Box
        sx={{
          marginBottom: "100px",
          padding: Padding,
          "& .MuiContainer-root": {
            padding: 0,
          },
        }}
      >
        <Typography
          fontSize={"20px"}
          fontWeight={"700"}
          lineHeight={1}
          paddingBottom={"20px"}
          borderBottom={"1px solid #bdbdbd"}
          marginTop={"20px"}
        >
          <PERSON><PERSON><PERSON><PERSON> thành viên
        </Typography>
        <Box
          sx={{
            background: "#fff !important",
            borderRadius: "15px",
            marginTop: "16px",
            padding: "5px 20px",
          }}
        >
          <Tabs
            value={tabIndex}
            onChange={(e, newIndex) => setTabIndex(newIndex)}
            variant="scrollable"
            scrollButtons="auto"
            sx={{
              "& .MuiTabs-flexContainer": { justifyContent: "start" },
            }}
          >
            <Tab
              sx={{
                color: "#6d747f",
                fontSize: "16px",
                fontWeight: "bold",
                textTransform: "none",
                "&.Mui-selected": { color: "#000000" },
              }}
              label="Tổng quan"
            />
            <Tab
              sx={{
                color: "#6d747f",
                fontSize: "16px",
                fontWeight: "bold",
                textTransform: "none",
                "&.Mui-selected": { color: "#000000" },
              }}
              label="Chi tiết điểm người dùng"
            />
            <Tab
              sx={{
                color: "#6d747f",
                fontSize: "16px",
                fontWeight: "bold",
                textTransform: "none",
                "&.Mui-selected": { color: "#000000" },
              }}
              label="Lịch sử đổi điểm"
            />
            <Tab
              sx={{
                color: "#6d747f",
                fontSize: "16px",
                fontWeight: "bold",
                textTransform: "none",
                "&.Mui-selected": { color: "#000000" },
              }}
              label="Thiết lập quy tắc điểm"
            />
          </Tabs>
          <Box mt={3}>
            {tabIndex === 0 && <OverviewTab tabIndex={tabIndex} setTabIndex={setTabIndex} />}
            {tabIndex === 1 && <UserPointsTab />}
            {tabIndex === 2 && <RedeemProfileTab />}
            {tabIndex === 3 && <RulesTab />}
          </Box>
        </Box>
      </Box>
    </DashboardLayout>
  );
};

export default MemberPoints;
