import React, { useState, useEffect, useRef } from "react";
import {
  Box,
  Card,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  TablePagination,
  Typography,
  TextField,
  Tabs,
  Tab,
  IconButton,
  Stack,
  Checkbox,
  Button,
  Popover,
  Tooltip,
  InputAdornment,
  Fade,
  Alert,
  Skeleton,
} from "@mui/material";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import { useVoucher } from "@/src/api/hooks/voucher/use-voucher";
import { useStoreId } from "@/src/hooks/use-store-id";
import { useSnackbar } from "@/src/hooks/use-snackbar";
import { VOUCHER_STATUS, VOUCHER_STATUS_LABLE, VOUCHER_TYPE } from "@/src/api/types/voucher.type";
import { useRouter } from "next/router";
import { paths } from "@/src/paths";
import TitleDialog from "@/src/components/dialog/TitleDialog";
import { usePathname } from "next/navigation";
import { PERMISSION_TYPE_ENUM } from "@/src/constants/constant";
import { useAllPermissions } from "@/src/api/hooks/auth/use-all-permissions";
import { rowPerPageOptionsDefault } from "@/src/types/store/enum-type";
import {
  effectiveDateVoucher,
  releaseTypeText,
} from "@/src/components/vouchers/promotions/PromotionSummaryBox";
import { StatusBadge } from "../list";
import { QRCode } from "zmp-qrcode";
import { toPng } from "html-to-image";
import FileDownloadOutlinedIcon from "@mui/icons-material/FileDownloadOutlined";
import QrCodeIcon from "@mui/icons-material/QrCode";
import { useDebounce } from "@/src/hooks/use-debounce";
import { StorageService } from "nextjs-api-lib";
import { useShop } from "@/src/api/hooks/shop/use-shop";
import { logger } from "@/src/utils/logger";
import TruncatedText from "@/src/components/truncated-text/truncated-text";
import { Search } from "@mui/icons-material";
import { useAppSelector } from "@/src/redux/hooks";
import {
  VoucherTableSkeleton,
  VoucherEmptyState,
} from "@/src/components/vouchers/VoucherTableSkeleton";
import { formatTruncatedText } from "@/src/utils/format";

interface VoucherListProps {
  voucherTypes: string[];
  codeType: string;
  onEditVoucher?: (voucherType: string, voucherId: string) => void;
  selected: string[];
  setSelected: (selected: string[]) => void;
  refreshKey?: number;
  onAddNew?: () => void;
}

const VoucherList = ({
  voucherTypes,
  codeType,
  onEditVoucher,
  selected,
  setSelected,
  refreshKey,
  onAddNew,
}: VoucherListProps) => {
  const pathname = usePathname();
  const router = useRouter();
  const storeId = useStoreId();
  const { listVoucher, deleteVoucher, loading } = useVoucher();
  const snackbar = useSnackbar();
  const { permissions } = useAllPermissions();
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [vouchers, setVouchers] = useState([]);
  const [totalCount, setTotalCount] = useState(0);
  const [searchText, setSearchText] = useState("");
  const [searchTabIndex, setSearchTabIndex] = useState("All");
  const [isDeleteMany, setIsDeleteMany] = useState(false);
  const [confirmDialog, setConfirmDialog] = useState({ open: false, voucher: null });
  const debouncedSearchValue = useDebounce(searchText, 500);
  const currentShop = useAppSelector((state) => state.shop.currentShop);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const searchTabs = [
    { key: "All", title: "Tất cả" },
    { key: VOUCHER_STATUS.ACTIVED, title: VOUCHER_STATUS_LABLE[VOUCHER_STATUS.ACTIVED] },
    { key: VOUCHER_STATUS.INACTIVED, title: VOUCHER_STATUS_LABLE[VOUCHER_STATUS.INACTIVED] },
    { key: VOUCHER_STATUS.EXPIRED, title: VOUCHER_STATUS_LABLE[VOUCHER_STATUS.EXPIRED] },
  ];

  const isGranted = (url, permission) => {
    if (url.endsWith("/")) {
      url = url.slice(0, -1);
    }
    const matchingUrl = Object.keys(permissions).find((key) => key === url);
    if (!matchingUrl) return false;
    return permissions[matchingUrl]?.includes(permission) || false;
  };

  const fetchVoucherList = async (currentPage, pageSize, searchQuery, searchType) => {
    try {
      setIsLoading(true);
      setError(null);

      const skip = currentPage;
      const limit = pageSize;
      const response = await listVoucher({
        skip,
        limit,
        shopId: storeId,
        search: searchQuery,
        type: voucherTypes,
        statusVoucher: searchType === "All" ? null : searchType,
        codeType: codeType,
      });

      if (response?.data?.result) {
        setVouchers(response.data.result.result);
        setTotalCount(response.data.result.total || 0);
      } else {
        setError(response?.data?.message || "Có lỗi xảy ra khi tải dữ liệu");
        snackbar.error(response?.data?.message);
      }
    } catch (err) {
      setError("Không thể tải danh sách voucher");
      snackbar.error("Không thể tải danh sách voucher");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (storeId) {
      fetchVoucherList(page, rowsPerPage, debouncedSearchValue, searchTabIndex);
    }
  }, [page, rowsPerPage, debouncedSearchValue, storeId, searchTabIndex, refreshKey]);

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
    setSelected([]);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleSearchTabChange = (event, newTabIndex) => {
    setPage(0);
    setSearchTabIndex(newTabIndex);
    setSelected([]);
  };

  const handleSelectAll = (event) => {
    if (event.target.checked) {
      setSelected(vouchers.map((c) => c.voucherId));
    } else {
      setSelected([]);
    }
  };

  const handleSelectOne = (event, id) => {
    if (event.target.checked) {
      setSelected([...selected, id]);
    } else {
      setSelected(selected.filter((item) => item !== id));
    }
  };

  const handleOpenConfirmDialog = (voucher) => {
    setConfirmDialog({ open: true, voucher });
  };

  const handleCloseConfirmDialog = () => {
    setConfirmDialog({ open: false, voucher: null });
  };

  const handleConfirmDelete = async () => {
    if (confirmDialog.voucher) {
      const response = await deleteVoucher([confirmDialog.voucher.voucherId]);
      if (response?.data.result) {
        snackbar.success("Xóa voucher thành công");
        fetchVoucherList(page, rowsPerPage, debouncedSearchValue, searchTabIndex);
      } else {
        snackbar.error(response.data.message);
      }
    }
    handleCloseConfirmDialog();
  };

  const handleConfirmDeleteMany = async () => {
    if (selected.length === 0) return;

    const response = await deleteVoucher(selected);
    if (response?.data?.result) {
      snackbar.success(`Xóa ${selected.length} voucher thành công`);
      fetchVoucherList(page, rowsPerPage, debouncedSearchValue, searchTabIndex);
      setSelected([]);
      setIsDeleteMany(false);
    } else {
      snackbar.error(response?.data?.message || "Xóa voucher thất bại");
    }
  };

  const handleCloseDeleteMany = () => {
    setIsDeleteMany(false);
  };

  const handleEditVoucher = (voucherType, voucherId) => {
    if (onEditVoucher) {
      onEditVoucher(voucherType, voucherId);
    } else {
      if (voucherType === VOUCHER_TYPE.PROMOTION) {
        router.push(`${paths.marketing.vouchers.promotionDetail}?id=${voucherId}`);
      } else if (voucherType === VOUCHER_TYPE.TRANSPORT) {
        router.push(`${paths.marketing.vouchers.transportDetail}?id=${voucherId}`);
      }
    }
  };
  const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
  const [selectedPointVoucherCode, setSelectedPointVoucherCode] = useState<{
    voucherCode: string;
    voucherCodeLink: string;
  } | null>(null);

  const qrRefs = useRef<{ [key: string]: HTMLDivElement | null }>({});

  const handlePointVoucherCodeClick = (
    event: React.MouseEvent<HTMLElement>,
    pointVoucherCode: { voucherCode: string; voucherCodeLink: string }
  ) => {
    setAnchorEl(event.currentTarget);
    setSelectedPointVoucherCode(pointVoucherCode);
  };

  const handleDownload = async (voucherCode: string) => {
    const node = qrRefs.current[voucherCode];
    if (!node) return;
    try {
      const dataUrl = await toPng(node, {
        skipAutoScale: true,
        width: 400,
        height: 400,
        skipFonts: true,
        filter: (node) => node.tagName !== "SCRIPT",
      });
      const link = document.createElement("a");
      link.href = dataUrl;
      link.download = `${voucherCode}.png`;
      link.click();
    } catch (err) {
      console.error("Download failed", err);
    }
  };

  const handlePointVoucherCodeClose = () => {
    setAnchorEl(null);
    setSelectedPointVoucherCode(null);
  };

  return (
    <Box>
      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      <Box>
        <Box
          sx={{
            display: "flex",
            flexDirection: { xs: "column", md: "row" },
            justifyContent: "space-between",
            alignItems: { xs: "flex-start", md: "center" },
            gap: { xs: 2, md: 0 },
            mb: 3,
          }}
        >
          <Box sx={{ width: { xs: "100%", md: "auto" }, overflow: "hidden" }}>
            <Tabs
              value={searchTabIndex}
              onChange={handleSearchTabChange}
              sx={{
                "& .MuiTabs-indicator": {
                  color: "#2654FE",
                },
                "& .MuiTabs-scroller": {
                  "& .MuiTabs-flexContainer": {
                    gap: { xs: "8px", md: "0px" },
                  },
                },
              }}
              variant="scrollable"
              scrollButtons={false}
            >
              {searchTabs.map((tab) => (
                <Tab
                  sx={{
                    textTransform: "none",
                    color: "black",
                    minWidth: { xs: "80px", md: "60px" },
                    fontSize: { xs: "14px", md: "14px" },
                    padding: { xs: "6px 8px", md: "6px 12px" },
                    minHeight: "40px",
                    "&.Mui-selected": {
                      color: "#2654FE",
                    },
                  }}
                  key={tab.key}
                  value={tab.key}
                  label={tab.title}
                />
              ))}
            </Tabs>
          </Box>

          <Box sx={{ width: { xs: "100%", md: "300px" } }}>
            <TextField
              placeholder="Tìm kiếm mã giảm giá"
              variant="outlined"
              size="small"
              fullWidth
              onChange={(e) => {
                setPage(0);
                setSearchText(e.target.value);
              }}
              sx={{
                "& .MuiOutlinedInput-root": {
                  borderRadius: "8px",
                  height: "40px",
                },
              }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Search />
                  </InputAdornment>
                ),
              }}
            />
          </Box>
        </Box>

        <Box sx={{ width: "100%", overflowX: "auto" }}>
          <Table
            sx={{
              width: "max-content",
              minWidth: "100%",
              tableLayout: "auto",
            }}
          >
            <TableHead>
              <TableRow>
                <TableCell padding="checkbox">
                  <Checkbox
                    checked={vouchers.length > 0 && selected.length === vouchers.length}
                    indeterminate={selected.length > 0 && selected.length < vouchers.length}
                    onChange={handleSelectAll}
                  />
                </TableCell>
                <TableCell sx={{ width: 5 }}>STT</TableCell>
                <TableCell sx={{ width: 200 }}>Voucher</TableCell>
                <TableCell sx={{ width: 150 }}>Mã giảm giá</TableCell>
                <TableCell sx={{ width: 200 }}>Loại voucher</TableCell>
                <TableCell sx={{ width: 80 }}>Trạng thái</TableCell>
                <TableCell sx={{ width: 80 }}>Đã sử dụng</TableCell>
                <TableCell sx={{ width: 80 }}>Đã phát</TableCell>
                <TableCell sx={{ width: 150 }}>Ngày có hiệu lực</TableCell>
                <TableCell sx={{ width: 80 }}>Quản lý</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {isLoading
                ? // Loading skeleton rows
                  Array.from({ length: rowsPerPage }).map((_, index) => (
                    <TableRow key={`skeleton-${index}`}>
                      <TableCell padding="checkbox">
                        <Skeleton variant="rectangular" width={20} height={20} />
                      </TableCell>
                      <TableCell>
                        <Skeleton variant="text" width={30} />
                      </TableCell>
                      <TableCell>
                        <Stack direction="row" spacing={2} alignItems="center">
                          <Skeleton
                            variant="rectangular"
                            width={50}
                            height={50}
                            sx={{ borderRadius: 1 }}
                          />
                          <Stack spacing={0.5}>
                            <Skeleton variant="text" width={150} />
                            <Skeleton variant="text" width={100} />
                          </Stack>
                        </Stack>
                      </TableCell>
                      <TableCell>
                        <Skeleton
                          variant="rectangular"
                          width={80}
                          height={32}
                          sx={{ borderRadius: 1 }}
                        />
                      </TableCell>
                      <TableCell>
                        <Skeleton
                          variant="rectangular"
                          width={70}
                          height={24}
                          sx={{ borderRadius: 1 }}
                        />
                      </TableCell>
                      <TableCell>
                        <Skeleton variant="text" width={60} />
                      </TableCell>
                      <TableCell>
                        <Skeleton variant="text" width={60} />
                      </TableCell>
                      <TableCell>
                        <Skeleton variant="text" width={120} />
                      </TableCell>
                      <TableCell>
                        <Stack direction="row" spacing={1}>
                          <Skeleton variant="circular" width={32} height={32} />
                          <Skeleton variant="circular" width={32} height={32} />
                        </Stack>
                      </TableCell>
                    </TableRow>
                  ))
                : vouchers.length > 0
                ? vouchers.map((c, index) => (
                    <TableRow key={c.voucherId}>
                      <TableCell padding="checkbox">
                        <Checkbox
                          checked={selected.includes(c.voucherId)}
                          onChange={(event) => handleSelectOne(event, c.voucherId)}
                        />
                      </TableCell>
                      <TableCell>{page * rowsPerPage + index + 1}</TableCell>
                      <TableCell>
                        <Box display={"flex"} alignItems={"center"} gap={2}>
                          <img
                            style={{ width: 50, height: 50, objectFit: "cover", borderRadius: 5, flexShrink: 0 }}
                            src={c.image?.link || currentShop.shopLogo.link}
                            alt={c.voucherName}
                            onError={(e) => {
                              (e.target as HTMLImageElement).src = currentShop.shopLogo.link;
                            }}
                          />
                          <Box>
                            <TruncatedText
                              {...formatTruncatedText({
                                text: c.voucherName,
                                isGranted: isGranted(pathname, PERMISSION_TYPE_ENUM.Edit),
                                openInNewTab: false,
                                actionNeed: () => handleEditVoucher(c.voucherType, c.voucherId),
                                width: "300px",
                                fontSize: 18,
                              })}

                            />
                            <TruncatedText
                              typographyProps={{ fontSize: 14, fontWeight: 400, color: "#424242" }}
                              text={
                                Array.isArray(c.voucherDetails) &&
                                c.voucherDetails.length > 0 &&
                                c.voucherDetails[0]?.voucherCode
                                  ? c.voucherDetails[0].voucherCode
                                  : ""
                              }
                            />
                          </Box>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="outlined"
                          size="small"
                          startIcon={<QrCodeIcon />}
                          onClick={(event) =>
                            handlePointVoucherCodeClick(event, {
                              voucherCode: c.voucherDetails[0].voucherCode,
                              voucherCodeLink: c.voucherDetails[0].voucherCodeLink,
                            })
                          }
                        >
                          QR Code
                        </Button>
                      </TableCell>
                      <TableCell>
                        <Typography>
                          {c.voucherType === "Promotion"
                            ? "Giảm giá sản phẩm"
                            : "Giảm giá phí vận chuyển"}
                        </Typography>
                        <Typography variant="subtitle2" color="text.secondary">
                          {releaseTypeText(c.releaseType)}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <StatusBadge status={c.status} />
                      </TableCell>
                      <TableCell>{c.quantityUsed}</TableCell>
                      <TableCell>{c.quantity - (c.remainingStock || 0)}</TableCell>
                      <TableCell>{effectiveDateVoucher(c)}</TableCell>
                      <TableCell>
                        <Stack direction="row" spacing={1}>
                          {isGranted(pathname, PERMISSION_TYPE_ENUM.Add) ? (
                            <IconButton
                              size="small"
                              color="primary"
                              onClick={() => handleEditVoucher(c.voucherType, c.voucherId)}
                            >
                              <EditIcon sx={{ fontSize: 20 }} />
                            </IconButton>
                          ) : (
                            <Tooltip title="Bạn không có quyền sửa">
                              <span>
                                <IconButton size="small" disabled={true}>
                                  <EditIcon sx={{ fontSize: 20 }} />
                                </IconButton>
                              </span>
                            </Tooltip>
                          )}
                          {isGranted(pathname, PERMISSION_TYPE_ENUM.Add) ? (
                            <IconButton
                              size="small"
                              color="error"
                              onClick={() => handleOpenConfirmDialog(c)}
                            >
                              <DeleteIcon sx={{ fontSize: 20 }} />
                            </IconButton>
                          ) : (
                            <Tooltip title="Bạn không có quyền xoá">
                              <span>
                                <IconButton size="small" disabled={true}>
                                  <DeleteIcon sx={{ fontSize: 20 }} />
                                </IconButton>
                              </span>
                            </Tooltip>
                          )}
                        </Stack>
                      </TableCell>
                    </TableRow>
                  ))
                : null}
            </TableBody>
          </Table>

          {/* Empty State */}
          {!isLoading && vouchers.length === 0 && (
            <VoucherEmptyState
              title="Chưa có voucher khuyến mãi nào"
              description="Tạo voucher khuyến mãi đầu tiên để bắt đầu chương trình giảm giá cho khách hàng"
              action={
                <Button
                  variant="contained"
                  color="primary"
                  onClick={(e) => {
                    e.stopPropagation();
                    e.preventDefault();
                    if (onAddNew) {
                      onAddNew();
                    } else {
                      router.push(paths.marketing.vouchers.newPromotion);
                    }
                  }}
                  sx={{
                    borderRadius: 2,
                    px: 3,
                    py: 1.5,
                    fontWeight: 600,
                  }}
                >
                  Tạo voucher
                </Button>
              }
            />
          )}
        </Box>

        {/* Pagination - only show when there are vouchers */}
        {vouchers.length > 0 && (
          <Fade in={true}>
            <Box>
              <TablePagination
                component="div"
                count={totalCount}
                page={page}
                onPageChange={handleChangePage}
                rowsPerPage={rowsPerPage}
                onRowsPerPageChange={handleChangeRowsPerPage}
                rowsPerPageOptions={rowPerPageOptionsDefault}
                labelRowsPerPage="Số dòng mỗi trang"
                sx={{
                  borderTop: 1,
                  borderColor: "divider",
                  "& .MuiTablePagination-toolbar": {
                    px: 2,
                  },
                }}
              />
            </Box>
          </Fade>
        )}
      </Box>

      <TitleDialog
        title="Xóa voucher"
        open={confirmDialog.open}
        handleClose={handleCloseConfirmDialog}
        handleSubmit={handleConfirmDelete}
        submitBtnTitle="Xác nhận"
        color="error"
      >
        <Box>
          <Typography>Bạn có chắc muốn xóa voucher này?</Typography>
        </Box>
      </TitleDialog>

      <TitleDialog
        title="Xóa voucher"
        open={isDeleteMany}
        handleClose={handleCloseDeleteMany}
        handleSubmit={handleConfirmDeleteMany}
        submitBtnTitle="Xác nhận"
        color="error"
      >
        <Box>
          <Typography>
            Bạn có chắc muốn xóa {selected.length > 0 && `${selected.length}`} voucher này?
          </Typography>
        </Box>
      </TitleDialog>
      <Popover
        open={!!anchorEl}
        anchorEl={anchorEl}
        onClose={handlePointVoucherCodeClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "center",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "center",
        }}
      >
        {selectedPointVoucherCode && (
          <Box sx={{ p: 3, minWidth: 200 }}>
            <Typography variant="h6" gutterBottom align="center">
              Mã QR
            </Typography>

            {/* QR Code for scanning */}
            <Box
              sx={{ display: "flex", justifyContent: "center", mb: 2 }}
              ref={(el) => {
                if (selectedPointVoucherCode) {
                  qrRefs.current[selectedPointVoucherCode.voucherCode] =
                    el as HTMLDivElement | null;
                }
              }}
            >
              {selectedPointVoucherCode && (
                <QRCode value={selectedPointVoucherCode.voucherCodeLink} size={100} />
              )}
            </Box>

            {/* Action buttons */}
            <Box sx={{ display: "flex", gap: 1, justifyContent: "center" }}>
              <Button
                variant="outlined"
                size="small"
                startIcon={<FileDownloadOutlinedIcon />}
                onClick={() => {
                  if (selectedPointVoucherCode) {
                    handleDownload(selectedPointVoucherCode.voucherCode);
                  }
                  handlePointVoucherCodeClose();
                }}
              >
                Tải xuống
              </Button>
            </Box>
          </Box>
        )}
      </Popover>
    </Box>
  );
};

export default VoucherList;
