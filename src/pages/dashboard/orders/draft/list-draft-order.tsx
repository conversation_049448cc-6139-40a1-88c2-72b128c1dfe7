import { useState } from "react";
import DashboardLayout from "../../../../layouts/dashboard";
import { Box, Card, TextField } from "@mui/material";
import PageTitleWithBackBtn from "@/src/layouts/dashboard/page-title-with-back-btn/page-title";
import { useRouter } from "next/router";
import { paths } from "@/src/paths";
import ListDraftOrder from "@/src/components/orders/draft/ListDraftOrder";
import TitleTypography from "@/src/components/title-typography/title-typography";
import { Padding } from "@/src/styles/CommonStyle";
import { useAllPermissions } from "@/src/api/hooks/auth/use-all-permissions";
import { PERMISSION_TYPE_ENUM } from "@/src/constants/constant";
import { usePathname } from "next/navigation";
import ActionButton from "@/src/components/common/ActionButton";

export default function listdraftorder() {
  const [searchText, setSearchText] = useState("");
  const router = useRouter();
  const pathname = usePathname();
  const { permissions } = useAllPermissions();
  const isGranted = (url, permission) => {
    if (url.endsWith("/")) {
      url = url.slice(0, -1);
    }
    const matchingUrl = Object.keys(permissions).find((key) => key === url);
    if (!matchingUrl) return false;
    return permissions[matchingUrl]?.includes(permission) || false;
  };
  const onClickAddNew = () => {
    router.push(paths.orders.draft.create);
  };

  return (
    <DashboardLayout>
      <Box sx={{ p: Padding, paddingTop: "20px" }}>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            borderBottom: "1px solid #bdbdbd",
            paddingBottom: "10px",
          }}
          flexDirection={{
            xs: "column",
            sm: "row",
          }}
        >
          <TitleTypography
            sx={{
              textTransform: "none",
              color: " #000 !important",
              fontSize: "20px !important",
              fontWeight: "700",
              lineHeight: "20px",
              mb: { xs: 1, sm: 0 },
            }}
          >
            Đơn nháp
          </TitleTypography>
        </Box>
        <Card sx={{ p: 2 }}>
          <Box
            sx={{
              display: "flex",
              justifyContent: { xs: "flex-start", sm: "flex-end" }, 
              mb: 2,
            }}
          >
            <ActionButton
              permission={PERMISSION_TYPE_ENUM.Add}
              tooltip="Bạn không có quyền tạo mới đơn hàng"
              isGranted={isGranted}
              pathname={pathname}
              onClick={onClickAddNew}
              variant="contained"
              sx={{
                background: "#2654FE",
                px: 3,
                py: 1.2,
                fontWeight: 600,
              }}
              size="medium"
            >
              Đơn hàng mới
            </ActionButton>
          </Box>
          {/* <Box sx={{ mb: 3 }}>
            <TextField
              placeholder="Tìm kiếm đơn hàng"
              variant="outlined"
              size="small"
              sx={{ width: '300px' }}
              onChange={(e) => setSearchText(e.target.value)}
            />
          </Box> */}
          <ListDraftOrder searchText={searchText} isGranted={isGranted} />
        </Card>
      </Box>
    </DashboardLayout>
  );
}
