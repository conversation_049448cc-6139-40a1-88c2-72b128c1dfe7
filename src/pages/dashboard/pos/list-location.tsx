import { useEffect, useState } from "react";
import DashboardLayout from "@/src/layouts/dashboard";
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
} from "@mui/material";
import ArrowForwardIcon from "@mui/icons-material/ArrowForward";
import { branchService } from "@/src/api/services/branch/branch.service";
import { useStoreId } from "@/src/hooks/use-store-id";
import { paths } from "@/src/paths";
import { useRouter } from "next/router";

export default function Dashboard() {
  const [listBranch, setListBranch] = useState([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedBranch, setSelectedBranch] = useState(null);
  const [shiftDetails, setShiftDetails] = useState({
    startShift: "",
    initialFund: "",
    notes: "",
  });
  const storeId = useStoreId();
  const router = useRouter();

  const fetchBranch = async () => {
    try {
      const params = { skip: 0, limit: 99 };
      const res = await branchService.getBranchs(storeId, params);
      setListBranch(res?.data?.data);
    } catch (error) {
      console.error("Error fetching branches:", error);
    }
  };

  useEffect(() => {
    if (storeId) {
      fetchBranch();
    }
  }, [storeId]);

  const handleBranchClick = (branch) => {
    setSelectedBranch(branch);
    // setOpenDialog(true);
    localStorage.setItem("selectedBranch", JSON.stringify(branch));
    localStorage.setItem("shiftDetails", JSON.stringify(shiftDetails));
    // setOpenDialog(false);
    router.push(paths.pos.home);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const handleStartShift = () => {
    localStorage.setItem("selectedBranch", JSON.stringify(selectedBranch));
    localStorage.setItem("shiftDetails", JSON.stringify(shiftDetails));
    setOpenDialog(false);
    router.push(paths.pos.home);
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setShiftDetails((prevState) => ({
      ...prevState,
      [name]: value,
    }));
  };

  return (
    <DashboardLayout>
      <Box
        sx={{
          display: "flex",
          gap: 2,
          p: 2,
          flexWrap: "wrap",
        }}
      >
        {listBranch.map((branch) => (
          <Card
            key={branch.branchId}
            sx={{
              minWidth: 275,
              maxWidth: 345,
              border: "1px solid #e0e0e0",
              borderRadius: "16px",
              boxShadow: "none",
            }}
          >
            <CardContent>
              <Typography variant="h6" gutterBottom>
                {branch.branchName}
              </Typography>
              <Typography color="text.secondary" gutterBottom>
                {/* Trạng thái: {branch.status} */}
                Trạng thái: Hoạt động
              </Typography>
              {/* <Typography color="text.secondary" gutterBottom>
                Số dư: {branch.balance}đ
              </Typography> */}
              <Box sx={{ mt: 1 }}>
                <Button
                  sx={{
                    color: "#2962ff",
                    textTransform: "none",
                    p: 0,
                    "&:hover": { backgroundColor: "transparent" },
                    display: "flex",
                    alignItems: "center",
                    width: "100%",
                    justifyContent: "space-between",
                  }}
                  onClick={() => handleBranchClick(branch)}
                >
                  <Typography sx={{ flexGrow: 1, textAlign: "left", fontWeight: "bold" }}>
                    {branch.status === "Đang mở" ? "Tiếp tục bán hàng" : "Mở máy bán hàng"}
                  </Typography>
                  <Box
                    sx={{
                      width: 32,
                      height: 32,
                      borderRadius: "50%",
                      border: "3px solid #2962ff",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      flexShrink: 0,
                    }}
                  >
                    <ArrowForwardIcon sx={{ color: "#2962ff", fontSize: 30 }} />
                  </Box>
                </Button>
              </Box>
            </CardContent>
          </Card>
        ))}
      </Box>

      <Dialog
        open={openDialog}
        onClose={handleCloseDialog}
        sx={{
          "& .MuiDialog-paper": {
            maxWidth: "500px",
            width: "100%",
            m: 2,
            p: 2,
          },
        }}
      >
        <DialogTitle
          sx={{
            textAlign: "center",
            fontSize: "20px",
            fontWeight: "bold",
            pb: 2,
          }}
        >
          Bắt đầu ca
        </DialogTitle>
        <DialogContent>
          <Typography variant="body1" sx={{ mb: 1 }}>
            Tiền quỹ đầu ca
          </Typography>
          <TextField
            fullWidth
            name="initialFund"
            type="text"
            value={shiftDetails.initialFund}
            onChange={handleChange}
            sx={{ mb: 2 }}
            InputProps={{
              endAdornment: <Typography>đ</Typography>,
            }}
          />
          <Typography variant="body1" sx={{ mb: 1 }}>
            Ghi chú bắt đầu ca
          </Typography>
          <TextField
            fullWidth
            name="notes"
            multiline
            rows={4}
            value={shiftDetails.notes}
            onChange={handleChange}
          />
        </DialogContent>
        <DialogActions sx={{ mt: 2, gap: 1 }}>
          <Button
            onClick={handleCloseDialog}
            variant="outlined"
            sx={{
              flex: 1,
              bgcolor: "#e0e0e0",
              border: "none",
              color: "black",
              "&:hover": {
                bgcolor: "#d5d5d5",
                border: "none",
              },
            }}
          >
            Hủy bỏ
          </Button>
          <Button
            onClick={handleStartShift}
            variant="contained"
            sx={{
              flex: 1,
              bgcolor: "#1976d2",
              "&:hover": {
                bgcolor: "#1565c0",
              },
            }}
          >
            Bắt đầu ca
          </Button>
        </DialogActions>
      </Dialog>
    </DashboardLayout>
  );
}
