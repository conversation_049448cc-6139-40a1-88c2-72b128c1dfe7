import React, { useEffect, useState } from "react";
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  IconButton,
  TextField,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Checkbox,
  Paper,
  Avatar,
  TablePagination,
  Tooltip,
  Drawer,
  Tabs,
  Tab,
  Select,
  MenuItem,
  InputAdornment,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import { PriceListDto, PriceListItemDto } from "@/src/api/services/price-list/price-list.service";
import { usePriceList } from "@/src/api/hooks/price-list/use-price-list";
import { useStoreId } from "@/src/hooks/use-store-id";
import { rowPerPageOptionsDefault } from "@/src/types/store/enum-type";
import { formatPrice } from "@/src/api/types/membership.types";
import { TreeCategory } from "@/src/api/types/product-category.types";
import { getCategoryName } from "../../product/product-management/product-management";
import { renderCategoryOptions } from "./modal-manage-product-price-list";
import TruncatedText from "@/src/components/truncated-text/truncated-text";
import { Search } from "@mui/icons-material";
import { useAppSelector } from "@/src/redux/hooks";
import { useValidImage } from "@/src/hooks/useValidImage";

interface ModalAddProductToPriceListProps {
  isOpenModalListProduct: boolean;
  onClose: () => void;
  selected: string[];
  setSelected: React.Dispatch<React.SetStateAction<string[]>>;
  handleSelectOne: (id: string) => void;
  listProductNotInPriceList: PriceListItemDto[];
  setListProductNotInPriceList: React.Dispatch<React.SetStateAction<PriceListItemDto[]>>;

  isOpenModalManageProduct: boolean;
  setIsOpenModalManageProduct: (open: boolean) => void;
  priceListManage: PriceListDto;
  totalCountItem: number;
  setTotalCountItem: React.Dispatch<React.SetStateAction<number>>;
  page?: number;
  setPage?: React.Dispatch<React.SetStateAction<number>>;
  rowsPerPage?: number;
  setRowsPerPage?: React.Dispatch<React.SetStateAction<number>>;
  fetchListProductNotInPriceList?: any;
  fetchListProductByPriceList?: any;
  debounceSearchItemValue?: string;
  search?: string;
  setSearch?: React.Dispatch<React.SetStateAction<string>>;
  tabValue?: number;
  setTabValue?: React.Dispatch<React.SetStateAction<number>>;
  categories?: TreeCategory[];
  selectedCategoryIdNotIn?: string;
  setSelectedCategoryIdNotIn?: React.Dispatch<React.SetStateAction<string>>;
}

const ModalAddProductToPriceList: React.FC<ModalAddProductToPriceListProps> = ({
  isOpenModalListProduct,
  onClose,
  selected,
  setSelected,
  handleSelectOne,
  isOpenModalManageProduct,
  setIsOpenModalManageProduct,
  priceListManage,
  listProductNotInPriceList,
  setListProductNotInPriceList,
  totalCountItem,
  setTotalCountItem,
  page,
  setPage,
  rowsPerPage,
  setRowsPerPage,
  fetchListProductNotInPriceList,
  fetchListProductByPriceList,
  debounceSearchItemValue,
  search,
  setSearch,
  tabValue,
  setTabValue,
  categories,
  selectedCategoryIdNotIn,
  setSelectedCategoryIdNotIn,
}) => {
  const { addProductsForPriceList } = usePriceList();
  const storeId = useStoreId();
  const currentShop = useAppSelector((state) => state.shop.currentShop);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
    setPage(0);
    setSearch("");
  };

  useEffect(() => {
    fetchListProductNotInPriceList();
  }, [storeId, page, rowsPerPage, debounceSearchItemValue, tabValue, selectedCategoryIdNotIn]);

  const handleSelectAll = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.checked) {
      setSelected(listProductNotInPriceList.map((row) => row.itemsId));
    } else {
      setSelected([]);
    }
  };

  const handleSelectOneItemAdd = (id: string) => {
    setSelected((prev) => (prev.includes(id) ? prev.filter((item) => item !== id) : [...prev, id]));
  };

  const handleUpdateItemForPriceList = async () => {
    const res = await addProductsForPriceList(storeId, priceListManage?.priceListId, selected);
    if (res?.status === 200) {
      fetchListProductByPriceList();
      setIsOpenModalManageProduct(true);
      setSelected([]);
      onClose();
    }
  };

  return (
    <Drawer
      anchor="right"
      open={isOpenModalListProduct}
      onClose={() => {
        onClose();
        setIsOpenModalManageProduct(true);
        setSearch("");
      }}
      PaperProps={{
        sx: { height: "100vh", width: 1000, maxWidth: "100vw", p: 3, boxSizing: "border-box" },
      }}
    >
      <Box>
        <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
          <Typography variant="h6" sx={{ flex: 1, fontWeight: 700 }}>
            Thêm sản phẩm vào bảng giá
          </Typography>
          <IconButton onClick={onClose}>
            <CloseIcon />
          </IconButton>
        </Box>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          sx={{
            mb: 3,
            "& .MuiTabs-indicator": {
              background: "#2654FE",
            },
          }}
        >
          <Tab
            sx={{
              textTransform: "none !important",
              color: "#000",
              "&.Mui-selected": {
                color: "#2654FE !important",
                height: "3px",
              },
            }}
            value={0}
            label={"Sản phẩm"}
          />
          <Tab
            value={1}
            sx={{
              textTransform: "none !important",
              color: "#000",
              "&.Mui-selected": {
                color: "#2654FE !important",
                height: "3px",
              },
            }}
            label={"Dịch vụ"}
          />
        </Tabs>

        <Box sx={{ mb: 2 }}>
          <Select
            displayEmpty
            sx={{
              width: "240px",
              height: "40px",
              mr: 2,
              pr: 4,
              "& .MuiSelect-select": {
                height: "45px",
                display: "flex",
                alignItems: "center",
                paddingTop: 0,
                paddingBottom: 0,
              },
            }}
            labelId="category-select-label"
            value={selectedCategoryIdNotIn}
            label="Chọn danh mục"
            onChange={(e) => setSelectedCategoryIdNotIn(e.target.value)}
          >
            <MenuItem value="">Tất cả danh mục</MenuItem>
            {renderCategoryOptions(categories)}
          </Select>
          <TextField
            size="small"
            placeholder="Tìm kiếm sản phẩm"
            sx={{ width: 240, mr: 2, bgcolor: "#fafafa" }}
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Search />
                </InputAdornment>
              ),
            }}
          />
        </Box>

        <Box
          sx={{
            position: "relative",
            height: "calc(100vh - 280px)",
            overflow: "auto",
            mb: 2,
          }}
        >
          <TableContainer component={Paper} sx={{ borderRadius: 3, boxShadow: "none" }}>
            <Table sx={{}}>
              <TableHead>
                <TableRow>
                  <TableCell padding="checkbox">
                    <Checkbox
                      checked={
                        selected.length === listProductNotInPriceList.length &&
                        listProductNotInPriceList.length > 0
                      }
                      indeterminate={
                        selected.length > 0 && selected.length < listProductNotInPriceList.length
                      }
                      onChange={handleSelectAll}
                    />
                  </TableCell>
                  <TableCell sx={{ width: "5%", fontSize: 15 }} align="center">
                    STT
                  </TableCell>
                  <TableCell sx={{ width: "50px", fontSize: 15 }}>Mã sản phẩm</TableCell>
                  <TableCell sx={{ width: "300px", fontSize: 15 }}>Tên sản phẩm</TableCell>
                  <TableCell sx={{ minWidth: "150px", fontSize: 15 }}>Danh mục</TableCell>
                  <TableCell align="right" sx={{ width: "100px", fontSize: 15 }}>
                    Giá bán
                  </TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {Array.isArray(listProductNotInPriceList) &&
                listProductNotInPriceList.length > 0 ? (
                  listProductNotInPriceList.map((row, index) => {
                    return (
                      <RenderItem
                        categories={categories}
                        handleSelectOneItemAdd={handleSelectOneItemAdd}
                        index={index}
                        page={page}
                        row={row}
                        rowsPerPage={rowsPerPage}
                        selected={selected}
                        currentShop={currentShop}
                      />
                    );
                  })
                ) : (
                  <>
                    <TableRow>
                      <TableCell
                        sx={{
                          paddingTop: 4,
                          fontSize: { xs: "0.875rem", sm: "1rem" },
                          fontWeight: 400,
                          color: "#222",
                        }}
                        colSpan={9}
                        align="center"
                      >
                        Không có dữ liệu
                      </TableCell>
                    </TableRow>
                  </>
                )}
              </TableBody>
            </Table>
            <TablePagination
              component="div"
              count={totalCountItem}
              page={page}
              onPageChange={(_, newPage) => setPage(newPage)}
              rowsPerPage={rowsPerPage}
              onRowsPerPageChange={(e) => {
                setRowsPerPage(parseInt(e.target.value, 10));
                setPage(0);
              }}
              rowsPerPageOptions={rowPerPageOptionsDefault}
              labelRowsPerPage="Số dòng trên trang:"
            />
          </TableContainer>
        </Box>
        <Box
          sx={{
            position: "absolute",
            mt: 3,
            bottom: 0,
            left: 0,
            width: "100%",
            bgcolor: "#fff",
            py: 2,
            px: 3,
            borderTop: "1px solid #eee",
            display: "flex",
            justifyContent: "flex-end",
            zIndex: 10,
          }}
        >
          <Button sx={{ minWidth: 100 }} variant="contained" onClick={handleUpdateItemForPriceList}>
            Lưu
          </Button>
        </Box>
      </Box>
    </Drawer>
  );
};
export default ModalAddProductToPriceList;

const RenderItem = ({
  row,
  selected,
  handleSelectOneItemAdd,
  page,
  rowsPerPage,
  index,
  categories,
  currentShop,
}) => {
  const imgItemSrc = useValidImage(row?.images?.[0]?.link, currentShop?.shopLogo?.link);
  const imgVariantSrc = useValidImage(row.variantImage?.link, imgItemSrc);
  return (
    <TableRow key={row.itemsId}>
      <TableCell padding="checkbox">
        <Checkbox
          checked={selected.includes(row.itemsId)}
          onChange={() => handleSelectOneItemAdd(row.itemsId)}
        />
      </TableCell>
      <TableCell sx={{ fontSize: 15 }} align="center">
        {page * rowsPerPage + index + 1}
      </TableCell>

      <TableCell sx={{ fontSize: 15 }}>{row.itemsCode}</TableCell>
      <TableCell sx={{ maxWidth: "300px" }}>
        <Box
          sx={{
            display: "flex",
            alignItems: "flex-start",
            gap: 1,
            width: "100%",
          }}
        >
          <Avatar src={imgVariantSrc} sx={{ width: 34, height: 34, bgcolor: "#e0e0e0" }} />
          <Box sx={{ display: "flex", flexDirection: "column", width: "80%" }}>
            <TruncatedText typographyProps={{ width: 150 }} text={row.itemsName} />
            <Typography
              sx={{
                fontSize: 14,
                display: "flex",
                flexDirection: "column",
                width: "100%",
              }}
            >
              {row.isVariant === true && (
                <>
                  {row.variantNameOne && row.variantValueOne && (
                    <span style={{ display: "flex", alignItems: "center" }}>
                      <TruncatedText
                        typographyProps={{ fontSize: 14, maxWidth: "50%" }}
                        text={row.variantNameOne}
                      />
                      :
                      <TruncatedText
                        typographyProps={{
                          fontSize: 14,
                          pl: 0.4,
                          maxWidth: "40%",
                        }}
                        text={row.variantValueOne}
                      />
                    </span>
                  )}
                  {row.variantNameTwo && row.variantValueTwo && (
                    <span style={{ display: "flex", alignItems: "center" }}>
                      <TruncatedText
                        typographyProps={{ fontSize: 14, maxWidth: "50%" }}
                        text={row.variantNameTwo}
                      />
                      :
                      <TruncatedText
                        text={row.variantValueTwo}
                        typographyProps={{
                          fontSize: 14,
                          pl: 0.4,
                          maxWidth: "40%",
                        }}
                      />
                    </span>
                  )}
                  {row.variantNameThree && row.variantValueThree && (
                    <span style={{ display: "flex", alignItems: "center" }}>
                      <TruncatedText
                        typographyProps={{ fontSize: 14, maxWidth: "50%" }}
                        text={row.variantNameThree}
                      />
                      :
                      <TruncatedText
                        text={row.variantValueThree}
                        typographyProps={{
                          fontSize: 14,
                          pl: 0.4,
                          maxWidth: "40%",
                        }}
                      />
                    </span>
                  )}
                </>
              )}
            </Typography>
          </Box>
        </Box>
      </TableCell>
      <TableCell sx={{ fontSize: 15, maxWidth: "70%" }}>
        <TruncatedText
          typographyProps={{ fontSize: 15, width: 100 }}
          text={getCategoryName(row?.categoryIds, categories)}
        />
      </TableCell>
      <TableCell sx={{ fontSize: 15, width: 150 }} align="right">
        {formatPrice(row.price)} VNĐ
      </TableCell>
    </TableRow>
  );
};
