import { useState, useEffect, useRef } from "react";
import { useRouter } from "next/router";
import { useTranslation } from "react-i18next";
import {
  Box,
  Button,
  Card,
  TextField,
  Typography,
  IconButton,
  CircularProgress,
  FormControlLabel,
  Grid,
  MenuItem,
  FormHelperText,
} from "@mui/material";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import { useFormik } from "formik";
import * as Yup from "yup";
import { FileDropzone } from "@/src/components/file-dropzone";
import DashboardLayout from "@/src/layouts/dashboard";
import CustomSwitch from "@/src/components/custom-switch";
import CustomSelect from "@/src/components/custom-select";
import { paths } from "@/src/paths";
import { tokens } from "@/src/locales/tokens";
import TitleTypography from "@/src/components/title-typography/title-typography";
import { useSnackbar } from "@/src/hooks/use-snackbar";
import { useStoreId } from "@/src/hooks/use-store-id";
import { useProductCategory } from "@/src/api/hooks/dashboard/product/use-category";
import { CategoryType, GetProductCategoryRequest } from "@/src/api/types/product-category.types";
import { StorageService } from "nextjs-api-lib";
import { logger } from "@/src/utils/logger";
import { Padding } from "@/src/styles/CommonStyle";
import { FILE_SIZE_2MB, MAX_FILE_IMAGE } from "@/src/constants/constant";
import { CommonMediaUpload } from "@/src/components/common-media-upload";
import { ExistingMediaFile } from "../product-management/create/create";
import { CreateFileGroupRequest, GetGroupFileRequest, RefType } from "@/src/api/types/media.types";
import { useMedia } from "@/src/api/hooks/media/use-media";
import { CategoryDto } from "@/src/api/services/dashboard/product/category.service";
import { ImageProcessor } from "@/src/utils/image-processor";
import { FileType } from "@/src/constants/file-types";
import TruncatedText from "@/src/components/truncated-text/truncated-text";

interface FormValues {
  categoryName: string;
  parentId: string;
  categoryPosition: number;
  image: File | null;
  isHome: boolean;
  publish: "Publish" | "UnPublish";
  active: "Actived" | "InActived";
  submit: null;
}

export const isValidImageFile = (file: File): boolean => {
  const filePath = file?.name || "";

  const fileExtension = filePath.split(".").pop()?.toLowerCase() || "";

  const allowedExtensions = ["png", "jpg", "jpeg", "jfif"];
  return allowedExtensions.includes(fileExtension);
};

const CreateCategory = () => {
  const { t } = useTranslation();
  const router = useRouter();
  const snackbar = useSnackbar();
  const storeId = useStoreId();
  const [categoryType, setCategoryType] = useState<CategoryType>("Product");
  const [isEdit, setIsEdit] = useState(false);
  const [isPublished, setIsPublished] = useState(false);
  const [parentCategories, setParentCategories] = useState<any[]>([]);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [previewUrls, setPreviewUrls] = useState<string[]>([]);
  const [existingFiles, setExistingFiles] = useState<ExistingMediaFile[]>([]);
  const [localFiles, setLocalFiles] = useState<File[]>([]);
  const [defaultGroupId, setDefaultGroupId] = useState<string>("");
  const { uploadFile, getGroups } = useMedia();

  const [errorMsg, setErrorMsg] = useState<string>("");

  const {
    getProductCategory,
    createProductCategory,
    updateProductCategory,
    getProductCategoryDetail,
    updateProductCategoryImage,
  } = useProductCategory();
  const [newImageFile, setNewImageFile] = useState<File | null>(null);

  useEffect(() => {
    const fetchDefaultGroup = async () => {
      try {
        const data: GetGroupFileRequest = {
          ShopId: storeId,
          Skip: 0,
          Limit: 10,
        };
        const response = await getGroups(data);
        if (response.data?.data?.length > 0) {
          setDefaultGroupId(response.data.data[0].groupFileId);
        }
      } catch (error) {}
    };
    fetchDefaultGroup();
  }, [storeId]);

  useEffect(() => {
    const type = StorageService.get("categoryType");
    if (type) {
      setCategoryType(type as CategoryType);
      StorageService.remove("categoryType");
    }

    if (storeId) {
      fetchParentCategories();

      const categoryId = router.query.id;
      if (categoryId) {
        setIsEdit(true);
        fetchCategoryDetail();
      }
    }
  }, [categoryType, storeId]);

  const fetchCategoryDetail = async () => {
    try {
      if (!router.query.id || !storeId) return;

      const detail = (await getProductCategoryDetail(router.query.id as string)) as any;
      if (!detail?.data) return;

      const formData = {
        categoryName: detail.data.categoryName || "",
        parentId: detail.data.parentId || "",
        categoryPosition: detail.data.categoryPosition || 0,
        image: detail?.data?.image,
        isHome: Boolean(detail.data.isHome),
        publish: detail.data.publish || "UnPublish",
        active: detail.data.active || "InActived",
        submit: null,
      };
      setExistingFiles(detail.data.image ? [detail.data.image] : []);

      formik.setValues(formData);
      setIsPublished(detail.data.publish === "Publish");

      if (detail.data.categoryIcon) {
        setPreviewUrls([detail.data.categoryIcon]);
        setImagePreview(detail.data.categoryIcon);
      }
    } catch (error) {
      logger.error("Error fetching category detail:", error);
      router.back();
    }
  };

  useEffect(() => {
    if (router.query.id && storeId) {
      setIsEdit(true);
      fetchCategoryDetail();
    }
  }, [router.query.id, storeId]);

  const fetchParentCategories = async () => {
    try {
      if (!storeId) return;

      const params: GetProductCategoryRequest = {
        categoryType: categoryType,
        shopId: storeId,
        partnerId: StorageService.get("partnerId") as string | null,
        paging: {
          nameType: "Created",
          sortType: "desc",
          name: "Created",
          sort: "desc",
          pageSize: 100,
          pageIndex: 0,
        },
      };
      const response = await getProductCategory(params);
      if (response?.data?.data) {
        const filteredCategories = response.data.data.filter(
          (category) => category.categoryId !== router.query.id
        );
        setParentCategories(filteredCategories);
      }
    } catch (error) {
      // Error handled by API layer
    }
  };

  const handleImageDrop = (acceptedFiles: File[]) => {
    if (acceptedFiles[0]) {
      const file = acceptedFiles[0];
      if (isValidImageFile(file)) {
        setNewImageFile(file);
        formik.setFieldValue("image", file);
        const previewUrl = URL.createObjectURL(file);
        setImagePreview(previewUrl);
        setPreviewUrls([previewUrl]);
      } else {
        snackbar.error(
          "Định dạng file không hợp lệ. Hãy chọn file định dạng PNG, JPG, JPEG hoặc JFIF."
        );
        setImagePreview(null);
        setPreviewUrls([]);
      }
    }
  };

  const handleImageRemove = () => {
    setNewImageFile(null);
    formik.setFieldValue("image", null);
    setImagePreview(null);
    setPreviewUrls([]);
  };

  const handlePublishChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setIsPublished(event.target.checked);
    formik.setFieldValue("publish", event.target.checked ? "Publish" : "UnPublish");
    formik.setFieldValue("active", event.target.checked ? "Actived" : "InActived");
  };

  const handleCancel = () => {
    router.back();
  };

  const validationSchema = Yup.object({
    categoryName: Yup.string()
      .trim()
      .required(t(tokens.contentManagement.category.create.form.nameRequired))
      .max(255, t(tokens.common.validation.maxLength, { max: 255 })),
    categoryPosition: Yup.number()
      .required(t(tokens.contentManagement.category.create.form.positionRequired))
      .min(1, t(tokens.contentManagement.category.create.form.positionMin))
      .max(100, t(tokens.common.validation.maxNumber, { max: 100 })), // Updated max value to 100
    // image: Yup.mixed().when([], {
    //   is: () => !isEdit,
    //   then: (schema) =>
    //     schema.required(t(tokens.contentManagement.category.create.form.imageRequired)),
    //   otherwise: (schema) => schema.nullable(),
    // }),
  });

  const formik = useFormik<FormValues>({
    initialValues: {
      categoryName: "",
      parentId: "",
      categoryPosition: 100,
      image: null,
      isHome: false,
      publish: "UnPublish",
      active: "InActived",
      submit: null,
    },
    validationSchema,
    validateOnChange: true,
    validateOnBlur: true,
    onSubmit: handleSubmit,
  });

  useEffect(() => {}, [newImageFile]);

  async function handleSubmit(values: FormValues) {
    try {
      let uploadedFiles = [];
      if (localFiles.length + existingFiles.length === 0) {
        setErrorMsg("Hãy chọn ít nhất 1 ảnh");
        return;
      }
      if (localFiles.length > 0) {
        for (const file of localFiles) {
          const processedFile = await ImageProcessor.processImage(file);
          const data: CreateFileGroupRequest = {
            FileUpload: processedFile,
            ShopId: storeId,
            GroupFileId: defaultGroupId,
            RefType: categoryType === "Product" ? RefType.CategoryProduct : RefType.CategoryService,
            RefId: "",
          };
          const response = await uploadFile(data);
          uploadedFiles.push(response.data);
        }
      }
      const newImages = uploadedFiles.map((file) => ({
        type: file.type === "IMAGE" ? FileType.IMAGE : FileType.VIDEO,
        link: file.url || file.link,
        mediaFileId: file.mediaFileId,
      }));
      const payload: CategoryDto = {
        categoryId: "",
        parentId: values.parentId || null,
        shopId: storeId,
        categoryType: categoryType,
        categoryName: values.categoryName?.trim() || null,
        categoryLevel: "",
        partnerId: StorageService.get("partnerId") as string | null,
        image:
          newImages.length > 0
            ? newImages[0]
            : existingFiles[0]
            ? {
                type: existingFiles[0].type,
                link: existingFiles[0].link,
                mediaFileId: existingFiles[0].mediaFileId!,
              }
            : undefined,
        categoryDesc: "",
        categoryPosition: values.categoryPosition,
        isHome: values.isHome,
        publish: isPublished ? "Publish" : "UnPublish",
        ruleActive: isPublished ? "Actived" : "InActived",
      };

      if (isEdit) {
        await updateProductCategory({
          ...payload,
          categoryId: router.query.id as string,
        });

        snackbar.success(t(tokens.contentManagement.category.edit.success));
        if (categoryType === "Product") {
          router.push(`${paths.dashboard.product.categoryManagement}/?tab=0`);
        } else {
          router.push(`${paths.dashboard.product.categoryManagement}/?tab=1`);
        }
      } else {
        const createResponse = (await createProductCategory(payload)) as any;
        snackbar.success(t(tokens.contentManagement.category.create.success));
        if (categoryType === "Product") {
          router.push(`${paths.dashboard.product.categoryManagement}/?tab=0`);
        } else {
          router.push(`${paths.dashboard.product.categoryManagement}/?tab=1`);
        }
      }
    } catch (error) {
      logger.error("Error in form submission:", error);
      snackbar.error(
        isEdit
          ? t(tokens.contentManagement.category.edit.error)
          : t(tokens.contentManagement.category.create.error)
      );
    }
  }

  const handleFilesChange = (files: File[]) => {
    setExistingFiles([]);
    setLocalFiles([]);
    if (files.length > MAX_FILE_IMAGE) {
      snackbar.error(`Bạn chỉ có thể tải lên tối đa ${MAX_FILE_IMAGE} tệp hình ảnh.`);
      return;
    }
    if (files.length > 0 && !isValidImageFile(files[0])) {
      snackbar.error(
        "Định dạng file không hợp lệ. Hãy chọn file định dạng PNG, JPG, JPEG hoặc JFIF."
      );
      return;
    }
    formik.setFieldValue("image", files[0] || null);
    // setShowPopup(true);
    // Cập nhật localFiles và imagePreview

    if (files.length > 0) {
      setLocalFiles(files);
      setImagePreview(URL.createObjectURL(files[0]));
    } else {
      setImagePreview(null);
    }
  };

  const handleRemove = (index: number, isExisting: boolean) => {
    if (isExisting) {
      const updated = [...existingFiles];
      updated.splice(index, 1);
      setExistingFiles(updated);
    } else {
      const localIdx = index - existingFiles.length;
      setLocalFiles((prev) => prev.filter((_, i) => i !== localIdx));
    }
  };

  return (
    <DashboardLayout>
      <Box sx={{ p: Padding }}>
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            mb: 3,
            paddingBottom: "20px",
            marginBottom: "20px",
            borderBottom: "1px solid #bdbdbd",
          }}
        >
          <IconButton onClick={handleCancel} sx={{ mr: 2 }}>
            <ArrowBackIcon />
          </IconButton>
          <TitleTypography sx={{ fontSize: "20px !important", lineHeight: "1" }}>
            {isEdit
              ? categoryType === "Product"
                ? "Hàng hoá/Danh mục/Danh mục sản phẩm/Chỉnh sửa"
                : "Hàng hoá/Danh mục/Danh mục dịch vụ/Chỉnh sửa"
              : categoryType === "Product"
              ? "Hàng hoá/Danh mục/Danh mục sản phẩm/Tạo mới"
              : "Hàng hoá/Danh mục/Danh mục dịch vụ/Tạo mới"}
          </TitleTypography>
        </Box>

        <Card sx={{ p: 3 }}>
          <Grid container spacing={4}>
            {/* Left Section */}
            <Grid item xs={12} md={6}>
              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between",
                  mb: 4,
                }}
              >
                <Typography variant="h6">
                  {t(tokens.contentManagement.category.create.form.contentTitle)}
                </Typography>
                <FormControlLabel
                  control={<CustomSwitch checked={isPublished} onChange={handlePublishChange} />}
                  label={t(tokens.contentManagement.category.create.form.isActive)}
                  labelPlacement="end"
                  sx={{ mr: 0, "& .MuiFormControlLabel-label": { ml: 2 } }}
                />
              </Box>

              <Typography variant="subtitle1" sx={{ mt: 2, mb: 1, display: "flex" }}>
                {t(tokens.contentManagement.category.create.form.categoryName)}
                <Typography sx={{ color: "red", ml: 0.5 }}>*</Typography>
              </Typography>
              <TextField
                fullWidth
                label=""
                name="categoryName"
                value={formik.values.categoryName}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={!!(formik.touched.categoryName && formik.errors.categoryName)}
                helperText={formik.touched.categoryName && formik.errors.categoryName}
                sx={{ mb: 3 }}
              />

              <Typography variant="subtitle1" sx={{ mt: 2, mb: 1, display: "flex" }}>
                {t(tokens.contentManagement.category.create.form.parentCategory)}
              </Typography>
              <TextField
                select
                fullWidth
                label=""
                name="parentId"
                value={formik.values.parentId}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={!!(formik.touched.parentId && formik.errors.parentId)}
                helperText={formik.touched.parentId && formik.errors.parentId}
                sx={{ mb: 3 }}
              >
                <MenuItem value="">
                  <em>{t(tokens.common.none)}</em>
                </MenuItem>
                {parentCategories.map((category) => (
                  <MenuItem key={category.categoryId} value={category.categoryId}>
                    <TruncatedText text={category.categoryName} typographyProps={{ width: 700 }} />
                  </MenuItem>
                ))}
              </TextField>

              <Typography variant="subtitle1" sx={{ mt: 2, mb: 1, display: "flex" }}>
                {t(tokens.contentManagement.category.create.form.position)}
              </Typography>
              <TextField
                fullWidth
                type="text"
                label=""
                name="categoryPosition"
                value={formik.values.categoryPosition}
                onChange={(e) => {
                  const value = e.target.value;
                  if (/^\d*$/.test(value)) {
                    const numValue = value === "" ? "" : Number(value);
                    if (numValue === "" || (numValue >= 1 && numValue <= 100)) {
                      formik.setFieldValue("categoryPosition", numValue);
                    }
                  }
                }}
                onKeyDown={(e) => {
                  if (["e", "E", "-", "+", "."].includes(e.key)) {
                    e.preventDefault();
                  }
                }}
                onBlur={formik.handleBlur}
                error={!!(formik.touched.categoryPosition && formik.errors.categoryPosition)}
                inputProps={{
                  inputMode: "numeric",
                  pattern: "[0-9]*",
                }}
                sx={{ mb: 0.5 }}
                onWheel={(e) => e.target instanceof HTMLElement && e.target.blur()}
              />
              {(formik.touched.categoryPosition && formik.errors.categoryPosition) || (
                <FormHelperText sx={{ color: "text.secondary", fontStyle: "italic" }}>
                  "Vị trí hiển thị của danh mục (1-100). Số càng nhỏ thì vị trí càng cao."
                </FormHelperText>
              )}
            </Grid>

            {/* Right Section */}
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle1" sx={{ mb: 2 }}>
                {t(tokens.contentManagement.category.create.form.imageTitle)}
              </Typography>
              <CommonMediaUpload
                caption="Thêm ảnh"
                maxFiles={1}
                accept={{
                  "image/*": [".png", ".jpg", ".jpeg"],
                }}
                maxSize={FILE_SIZE_2MB} // 2MB
                existingFiles={existingFiles}
                localFiles={localFiles}
                setLocalFiles={setLocalFiles}
                onFilesChange={handleFilesChange}
                onRemove={handleRemove}
                defaultGroupId={defaultGroupId}
                setExistingFiles={setExistingFiles}
                isShowPreviewImage={true}
                errorMsg={errorMsg}
                setErrorMsg={setErrorMsg}
              />
              {/* <FileDropzone
                accept={{ "image/*": [".png", ".jpg", ".jpeg"] }}
                caption={t(tokens.contentManagement.category.create.form.imageCaption)}
                maxFiles={1}
                maxSize={FILE_SIZE_2MB}
                onDrop={handleImageDrop}
                onRemove={handleImageRemove}
                showError={!isEdit && !!(formik.touched.image && formik.errors.image)}
                existingFiles={previewUrls}
                previewUrlsImg={previewUrls}
                setPreviewUrlsImg={setPreviewUrls}
              />
              {!isEdit && formik.touched.image && formik.errors.image && (
                <Typography variant="caption" color="error" sx={{ mt: 1 }}>
                  {formik.errors.image as string}
                </Typography>
              )} */}
            </Grid>
          </Grid>

          <Box sx={{ display: "flex", gap: 2, justifyContent: "flex-end", mt: 4 }}>
            <Button
              onClick={handleCancel}
              variant="outlined"
              sx={{ minWidth: 120, color: "#2654FE", borderColor: "#2654FE" }}
            >
              {t(tokens.common.cancel)}
            </Button>
            <Button
              variant="contained"
              onClick={() => formik.handleSubmit()}
              disabled={formik.isSubmitting}
              sx={{ minWidth: 120, background: "#2654FE" }}
            >
              {formik.isSubmitting ? <CircularProgress size={24} /> : t(tokens.common.save)}
            </Button>
          </Box>
        </Card>
      </Box>
    </DashboardLayout>
  );
};

export default CreateCategory;
