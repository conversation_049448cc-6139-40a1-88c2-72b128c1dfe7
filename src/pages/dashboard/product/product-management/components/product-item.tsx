import React from "react";
import { getCategoryName, getFirstVariant } from "../product-management";
import { useValidImage } from "@/src/hooks/useValidImage";
import { Box, Checkbox, Chip, IconButton, TableCell, TableRow, Tooltip } from "@mui/material";
import ShowImage from "@/src/components/show-image";
import TruncatedText from "@/src/components/truncated-text/truncated-text";
import { StatusChip } from "@/src/components/components/status-chip";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import { paths } from "@/src/paths";
import { formatTruncatedText } from "@/src/utils/format";

const ProductItem = ({
  item,
  index,
  page,
  rowsPerPage,
  selectedIds,
  handleCheck,
  handleEdit,
  handleDelete,
  categories,
  pathname,
  tabValue,
  currentShop,
  t,
  isGranted,
}) => {
  const firstVariant = getFirstVariant(item);
  const price = firstVariant ? firstVariant.price : item.price;
  const priceReal = firstVariant ? firstVariant.priceReal : item.priceReal;
  const quantity = firstVariant ? firstVariant.quantity : item.quantity;
  const quantityPurchase = item.sold;

  const image = item.images[0]?.link || firstVariant?.variantImage?.link;
  const imageSrc = useValidImage(image, currentShop?.shopLogo?.link || "/default.jpg");
  return (
    <TableRow key={item.itemsCode}>
      <TableCell padding="checkbox">
        <Checkbox
          checked={selectedIds.includes(item.itemsCode)}
          onChange={() => handleCheck(item.itemsCode)}
        />
      </TableCell>
      <TableCell sx={{ pl: 3 }}>{page * rowsPerPage + index + 1}</TableCell>
      <TableCell sx={{ pl: 3, fontWeight: 500 }}>{item.itemsCode}</TableCell>

      <TableCell sx={{ pl: 3 }}>
        <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
          <img
            style={{ width: 40, height: 40, borderRadius: 999, objectFit: "cover" }}
            src={imageSrc}
          />
          <TruncatedText
            {...formatTruncatedText({
              text: item.itemsName,
              isGranted: isGranted(pathname, "Edit"),
              openInNewTab: false,
              actionNeed: () => handleEdit(item),
            })}
          />
        </Box>
      </TableCell>

      <TableCell>
        <Box>
          {price !== priceReal && (
            <Box
              sx={{
                color: "error.main",
                textDecoration: "line-through",
                mb: 0.5,
              }}
            >
              {priceReal?.toLocaleString()}đ
            </Box>
          )}
          <Box sx={{ fontWeight: 500 }}>{price?.toLocaleString()}đ</Box>
        </Box>
      </TableCell>

      <TableCell>
        <Box>
          <Box sx={{ mb: 0.5 }}>Tồn kho: {quantity?.toLocaleString()}</Box>
          <Box sx={{ color: "text.secondary", fontSize: "0.875rem" }}>
            Đã bán: {quantityPurchase?.toLocaleString()}
          </Box>
        </Box>
      </TableCell>

      <TableCell>
        <StatusChip status={item.typePublish} type="category" />
      </TableCell>

      {/* <TableCell>
        <Chip
          label={t(item.isTop ? "Nổi bật" : "Không nổi bật")}
          color={item.isTop ? "primary" : "default"}
          size="small"
          variant={item.isTop ? "filled" : "outlined"}
        />
      </TableCell> */}

      <TableCell>
        <TruncatedText
          typographyProps={{ fontSize: 15, width: "150px" }}
          text={getCategoryName(item.categoryIds, categories)}
        />
      </TableCell>

      <TableCell
        sx={{
          pr: 3,
          display: "flex",
          flexDirection: "column",
          alignItems: "start",
          position: "sticky",
          right: 0,
          backgroundColor: "#fff",
          zIndex: 2,
          padding: "25px 16px",
          boxShadow: "-2px 0 5px rgba(0,0,0,0.1)",
        }}
      >
        <Box sx={{ display: "flex", gap: 1 }}>
          <Tooltip title={!isGranted(pathname, "Edit") ? "Bạn không có quyền sửa" : "Sửa"}>
            <span>
              <IconButton
                component="a"
                href={`${paths.dashboard.product.updateProduct}?id=${item.itemsCode}&type=${
                  tabValue === 0 ? "Product" : "Service"
                }`}
                onClick={(e) => {
                  e.preventDefault();
                  handleEdit(item);
                }}
                size="small"
                sx={{ color: "primary.main" }}
                disabled={!isGranted(pathname, "Edit")}
              >
                <EditIcon fontSize="small" />
              </IconButton>
            </span>
          </Tooltip>

          <Tooltip title={!isGranted(pathname, "Delete") ? "Bạn không có quyền xoá" : "Xóa"}>
            <span>
              <IconButton
                onClick={() => handleDelete(item)}
                size="small"
                sx={{ color: "error.main" }}
                disabled={!isGranted(pathname, "Delete")}
              >
                <DeleteIcon fontSize="small" />
              </IconButton>
            </span>
          </Tooltip>
        </Box>
      </TableCell>
    </TableRow>
  );
};

export default ProductItem;
