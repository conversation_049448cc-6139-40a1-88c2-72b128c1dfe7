import React, { memo, useState, useCallback, useEffect } from "react";
import { <PERSON>, <PERSON>, debounce, TextField, Typography } from "@mui/material";
import { FormikProps } from "formik";
import dynamic from "next/dynamic";
import { ProductFormValues } from "../../../../../../types/product/form";
import { useStoreId } from "@/src/hooks/use-store-id";
import { useMedia } from "@/src/api/hooks/media/use-media";

const ReactQuillEditor = dynamic(() => import("@/src/components/react-quill-editor"), {
  ssr: false,
  loading: () => <p>Loading editor...</p>,
});

interface BasicInformationProps {
  formik: FormikProps<ProductFormValues>;
  itemsType: "Product" | "Service";
}

/**
 * BasicInformation Component
 * Handles the basic information form section for products/services
 */
const BasicInformation: React.FC<BasicInformationProps> = memo(({ formik, itemsType }) => {
  const [localName, setLocalName] = useState(formik.values.itemsName);
  const storeId = useStoreId();
  const { getGroups } = useMedia();

  const [defaultGroupId, setDefaultGroupId] = useState<string>("");

  useEffect(() => {
    const fetchDefaultGroup = async () => {
      try {
        const data = {
          ShopId: storeId,
          Skip: 0,
          Limit: 1,
        };
        const response = await getGroups(data);
        if (response && response.data && response.data.data && response.data.data.length > 0) {
          setDefaultGroupId(response.data.data[0].groupFileId);
        }
      } catch (error) {
        console.error("Error fetching groups:", error);
      }
    };
    if (storeId) fetchDefaultGroup();
  }, [storeId]);

  // Debounced function to update formik state
  const debouncedUpdateFormik = useCallback(
    debounce((content: string) => {
      formik.setFieldValue("description", content);
    }, 500),
    [formik]
  );

  // Update local state while typing
  const handleNameChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value;
      setLocalName(value);
      formik.setFieldValue("itemsName", value);
      formik.setFieldTouched("itemsName", true, false);
    },
    [formik]
  );

  // Handle description change from editor
  const handleDescriptionChange = useCallback(
    (content: string) => {
      debouncedUpdateFormik(content);
    },
    [debouncedUpdateFormik]
  );

  // Update formik state and validate on blur
  const handleNameBlur = useCallback(
    (e: React.FocusEvent<HTMLInputElement>) => {
      formik.setFieldValue("itemsName", localName, true);
      formik.handleBlur(e);
    },
    [formik, localName]
  );

  React.useEffect(() => {
    setLocalName(formik.values.itemsName);
  }, [formik.values.itemsName]);

  const getNameLabel = () => (itemsType === "Product" ? "Tên Sản Phẩm" : "Tên Dịch Vụ");

  return (
    <Card sx={{ p: 3 }}>
      <Box sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
        <Typography variant="body2" sx={{ display: "flex", fontSize: "16px", fontWeight: 600 }}>
          {getNameLabel()} <Typography sx={{ color: "red", ml: 0.5 }}>*</Typography>
        </Typography>
        <TextField
          fullWidth
          name="itemsName"
          value={localName}
          onChange={handleNameChange}
          onBlur={handleNameBlur}
          error={!!(formik.touched.itemsName && formik.errors.itemsName)}
          helperText={formik.touched.itemsName && formik.errors.itemsName}
          sx={{
            "& .MuiOutlinedInput-root": {
              backgroundColor: "background.paper",
              height: "45px",
            },
          }}
        />
      </Box>

      <Box sx={{ position: "relative", mt: 2 }}>
        <Typography variant="body2" sx={{ mb: 1, fontSize: "16px", fontWeight: 600 }}>
          Mô tả
        </Typography>

        <ReactQuillEditor
          value={formik.values.description || ""}
          onChange={handleDescriptionChange}
          shopId={storeId}
          defaultGroupId={defaultGroupId}
          error={formik.errors.description}
        />
      </Box>
    </Card>
  );
});

BasicInformation.displayName = "BasicInformation";

export default BasicInformation;
