import React, { useMemo } from "react";
import {
  Box,
  Card,
  Typography,
  SelectChangeEvent,
  Chip,
  Select,
  MenuItem,
  FormControl,
  FormHelperText,
  Tooltip,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import { FormikProps } from "formik";
import { tokens } from "@/src/locales/tokens";
import { ProductFormValues } from "../../../../../../types/product/form";
import TruncatedText from "@/src/components/truncated-text/truncated-text";
import { Close } from "@mui/icons-material";

interface CategorySelectionProps {
  formik: FormikProps<ProductFormValues>;
  categories: any[];
  itemsType: "Product" | "Service";
}

const CategorySelection: React.FC<CategorySelectionProps> = ({ formik, categories, itemsType }) => {
  const { t } = useTranslation();
  const hasCategories = Array.isArray(categories) && categories.length > 0;

  const categoryOptions = useMemo(() => {
    if (!hasCategories) return [];

    const flattenCategories = (cat, level = 0) => {
      const children = cat.listSubCategory.map((subCat) => flattenCategories(subCat, level + 1));
      return [{ value: cat.categoryId, label: cat.categoryName, level }, ...children.flat()];
    };

    const flattenedCategories = categories.flatMap((cat) => flattenCategories(cat));
    return flattenedCategories;
  }, [categories]);

  // Handle parent category change
  const handleCategoryChange = (event: SelectChangeEvent<string[]>) => {
    const categoryIds = event.target.value as string[];
    formik.setFieldValue("categoryIds", categoryIds);
  };

  const renderValue = (selected: string[] = []) => (
    <Box
      sx={{
        display: "flex",
        flexWrap: "wrap",
        gap: 0.5,
        p: 0,
        maxWidth: "100%",
      }}
    >
      {selected
        .map((value) => categoryOptions?.find((opt) => opt.value === value))
        .filter((opt) => !!opt?.label)
        .map((opt) => (
          <Chip
            key={opt.value}
            label={opt.label}
            size="small"
            sx={{
              maxWidth: "200px",
              "& .MuiChip-label": {
                overflow: "hidden",
                textOverflow: "ellipsis",
                whiteSpace: "nowrap",
              },
            }}
            onMouseDown={(e) => {
              e.stopPropagation();
            }}
            onDelete={(e) => {
              e.stopPropagation();
              const newSelected = (formik.values.categoryIds || []).filter(
                (v: string) => v !== opt.value
              );
              formik.setFieldValue("categoryIds", newSelected);
            }}
            deleteIcon={<Close />}
          />
        ))}
    </Box>
  );

  return (
    <Card sx={{ p: 2.5 }}>
      <Typography variant="subtitle1" sx={{ mb: 2.5, fontWeight: 700 }}>
        {t(tokens.contentManagement.product.create.categories)}
      </Typography>

      <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
        {/* Parent Category Select */}
        <FormControl fullWidth error={!!(formik.touched.categoryIds && formik.errors.categoryIds)}>
          <Typography
            variant="body2"
            sx={{ display: "flex", fontSize: "16px", fontWeight: 600, mt: 0, mb: 1 }}
          >
            Danh mục sản phẩm <Typography sx={{ color: "red", ml: 0.5 }}>*</Typography>
          </Typography>

          <Select
            multiple
            value={hasCategories ? formik.values.categoryIds || [] : []}
            onChange={handleCategoryChange}
            label={t(tokens.contentManagement.product.create.parentCategory)}
            sx={{
              minHeight: "45px",
              backgroundColor: "background.paper",
              ".MuiSelect-select": {
                paddingTop: "8px",
                paddingBottom: "8px",
                minHeight: "29px !important",
              },
              "&.MuiInputBase-root": {
                minHeight: "45px",
              },
              pr: 4,
            }}
            renderValue={renderValue}
            disabled={!hasCategories}
          >
            {categoryOptions.map((option) => (
              <MenuItem key={option.value} value={option.value} sx={{ width: 600 }}>
                <TruncatedText text={option.label} isLink={true} />
              </MenuItem>
            ))}
          </Select>
          {formik.touched.categoryIds && formik.errors.categoryIds && (
            <FormHelperText>{formik.errors.categoryIds}</FormHelperText>
          )}
        </FormControl>
      </Box>
    </Card>
  );
};

export default CategorySelection;
