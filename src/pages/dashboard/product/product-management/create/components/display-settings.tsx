import React from "react";
import { <PERSON>, Card, TextField, Typography, FormControlLabel } from "@mui/material";
import { useTranslation } from "react-i18next";
import { FormikProps } from "formik";
import { tokens } from "@/src/locales/tokens";
import CustomSwitch from "@/src/components/custom-switch";
import { ProductFormValues } from "../../../../../../types/product/form";

interface DisplaySettingsProps {
  formik: FormikProps<ProductFormValues>;
}

const DisplaySettings: React.FC<DisplaySettingsProps> = ({ formik }) => {
  const { t } = useTranslation();

  const handlePublishChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    formik.setFieldValue("typePublish", e.target.checked ? "Publish" : "UnPublish");
  };

  const handleTopChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    formik.setFieldValue("isTop", e.target.checked);
  };

  return (
    <Card sx={{ p: 2.5 }}>
      <Typography variant="subtitle1" sx={{ mb: 2.5, fontWeight: 600 }}>
        Cài đặt hiển thị
      </Typography>

      <Box sx={{ display: "flex", flexDirection: "column", gap: 2.5 }}>
        <FormControlLabel
          control={
            <CustomSwitch
              checked={formik.values.typePublish === "Publish"}
              onChange={handlePublishChange}
            />
          }
          label={
            <Typography variant="body2" sx={{ color: "text.primary", ml: 1.5 }}>
              Hiển thị sản phẩm
            </Typography>
          }
          sx={{ mx: 0 }}
        />

        {/* <FormControlLabel
          control={<CustomSwitch checked={formik.values.isTop} onChange={handleTopChange} />}
          label={
            <Typography variant="body2" sx={{ color: "text.primary", ml: 1.5 }}>
              Sản phẩm nổi bật
            </Typography>
          }
          sx={{ mx: 0 }}
        /> */}

        <Box>
          <Typography
            variant="body2"
            sx={{ display: "flex", fontSize: "16px", fontWeight: 600, mt: 0, mb: 1 }}
          >
            Thứ tự hiển thị{" "}
          </Typography>
          <TextField
            fullWidth
            type="text"
            size="small"
            name="itemsPosition"
            value={formik.values.itemsPosition}
            onChange={(e) => {
              const value = e.target.value;
              if (/^\d*$/.test(value)) {
                formik.setFieldValue("itemsPosition", value === "" ? "" : Number(value));
              }
            }}
            error={!!(formik.touched.itemsPosition && formik.errors.itemsPosition)}
            helperText={formik.touched.itemsPosition && formik.errors.itemsPosition}
            onKeyDown={(e) => {
              if (["-", "e", "E", "+", "."].includes(e.key)) {
                e.preventDefault();
              }
            }}
            sx={{
              "& .MuiOutlinedInput-root": {
                backgroundColor: "background.paper",
                height: "45px",
              },
            }}
            onWheel={(e) => e.target instanceof HTMLElement && e.target.blur()}
          />
        </Box>

        <Box>
          <Typography
            variant="body2"
            sx={{ display: "flex", fontSize: "16px", fontWeight: 600, mt: 0, mb: 1 }}
          >
            Số lượng đã bán{" "}
          </Typography>
          <TextField
            fullWidth
            type="text"
            size="small"
            name="sold"
            value={formik.values.sold}
            onChange={(e) => {
              const value = e.target.value;
              if (/^\d*$/.test(value)) {
                formik.setFieldValue("sold", value === "" ? "" : Number(value));
              }
            }}
            error={!!(formik.touched.sold && formik.errors.sold)}
            helperText={formik.touched.sold && formik.errors.sold}
            onKeyDown={(e) => {
              if (["-", "e", "E", "+", "."].includes(e.key)) {
                e.preventDefault();
              }
            }}
            sx={{
              "& .MuiOutlinedInput-root": {
                backgroundColor: "background.paper",
                height: "45px",
              },
            }}
            onWheel={(e) => e.target instanceof HTMLElement && e.target.blur()}
          />
        </Box>
      </Box>
    </Card>
  );
};

export default DisplaySettings;
