import React, { use<PERSON><PERSON>back, useState, useEffect } from "react";
import {
  <PERSON>,
  <PERSON>,
  <PERSON>po<PERSON>,
  IconButton,
  Button,
  Stack,
  DialogContent,
  Dialog,
  DialogTitle,
  DialogActions,
  CircularProgress,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import { FormikProps } from "formik";
import { tokens } from "@/src/locales/tokens";
import { useDropzone } from "react-dropzone";
import {
  Info as InfoIcon,
  CloudUpload as UploadIcon,
  Link as LinkIcon,
  PhotoLibrary as LibraryIcon,
  Delete as DeleteIcon,
  PlayCircle as PlayCircleIcon,
  Close as CloseIcon,
} from "@mui/icons-material";
import { ProductFormValues } from "../../../../../../types/product/form";
import { FileType } from "@/src/constants/file-types";
import {
  MediaFile as APIMediaFile,
  CreateFileGroupRequest,
  GetGroupFileRequest,
  RefType,
} from "@/src/api/types/media.types";
import { MediaFile as FormMediaFile } from "@/src/constants/file-types";
import DialogAddMediaUrl from "../../media/dialog-add-media-url";
import DialogAddMediaLibrary from "../../media/dialog-add-media-library";
import { useMedia } from "@/src/api/hooks/media/use-media";
import useSnackbar from "@/src/hooks/use-snackbar";
import { ImageProcessor } from "@/src/utils/image-processor";
import { mediaService } from "@/src/api/services/media/media.service";
import { isValidImageFile } from "../../../category-management/create";
import { FILE_SIZE_2MB } from "@/src/constants/constant";
import { useStoreId } from "@/src/hooks/use-store-id";

interface MediaUploadProps {
  formik: FormikProps<ProductFormValues>;
}

const MediaUpload: React.FC<MediaUploadProps> = ({ formik }) => {
  const { t } = useTranslation();
  const [openUrlDialog, setOpenUrlDialog] = useState(false);
  const [openLibraryDialog, setOpenLibraryDialog] = useState(false);
  const [selectedVideo, setSelectedVideo] = useState<number | null>(null);
  const [defaultGroupId, setDefaultGroupId] = useState<string>("");
  const { getGroups } = useMedia();
  const snackbar = useSnackbar();
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const [isUploading, setIsUploading] = useState(false);
  const storeId = useStoreId();

  // Add validation function with immediate feedback
  const validateImages = useCallback(
    async (files: File[]) => {
      try {
        if (files[0]) {
          if (isValidImageFile(files[0])) {
            const currentImages = formik.values.images;
            const newTotalCount = currentImages.length + files.length;
            // Basic count validation
            if (newTotalCount > 20) {
              snackbar.error("Chỉ được thêm tối đa 20 ảnh");
              return;
            }

            // Validate file types and sizes
            const invalidFiles = files.filter((file) => {
              const isValidType = file.type.startsWith("image/") || file.type.startsWith("video/");
              const isValidSize = file.size <= 10485760; // 10MB
              return !isValidType || !isValidSize;
            });
            if (invalidFiles.length > 0) {
              snackbar.error("Đường dẫn hoặc kích thước file không hợp lệ tối đa 10MB");
            }

            // Trigger formik validation
            await formik.validateField("images");
            return true;
          } else {
            snackbar.error(
              "Định dạng file không hợp lệ. Hãy chọn file định dạng PNG, JPG, JPEG hoặc JFIF."
            );
            return false;
          }
        }
      } catch (error) {
        formik.setFieldError("images", error.message);
        return false;
      }
    },
    [formik]
  );

  // Handle file upload with validation
  const handleFileUpload = useCallback(
    async (files: File[]) => {
      if (!defaultGroupId) {
        snackbar.error("No default group found for upload");
        return;
      }

      const remainingSlots = 20 - formik.values.images.length;
      if (remainingSlots <= 0) {
        snackbar.error("Chỉ được thêm tối đa 20 ảnh");
        return;
      }

      setIsUploading(true);
      setUploadProgress(0);

      try {
        const totalFiles = files.length;
        let completedFiles = 0;
        const uploadedFiles = [];

        for (const file of files) {
          // Process image first
          const processedFile = await ImageProcessor.processImage(file);

          const data: CreateFileGroupRequest = {
            FileUpload: processedFile,
            GroupFileId: defaultGroupId,
            ShopId: storeId,
            RefType: RefType.Product,
          };
          // Upload processed file
          const response = await mediaService.uploadFile(data);

          uploadedFiles.push(response.data);
          completedFiles++;
          setUploadProgress((completedFiles / totalFiles) * 100);
        }

        const newImages = uploadedFiles.map((file) => ({
          type: file.type === "IMAGE" ? FileType.IMAGE : FileType.VIDEO,
          link: file.url || file.link,
        }));

        // Update formik with validation
        await formik.setFieldValue("images", [...formik.values.images, ...newImages], true);
        await formik.validateField("images");

        snackbar.success("Thêm mới ảnh thành công");
      } catch (error) {
        snackbar.error("Thêm mới ảnh thất bại");
      } finally {
        setIsUploading(false);
        setUploadProgress(0);
      }
    },
    [defaultGroupId, formik, snackbar]
  );
  const onDrop = useCallback(
    async (acceptedFiles: File[]) => {
      if (acceptedFiles?.length > 20) {
        snackbar.error("Chỉ thêm được tối đa 20 ảnh");
      } else {
        const isValid = await validateImages(acceptedFiles);
        if (isValid) {
          await handleFileUpload(acceptedFiles);
        }
      }
    },
    [validateImages, handleFileUpload]
  );

  // Initialize useDropzone with onDrop
  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop, // Pass onDrop callback here
    accept: {
      "image/png": [".png"],
      "image/jpeg": [".jpg", ".jpeg"],
    },
    maxSize: FILE_SIZE_2MB, // 2MB
    multiple: true,
  });

  // Fetch default group on mount
  useEffect(() => {
    const fetchDefaultGroup = async () => {
      try {
        const data: GetGroupFileRequest = {
          ShopId: storeId,
          Limit: 100,
          Skip: 0,
        };
        const response = await getGroups(data);
        if (response.data?.data?.length > 0) {
          setDefaultGroupId(response.data.data[0].groupFileId);
        }
      } catch (error) {}
    };
    fetchDefaultGroup();
  }, [storeId]);

  // Handle media from library with validation
  const handleAddFromLibrary = useCallback(
    async (mediaFiles: APIMediaFile[]) => {
      const convertedFiles = mediaFiles.map(convertMediaFile);

      // Validate total count
      if (formik.values.images.length + convertedFiles.length > 20) {
        snackbar.error("Chỉ được thêm tối đa 20 ảnh");
        return;
      }

      // Update formik with validation
      await formik.setFieldValue("images", [...formik.values.images, ...convertedFiles], true);
      await formik.validateField("images");

      setOpenLibraryDialog(false);
    },
    [formik, snackbar]
  );

  // Handle media from URL with validation
  const handleAddFromUrl = useCallback(
    async (media: FormMediaFile) => {
      // Validate total count
      if (formik.values.images.length >= 20) {
        snackbar.error("Chỉ được thêm tối đa 20 ảnh");
        return;
      }

      // Update formik with validation
      await formik.setFieldValue("images", [...formik.values.images, media], true);
      await formik.validateField("images");

      setOpenUrlDialog(false);
    },
    [formik, snackbar]
  );

  // Handle remove image with validation
  const handleRemoveImage = useCallback(
    async (index: number) => {
      const newImages = formik.values.images.filter((_: any, i: number) => i !== index);
      await formik.setFieldValue("images", newImages, true);
      await formik.validateField("images");
    },
    [formik]
  );

  // Convert API MediaFile to Form MediaFile
  const convertMediaFile = (apiFile: APIMediaFile): FormMediaFile => {
    return {
      type: apiFile.type === "IMAGE" ? FileType.IMAGE : FileType.VIDEO,
      link: apiFile.link,
      mediaFileId: apiFile.mediaFileId || "",
    };
  };

  // Handle video preview
  const handleVideoClick = (index: number) => {
    setSelectedVideo(index);
  };

  const handleCloseVideo = () => {
    setSelectedVideo(null);
  };

  // Video Preview Dialog Component
  const VideoPreviewDialog = ({
    file,
    open,
    onClose,
  }: {
    file: FormMediaFile;
    open: boolean;
    onClose: () => void;
  }) => (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          maxHeight: "90vh",
        },
      }}
    >
      <DialogTitle>
        <Box sx={{ display: "flex", alignItems: "center", justifyContent: "space-between" }}>
          <Typography variant="h6">Video Preview</Typography>
          <IconButton onClick={onClose} size="small">
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>
      <DialogContent sx={{ p: 0 }}>
        <video
          src={file.link}
          controls
          autoPlay
          style={{
            width: "100%",
            height: "auto",
            maxHeight: "calc(90vh - 100px)",
          }}
          onError={(e) => {
            console.error("Video loading error:", e);
          }}
        />
      </DialogContent>
      <DialogActions sx={{ p: 2 }}>
        <Button onClick={onClose} variant="outlined">
          {t(tokens.common.close)}
        </Button>
      </DialogActions>
    </Dialog>
  );

  return (
    <Card sx={{ p: 2.5 }}>
      {/* Header Row */}
      <Box sx={{ display: "flex", alignItems: "center", justifyContent: "space-between", mb: 2.5 }}>
        <Box sx={{ display: "flex", alignItems: "center" }}>
          <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
            {/* {t(tokens.contentManagement.product.create.media.title)} */}
            Hình ảnh sản phẩm ({formik.values.images.length}/20)
          </Typography>
          <IconButton size="small" sx={{ ml: 1 }}>
            <InfoIcon fontSize="small" />
          </IconButton>
        </Box>

        <Stack
          direction={{ xs: "column", sm: "row" }}
          spacing={1.5}
          sx={{
            width: { xs: "100%", sm: "auto" },
            alignItems: { xs: "stretch", sm: "center" },
          }}
        >
          <Button
            variant="outlined"
            size="small"
            startIcon={<LinkIcon />}
            onClick={() => setOpenUrlDialog(true)}
            sx={{
              minWidth: 130,
              height: 36,
              fontSize: "0.875rem",
              color: "#2654FE",
              borderColor: "#2654FE",
              width: { xs: "100%", sm: "auto" },
            }}
          >
            {t(tokens.contentManagement.product.create.media.addFromUrl)}
          </Button>
          <Button
            variant="outlined"
            size="small"
            startIcon={<LibraryIcon />}
            onClick={() => setOpenLibraryDialog(true)}
            sx={{
              minWidth: 130,
              height: 36,
              fontSize: "0.875rem",
              color: "#2654FE",
              borderColor: "#2654FE",
              width: { xs: "100%", sm: "auto" },
            }}
          >
            {t(tokens.contentManagement.product.create.media.addFromLibrary)}
          </Button>
        </Stack>
      </Box>

      {/* Upload Area */}
      <Stack spacing={2}>
        <Box
          {...getRootProps()}
          sx={{
            border: "1px dashed",
            borderColor: formik.touched.images && formik.errors.images ? "error.main" : "divider",
            borderRadius: 1,
            p: 3,
            textAlign: "center",
            cursor: "pointer",
            bgcolor: "background.paper",
            transition: "background-color 0.2s ease-in-out",
            "&:hover": {
              bgcolor: "action.hover",
            },
          }}
        >
          <input {...getInputProps()} />
          {isUploading ? (
            <Box sx={{ textAlign: "center" }}>
              <CircularProgress variant="determinate" value={uploadProgress} />
              <Typography variant="body2" sx={{ mt: 1 }}>
                Uploading... {Math.round(uploadProgress)}%
              </Typography>
            </Box>
          ) : (
            <>
              <UploadIcon sx={{ mb: 1, color: "text.secondary", fontSize: 40 }} />
              <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 500 }}>
                {isDragActive ? "Drop files here" : "Drag and drop files here or click to browse"}
              </Typography>
            </>
          )}
        </Box>
      </Stack>

      {/* Preview Grid */}
      {formik.values.images.length > 0 && (
        <Box
          sx={{
            mt: 2.5,
            display: "grid",
            gridTemplateColumns: "repeat(auto-fill, minmax(100px, 1fr))",
            gap: 1,
          }}
        >
          {formik.values.images.map((file, index) => (
            <Box
              key={index}
              sx={{
                position: "relative",
                width: "100%",
                height: "100px",
                borderRadius: 1,
                overflow: "hidden",
                cursor: file.type === FileType.VIDEO ? "pointer" : "default",
                "&:hover .media-actions": {
                  opacity: 1,
                },
              }}
              onClick={() => file.type === FileType.VIDEO && handleVideoClick(index)}
            >
              {file.type === FileType.VIDEO ? (
                <Box sx={{ position: "relative", height: "100%" }}>
                  <video
                    src={file.link}
                    style={{
                      width: "100%",
                      height: "100%",
                      objectFit: "cover",
                    }}
                  />
                  <Box
                    sx={{
                      position: "absolute",
                      top: 0,
                      left: 0,
                      right: 0,
                      bottom: 0,
                      bgcolor: "rgba(0,0,0,0.4)",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                    }}
                  >
                    <PlayCircleIcon
                      sx={{
                        color: "white",
                        fontSize: 32,
                        opacity: 0.9,
                      }}
                    />
                  </Box>
                </Box>
              ) : (
                <img
                  src={file.link}
                  alt={`Preview ${index}`}
                  style={{
                    width: "100%",
                    height: "100%",
                    objectFit: "cover",
                  }}
                />
              )}

              {/* Delete Button */}
              <Box
                className="media-actions"
                sx={{
                  position: "absolute",
                  top: 0,
                  right: 0,
                  p: 0.5,
                  opacity: 0,
                  transition: "opacity 0.2s",
                  bgcolor: "rgba(0,0,0,0.5)",
                  borderRadius: "0 0 0 8px",
                  zIndex: 1,
                }}
                onClick={(e) => {
                  e.stopPropagation();
                  handleRemoveImage(index);
                }}
              >
                <IconButton
                  size="small"
                  sx={{
                    color: "white",
                    "&:hover": {
                      bgcolor: "rgba(255,255,255,0.2)",
                    },
                  }}
                >
                  <DeleteIcon fontSize="small" />
                </IconButton>
              </Box>
            </Box>
          ))}
        </Box>
      )}

      {/* Video Preview Dialog */}
      {/* {selectedVideo !== null && (
        <VideoPreviewDialog
          file={formik.values.images[selectedVideo]}
          open={selectedVideo !== null}
          onClose={handleCloseVideo}
        />
      )} */}

      {/* Show validation errors */}
      {formik.touched.images && formik.errors.images && (
        <Typography
          color="error"
          variant="caption"
          sx={{
            mt: 1,
            display: "block",
            fontWeight: 500,
          }}
        >
          {formik.errors.images as string}
        </Typography>
      )}

      {/* <DialogAddMediaUrl
        open={openUrlDialog}
        onClose={() => setOpenUrlDialog(false)}
        onSave={handleAddFromUrl}
        defaultGroupId={defaultGroupId}
      />
      <DialogAddMediaLibrary
        open={openLibraryDialog}
        onClose={() => setOpenLibraryDialog(false)}
        onSave={handleAddFromLibrary}
        maxSelect={20 - formik.values.images.length}
      /> */}
    </Card>
  );
};

export default MediaUpload;
