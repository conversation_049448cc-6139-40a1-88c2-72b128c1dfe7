import {
  Box,
  Card,
  FormControl,
  FormHelperText,
  InputAdornment,
  InputLabel,
  TextField,
  Typography,
} from "@mui/material";
import React from "react";

const ProductTaxRate = ({ formik, shopTaxRate }) => {
  return (
    <>
      <Card sx={{ p: 2.5, boxShadow: "0px 2px 6px rgba(0, 0, 0, 0.1)", borderRadius: 2 }}>
        <Typography variant="subtitle1" sx={{ mb: 2.5, fontWeight: 700 }}>
          Thuế
        </Typography>

        <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
          <FormControl
            fullWidth
            error={!!(formik.touched.customTaxRate && formik.errors.customTaxRate)}
          >
            <Typography
              variant="body2"
              sx={{ mb: 1, color: "text.primary", fontWeight: 600, fontSize: "16px" }}
            >
              T<PERSON><PERSON> sản phẩm
            </Typography>
            <TextField
              fullWidth
              type="number"
              size="small"
              name="customTaxRate"
              value={formik.values.customTaxRate}
              placeholder="Nhập tỉ lệ thuế"
              onChange={(e) => {
                const value = e.target.value;
                if (value === "" || value === "0" || parseFloat(value) >= 0) {
                  formik.setFieldValue("customTaxRate", value === "" ? null : parseFloat(value));
                }
              }}
              error={!!(formik.touched.customTaxRate && formik.errors.customTaxRate)}
              helperText={formik.touched.customTaxRate && formik.errors.customTaxRate}
              InputProps={{
                inputProps: { min: 0 },
                endAdornment: <InputAdornment position="end">%</InputAdornment>,
              }}
              sx={{
                "& .MuiOutlinedInput-root": {
                  backgroundColor: "background.paper",
                  "&:hover .MuiOutlinedInput-notchedOutline": {
                    borderColor: "primary.main",
                  },
                  height: "45px",
                },
                "& input[type=number]::-webkit-inner-spin-button, & input[type=number]::-webkit-outer-spin-button":
                  {
                    "-webkit-appearance": "none",
                    margin: 0,
                  },
                "& input[type=number]": {
                  "-moz-appearance": "textfield",
                },
              }}
              onWheel={(e) => e.target instanceof HTMLElement && e.target.blur()}
              onKeyDown={(e) => {
                if (e.key === "e" || e.key === "E" || e.key === "-") {
                  e.preventDefault();
                }
              }}
            />
            {!formik.values.customTaxRate && formik.values.customTaxRate !== 0 && (
              <FormHelperText sx={{ mt: 1, color: "text.secondary", fontStyle: "italic" }}>
                {`Tỉ lệ thuế sẽ được áp dụng theo tỉ lệ của cửa hàng: ${
                  shopTaxRate ? shopTaxRate : 0
                }%`}
              </FormHelperText>
            )}
          </FormControl>
        </Box>
      </Card>
    </>
  );
};

export default ProductTaxRate;
