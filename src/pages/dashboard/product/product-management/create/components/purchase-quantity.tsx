import React, { memo, useState, useCallback } from "react";
import { <PERSON>, Card, FormHelperText, Tooltip, Typography } from "@mui/material";
import { useTranslation } from "react-i18next";
import { FormikProps } from "formik";
import { tokens } from "@/src/locales/tokens";
import CustomTextField from "@/src/components/custom-textfield";
import { ProductFormValues } from "../../../../../../types/product/form";
import { ErrorOutline } from "@mui/icons-material";

interface PurchaseQuantityProps {
  formik: FormikProps<ProductFormValues>;
}

const PurchaseQuantity: React.FC<PurchaseQuantityProps> = memo(({ formik }) => {
  const { t } = useTranslation();

  // Local state for optimizing input performance
  const [localQuantity, setLocalQuantity] = useState(formik.values.quantity);
  const [localQuantityPurchase, setLocalQuantityPurchase] = useState(
    formik.values.quantityPurchase
  );

  // Update local state while typing
  const handleQuantityChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const input = e.target;
      const value = input.value.replace(/[^0-9]/g, ""); // Only allow numbers

      // If user starts typing and current value is "0", clear it
      if (localQuantity === 0 && value !== "0") {
        setLocalQuantity(Number(value));
        // Set cursor to the end
        input.value = value;
        const length = input.value.length;
        input.setSelectionRange(length, length);
      } else {
        setLocalQuantity(Number(value || 0));
      }
    },
    [localQuantity]
  );

  const handleQuantityPurchaseChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const input = e.target;
      const value = input.value.replace(/[^0-9]/g, ""); // Only allow numbers

      // If user starts typing and current value is "0", clear it
      if (localQuantityPurchase === 0 && value !== "0") {
        setLocalQuantityPurchase(Number(value));
        // Set cursor to the end
        input.value = value;
        const length = input.value.length;
        input.setSelectionRange(length, length);
      } else {
        setLocalQuantityPurchase(Number(value || 0));
      }
    },
    [localQuantityPurchase]
  );

  // Update formik state and validate on blur
  const handleQuantityBlur = useCallback(
    (e: React.FocusEvent<HTMLInputElement>) => {
      formik.setFieldValue("quantity", localQuantity, true);
      formik.handleBlur(e);
    },
    [formik, localQuantity]
  );

  const handleQuantityPurchaseBlur = useCallback(
    (e: React.FocusEvent<HTMLInputElement>) => {
      formik.setFieldValue("quantityPurchase", localQuantityPurchase, true);
      formik.handleBlur(e);
    },
    [formik, localQuantityPurchase]
  );

  // Handle focus to select all text
  const handleFocus = useCallback((e: React.FocusEvent<HTMLInputElement>) => {
    e.target.select();
  }, []);

  // Sync local state when formik values change externally
  React.useEffect(() => {
    setLocalQuantity(formik.values.quantity);
  }, [formik.values.quantity]);

  React.useEffect(() => {
    setLocalQuantityPurchase(formik.values.quantityPurchase);
  }, [formik.values.quantityPurchase]);

  return (
    <Card sx={{ p: 2.5 }}>
      <Typography variant="subtitle1" sx={{ mb: 2.5, fontWeight: 700 }}>
        {t(tokens.contentManagement.product.create.inventory.title)}
      </Typography>

      <Box sx={{ display: "flex", gap: 2 }}>
        {/* Tồn kho */}
        <Box sx={{ flex: 1 }}>
          <Typography
            variant="body2"
            sx={{ display: "flex", fontSize: "16px", fontWeight: 600, mt: 0, mb: 1 }}
          >
            {t(tokens.contentManagement.product.create.inventory.stock)}{" "}
          </Typography>
          <CustomTextField
            fullWidth
            type="text"
            name="quantity"
            value={localQuantity}
            onChange={handleQuantityChange}
            onBlur={handleQuantityBlur}
            onFocus={handleFocus}
            error={formik.touched.quantity && Boolean(formik.errors.quantity)}
            helperText={
              formik.touched.quantity && formik.errors.quantity
                ? String(formik.errors.quantity)
                : ""
            }
            inputProps={{
              inputMode: "numeric",
              pattern: "[0-9]*",
            }}
            sx={{
              "& .MuiOutlinedInput-root": {
                backgroundColor: "background.paper",
                height: "45px",
              },
            }}
          />
        </Box>

        {/* Mua tối đa */}
        <Box sx={{ flex: 1 }}>
          <Typography
            variant="body2"
            sx={{ display: "flex", fontSize: "16px", fontWeight: 600, mt: 0, mb: 1 }}
          >
            {t(tokens.contentManagement.product.create.inventory.maxQuantity)}{" "}
            <Tooltip
              title="Số lượng tối đa mà mỗi khách hàng có thể mua sản phẩm này"
              placement="top"
            >
              <Typography sx={{ ml: 1 }}>
                <ErrorOutline
                  sx={{ fontSize: 18, cursor: "pointer", mb: "3px", color: "rgb(206, 166, 73)" }}
                />
              </Typography>
            </Tooltip>
          </Typography>
          <CustomTextField
            fullWidth
            type="text"
            name="quantityPurchase"
            value={localQuantityPurchase}
            onChange={handleQuantityPurchaseChange}
            onBlur={handleQuantityPurchaseBlur}
            onFocus={handleFocus}
            error={formik.touched.quantityPurchase && Boolean(formik.errors.quantityPurchase)}
            inputProps={{
              inputMode: "numeric",
              pattern: "[0-9]*",
              min: 0,
              max: 1000,
            }}
            sx={{
              "& .MuiOutlinedInput-root": {
                backgroundColor: "background.paper",
                height: "45px",
              },
            }}
          />
          <FormHelperText sx={{ mt: 1, ml: 1, color: "text.secondary", fontStyle: "italic" }}>
            {`Nhập `}
            <b>0</b>
            {` nếu `}
            <b>không giới hạn</b>
            {` số lượng mua tối đa`}
          </FormHelperText>
        </Box>
      </Box>
    </Card>
  );
});

PurchaseQuantity.displayName = "PurchaseQuantity";

export default PurchaseQuantity;
