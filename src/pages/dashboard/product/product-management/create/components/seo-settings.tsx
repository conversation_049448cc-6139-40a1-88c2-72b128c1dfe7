import React, { useState } from "react";
import { <PERSON>, <PERSON>, Typography, Button } from "@mui/material";
import { useTranslation } from "react-i18next";
import { FormikProps } from "formik";
import { tokens } from "@/src/locales/tokens";
import { ProductFormValues } from "../../../../../../types/product/form";
import DialogAddSeo from "../../seo/dialog-add-seo";

interface SeoSettingsProps {
  formik: FormikProps<ProductFormValues>;
}

const SeoSettings: React.FC<SeoSettingsProps> = ({ formik }) => {
  const { t } = useTranslation();
  const [openSeoDialog, setOpenSeoDialog] = useState(false);

  const handleSaveSeo = (seoData: any) => {
    formik.setFieldValue("seoTags[0]", seoData);
  };

  return (
    <Card sx={{ p: 2.5 }}>
      <Box sx={{ display: "flex", flexDirection: "column", gap: 2.5 }}>
        <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
          <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
            {t(tokens.contentManagement.product.create.seo.title)}
          </Typography>
          <Button
            sx={{ textTransform: "none !important", color: "#2654FE" }}
            variant="text"
            color="primary"
            onClick={() => setOpenSeoDialog(true)}
          >
            {t(tokens.contentManagement.product.create.seo.editButton)}
          </Button>
        </Box>

        <Typography variant="body2" color="text.secondary">
          {t(tokens.contentManagement.product.create.seo.description)}
        </Typography>
      </Box>

      <DialogAddSeo
        open={openSeoDialog}
        onClose={() => setOpenSeoDialog(false)}
        onSave={handleSaveSeo}
        initialData={formik.values.seoTags[0]}
      />
    </Card>
  );
};

export default SeoSettings;
