import React from "react";
import { Box, Typography, Card } from "@mui/material";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import LocalShippingIcon from "@mui/icons-material/LocalShipping";
import EventIcon from "@mui/icons-material/Event";
import { useTranslation } from "react-i18next";
import { tokens } from "@/src/locales/tokens";

type ItemType = "Product" | "Service";

interface TypeSelectionProps {
  itemsType: ItemType;
  isEdit: boolean;
  setItemsType: (type: ItemType) => void;
}

const TypeSelection: React.FC<TypeSelectionProps> = ({ itemsType, isEdit, setItemsType }) => {
  const { t } = useTranslation();

  const handleTypeChange = (type: ItemType) => {
    if (!isEdit) {
      setItemsType(type);
    }
  };

  // Common styles for both buttons
  const commonButtonStyles = {
    p: 2,
    cursor: isEdit ? "default" : "pointer",
    borderRadius: 1,
    border: "1px solid",
    transition: "all 0.2s ease-in-out",
    position: "relative",
    width: "33.33%",
    minWidth: "250px",
    "&:hover": !isEdit && {
      borderColor: "primary.main",
      bgcolor: "primary.lighter",
      "& .MuiTypography-subtitle2": {
        color: "primary.main",
      },
      "& .icon": {
        color: "primary.main",
      },
    },
  };

  // Render Product Button
  const renderProductButton = () => (
    <Box
      onClick={() => handleTypeChange("Product")}
      sx={{
        ...commonButtonStyles,
        borderColor: itemsType === "Product" ? "#2654FE" : "divider",
        bgcolor: itemsType === "Product" ? "primary.lighter" : "background.paper",
        boxShadow: itemsType === "Product" ? 2 : 0,
      }}
    >
      {itemsType === "Product" && (
        <CheckCircleIcon
          sx={{
            position: "absolute",
            top: 8,
            right: 8,
            fontSize: 20,
            color: "primary.main",
            fill: "#2654FE",
          }}
        />
      )}
      <Box sx={{ display: "flex", alignItems: "flex-start", gap: 1.5 }}>
        <LocalShippingIcon
          className="icon"
          sx={{
            fontSize: 28,
            color: itemsType === "Product" ? "primary.main" : "text.secondary",
            mt: 0.5,
            transition: "color 0.2s ease-in-out",
            fill: "#2654FE",
          }}
        />
        <Box>
          <Typography
            variant="subtitle2"
            sx={{
              mb: 0.5,
              fontWeight: itemsType === "Product" ? 600 : 500,
              transition: "color 0.2s ease-in-out",
            }}
          >
            {t(tokens.contentManagement.product.create.physicalProduct)}
          </Typography>
          <Typography
            variant="caption"
            color="text.secondary"
            sx={{ display: "block", lineHeight: 1.4 }}
          >
            {t(tokens.contentManagement.product.create.physicalDescription)}
          </Typography>
        </Box>
      </Box>
    </Box>
  );

  // Render Service Button
  const renderServiceButton = () => (
    <Box
      onClick={() => handleTypeChange("Service")}
      sx={{
        ...commonButtonStyles,
        borderColor: itemsType === "Service" ? "#2654FE" : "divider",
        bgcolor: itemsType === "Service" ? "primary.lighter" : "background.paper",
        boxShadow: itemsType === "Service" ? 2 : 0,
      }}
    >
      {itemsType === "Service" && (
        <CheckCircleIcon
          sx={{
            position: "absolute",
            top: 8,
            right: 8,
            fontSize: 20,
            color: "primary.main",
            fill: "#2654FE",
          }}
        />
      )}
      <Box sx={{ display: "flex", alignItems: "flex-start", gap: 1.5 }}>
        <EventIcon
          className="icon"
          sx={{
            fontSize: 28,
            color: itemsType === "Service" ? "primary.main" : "text.secondary",
            mt: 0.5,
            transition: "color 0.2s ease-in-out",
            fill: "#2654FE",
          }}
        />
        <Box>
          <Typography
            variant="subtitle2"
            sx={{
              mb: 0.5,
              fontWeight: itemsType === "Service" ? 600 : 500,
              transition: "color 0.2s ease-in-out",
            }}
          >
            {t(tokens.contentManagement.product.create.service)}
          </Typography>
          <Typography
            variant="caption"
            color="text.secondary"
            sx={{ display: "block", lineHeight: 1.4 }}
          >
            {t(tokens.contentManagement.product.create.serviceDescription)}
          </Typography>
        </Box>
      </Box>
    </Box>
  );

  return (
    <Card sx={{ p: 2.5, mb: 3 }}>
      <Typography variant="h6" sx={{ mb: 2.5, textAlign: "start" }}>
        {t(tokens.contentManagement.product.create.typeLabel)}
      </Typography>

      <Box
        sx={{
          display: "flex",
          justifyContent: "flex-start",
          gap: 2,
          width: "100%",
          overflowX: "auto",
          "&::-webkit-scrollbar": {
            height: 8,
          },
          "&::-webkit-scrollbar-thumb": {
            borderRadius: 4,
            backgroundColor: "rgba(0,0,0,0.2)",
          },
        }}
      >
        {(!isEdit || (isEdit && itemsType === "Product")) && renderProductButton()}
        {(!isEdit || (isEdit && itemsType === "Service")) && renderServiceButton()}
        {!isEdit && <Box sx={{ width: "33.33%", minWidth: "250px" }} />}
      </Box>
    </Card>
  );
};

export default TypeSelection;
