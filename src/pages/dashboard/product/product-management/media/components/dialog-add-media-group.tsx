import React, { useState, useEffect } from "react";
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  IconButton,
  TextField,
  CircularProgress,
} from "@mui/material";
import { Close as CloseIcon } from "@mui/icons-material";
import * as Yup from "yup";
import { useMedia } from "@/src/api/hooks/media/use-media";
import { useStoreId } from "@/src/hooks/use-store-id";
import { StorageService } from "nextjs-api-lib";
import { CreateGroupFileRequest, MediaGroup } from "@/src/api/types/media.types";
import useSnackbar from "@/src/hooks/use-snackbar";

interface DialogAddMediaGroupProps {
  open: boolean;
  onClose: () => void;
  onSuccess: () => void;
  groupData?: MediaGroup | null;
}

const validationSchema = Yup.object({
  groupName: Yup.string()
    .required("Vui lòng nhập tên nhóm")
    .min(2, "Tên nhóm phải có ít nhất 2 ký tự")
    .max(50, "Tên nhóm không được vượt quá 50 ký tự"),
});

const DialogAddMediaGroup: React.FC<DialogAddMediaGroupProps> = ({
  open,
  onClose,
  onSuccess,
  groupData,
}) => {
  const [groupName, setGroupName] = useState("");
  const [originalGroupName, setOriginalGroupName] = useState("");
  const [error, setError] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { createGroup, updateGroup } = useMedia();
  const storeId = useStoreId();
  const partnerId = StorageService.get("partnerId") || "";
  const snackbar = useSnackbar();

  useEffect(() => {
    if (groupData) {
      setGroupName(groupData.groupName);
      setOriginalGroupName(groupData.groupName);
    } else {
      setGroupName("");
      setOriginalGroupName("");
    }
  }, [groupData]);

  const handleClose = () => {
    setGroupName("");
    setError("");
    onClose();
  };

  const handleSubmit = async () => {
    try {
      await validationSchema.validate({ groupName });
      setIsSubmitting(true);
      setError("");

      const data: CreateGroupFileRequest = {
        groupFileId: groupData?.groupFileId || "",
        partnerId,
        numberFile: 0,
        shopId: storeId,
        groupName: groupName.trim(),
        status: "Actived",
        created: groupData?.created || new Date().toISOString(),
        updated: new Date().toISOString(),
      };

      if (groupData) {
        await updateGroup(data);
        snackbar.success("Cập nhật nhóm thành công");
      } else {
        await createGroup(data);
        snackbar.success("Thêm nhóm mới thành công");
      }

      handleClose();
      onSuccess();
    } catch (err: any) {
      setError(err.message || "Có lỗi xảy ra, vui lòng thử lại");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
      <DialogTitle>
        <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
          {groupData ? "Sửa nhóm" : "Thêm nhóm mới"}
          <IconButton onClick={handleClose} size="small">
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent>
        <Box sx={{ mt: 2 }}>
          <TextField
            fullWidth
            label="Tên nhóm"
            value={groupName}
            onChange={(e) => {
              setGroupName(e.target.value);
              setError("");
            }}
            error={!!error}
            helperText={error}
            placeholder="Nhập tên nhóm"
            InputLabelProps={{
              shrink: true,
              classes: { asterisk: "Mui-required-red" },
            }}
            disabled={isSubmitting}
            autoFocus
          />
        </Box>
      </DialogContent>

      <DialogActions sx={{ p: 2.5 }}>
        <Button onClick={handleClose} disabled={isSubmitting}>
          Hủy
        </Button>
        <Button
          variant="contained"
          onClick={handleSubmit}
          disabled={
            isSubmitting ||
            !groupName.trim() ||
            (groupData ? groupName.trim() === originalGroupName.trim() : false)
          }
        >
          {isSubmitting ? <CircularProgress size={24} /> : groupData ? "Cập nhật" : "Thêm nhóm"}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default DialogAddMediaGroup;
