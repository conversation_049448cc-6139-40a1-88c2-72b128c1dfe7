import React, { useState } from "react";
import {
  Box,
  List,
  ListItemButton,
  ListItemText,
  ListItemSecondaryAction,
  Button,
  Divider,
  Paper,
  CircularProgress,
  Typography,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from "@mui/material";
import {
  Add as AddIcon,
  FolderOutlined as FolderIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Image as ImageIcon,
  VideoFile as VideoIcon,
} from "@mui/icons-material";
import { MediaGroup } from "@/src/api/types/media.types";
import DialogAddMediaGroup from "./dialog-add-media-group";
import { useMedia } from "@/src/api/hooks/media/use-media";
import useSnackbar from "@/src/hooks/use-snackbar";

interface MediaGroupsSidebarProps {
  groups: MediaGroup[];
  selectedGroup: MediaGroup | null;
  onSelectGroup: (group: MediaGroup) => void;
  loading: boolean;
  type: "image" | "video";
  onRefresh: () => void;
}

const MediaGroupsSidebar: React.FC<MediaGroupsSidebarProps> = ({
  groups,
  selectedGroup,
  onSelectGroup,
  loading,
  type,
  onRefresh,
}) => {
  const [openAddDialog, setOpenAddDialog] = useState(false);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [selectedGroupForAction, setSelectedGroupForAction] = useState<MediaGroup | null>(null);
  const { deleteGroup } = useMedia();
  const snackbar = useSnackbar();

  const handleEditClick = (group: MediaGroup, event: React.MouseEvent) => {
    event.stopPropagation();
    setSelectedGroupForAction(group);
    setOpenAddDialog(true);
  };

  const handleDeleteClick = (group: MediaGroup, event: React.MouseEvent) => {
    event.stopPropagation();
    setSelectedGroupForAction(group);
    setOpenDeleteDialog(true);
  };

  const handleDeleteConfirm = async () => {
    try {
      if (selectedGroupForAction) {
        await deleteGroup(selectedGroupForAction.groupFileId);
        snackbar.success("Xóa nhóm thành công");

        // Clear selected group if it was deleted
        if (selectedGroup?.groupFileId === selectedGroupForAction.groupFileId) {
          onSelectGroup(null);
        }

        // Refresh the groups list
        onRefresh();
      }
    } catch (error) {
      snackbar.error("Có lỗi xảy ra khi xóa nhóm");
    } finally {
      setOpenDeleteDialog(false);
      setSelectedGroupForAction(null);
    }
  };

  return (
    <Paper
      elevation={0}
      sx={{
        width: "280px",
        height: "100%",
        borderRight: 1,
        borderColor: "divider",
        display: "flex",
        flexDirection: "column",
      }}
    >
      {/* Header */}
      <Box
        sx={{
          p: 2,
          borderBottom: 1,
          borderColor: "divider",
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
        }}
      >
        <Typography variant="subtitle1" fontWeight="medium">
          Nhóm {type === "image" ? "ảnh" : "video"}
        </Typography>
        <Tooltip title="Thêm nhóm mới">
          <IconButton size="small" onClick={() => setOpenAddDialog(true)} color="primary">
            <AddIcon />
          </IconButton>
        </Tooltip>
      </Box>

      {/* List Content */}
      <Box sx={{ flexGrow: 1, overflow: "auto" }}>
        {loading ? (
          <Box sx={{ p: 4, textAlign: "center" }}>
            <CircularProgress size={24} />
          </Box>
        ) : groups.length === 0 ? (
          <Box sx={{ p: 3, textAlign: "center" }}>
            <FolderIcon sx={{ fontSize: 40, color: "text.disabled", mb: 1 }} />
            <Typography variant="body2" color="text.secondary">
              Chưa có nhóm nào
            </Typography>
            <Button
              variant="outlined"
              startIcon={<AddIcon />}
              size="small"
              onClick={() => setOpenAddDialog(true)}
              sx={{ mt: 2 }}
            >
              Thêm nhóm đầu tiên
            </Button>
          </Box>
        ) : (
          <List sx={{ py: 0 }}>
            {groups.map((group, index) => (
              <ListItemButton
                key={group.groupFileId}
                selected={selectedGroup?.groupFileId === group.groupFileId}
                onClick={() => onSelectGroup(group)}
                sx={{
                  py: 1.5,
                  px: 2,
                  "&.Mui-selected": {
                    bgcolor: "primary.lighter",
                    "&:hover": {
                      bgcolor: "primary.lighter",
                    },
                  },
                }}
              >
                <Box sx={{ display: "flex", alignItems: "center", width: "100%", pr: 6 }}>
                  <Box sx={{ minWidth: 0 }}>
                    <Typography
                      variant="subtitle2"
                      sx={{
                        fontWeight: selectedGroup?.groupFileId === group.groupFileId ? 600 : 400,
                        color:
                          selectedGroup?.groupFileId === group.groupFileId
                            ? "primary.main"
                            : "text.primary",
                        whiteSpace: "nowrap",
                        overflow: "hidden",
                        textOverflow: "ellipsis",
                      }}
                    >
                      {group.groupName}
                    </Typography>
                    <Typography
                      variant="caption"
                      sx={{
                        color: "text.secondary",
                        display: "block",
                        mt: 0.2,
                      }}
                    >
                      {group.numberFile || 0} {type === "image" ? "ảnh" : "video"}
                    </Typography>
                  </Box>
                </Box>
                <ListItemSecondaryAction sx={{ right: 8 }}>
                  <Tooltip title="Sửa nhóm">
                    <IconButton
                      edge="end"
                      size="small"
                      onClick={(e) => handleEditClick(group, e)}
                      sx={{
                        mr: 1,
                        color: "text.secondary",
                        "&:hover": {
                          color: "primary.main",
                          bgcolor: "primary.lighter",
                        },
                      }}
                    >
                      <EditIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>

                  {index > 0 && (
                    <Tooltip title="Xóa nhóm">
                      <IconButton
                        edge="end"
                        size="small"
                        onClick={(e) => handleDeleteClick(group, e)}
                        sx={{
                          color: "text.secondary",
                          "&:hover": {
                            color: "error.main",
                            bgcolor: "error.lighter",
                          },
                        }}
                      >
                        <DeleteIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  )}
                </ListItemSecondaryAction>
              </ListItemButton>
            ))}
          </List>
        )}
      </Box>

      {/* Add/Edit Dialog */}
      <DialogAddMediaGroup
        open={openAddDialog}
        onClose={() => {
          setOpenAddDialog(false);
          setSelectedGroupForAction(null);
        }}
        onSuccess={() => {
          setOpenAddDialog(false);
          setSelectedGroupForAction(null);
          onRefresh();
        }}
        groupData={selectedGroupForAction}
      />

      {/* Delete Confirmation Dialog */}
      <Dialog open={openDeleteDialog} onClose={() => setOpenDeleteDialog(false)}>
        <DialogTitle>Xác nhận xóa nhóm</DialogTitle>
        <DialogContent>
          <Typography>
            Bạn có chắc chắn muốn xóa nhóm "{selectedGroupForAction?.groupName}" không?
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDeleteDialog(false)}>Hủy</Button>
          <Button onClick={handleDeleteConfirm} color="error" variant="contained">
            Xóa
          </Button>
        </DialogActions>
      </Dialog>
    </Paper>
  );
};

export default MediaGroupsSidebar;
