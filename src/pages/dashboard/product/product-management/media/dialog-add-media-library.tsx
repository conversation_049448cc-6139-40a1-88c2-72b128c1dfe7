import React, { useState } from "react";
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  <PERSON>alog<PERSON><PERSON>,
  <PERSON>ton,
  Box,
  Typography,
  IconButton,
  Tabs,
  Tab,
  Paper,
} from "@mui/material";
import { Close as CloseIcon } from "@mui/icons-material";
import { MediaFile } from "@/src/api/types/media.types";
import MediaLibraryContent from "./components/media-library-content";
import { ExistingMediaFile } from "../create/create";
import useSnackbar from "@/src/hooks/use-snackbar";
import { LocalPreview } from "@/src/components/common-media-upload";

interface DialogAddMediaLibraryProps {
  open: boolean;
  onClose: () => void;
  onSave: (listMedia: File[]) => void;
  maxSelect?: number;
  existingFiles?: ExistingMediaFile[];
  setExistingFiles?: any;
  localFiles?: File[];
  setLocalFiles?: (files: File[]) => void;
  maxFile?: number;
  errorMsg?: string;
  setErrorMsg?: any;
  localPreviews?: LocalPreview[];
  setLocalPreviews?: any;
  allPreviews?: Array<ExistingMediaFile | (LocalPreview & { isLocal?: boolean })>;
  setAllPreviews?: React.Dispatch<
    React.SetStateAction<
      (
        | ExistingMediaFile
        | (LocalPreview & {
            isLocal?: boolean;
          })
      )[]
    >
  >;
  onlyLib?: boolean;
  handleImageSelect?: any;
}

const DialogAddMediaLibrary: React.FC<DialogAddMediaLibraryProps> = ({
  open,
  onClose,
  onSave,
  maxSelect = 1,
  existingFiles,
  setExistingFiles,
  localFiles,
  setLocalFiles,
  maxFile,
  errorMsg,
  setErrorMsg,
  localPreviews,
  setLocalPreviews,
  allPreviews,
  setAllPreviews,
  onlyLib = false,
  handleImageSelect,
}) => {
  const [selectedTab, setSelectedTab] = useState(0);
  const [selectedItems, setSelectedItems] = useState<ExistingMediaFile[]>([]);
  const snackbar = useSnackbar();
  const handleTabChange = (_: React.SyntheticEvent, newValue: number) => {
    setSelectedTab(newValue);
    setSelectedItems([]); // Reset selected items when changing tabs
  };

  const handleSelect = (items: ExistingMediaFile[]) => {
    setSelectedItems(items);
  };

  // const handleSave = () => {
  //   // onSave(selectedItems);
  //   if (selectedItems.length > maxFile) {
  //     snackbar.error(`Bạn chỉ có thể chọn tối đa ${maxFile} tệp.`);
  //     return;
  //   }
  //   setExistingFiles((prev) => [...prev, ...selectedItems]);
  //   handleClose();
  // };
  // const handleSave = () => {
  //   console.log({ allPreviews });
  //   console.log({ existingFiles });
  //   console.log({ localPreviews });
  //   console.log({ selectedItems });
  //   if (maxFile === 1) {
  //     if (allPreviews && allPreviews.length > 0) {
  //       if (existingFiles.length + selectedItems.length + localPreviews.length > maxFile) {
  //         snackbar.error(`Bạn chỉ có thể lưu tối đa ${maxFile} tệp.`);
  //         onClose();
  //         return;
  //       }
  //       setAllPreviews([]);
  //       setExistingFiles([]);
  //       setExistingFiles([...selectedItems]); // Thay thế hoàn toàn
  //       setAllPreviews(selectedItems);
  //     } else {
  //       if (existingFiles.length + selectedItems.length + localPreviews.length > maxFile) {
  //         snackbar.error(`Bạn chỉ có thể lưu tối đa ${maxFile} tệp.`);
  //         onClose();
  //         return;
  //       }
  //     }
  //     // setAllPreviews([]);

  //     // onSave(selectedItems[0]);
  //   } else {
  //     if (existingFiles.length + selectedItems.length + localPreviews.length > maxFile) {
  //       snackbar.error(`Bạn chỉ có thể lưu tối đa ${maxFile} tệp.`);
  //       onClose();
  //       return;
  //     }
  //     setExistingFiles((prev) => [...prev, ...selectedItems]); // Thêm vào danh sách cũ
  //   }
  //   handleClose();
  // };

  const handleSave = () => {
    if (onlyLib === true) {
      setExistingFiles([...selectedItems]);
      handleImageSelect(selectedItems);
    } else {
      if (maxFile === 1) {
        if (selectedItems.length === 1) {
          setLocalFiles([]); // Xóa toàn bộ files cũ trước
          setLocalPreviews([]); // Xóa toàn bộ previews cũ trước
          setExistingFiles([]);
          setAllPreviews([]); // Xóa toàn bộ previews cũ trước
          setExistingFiles([...selectedItems]);
          setAllPreviews([...selectedItems]); // Thêm ảnh mới vào
          setErrorMsg("");
          handleClose();
        } else if (selectedItems.length > 1) {
          snackbar.error("Chỉ được chọn tối đa 1 tệp.");
        }
        return;
      }

      // Trường hợp maxFile > 1 giữ nguyên logic cũ
      if (existingFiles?.length + selectedItems?.length + (localPreviews?.length || 0) > maxFile) {
        snackbar.error(`Bạn chỉ có thể lưu tối đa ${maxFile} tệp.`);
        onClose();
        return;
      }
      setExistingFiles((prev) => [...prev, ...selectedItems]);
      handleClose();
    }
    setSelectedItems([]);
  };

  const handleClose = () => {
    setSelectedItems([]);
    setSelectedTab(0);
    onClose();
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="lg"
      fullWidth
      PaperProps={{
        sx: {
          height: "90vh",
          maxHeight: "calc(100% - 64px)",
          display: "flex",
          flexDirection: "column",
        },
      }}
    >
      {/* Dialog Header */}
      <DialogTitle>
        <Box sx={{ display: "flex", alignItems: "center", justifyContent: "space-between" }}>
          <Typography variant="h6">Thư viện media</Typography>
          <IconButton onClick={handleClose} size="small">
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>

      {/* Tabs */}
      <Paper elevation={0} sx={{ borderBottom: 1, borderColor: "divider" }}>
        <Tabs value={selectedTab} onChange={handleTabChange} sx={{ px: 3 }}>
          <Tab label="Hình ảnh" />
          {/* <Tab label="Video" /> */}
        </Tabs>
      </Paper>

      {/* Dialog Content */}
      <DialogContent sx={{ p: 0, display: "flex", flexDirection: "column", overflow: "hidden" }}>
        <MediaLibraryContent
          type={selectedTab === 0 ? "image" : "video"}
          selectedItems={selectedItems}
          onSelect={handleSelect}
          maxSelect={maxSelect}
        />
      </DialogContent>

      {/* Dialog Actions */}
      <DialogActions
        sx={{
          p: 3,
          borderTop: 1,
          borderColor: "divider",
          bgcolor: "background.paper",
        }}
      >
        <Button onClick={handleClose} variant="outlined" color="inherit">
          Hủy
        </Button>
        <Button onClick={handleSave} variant="contained" disabled={selectedItems.length === 0}>
          Chọn ({selectedItems.length})
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default DialogAddMediaLibrary;
