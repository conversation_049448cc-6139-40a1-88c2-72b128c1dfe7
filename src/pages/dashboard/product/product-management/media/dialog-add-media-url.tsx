import React, { useEffect, useRef, useState } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  IconButton,
  TextField,
  Typography,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import { useTranslation } from "react-i18next";
import { tokens } from "@/src/locales/tokens";
import { FileType, MediaFile } from "@/src/constants/file-types";
import * as Yup from "yup";
import { mediaService } from "@/src/api/services/media/media.service";

interface DialogAddMediaUrlProps {
  open: boolean;
  onClose: () => void;
  onSave: (listMedia: File[]) => void;
  defaultGroupId?: string;
  maxSize?: number;
  acceptFile?: Record<string, string[]>;
}

const validationSchema = Yup.object({
  url: Yup.string().url("URL không hợp lệ").required("Vui lòng nhập URL"),
});

const DialogAddMediaUrl: React.FC<DialogAddMediaUrlProps> = ({
  open,
  onClose,
  onSave,
  defaultGroupId,
  maxSize,
  acceptFile,
}) => {
  const { t } = useTranslation();
  const [url, setUrl] = useState("");
  const [error, setError] = useState("");
  const [isPreviewLoaded, setIsPreviewLoaded] = useState(false);
  const [type, setType] = useState<FileType>(FileType.IMAGE);
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (open && inputRef.current) {
      inputRef.current.focus();
    }
    if (open) {
      setError("");
      setIsPreviewLoaded(false);
      setUrl("");
    }
  }, [open]);

  // async function fetchImageAsFile(url: string, filename = "image.jpg"): Promise<File> {
  //   const response = await fetch(url);
  //   if (!response.ok) throw new Error("Không thể tải hình ảnh từ URL");
  //   const blob = await response.blob();
  //   const ext = url.split(".").pop()?.split(/\#|\?/)[0] || "jpg";
  //   return new File([blob], `${filename}.${ext}`, { type: blob.type });
  // }
  async function fetchImageAsFile(url: string): Promise<File> {
    const response = await fetch(url);
    if (!response.ok) throw new Error("Không thể tải hình ảnh từ URL");

    const blob = await response.blob();

    const now = new Date();
    const pad = (n: number) => n.toString().padStart(2, "0");

    const timestamp = `${now.getFullYear()}${pad(now.getMonth() + 1)}${pad(now.getDate())}_${pad(
      now.getHours()
    )}${pad(now.getMinutes())}${pad(now.getSeconds())}`;

    const mimeToExt: Record<string, string> = {
      "image/jpeg": "jpg",
      "image/png": "png",

      // "image/webp": "webp",
      // "image/gif": "gif",
      // "image/bmp": "bmp",
      // "image/svg+xml": "svg",
    };

    const ext = mimeToExt[blob.type] || "jpg";

    const filename = `image_${timestamp}.${ext}`;

    return new File([blob], filename, { type: blob.type });
  }

  const handleSave = async () => {
    try {
      await validationSchema.validate({ url });

      // if (acceptFile) {
      //   const urlExt = url.split(".").pop()?.split(/\#|\?/)[0]?.toLowerCase();

      //   const allowedExts = Object.values(acceptFile)
      //     .flat()
      //     .map((ext) => ext.replace(".", "").toLowerCase());

      //   if (!urlExt || !allowedExts.includes(urlExt)) {
      //     setError("Định dạng file không hợp lệ!");
      //     return;
      //   }
      // }

      const file = await fetchImageAsFile(url);
      if (maxSize && file.size > maxSize) {
        setError(`File vượt quá kích thước tối đa ${(maxSize / 1024 / 1024).toFixed(1)}MB`);
        return;
      }
      onSave([file]);
      // const processedFile = file;
      // const response = await mediaService.uploadFile({
      //   groupId: defaultGroupId,
      //   file: processedFile,
      // });
      // console.log({ response });
      // const uploadedUrl = response?.data?.link;
      // if (!uploadedUrl) throw new Error("Upload thất bại");

      // const media: MediaFile = {
      //   type,
      //   link: uploadedUrl,
      // };
      // onSave(media);

      setUrl("");
      setError("");
      onClose();
    } catch (err) {
      setError(err.message || "Có lỗi xảy ra");
    }
  };

  const handlePreviewLoad = () => {
    setIsPreviewLoaded(true);
  };

  const handlePreviewError = () => {
    setError("Không thể tải hình ảnh. Vui lòng kiểm tra lại URL");
    setIsPreviewLoaded(false);
  };

  const handleClose = () => {
    setUrl("");
    setType(FileType.IMAGE);
    setError("");
    setIsPreviewLoaded(false);
    onClose();
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
      <DialogTitle sx={{ pb: 1 }}>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            fontSize: 18,
            fontWeight: 600,
          }}
        >
          {t(tokens.contentManagement.product.create.media.addFromUrl)}
          <IconButton onClick={onClose}>
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent>
        <Box sx={{ mt: 0 }}>
          <Typography sx={{ display: "flex", fontSize: 16, fontWeight: 500 }}>
            URL hình ảnh <Typography sx={{ color: "red", ml: 0.5 }}>*</Typography>
          </Typography>
          <TextField
            fullWidth
            label=""
            inputRef={inputRef}
            value={url}
            onChange={(e) => {
              setUrl(e.target.value);
              setError("");
              setIsPreviewLoaded(false);
            }}
            sx={{ mt: 0.5 }}
            error={!!error}
            helperText={
              error
                ? "Không thể tải hình ảnh từ URL này. Có thể ảnh đã bị xóa, link sai, hoặc server không cho phép truy cập từ bên ngoài (CORS)."
                : ""
            }
          />

          {url && (
            <img
              src={url}
              alt="Preview"
              style={{ display: "none" }}
              onLoad={() => {
                setIsPreviewLoaded(true);
                setError("");
              }}
              onError={() => {
                setError(
                  "Không thể tải hình ảnh từ URL này. Có thể ảnh đã bị xóa, link sai, hoặc server không cho phép truy cập từ bên ngoài (CORS)."
                );
                setIsPreviewLoaded(false);
              }}
            />
          )}
          {url && (
            <Box sx={{ mt: 3, position: "relative" }}>
              <Typography sx={{ fontSize: 16, fontWeight: 500 }}>Xem trước:</Typography>
              <Box
                sx={{
                  width: "100%",
                  height: 200,
                  border: "1px solid",
                  borderColor: "divider",
                  borderRadius: 1,
                  overflow: "hidden",
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  bgcolor: "background.paper",
                }}
              >
                <img
                  src={url}
                  alt="Preview"
                  style={{
                    maxWidth: "100%",
                    maxHeight: "100%",
                    objectFit: "contain",
                    opacity: isPreviewLoaded && !error ? 1 : 0,
                    transition: "opacity 0.2s",
                  }}
                />
              </Box>
            </Box>
          )}
        </Box>
      </DialogContent>

      <DialogActions sx={{ pr: 3 }}>
        <Button variant="outlined" onClick={handleClose}>
          Hủy
        </Button>
        <Button variant="contained" onClick={handleSave} disabled={!url}>
          Thêm
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default DialogAddMediaUrl;

// import React, { useState } from "react";
// import {
//   Dialog,
//   DialogTitle,
//   DialogContent,
//   DialogActions,
//   Button,
//   Box,
//   IconButton,
//   TextField,
//   Typography,
// } from "@mui/material";
// import CloseIcon from "@mui/icons-material/Close";
// import { useTranslation } from "react-i18next";
// import { tokens } from "@/src/locales/tokens";
// import { FileType, MediaFile } from "@/src/constants/file-types";
// import * as Yup from "yup";
// import { mediaService } from "@/src/api/services/media/media.service";

// interface DialogAddMediaUrlProps {
//   open: boolean;
//   onClose: () => void;
//   onSave: (media: MediaFile) => void;
//   defaultGroupId?: string;
// }

// const validationSchema = Yup.object({
//   url: Yup.string().url("URL không hợp lệ").required("Vui lòng nhập URL"),
// });

// const DialogAddMediaUrl: React.FC<DialogAddMediaUrlProps> = ({
//   open,
//   onClose,
//   onSave,
//   defaultGroupId,
// }) => {
//   const { t } = useTranslation();
//   const [url, setUrl] = useState("");
//   const [error, setError] = useState("");
//   const [isPreviewLoaded, setIsPreviewLoaded] = useState(false);
//   const [type, setType] = useState<FileType>(FileType.IMAGE);

//   async function fetchImageAsFile(url: string, filename = "image.jpg"): Promise<File> {
//     const response = await fetch(url);
//     if (!response.ok) throw new Error("Không thể tải hình ảnh từ URL");
//     const blob = await response.blob();
//     const ext = url.split(".").pop()?.split(/\#|\?/)[0] || "jpg";
//     return new File([blob], `${filename}.${ext}`, { type: blob.type });
//   }

//   const handleSave = async () => {
//     try {
//       await validationSchema.validate({ url });
//       const file = await fetchImageAsFile(url);
//       const processedFile = file;
//       const response = await mediaService.uploadFile({
//         groupId: defaultGroupId,
//         file: processedFile,
//       });
//       console.log({ response });
//       const uploadedUrl = response?.data?.link;
//       if (!uploadedUrl) throw new Error("Upload thất bại");

//       const media: MediaFile = {
//         type,
//         link: uploadedUrl,
//       };
//       onSave(media);

//       setUrl("");
//       setError("");
//       onClose();
//     } catch (err) {
//       setError(err.message || "Có lỗi xảy ra");
//     }
//   };

//   const handlePreviewLoad = () => {
//     setIsPreviewLoaded(true);
//   };

//   const handlePreviewError = () => {
//     setError("Không thể tải hình ảnh. Vui lòng kiểm tra lại URL");
//     setIsPreviewLoaded(false);
//   };

//   const handleClose = () => {
//     setUrl("");
//     setType(FileType.IMAGE);
//     onClose();
//   };

//   return (
//     <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
//       <DialogTitle sx={{ pb: 1 }}>
//         <Box
//           sx={{
//             display: "flex",
//             justifyContent: "space-between",
//             alignItems: "center",
//             fontSize: 18,
//             fontWeight: 600,
//           }}
//         >
//           {t(tokens.contentManagement.product.create.media.addFromUrl)}
//           <IconButton onClick={onClose}>
//             <CloseIcon />
//           </IconButton>
//         </Box>
//       </DialogTitle>

//       <DialogContent>
//         <Box sx={{ mt: 0 }}>
//           <Typography sx={{ display: "flex", fontSize: 16, fontWeight: 500 }}>
//             URL hình ảnh <Typography sx={{ color: "red", ml: 0.5 }}>*</Typography>
//           </Typography>
//           <TextField
//             fullWidth
//             label=""
//             value={url}
//             onChange={(e) => {
//               setUrl(e.target.value);
//               setError("");
//               setIsPreviewLoaded(false);
//             }}
//             sx={{ mt: 0.5 }}
//             error={!!error}
//             helperText={error}
//           />

//           {url && (
//             <Box sx={{ mt: 3, position: "relative" }}>
//               <Typography sx={{ fontSize: 16, fontWeight: 500 }}>Xem trước:</Typography>
//               <Box
//                 sx={{
//                   width: "100%",
//                   height: 200,
//                   border: "1px solid",
//                   borderColor: "divider",
//                   borderRadius: 1,
//                   overflow: "hidden",
//                   display: "flex",
//                   justifyContent: "center",
//                   alignItems: "center",
//                   bgcolor: "background.paper",
//                 }}
//               >
//                 <img
//                   src={url}
//                   alt="Preview"
//                   onLoad={handlePreviewLoad}
//                   onError={handlePreviewError}
//                   style={{
//                     maxWidth: "100%",
//                     maxHeight: "100%",
//                     objectFit: "contain",
//                     opacity: isPreviewLoaded ? 1 : 0,
//                   }}
//                 />
//               </Box>
//             </Box>
//           )}
//         </Box>
//       </DialogContent>

//       <DialogActions sx={{ pr: 3 }}>
//         <Button variant="outlined" onClick={handleClose}>
//           Hủy
//         </Button>
//         <Button variant="contained" onClick={handleSave} disabled={!url || !isPreviewLoaded}>
//           Thêm
//         </Button>
//       </DialogActions>
//     </Dialog>
//   );
// };

// export default DialogAddMediaUrl;
