import React, { useCallback, useMemo, memo, useState, useEffect, useRef } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  TextField,
  Alert,
  Box,
  Typography,
  InputAdornment,
  Button,
  Stack,
  Tooltip,
  IconButton,
} from "@mui/material";
import { AddCircle as AddCircleIcon, ContentCopy as ContentCopyIcon } from "@mui/icons-material";
import { useTranslation } from "react-i18next";
import { tokens } from "@/src/locales/tokens";
import { ProductVariant, ValidationErrors } from "../../../../../../types/product/variant";
import { formatCurrency, parseCurrency } from "@/src/utils/format-number";
import { FixedSizeList as List } from "react-window";
import AutoSizer from "react-virtualized-auto-sizer";
import debounce from "lodash/debounce";
import DialogAddMediaLibrary from "../../media/dialog-add-media-library";
import { MediaFile } from "@/src/api/types/media.types";
import { FileType } from "@/src/constants/file-types";
import { enqueueSnackbar } from "notistack";
import { ExistingMediaFile } from "../../create/create";

interface VariantsTableProps {
  variants: ProductVariant[];
  onVariantChange: (index: number, field: keyof ProductVariant | "all", value: any) => void;
  errors: ValidationErrors[];
  existingFiles: ExistingMediaFile[];
  setExistingFiles: React.Dispatch<React.SetStateAction<ExistingMediaFile[]>>;
}

// Định nghĩa các kích thước cố định cho cells
const CELL_WIDTHS = {
  image: "100px",
  specification: "180px",
  price: "170px",
  quantity: "170px",
  actions: "30px",
} as const;

const HEADER_HEIGHT = "56px"; // Chiều cao cố định cho header

interface PriceCellProps {
  index: number;
  field: "priceCapital" | "price" | "priceReal";
  value: number;
  label: string;
  tooltip: string;
  onChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  error?: string;
  onCopy?: () => void;
}

const PriceCell: React.FC<PriceCellProps> = ({
  index,
  field,
  value,
  label,
  tooltip,
  onChange,
  error,
}) => {
  const [inputValue, setInputValue] = useState(formatCurrency(value));
  const inputRef = useRef<HTMLInputElement>(null);
  const cursorPosition = useRef<number | null>(null);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    // Lưu vị trí con trỏ
    if (inputRef.current) {
      cursorPosition.current = inputRef.current.selectionStart;
    }
    setInputValue(newValue);
    onChange(e);
  };

  useEffect(() => {
    setInputValue(formatCurrency(value));
    // Khôi phục vị trí con trỏ sau khi update value
    if (inputRef.current && cursorPosition.current !== null) {
      inputRef.current.setSelectionRange(cursorPosition.current, cursorPosition.current);
    }
  }, [value]);

  return (
    <TableCell sx={{ width: CELL_WIDTHS[field], minWidth: CELL_WIDTHS[field] }}>
      <Stack direction="row" alignItems="center" spacing={1}>
        <TextField
          inputRef={inputRef}
          fullWidth
          size="small"
          label={label}
          value={inputValue}
          onChange={handleChange}
          error={!!error}
          FormHelperTextProps={{
            sx: {
              m: 0,
              position: "absolute",
              bottom: "-20px",
              whiteSpace: "nowrap",
              overflow: "hidden",
              textOverflow: "ellipsis",
              maxWidth: "160px", // Giới hạn chiều rộng của text lỗi
            },
          }}
          helperText={error}
          InputProps={{
            startAdornment: <InputAdornment position="start">₫</InputAdornment>,
            sx: {
              "& input": {
                textAlign: "right",
              },
              // Thêm style khi có lỗi
              ...(error && {
                backgroundColor: "error.lighter",
                "&:hover": {
                  backgroundColor: "error.light",
                },
              }),
            },
          }}
        />
      </Stack>
    </TableCell>
  );
};

interface QuantityCellProps {
  index: number;
  field: "quantity" | "quantityPurchase";
  value: number;
  label: string;
  tooltip: string;
  onChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  error?: string;
  onCopy?: () => void;
}

const QuantityCell: React.FC<QuantityCellProps> = ({
  index,
  field,
  value,
  label,
  tooltip,
  onChange,
  error,
  onCopy,
}) => {
  const [localValue, setLocalValue] = useState(value);
  const inputRef = useRef<HTMLInputElement>(null);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setLocalValue(Number(newValue));
    onChange(e);
  };

  useEffect(() => {
    setLocalValue(value);
  }, [value]);

  return (
    <TableCell sx={{ width: CELL_WIDTHS.quantity, minWidth: CELL_WIDTHS.quantity }}>
      <Tooltip title={error || tooltip} arrow placement="top">
        <TextField
          inputRef={inputRef}
          fullWidth
          size="small"
          type="number"
          label={label}
          value={localValue}
          onChange={handleChange}
          error={!!error}
          FormHelperTextProps={{
            sx: {
              m: 0,
              position: "absolute",
              bottom: "-20px",
              whiteSpace: "nowrap",
              overflow: "hidden",
              textOverflow: "ellipsis",
              maxWidth: "160px",
            },
          }}
          helperText={error}
          inputProps={{
            min: 0,
            sx: {
              textAlign: "right",
            },
          }}
          InputProps={{
            sx: {
              ...(error && {
                backgroundColor: "error.lighter",
                "&:hover": {
                  backgroundColor: "error.light",
                },
              }),
            },
          }}
        />
      </Tooltip>
    </TableCell>
  );
};

// Định nghĩa interface cho data được truyền vào Row
interface RowData {
  variants: ProductVariant[];
  renderSpecifications: (variant: ProductVariant) => React.ReactNode;
  renderPriceCell: (
    index: number,
    variant: ProductVariant,
    field: "priceCapital" | "price" | "priceReal"
  ) => React.ReactNode;
  renderQuantityCell: (
    index: number,
    variant: ProductVariant,
    field: "quantity" | "quantityPurchase"
  ) => React.ReactNode;
  onVariantChange: (index: number, field: keyof ProductVariant | "all", value: any) => void;
  handleImageClick?: (index: number) => void;
  onImageSelect?: (index: number, image: MediaFile) => void;
  handleCopyRow: (sourceIndex: number) => void;
  existingFiles: ExistingMediaFile[];
  setExistingFiles: React.Dispatch<React.SetStateAction<ExistingMediaFile[]>>;
}

interface RowProps {
  index: number;
  style: React.CSSProperties;
  data: RowData;
}

const Row = memo(
  ({ index, style, data }: RowProps) => {
    const {
      variants,
      renderSpecifications,
      renderPriceCell,
      renderQuantityCell,
      onVariantChange,
      handleCopyRow,
      existingFiles,
      setExistingFiles,
    } = data;
    const variant = variants[index];
    const [openLibraryDialog, setOpenLibraryDialog] = useState(false);

    const handleImageSelect = (mediaFiles: MediaFile[]) => {
      if (mediaFiles.length > 0) {
        const selectedImage = mediaFiles[0];

        // Cập nhật variantImage với dữ liệu mới
        onVariantChange(index, "variantImage", {
          type: FileType.IMAGE,
          link: selectedImage?.link,
          mediaFileId: selectedImage?.mediaFileId,
        });
      }
      setOpenLibraryDialog(false);
    };

    return (
      <TableRow
        key={variant.itemsId}
        style={{
          ...style,
          height: "80px",
        }}
        sx={{
          "&:hover": {
            backgroundColor: "action.hover",
          },
          "& > td": {
            borderBottom: "1px solid",
            borderColor: "divider",
            p: 1,
            verticalAlign: "middle",
            textAlign: "start",
          },
        }}
      >
        <TableCell sx={{ width: CELL_WIDTHS.image, minWidth: CELL_WIDTHS.image }}>
          {variant.variantImage?.link ? (
            <Box
              onClick={() => setOpenLibraryDialog(true)}
              sx={{
                position: "relative",
                width: 60,
                height: 60,
                borderRadius: 1,
                overflow: "hidden",
                cursor: "pointer",
                "&:hover": {
                  "& .change-image": {
                    opacity: 1,
                  },
                },
              }}
            >
              <Box
                component="img"
                src={variant.variantImage.link || "/assets/images/default-product.png"}
                onError={(e: React.SyntheticEvent<HTMLImageElement>) => {
                  e.currentTarget.src = "/assets/image_empty.png";
                  e.currentTarget.classList.add("error-image");
                }}
                sx={{
                  width: "100%",
                  height: "100%",
                  objectFit: "cover",
                  "&.error-image": {
                    objectFit: "contain",
                    padding: 1,
                    bgcolor: "background.neutral",
                  },
                }}
              />
              <Box
                className="change-image"
                sx={{
                  position: "absolute",
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  bgcolor: "rgba(0,0,0,0.5)",
                  opacity: 0,
                  transition: "opacity 0.2s",
                  borderRadius: 1,
                }}
              >
                <Typography
                  variant="caption"
                  sx={{
                    color: "white",
                    fontWeight: 500,
                    px: 1,
                    py: 0.5,
                    borderRadius: 0.5,
                    bgcolor: "rgba(0,0,0,0.25)",
                    backdropFilter: "blur(2px)",
                  }}
                >
                  Thay đổi
                </Typography>
              </Box>
            </Box>
          ) : (
            <Button
              onClick={() => setOpenLibraryDialog(true)}
              variant="outlined"
              sx={{
                borderStyle: "dashed",
                height: 60,
                width: 60,
                minWidth: 60,
                p: 0,
                borderRadius: 1,
                "&:hover": {
                  borderStyle: "dashed",
                  bgcolor: "primary.lighter",
                },
              }}
            >
              <AddCircleIcon />
            </Button>
          )}

          <DialogAddMediaLibrary
            open={openLibraryDialog}
            onClose={() => setOpenLibraryDialog(false)}
            onSave={() => {}}
            maxSelect={1}
            existingFiles={existingFiles}
            setExistingFiles={setExistingFiles}
            onlyLib={true}
            handleImageSelect={handleImageSelect}
          />
        </TableCell>

        <TableCell sx={{ width: CELL_WIDTHS.specification, minWidth: CELL_WIDTHS.specification }}>
          {renderSpecifications(variant)}
        </TableCell>

        {renderPriceCell(index, variant, "priceCapital")}
        {renderPriceCell(index, variant, "priceReal")}
        {renderPriceCell(index, variant, "price")}

        {renderQuantityCell(index, variant, "quantity")}
        {renderQuantityCell(index, variant, "quantityPurchase")}

        <TableCell
          sx={{
            width: CELL_WIDTHS.actions,
            minWidth: CELL_WIDTHS.actions,
            textAlign: "center",
          }}
        >
          <Tooltip title="Sao chép giá trị của dòng này cho tất cả biến thể">
            <IconButton
              size="small"
              onClick={() => handleCopyRow(index)}
              sx={{
                "&:hover": {
                  color: "primary.main",
                },
              }}
            >
              <ContentCopyIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        </TableCell>
      </TableRow>
    );
  },
  (prevProps, nextProps) => {
    return (
      prevProps.index === nextProps.index &&
      prevProps.data.variants[prevProps.index] === nextProps.data.variants[nextProps.index]
    );
  }
);

const VariantsTable = memo<VariantsTableProps>(
  ({ variants, onVariantChange, errors, existingFiles, setExistingFiles }) => {
    const { t } = useTranslation();

    const renderSpecifications = useCallback((variant: ProductVariant) => {
      const specs = [
        variant.variantNameOne && {
          name: variant.variantNameOne,
          value: variant.variantValueOne,
        },
        variant.variantNameTwo && {
          name: variant.variantNameTwo,
          value: variant.variantValueTwo,
        },
        variant.variantNameThree && {
          name: variant.variantNameThree,
          value: variant.variantValueThree,
        },
      ].filter(Boolean);

      return (
        <Stack spacing={0.5}>
          {specs.map((spec, index) => (
            <Typography key={index} variant="body2" sx={{ lineHeight: 1.2 }}>
              {`${spec.name}: ${spec.value}`}
            </Typography>
          ))}
        </Stack>
      );
    }, []);

    const handlePriceChange = useCallback(
      (index: number, field: "priceCapital" | "price" | "priceReal") => {
        const debouncedUpdate = useMemo(
          () =>
            debounce((event: React.ChangeEvent<HTMLInputElement>) => {
              const newValue = parseCurrency(event.target.value);
              if (!isNaN(newValue)) {
                onVariantChange(index, field, newValue);
              }
            }, 1000),
          [index, field]
        );

        return (event: React.ChangeEvent<HTMLInputElement>) => {
          debouncedUpdate(event);
        };
      },
      [onVariantChange]
    );

    const handleCopyValue = (sourceIndex: number, field: keyof ProductVariant) => {
      const sourceValue = variants[sourceIndex][field];

      variants.forEach((variant, index) => {
        if (index !== sourceIndex) {
          onVariantChange(index, field, sourceValue);
        }
      });
    };

    const renderPriceCell = useCallback(
      (index: number, variant: ProductVariant, field: "priceCapital" | "price" | "priceReal") => {
        const labels = {
          priceCapital: t(tokens.contentManagement.product.variant.table.costPriceInput),
          priceReal: t(tokens.contentManagement.product.variant.table.listPriceInput),
          price: t(tokens.contentManagement.product.variant.table.salePriceInput),
        };

        const tooltips = {
          priceCapital: t(tokens.contentManagement.product.variant.helperText.costPrice),
          priceReal: t(tokens.contentManagement.product.variant.helperText.listPrice),
          price: t(tokens.contentManagement.product.variant.helperText.salePrice),
        };

        const error = errors[index]?.[field];

        return (
          <PriceCell
            index={index}
            field={field}
            value={variant[field] || 0}
            label={labels[field]}
            tooltip={tooltips[field]}
            onChange={handlePriceChange(index, field)}
            error={error}
            onCopy={() => handleCopyValue(index, field)}
          />
        );
      },
      [variants, errors, handlePriceChange, t]
    );

    const handleQuantityChange = useCallback(
      (index: number, field: "quantity" | "quantityPurchase") => {
        const debouncedUpdate = useMemo(
          () =>
            debounce((event: React.ChangeEvent<HTMLInputElement>) => {
              const newValue = parseInt(event.target.value, 10);
              if (!isNaN(newValue)) {
                const variant = variants[index];
                const newVariant = { ...variant };

                // Clear previous errors
                if (newVariant.errors) {
                  delete newVariant.errors[field];
                }

                // Validate quantity
                if (newValue <= 0) {
                  newVariant.errors = {
                    ...newVariant.errors,
                    [field]: t(
                      tokens.contentManagement.product.variant.validation[`${field}GreaterThanZero`]
                    ),
                  };
                }

                newVariant[field] = newValue;
                onVariantChange(index, "all", newVariant);
              }
            }, 1000),
          [index, field]
        );

        return (event: React.ChangeEvent<HTMLInputElement>) => {
          debouncedUpdate(event);
        };
      },
      [variants, onVariantChange, t]
    );

    const getQuantityError = (
      index: number,
      field: "quantity" | "quantityPurchase",
      value: number
    ) => {
      if (value < 0) {
        return t(tokens.contentManagement.product.variant.validation[`${field}Min`]);
      }
      // if (value === 0) {
      //   return t(tokens.contentManagement.product.variant.validation[`${field}GreaterThanZero`]);
      // }
      return "";
    };

    const renderQuantityCell = useCallback(
      (index: number, variant: ProductVariant, field: "quantity" | "quantityPurchase") => {
        const labels = {
          quantity: t(tokens.contentManagement.product.variant.table.stockInput),
          quantityPurchase: t(tokens.contentManagement.product.variant.table.maxQuantityInput),
        };

        const tooltips = {
          // quantity: t(tokens.contentManagement.product.variant.helperText.stock)
        };

        const error = getQuantityError(index, field, variant[field] || 0);

        return (
          <QuantityCell
            index={index}
            field={field}
            value={variant[field] || 0}
            label={labels[field]}
            tooltip={tooltips[field]}
            onChange={handleQuantityChange(index, field)}
            error={error}
            onCopy={() => handleCopyValue(index, field)}
          />
        );
      },
      [variants, errors, handleQuantityChange, t]
    );

    const handleCopyRow = useCallback(
      (sourceIndex: number) => {
        const sourceVariant = variants[sourceIndex];
        const fieldsToCopy = [
          "priceCapital",
          "price",
          "priceReal",
          "quantity",
          "quantityPurchase",
        ] as const;

        variants.forEach((_, targetIndex) => {
          if (targetIndex !== sourceIndex) {
            fieldsToCopy.forEach((field) => {
              const valueToUpdate = sourceVariant[field];
              console.log(
                `Copying ${field} from row ${sourceIndex} to row ${targetIndex}: ${valueToUpdate}`
              );

              onVariantChange(targetIndex, field, valueToUpdate);
            });
          }
        });

        enqueueSnackbar("Đã sao chép giá trị thành công", {
          variant: "success",
          autoHideDuration: 2000,
        });
      },
      [variants, onVariantChange, enqueueSnackbar]
    );

    const rowData: RowData = useMemo(
      () => ({
        variants,
        renderSpecifications,
        renderPriceCell,
        renderQuantityCell,
        onVariantChange,
        handleCopyRow,
        existingFiles,
        setExistingFiles,
      }),
      [
        variants,
        renderSpecifications,
        renderPriceCell,
        renderQuantityCell,
        onVariantChange,
        existingFiles,
        setExistingFiles,
        handleCopyRow,
      ]
    );

    if (variants.length === 0) return null;

    const TableHeader = () => (
      <TableHead>
        <TableRow
          sx={{
            height: HEADER_HEIGHT,
            "& th": {
              bgcolor: "background.neutral",
              borderBottom: "1px solid",
              borderColor: "divider",
              p: 1,
              fontWeight: "bold",
              textTransform: "uppercase",
              fontSize: "0.875rem",
              color: "text.primary",
              verticalAlign: "middle",
              textAlign: "center",
              whiteSpace: "nowrap", // Ngăn text xuống dòng
            },
          }}
        >
          <TableCell
            sx={{
              width: CELL_WIDTHS.image,
              minWidth: CELL_WIDTHS.image,
            }}
          >
            {t(tokens.contentManagement.product.variant.table.image)}
          </TableCell>

          <TableCell
            sx={{
              width: CELL_WIDTHS.specification,
              minWidth: CELL_WIDTHS.specification,
              textAlign: "left !important", // Override center alignment
            }}
          >
            {t(tokens.contentManagement.product.variant.table.specification)}
          </TableCell>

          <TableCell
            sx={{
              width: CELL_WIDTHS.price,
              minWidth: CELL_WIDTHS.price,
              textAlign: "left !important",
            }}
          >
            {t(tokens.contentManagement.product.variant.table.costPrice)}
          </TableCell>

          <TableCell
            sx={{
              width: CELL_WIDTHS.price,
              minWidth: CELL_WIDTHS.price,
              textAlign: "left !important",
            }}
          >
            {t(tokens.contentManagement.product.variant.table.listPrice)}
          </TableCell>

          <TableCell
            sx={{
              width: CELL_WIDTHS.price,
              minWidth: CELL_WIDTHS.price,
              textAlign: "left !important",
            }}
          >
            {t(tokens.contentManagement.product.variant.table.salePrice)}
          </TableCell>

          <TableCell
            sx={{
              width: CELL_WIDTHS.quantity,
              minWidth: CELL_WIDTHS.quantity,
              textAlign: "left !important",
            }}
          >
            {t(tokens.contentManagement.product.variant.table.stock)}
          </TableCell>

          <TableCell
            sx={{
              width: CELL_WIDTHS.quantity,
              minWidth: CELL_WIDTHS.quantity,
              textAlign: "left !important",
            }}
          >
            {t(tokens.contentManagement.product.variant.table.maxQuantity)}
          </TableCell>
        </TableRow>
      </TableHead>
    );

    const VirtualizedTableBody = () => (
      <TableBody>
        <tr>
          <td colSpan={7} style={{ padding: 0 }}>
            <div style={{ height: 400 }}>
              <AutoSizer>
                {({ height, width }) => (
                  <List
                    height={height}
                    itemCount={variants.length}
                    itemSize={80}
                    width={width}
                    itemData={rowData}
                  >
                    {Row}
                  </List>
                )}
              </AutoSizer>
            </div>
          </td>
        </tr>
      </TableBody>
    );

    return (
      <Box sx={{ mt: 2 }}>
        <Alert severity="info" sx={{ mb: 2 }}>
          {t(tokens.contentManagement.product.variant.dialog.variantsCreated, {
            count: variants.length,
          })}
        </Alert>

        <TableContainer
          component={Paper}
          sx={{
            border: "1px solid",
            borderColor: "divider",
            maxHeight: 400,
            "& .MuiTableRow-root": {
              // Tăng chiều cao của row để chứa error message
              minHeight: "80px",
              "& td": {
                position: "relative", // Để định vị error message
                py: 2, // Padding top/bottom lớn hơn
              },
            },
            "& .MuiTable-root": {
              borderCollapse: "separate",
              borderSpacing: "0",
            },
            // Thêm shadow cho header khi scroll
            "& .MuiTableHead-root": {
              position: "sticky",
              top: 0,
              zIndex: 2,
              "& .MuiTableRow-root": {
                boxShadow: "0 2px 4px rgba(0,0,0,0.08)",
              },
            },
          }}
        >
          <Table size="small" stickyHeader>
            <TableHeader />
            <VirtualizedTableBody />
          </Table>
        </TableContainer>
      </Box>
    );
  }
);

export default VariantsTable;
