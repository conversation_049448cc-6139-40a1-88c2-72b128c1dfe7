import React, { useState, useEffect, useRef, useCallback } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Stack,
  Typography,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import { tokens } from "@/src/locales/tokens";
import * as Yup from "yup";
import { ProductVariant, Specification } from "../../../../../types/product/variant";
import SpecificationForm from "./components/specification-form";
import VariantTable2 from "./components/variant-table2";
import { FileType } from "@/src/constants/file-types";
import { convertVariantsToSpecifications } from "@/src/utils/variant-utils";

interface DialogAddVariantProps {
  open: boolean;
  onClose: () => void;
  onSave: (variants: ProductVariant[]) => void;
  initialVariants?: ProductVariant[];
}

interface ValidationErrors {
  [key: string]: string;
}

const DialogAddVariant: React.FC<DialogAddVariantProps> = ({
  open,
  onClose,
  onSave,
  initialVariants,
}) => {
  const { t } = useTranslation();

  const [variants, setVariants] = useState<ProductVariant[]>([]);
  const [specifications, setSpecifications] = useState<Specification[]>([{ name: "", values: [] }]);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [errorsVariant, setErrorsVariant] = useState({});
  const variantIdMap = useRef(new Map<string, string>());
  const [touchedSpecs, setTouchedSpecs] = useState<Record<number, boolean>>({});
  const [localValues, setLocalValues] = useState<Record<number, string>>({});

  useEffect(() => {
    if (open && initialVariants?.length > 0) {
      variantIdMap.current = new Map(
        initialVariants.map((variant) => [
          `${variant.variantNameOne}:${variant.variantValueOne}|${variant.variantNameTwo}:${variant.variantValueTwo}|${variant.variantNameThree}:${variant.variantValueThree}`,
          variant.itemsId,
        ])
      );

      const specs = convertVariantsToSpecifications(initialVariants);
      setSpecifications(specs.length > 0 ? specs : [{ name: "", values: [] }]);
      setVariants(initialVariants);
    } else {
      setSpecifications([{ name: "", values: [] }]);
      setVariants([]);
      variantIdMap.current.clear();
      setErrors({});
      setErrorsVariant({});
      setLocalValues({});
    }
  }, [open, initialVariants]);

  const validateSpecName = (index: number, value: string) => {
    if (!touchedSpecs[index]) {
      return true;
    }

    if (value.trim() === "") {
      setErrors((prev) => ({ ...prev, [`spec${index}`]: "Tên không được để trống" }));
      return false;
    } else {
      setErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[`spec${index}`];
        return newErrors;
      });
      return true;
    }
  };

  const validateSpecValue = (index: number, value: string, specs: Specification[]) => {
    if (value.trim() === "" && (!specs[index].values || specs[index].values.length === 0)) {
      setErrors((prev) => ({ ...prev, [`specValue${index}`]: "Giá trị không được để trống" }));
      return false;
    } else {
      setErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[`specValue${index}`];
        return newErrors;
      });
      return true;
    }
  };

  const handleSpecNameChange = (index: number, value: string) => {
    const newSpecs = [...specifications];
    newSpecs[index] = { ...newSpecs[index], name: value };
    setSpecifications(newSpecs);

    validateSpecName(index, value);
  };

  const handleValueChange = (index: number, value: string) => {
    setLocalValues((prev) => ({
      ...prev,
      [index]: value,
    }));
    validateSpecValue(index, value, specifications);
  };

  const handleValueBlur = (index: number, value: string) => {
    validateSpecValue(index, value, specifications);
  };

  const handleAddValue = (index: number, value: string) => {
    const newSpecs = [...specifications];
    if (!newSpecs[index].values.includes(value)) {
      newSpecs[index].values.push(value);
      setSpecifications(newSpecs);
      setLocalValues((prev) => ({ ...prev, [index]: "" }));
      validateSpecValue(index, "", newSpecs);
      generateVariants(newSpecs);
    }
  };

  const handleRemoveValue = (specIndex: number, valueIndex: number) => {
    const newSpecs = [...specifications];
    newSpecs[specIndex].values.splice(valueIndex, 1);
    setSpecifications(newSpecs);
    generateVariants(newSpecs);
  };

  const handleAddSpecification = () => {
    if (specifications.length < 3) {
      setSpecifications([...specifications, { name: "", values: [] }]);
    }
  };

  const handleRemoveSpecification = (index: number) => {
    const newSpecs = specifications.filter((_, i) => i !== index);
    setSpecifications(newSpecs);
    generateVariants(newSpecs);
  };
  const handleSpecFocus = (index: number) => {
    setTouchedSpecs((prev) => ({ ...prev, [index]: true }));
  };

  const hasErrors =
    Object.values(errors).some(Boolean) ||
    Object.values(errorsVariant).some((rowErr) => rowErr && Object.values(rowErr).some(Boolean));

  const cartesian = (arrays: string[][]): string[][] => {
    const results: string[][] = [];
    const max = arrays.length - 1;

    const helper = (arr: string[] = [], i: number = 0) => {
      for (let j = 0, l = arrays[i].length; j < l; j++) {
        const copy = [...arr, arrays[i][j]];
        if (i === max) {
          results.push(copy);
        } else {
          helper(copy, i + 1);
        }
      }
    };

    helper();
    return results;
  };

  const generateVariants = (specs: Specification[]) => {
    const validSpecs = specs.filter((spec) => spec.name && spec.values.length > 0);
    if (!validSpecs.length) {
      setVariants([]);
      return;
    }

    const specValues = validSpecs.map((spec) => spec.values);
    const combinations = cartesian(specValues);

    let currentVariants = [...variants];

    const existingVariantsMap = new Map();
    currentVariants.forEach((variant) => {
      const variantProps = [
        { name: variant.variantNameOne, value: variant.variantValueOne },
        { name: variant.variantNameTwo, value: variant.variantValueTwo },
        { name: variant.variantNameThree, value: variant.variantValueThree },
      ].filter((prop) => prop.name && prop.value);

      const normalizedKey = variantProps
        .sort((a, b) => a.name.localeCompare(b.name))
        .map((prop) => `${prop.name}:${prop.value}`)
        .join("|");

      existingVariantsMap.set(normalizedKey, variant);
    });

    const newVariants = combinations.map((combination) => {
      const variantProps = [];
      for (let i = 0; i < validSpecs.length; i++) {
        if (validSpecs[i]?.name && combination[i]) {
          variantProps.push({
            name: validSpecs[i].name,
            value: combination[i],
          });
        }
      }

      const normalizedKey = variantProps
        .sort((a, b) => a.name.localeCompare(b.name))
        .map((prop) => `${prop.name}:${prop.value}`)
        .join("|");

      const existingVariant = existingVariantsMap.get(normalizedKey);

      if (existingVariant) {
        return {
          ...existingVariant,
          variantNameOne: validSpecs[0]?.name || "",
          variantNameTwo: validSpecs[1]?.name || "",
          variantNameThree: validSpecs[2]?.name || "",
        };
      }

      return {
        itemsId: variantIdMap.current.get(normalizedKey) || "",
        variantImage: { type: FileType.IMAGE, link: "", mediaFileId: "" },
        variantNameOne: validSpecs[0]?.name || "",
        variantValueOne: combination[0] || "",
        variantNameTwo: validSpecs[1]?.name || "",
        variantValueTwo: combination[1] || "",
        variantNameThree: validSpecs[2]?.name || "",
        variantValueThree: combination[2] || "",
        priceCapital: 0,
        priceReal: 0,
        price: 0,
        quantity: 0,
        quantityPurchase: 0,
        errors: {},
      };
    });

    setVariants(newVariants);
  };

  const handleSave = () => {
    onSave(variants.length > 0 ? variants : initialVariants || []);
    onClose();
  };

  return (
    <Dialog
      open={open}
      onClose={() => {
        setErrorsVariant({});
        setErrors({});
        onClose();
      }}
      maxWidth="lg"
      fullWidth
      sx={{ "& .MuiDialog-paper": { width: "90%", maxWidth: "1400px" } }}
    >
      <DialogTitle>{t(tokens.contentManagement.product.variant.dialog.title)}</DialogTitle>

      <DialogContent dividers>
        <Stack spacing={3}>
          <SpecificationForm
            specifications={specifications}
            errors={errors}
            setErrors={setErrors}
            onSpecNameChange={handleSpecNameChange}
            onValueChange={handleValueChange}
            onValueBlur={handleValueBlur}
            onAddValue={handleAddValue}
            onRemoveValue={handleRemoveValue}
            onAddSpecification={handleAddSpecification}
            handleSpecFocus={handleSpecFocus}
            onRemoveSpecification={handleRemoveSpecification}
            errorsVariant={errorsVariant}
            setErrorsVariant={setErrorsVariant}
            hasErrors={hasErrors}
          />

          <VariantTable2
            variants={variants}
            setVariants={setVariants}
            errors={errorsVariant}
            setErrors={setErrorsVariant}
            existingFiles={[]}
            setExistingFiles={() => {}}
          />
        </Stack>
      </DialogContent>

      <DialogActions sx={{ p: 2, gap: 1 }}>
        <Button variant="outlined" onClick={onClose}>
          {t(tokens.common.cancel)}
        </Button>
        <Button
          variant="contained"
          onClick={handleSave}
          disabled={variants.length === 0 || hasErrors}
        >
          {t(tokens.common.save)}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default DialogAddVariant;
