import React, { useState, useEffect } from "react";
import { useRouter } from "next/router";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as Yup from "yup";
import SettingLayout from "@/src/components/settings/settings-page/SettingLayout";
import {
  Box,
  Checkbox,
  FormControl,
  FormControlLabel,
  FormGroup,
  IconButton,
  Paper,
  TextField,
  Typography,
  Button,
  Collapse,
  Divider,
  FormHelperText,
} from "@mui/material";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import Grid from "@mui/system/Grid";
import { useSections } from "@/src/layouts/dashboard/config";
import { useFunction } from "@/src/api/hooks/function/use-function";
import { useRole } from "@/src/api/hooks/role/use-role";
import { FunctionObject, RoleBodyCreateApi } from "@/src/api/services/role/role.service";
import { paths } from "@/src/paths";
import AuthorizationScreenUpdate from "./components/authorization-screen-update";
import { PermissionType } from "@/src/constants/constant";

interface RoleFormData {
  displayName?: string;
  roleName?: string;
  roleDescription?: string;
}

export interface FunctionDto {
  functionId: string;
  functionCode: string;
  functionName: string;
  url: string;
  icon: string;
  orderNumber: number;
  roleDescription: string;
  permissions: PermissionType[];
  children: FunctionDto[];
}

export interface PackageDto {
  packageId: string;
  packageName: string;
  packageCode: string;
  startDate: string;
  endDate: string;
  functions: FunctionDto[];
}

const validationSchema = Yup.object({
  roleName: Yup.string()
    .required("Tên vai trò là bắt buộc")
    .min(2, "Tên vai trò phải có ít nhất 2 ký tự")
    .max(255, "Tên vai trò không được vượt quá 255 ký tự"),
  roleDescription: Yup.string().max(1024, "Mô tả không được vượt quá 1024 ký tự"),
});

export interface selectedPermissionsDto {
  moduleId: {
    functionId: string[];
  };
}

export default function AddRole() {
  const router = useRouter();
  const [activePackage, setActivePackage] = useState<PackageDto>();
  const { mode, title, subtitle } = router.query;
  const isViewMode = mode === "view";
  const [selectedPermissions, setSelectedPermissions] = useState<FunctionObject[]>([]);

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
    reset,
  } = useForm<RoleFormData>({
    resolver: yupResolver(validationSchema),
    defaultValues: {
      roleName: "",
      roleDescription: "",
    },
  });

  const [orderOpen, setOrderOpen] = useState(false);
  const [editOrderOpen, setEditOrderOpen] = useState(false);
  const sections = useSections();
  const { getActivePackageFunctions } = useFunction();
  const { createRole } = useRole();

  const fetchActivePackageFunctions = async () => {
    const res = await getActivePackageFunctions();
    if (res && res.status === 200) {
      setActivePackage(res.data.data);
    }
  };
  useEffect(() => {
    fetchActivePackageFunctions();
  }, []);

  const [selectAll, setSelectAll] = useState(false);

  const handleBackClick = () => {
    router.push(paths.settings.managementRole);
  };

  function convertPermissionsFormat(inputData) {
    try {
      const parentId = Object.keys(inputData)[0];

      if (!parentId) {
        return { functions: [] };
      }

      const childObjects = inputData[parentId];

      const functions = Object.entries(childObjects).map(([functionId, permissions]) => {
        return {
          functionId,
          permissions: Array.isArray(permissions) ? permissions : [],
        };
      });

      return { functions };
    } catch (error) {
      console.error("Error converting permissions format:", error);
      throw error;
    }
  }

  const onSubmit = async (data: RoleFormData) => {
    try {
      const objectData: RoleBodyCreateApi = {
        roleName: data.roleName,
        roleDescription: data.roleDescription,
        roleIcon: "",
        functions: selectedPermissions,
      };
      console.log({ objectData });
      const res = await createRole(objectData);
      if (res && res.status === 200) {
        router.push(paths.settings.managementRole);
      }
    } catch (error) {
      console.error("Submit error:", error);
    }
  };

  const handleOrderToggle = () => {
    setOrderOpen(!orderOpen);
  };

  const handleEditOrderToggle = () => {
    setEditOrderOpen(!editOrderOpen);
  };

  const handleSelectAllPermissions = () => {
    if (!activePackage?.functions?.length) return;

    const allPermissions: FunctionObject[] = [];

    // Process all functions and their children recursively
    const processFunctions = (functions: FunctionDto[]) => {
      functions.forEach((func) => {
        // Add current function with all available permissions
        allPermissions.push({
          functionId: func.functionId,
          permissions: func.permissions || [],
        });

        // Process children recursively
        if (func.children?.length) {
          processFunctions(func.children);
        }
      });
    };

    processFunctions(activePackage.functions);
    setSelectedPermissions(allPermissions);
  };

  return (
    <SettingLayout>
      <Grid container alignItems="center" spacing={2} sx={{ mb: 1 }}>
        <Grid>
          <IconButton onClick={handleBackClick}>
            <ArrowBackIcon />
          </IconButton>
        </Grid>
        <Grid>
          <Typography variant="h5">
            {title || "Thêm vai trò"}
            {subtitle && (
              <Typography variant="caption" color="primary" sx={{ ml: 1 }}>
                {subtitle}
              </Typography>
            )}
          </Typography>
        </Grid>
      </Grid>
      <Grid container spacing={3}>
        <Grid size={{ xs: 12, md: 12 }}>
          <Paper sx={{ p: 3, boxShadow: 3 }}>
            <Box sx={{ display: "flex" }}>
              <FormControl fullWidth sx={{ mb: 3 }}>
                <Typography sx={{ mb: 1, display: "flex", fontSize: 16, fontWeight: 500 }}>
                  <Typography
                    sx={{ marginRight: 0.5, color: "red", fontWeight: 600, fontSize: 18 }}
                  >
                    *
                  </Typography>{" "}
                  Tên vai trò
                </Typography>
                <TextField
                  size="small"
                  sx={{ border: "1px" }}
                  placeholder="Vui lòng nhập tên vai trò"
                  fullWidth
                  disabled={isViewMode}
                  {...register("roleName")}
                  error={!!errors.roleName}
                  helperText={errors.roleName?.message}
                />
              </FormControl>
            </Box>
            <FormControl fullWidth>
              <Typography
                component="label"
                variant="body2"
                sx={{ mb: 1, fontSize: 16, fontWeight: 500 }}
              >
                Mô tả
              </Typography>
              <TextField
                multiline
                rows={4}
                placeholder="Vui lòng nhập mô tả"
                fullWidth
                disabled={isViewMode}
                {...register("roleDescription")}
                error={!!errors.roleDescription}
                helperText={errors.roleDescription?.message}
              />
            </FormControl>
          </Paper>
        </Grid>
        <Grid size={{ xs: 12, md: 12 }}>
          <Paper sx={{ p: 3, boxShadow: 3 }}>
            <Box
              sx={{ display: "flex", justifyContent: "space-between", alignItems: "center", mb: 2 }}
            >
              <Typography variant="subtitle1">Quyền hạn</Typography>
              {!isViewMode && (
                <Button variant="outlined" color="primary" onClick={handleSelectAllPermissions}>
                  Chọn tất cả quyền
                </Button>
              )}
            </Box>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
              Vui lòng chọn các quyền mà vai trò này có thể chỉnh sửa.
            </Typography>
            <AuthorizationScreenUpdate
              sections={sections}
              activePackage={activePackage}
              selectedPermissions={selectedPermissions}
              setSelectedPermissions={setSelectedPermissions}
            />
          </Paper>
        </Grid>
      </Grid>
      {!isViewMode && (
        <Box sx={{ display: "flex", justifyContent: "flex-end", mt: 3 }}>
          <Button variant="outlined" sx={{ mr: 2 }} onClick={handleBackClick}>
            Hủy bỏ
          </Button>
          <Button variant="contained" color="primary" onClick={handleSubmit(onSubmit)}>
            Lưu
          </Button>
        </Box>
      )}
    </SettingLayout>
  );
}
