import {
  Box,
  Button,
  IconButton,
  InputAdornment,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Typography
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import SettingLayout from '@/src/components/settings/settings-page/SettingLayout';
import { useRouter } from 'next/router';
import { paths } from '@/src/paths';

const Header = (router) => (
  <Box sx={{ mb: 3, display: 'flex', alignItems: 'center' }}>
    <IconButton sx={{ mr: 1 }} onClick={() => router.push(paths.settings.bill)}>
      <ArrowBackIcon />
    </IconButton>
    <Typography variant="h5">Chi tiết lịch sử giao dịch</Typography>
  </Box>
);
const Filters = (
  <Box
    sx={{
      mb: 3,
      display: 'flex',
      gap: 2,
      flexWrap: 'wrap'
    }}
  >
    <Box sx={{ display: 'flex', gap: 2, flex: 1, flexWrap: 'wrap' }}>
      <TextField
        size="small"
        fullWidth
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <SearchIcon sx={{ mb: 1 }} />
            </InputAdornment>
          ),
          inputProps: {
            style: { padding: 1 }
          }
        }}
        sx={{ flex: '1 1 100%', maxWidth: { xs: '100%', md: 300 } }}
      />
      <TextField
        type="date"
        size="small"
        label="Từ ngày"
        InputLabelProps={{ shrink: true }}
        sx={{ flex: '1 1 100%', maxWidth: { xs: '100%', md: 150 } }}
      />
      <TextField
        type="date"
        size="small"
        label="Đến Ngày"
        InputLabelProps={{ shrink: true }}
        sx={{ flex: '1 1 100%', maxWidth: { xs: '100%', md: 150 } }}
      />
    </Box>
    <Button
      variant="outlined"
      sx={{
        flex: '1 1 100%',
        maxWidth: { xs: '100%', md: '30%' }
      }}
    >
      Xuất báo cáo
    </Button>
  </Box>
);
const TransactionTable = (
  <TableContainer component={Paper}>
    <Table>
      <TableHead>
        <TableRow sx={{ bgcolor: 'rgb(237, 240, 244)' }}>
          <TableCell>Thời gian</TableCell>
          <TableCell>Loại giao dịch</TableCell>
          <TableCell>Chi tiết</TableCell>
          <TableCell align="right">Biến động số dư</TableCell>
        </TableRow>
      </TableHead>
      <TableBody>
        {[1, 2].map((row) => (
          <TableRow key={row}>
            <TableCell>15:00:14 ngày 20/12/2024</TableCell>
            <TableCell>ZNS</TableCell>
            <TableCell>Mẫu tin thư cảm ơn</TableCell>
            <TableCell align="right">-200đ</TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  </TableContainer>
);
export default function TransactionHistory() {
  const router = useRouter();
  return (
    <SettingLayout>
      <Box sx={{ p: 3 }}>
        {Header(router)}
        <Paper sx={{ p: 3, borderRadius: 2, boxShadow: 3 }}>
          {Filters}
          {TransactionTable}
        </Paper>
      </Box>
    </SettingLayout>
  );
}
