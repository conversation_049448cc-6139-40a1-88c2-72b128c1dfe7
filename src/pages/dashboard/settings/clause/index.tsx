import React from "react";
import SettingLayout from "@/src/components/settings/settings-page/SettingLayout";
import Clause from "@/src/components/settings/settings-page/Clause";
import Grid from "@mui/system/Grid/Grid";
import { Typography } from "@mui/material";

export default function SettingClause() {
  return (
    <SettingLayout>
      <Grid>
        <Typography variant="h5" sx={{ ml: 1 }}>
          <PERSON><PERSON><PERSON><PERSON>, ch<PERSON><PERSON> s<PERSON>ch
        </Typography>
      </Grid>
      <Grid>
        <Clause />
      </Grid>
    </SettingLayout>
  );
}
