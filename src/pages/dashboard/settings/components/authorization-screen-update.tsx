import React, { useState, useEffect } from "react";
import {
  Box,
  Checkbox,
  Divider,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
} from "@mui/material";
import { PackageDto } from "../add-role";
import PermissionManager from "./permission-manger";
const permissionTranslations: Record<string, string> = {
  All: "Tất cả",
  View: "Xem",
  Add: "Thêm",
  Edit: "Chỉnh sửa",
  Delete: "Xoá",
  Import: "Nhập dữ liệu",
  Export: "<PERSON>ất dữ liệu",
  Deposit: "<PERSON>ạ<PERSON> tiền",
};

const AuthorizationScreenUpdate = ({
  sections,
  activePackage,
  selectedPermissions,
  setSelectedPermissions,
}: {
  sections: any;
  activePackage: PackageDto;
  selectedPermissions: any;
  setSelectedPermissions: any;
}) => {
  const pkg = activePackage;
  const permissionModules = pkg?.functions;
  const [currentPermissions, setCurrentPermissions] = useState(selectedPermissions || []);

  const isPermissionSelected = (moduleId, itemId, permissionId) => {
    const func = currentPermissions?.find((f) => f.functionId === itemId);
    return func?.permissions?.includes(permissionId);
  };

  useEffect(() => {
    if (Array.isArray(selectedPermissions)) {
      setCurrentPermissions(selectedPermissions);
      localStorage.setItem("currentPermissions", JSON.stringify(selectedPermissions));
    }
  }, [selectedPermissions]);

  useEffect(() => {
    const savedPermissions = localStorage.getItem("currentPermissions");
    if (savedPermissions) {
      const parsedPermissions = JSON.parse(savedPermissions);
      setCurrentPermissions(parsedPermissions);
      setSelectedPermissions(parsedPermissions);
    }
  }, [setSelectedPermissions]);

  const handlePermissionChange = (moduleId: string, itemId: string, permissionId: string) => {
    console.log({ moduleId });
    console.log({ itemId });
    console.log({ permissionId });
    const updatedPermissions = [...currentPermissions];
    const functionIndex = updatedPermissions.findIndex((f) => f.functionId === itemId);

    if (functionIndex === -1) {
      updatedPermissions.push({
        functionId: itemId,
        permissions: [permissionId],
      });
    } else {
      const existingFunction = updatedPermissions[functionIndex];
      const permissionIndex = existingFunction.permissions.indexOf(permissionId);

      if (permissionIndex === -1) {
        existingFunction.permissions.push(permissionId);
      } else {
        existingFunction.permissions = existingFunction.permissions.filter(
          (p) => p !== permissionId
        );
        if (existingFunction.permissions.length === 0) {
          updatedPermissions.splice(functionIndex, 1);
        }
      }
    }

    setCurrentPermissions(updatedPermissions);
    setSelectedPermissions(updatedPermissions);
    localStorage.setItem("currentPermissions", JSON.stringify(updatedPermissions));
  };

  // New method to handle selecting all permissions for a module and its children
  const handleSelectAll = (module: any, checked: boolean) => {
    const updatedPermissions = [...currentPermissions];

    // Function to add permission to the updatedPermissions array
    const addPermission = (itemId: string, permissionId: string) => {
      let functionIndex = updatedPermissions.findIndex((f) => f.functionId === itemId);

      if (functionIndex === -1) {
        updatedPermissions.push({
          functionId: itemId,
          permissions: [permissionId],
        });
      } else {
        const existingFunction = updatedPermissions[functionIndex];
        if (!existingFunction.permissions.includes(permissionId)) {
          existingFunction.permissions.push(permissionId);
        }
      }
    };

    // Function to remove permission from the updatedPermissions array
    const removePermission = (itemId: string, permissionId: string) => {
      const functionIndex = updatedPermissions.findIndex((f) => f.functionId === itemId);
      if (functionIndex !== -1) {
        const existingFunction = updatedPermissions[functionIndex];
        existingFunction.permissions = existingFunction.permissions.filter(
          (p) => p !== permissionId
        );

        if (existingFunction.permissions.length === 0) {
          updatedPermissions.splice(functionIndex, 1);
        }
      }
    };

    // Handle parent module permissions
    module.permissions.forEach((permission) => {
      if (checked) {
        addPermission(module.functionId, permission);
      } else {
        removePermission(module.functionId, permission);
      }
    });

    // Handle all child module permissions
    module.children.forEach((child) => {
      child.permissions.forEach((permission) => {
        if (checked) {
          addPermission(child.functionId, permission);
        } else {
          removePermission(child.functionId, permission);
        }
      });
    });

    setCurrentPermissions(updatedPermissions);
    setSelectedPermissions(updatedPermissions);
    localStorage.setItem("currentPermissions", JSON.stringify(updatedPermissions));
  };

  return (
    <Paper sx={{ p: 3, borderRadius: 2 }}>
      <PermissionManager
        modules={permissionModules}
        selectedPermissions={selectedPermissions}
        onPermissionChange={handlePermissionChange}
        onSelectAll={handleSelectAll}
      />
    </Paper>
  );
};

export default AuthorizationScreenUpdate;
