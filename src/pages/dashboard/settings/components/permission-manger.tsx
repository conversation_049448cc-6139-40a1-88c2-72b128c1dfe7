import { useState } from "react";
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import Checkbox from "@mui/material/Checkbox";
import FormControlLabel from "@mui/material/FormControlLabel";
import Grid from "@mui/material/Grid";
import { FunctionDto } from "../add-role";
import { PermissionType } from "@/src/constants/constant";

interface PermissionManagerProps {
  modules: FunctionDto[];
  selectedPermissions: Array<{ functionId: string; permissions: string[] }>;
  onPermissionChange: (moduleId: string, itemId: string, permissionId: string) => void;
  onSelectAll: (module: any, checked: boolean) => void;
}

export default function PermissionManager({
  modules,
  selectedPermissions,
  onPermissionChange,
  onSelectAll,
}: PermissionManagerProps) {
  return (
    <Box sx={{ display: "flex", flexDirection: "column", gap: 3 }}>
      {Array.isArray(modules) &&
        modules.map((module) => (
          <ModulePermission
            key={module.functionId}
            module={module}
            selectedPermissions={selectedPermissions}
            onPermissionChange={onPermissionChange}
            onSelectAll={onSelectAll}
          />
        ))}
    </Box>
  );
}

interface ModulePermissionProps {
  module: FunctionDto;
  selectedPermissions: Array<{ functionId: string; permissions: string[] }>;
  onPermissionChange: (moduleId: string, itemId: string, permissionId: string) => void;
  onSelectAll: (module: any, checked: boolean) => void;
}

function ModulePermission({
  module,
  selectedPermissions,
  onPermissionChange,
  onSelectAll,
}: ModulePermissionProps) {
  const [expanded, setExpanded] = useState(true);

  const toggleAllPermissions = (checked: boolean) => {
    onSelectAll(module, checked);
  };

  const isAllSelected = () => {
    // Check if all permissions for this module are selected
    const modulePermissions = selectedPermissions.find((p) => p.functionId === module.functionId);
    if (!modulePermissions) return false;

    // Check if all permissions for this module are selected
    const allModulePermissionsSelected = module.permissions.every((permission) =>
      modulePermissions.permissions.includes(permission)
    );

    // Check if all permissions for all children are selected
    const allChildrenPermissionsSelected = module.children.every((child) => {
      const childPermissions = selectedPermissions.find((p) => p.functionId === child.functionId);
      if (!childPermissions) return false;
      return child.permissions.every((permission) =>
        childPermissions.permissions.includes(permission)
      );
    });

    return allModulePermissionsSelected && allChildrenPermissionsSelected;
  };

  const isPermissionSelected = (permission: PermissionType) => {
    const permissionItem = selectedPermissions.find((p) => p.functionId === module.functionId);
    return permissionItem?.permissions.includes(permission) || false;
  };

  return (
    <Box
      sx={{
        pb: 2,
        borderBottom: "1px solid #e0e0e0",
        "&:last-child": { borderBottom: "none", pb: 0 },
      }}
    >
      <Grid container spacing={2} alignItems="center">
        {/* Main module */}
        <Grid item xs={12} sm={2.2}>
          <Typography
            variant="subtitle1"
            sx={{
              fontWeight: 600,
              marginRight: 1,
              mb: 1,
              ...(module.children.length > 0 ? { textTransform: "uppercase" } : {}),
            }}
          >
            {module.functionName}
          </Typography>
        </Grid>

        {/* All permissions checkbox */}
        <Grid item xs={12} sm={1.5}>
          {module.permissions.length > 1 && (
            <FormControlLabel
              control={
                <Checkbox
                  checked={isAllSelected()}
                  onChange={(e) => toggleAllPermissions(e.target.checked)}
                  size="small"
                />
              }
              label={
                <Typography variant="body2" sx={{ pointerEvents: "none" }}>
                  Tất cả quyền
                </Typography>
              }
              sx={{ mb: 1 }}
              labelPlacement="end"
            />
          )}
        </Grid>
      </Grid>
      {module.children.length > 0 && (
        <Box sx={{ pl: 3, mt: 2, display: "flex", flexDirection: "column", gap: 2 }}>
          {module.children.map((child) => (
            <ChildPermission
              key={child.functionId}
              parentId={module.functionId}
              child={child}
              selectedPermissions={selectedPermissions}
              onPermissionChange={onPermissionChange}
            />
          ))}
        </Box>
      )}
    </Box>
  );
}

interface ChildPermissionProps {
  parentId: string;
  child: FunctionDto;
  selectedPermissions: Array<{ functionId: string; permissions: string[] }>;
  onPermissionChange: (moduleId: string, itemId: string, permissionId: string) => void;
}

function ChildPermission({
  parentId,
  child,
  selectedPermissions,
  onPermissionChange,
}: ChildPermissionProps) {
  const isPermissionSelected = (permission: PermissionType) => {
    const permissionItem = selectedPermissions.find((p) => p.functionId === child.functionId);
    return permissionItem?.permissions.includes(permission) || false;
  };

  return (
    <Grid container spacing={2} alignItems="center">
      <Grid item xs={12} sm={2}>
        <Typography variant="body2" color="text.secondary">
          {child.functionName}
        </Typography>
      </Grid>
      {child.permissions.map((permission) => (
        <Grid item xs={6} sm={1.5} key={`${child.functionId}-${permission}`}>
          <FormControlLabel
            control={
              <Checkbox
                checked={isPermissionSelected(permission)}
                onChange={(e) => onPermissionChange(parentId, child.functionId, permission)}
                size="small"
              />
            }
            label={
              <Typography variant="body2" sx={{ pointerEvents: "none" }}>
                {permission === "View"
                  ? "Xem"
                  : permission === "Add"
                  ? "Thêm"
                  : permission === "Edit"
                  ? "Chỉnh sửa"
                  : permission === "Delete"
                  ? "Xóa"
                  : permission === "Import"
                  ? "Nhập"
                  : permission === "Export"
                  ? "Xuất"
                  : permission}
              </Typography>
            }
            labelPlacement="end"
          />
        </Grid>
      ))}
    </Grid>
  );
}
