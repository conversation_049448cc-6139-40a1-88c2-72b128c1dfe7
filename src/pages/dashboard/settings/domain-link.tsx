import React from 'react';
import SettingLayout from '@/src/components/settings/settings-page/SettingLayout';
import { useRouter } from 'next/router';
import { Box, IconButton, Typography, Paper, Stack, Button, Tooltip, Divider } from '@mui/material';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import InfoIcon from '@mui/icons-material/Info';
import Grid from '@mui/system/Grid';

interface DomainRecordProps {
  label: string;
  currentValue: string;
  requiredValue: string;
}

const DomainRecord = ({ label, currentValue, requiredValue }: DomainRecordProps) => (
  <Box sx={{ mt: 3 }}>
    <Stack direction="row" alignItems="center" spacing={1}>
      <Typography variant="subtitle1" sx={{ fontWeight: 500 }}>
        {label}
      </Typography>
      <Tooltip title="Thông tin bản ghi">
        <InfoIcon sx={{ fontSize: 20, color: 'orange' }} />
      </Tooltip>
    </Stack>
    <Box sx={{ mt: 1 }}>
      <Typography variant="body2" sx={{ color: 'text.secondary' }}>
        Giá trị hiện tại: {currentValue}
      </Typography>
      <Typography variant="body2" sx={{ color: 'text.secondary' }}>
        Giá trị bắt buộc: {requiredValue}
      </Typography>
    </Box>
  </Box>
);

const Header = ({ handleBackClick }) => (
  <Grid container justifyContent="center" sx={{ mb: 3 }}>
    <Grid size={{ xs: 12, md: 10 }}>
      <Box sx={{ display: 'flex', alignItems: 'center', spacing: 2 }}>
        <IconButton onClick={handleBackClick}>
          <ArrowBackIcon />
        </IconButton>
        <Typography variant="h5">Liên kết tên miền hiện có</Typography>
      </Box>
    </Grid>
  </Grid>
);

const DomainDetails = () => (
  <Grid container justifyContent="center">
    <Grid size={{ xs: 12, md: 10 }}>
      <Paper
        sx={{
          borderRadius: 2,
          p: 3,
          boxShadow: 3,
          backgroundColor: 'background.paper'
        }}
      >
        <Typography variant="h6" sx={{ mb: 2 }}>
          Kết nối với tên miền của bạn
        </Typography>
        <Divider sx={{ mb: 2 }} />
        <Typography variant="h6" sx={{ mb: 2 }}>
          Ngoại lệ đầu vào A và CNAME
        </Typography>
        <Typography variant="body2" sx={{ color: 'text.secondary', mb: 2 }}>
          Kiểm tra xem bạn đã nhập đúng giá trị yêu cầu chưa và xác minh lại kết nối.
        </Typography>

        <DomainRecord label="CNAME (www)" currentValue="syncsoft.vn" requiredValue="shops.myallvalue.com" />

        <DomainRecord label="Bản ghi A (@)" currentValue="137.59.105.30" requiredValue="170.106.112.112" />

        <Box sx={{ mt: 4, display: 'flex', justifyContent: 'flex-end' }}>
          <Button
            variant="contained"
            sx={{
              bgcolor: 'primary.main',
              '&:hover': {
                bgcolor: 'primary.dark'
              }
            }}
          >
            Xác minh lại
          </Button>
        </Box>
      </Paper>
    </Grid>
  </Grid>
);

export default function DomainLink() {
  const router = useRouter();

  const handleBackClick = () => {
    router.back();
  };

  const handleVerify = () => {
    console.log('Verifying domain settings...');
  };

  return (
    <SettingLayout>
      <Header handleBackClick={handleBackClick} />
      <DomainDetails />
    </SettingLayout>
  );
}
