import React from 'react';
import SettingLayout from '@/src/components/settings/settings-page/SettingLayout';
import { useRouter } from 'next/router';
import Grid from '@mui/system/Grid';
import { IconButton, Typography } from '@mui/material';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import DomainManagement from '@/src/components/settings/settings-page/DomainName';

export default function OverviewPage() {
  return (
    <SettingLayout>
      <Grid>
        <Grid>
          <Typography variant="h5" sx={{ ml: 1 }}>
            Tên miền riêng
          </Typography>
        </Grid>
      </Grid>
      <DomainManagement />
    </SettingLayout>
  );
}
