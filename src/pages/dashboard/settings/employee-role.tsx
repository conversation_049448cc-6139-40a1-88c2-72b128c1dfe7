import React, { useState, useRef, useEffect } from "react";
import { useRouter } from "next/router";
import { useF<PERSON>, Controller } from "react-hook-form";
import {
  Box,
  Button,
  Typography,
  Grid,
  Paper,
  IconButton,
  TextField,
  FormControl,
  Select,
  MenuItem,
  Checkbox,
  ListItemText,
  InputAdornment,
  Switch,
  FormHelperText,
} from "@mui/material";
import {
  ArrowBack as ArrowBackIcon,
  Add,
  KeyboardArrowDown as KeyboardArrowDownIcon,
  Close,
  DeleteOutline,
  Add as AddIcon,
  Refresh,
  VisibilityOff,
  Visibility,
} from "@mui/icons-material";
import SettingLayout from "@/src/components/settings/settings-page/SettingLayout";
import { useMedia } from "@/src/api/hooks/media/use-media";
import { useRole } from "@/src/api/hooks/role/use-role";
import { usePartnerEmployee } from "@/src/api/hooks/partner-employee/use-partner-employee";
import { paths } from "@/src/paths";
import useSnackbar from "@/src/hooks/use-snackbar";
import { RoleDto } from "@/src/components/settings/settings-page/Auth";
import TitleDialog from "@/src/components/dialog/TitleDialog";
import { Eye, EyeOff } from "@untitled-ui/icons-react";
import { FILE_SIZE_2MB } from "@/src/constants/constant";
import { CreateFileGroupRequest, GetGroupFileRequest, RefType } from "@/src/api/types/media.types";
import { useStoreId } from "@/src/hooks/use-store-id";
import { isValidImageFile } from "../product/category-management/create";

interface EmployeeFormData {
  store: string;
  role: string[];
  fullname: string;
  phoneNumber: string;
  username: string;
  password: string;
  profileImage?: File | null;
  status?: string;
  email?: string;
  avatar?: string;
}

interface EmployeeDto {
  fullname: string;
  phoneNumber: string;
  username: string;
  avatar?: string;
  roles: { roleId: string; roleName: string }[];
  status: string;
  email?: string;
}

interface PartnerEmployeeBodyCreateApi {
  fullname: string;
  roleIds: string[];
  phoneNumber: string;
  username: string;
  password?: string;
  avatar?: string;
  email?: string;
  status?: string;
}

interface PartnerEmployeeBodyUpdateApi {
  employeeId: string;
  body: PartnerEmployeeBodyCreateApi;
}

interface EmployeeChangePasswordBody {
  password?: string;
}
interface EmployeeChangePassword {
  employeeId: string;
  body: EmployeeChangePasswordBody;
}

export const VietnamFlag = () => {
  return (
    <Box
      sx={{
        width: "30px",
        height: "20px",
        backgroundColor: "#da251d",
        position: "relative",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
      }}
    >
      <Box
        component="svg"
        viewBox="0 0 24 24"
        sx={{
          width: "12px",
          height: "12px",
        }}
      >
        <polygon
          points="12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26"
          fill="#ffff00"
        />
      </Box>
    </Box>
  );
};

const generateRandomPassword = () => {
  const uppercase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
  const lowercase = "abcdefghijklmnopqrstuvwxyz";
  const numbers = "0123456789";
  const specialChars = "!@#$%^&*()-_=+[]{};:,.<>?";

  const all = uppercase + lowercase + numbers + specialChars;

  let password = "";
  password += uppercase[Math.floor(Math.random() * uppercase.length)];
  password += lowercase[Math.floor(Math.random() * lowercase.length)];
  password += numbers[Math.floor(Math.random() * numbers.length)];
  password += specialChars[Math.floor(Math.random() * specialChars.length)];

  for (let i = 4; i < 12; i++) {
    password += all[Math.floor(Math.random() * all.length)];
  }

  return password
    .split("")
    .sort(() => Math.random() - 0.5)
    .join("");
};

export default function EmployeeRole() {
  const router = useRouter();
  const [file, setFile] = useState<File | null>(null);
  const [profileImagePreview, setProfileImagePreview] = useState<string | null>(null);
  const { uploadFile, getGroups } = useMedia();
  const [defaultGroupId, setDefaultGroupId] = useState<string>("");
  const inputFileRef = useRef<HTMLInputElement>(null);
  const {
    createPartnerEmployee,
    getPartnerEmployeeById,
    updatePartnerEmployee,
    changePasswordEmployee,
  } = usePartnerEmployee();
  const { listRole } = useRole();
  const [roles, setRoles] = useState<RoleDto[]>([]);
  const [employee, setEmployee] = useState<EmployeeDto | null>(null);
  const [formInitialized, setFormInitialized] = useState(false);
  const snackbar = useSnackbar();
  const id = Array.isArray(router.query.id) ? router.query.id[0] : router.query.id;
  const [showPassword, setShowPassword] = useState(false);
  const [openDialog, setOpenDialog] = useState(false);
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [newPasswordError, setNewPasswordError] = useState("");
  const [confirmPasswordError, setConfirmPasswordError] = useState("");
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const shopId = useStoreId();

  const {
    control,
    register,
    handleSubmit,
    setValue,
    watch,
    reset,
    formState: { errors },
  } = useForm<EmployeeFormData>({
    defaultValues: {
      store: "",
      role: [],
      fullname: "",
      phoneNumber: "",
      username: "",
      password: "",
      profileImage: null,
      status: "Actived",
      email: "",
      avatar: "",
    },
  });

  const watchStore = watch("store");
  const watchAvatar = watch("avatar");

  // Fetch employee data when ID is available
  useEffect(() => {
    const fetchEmployee = async () => {
      if (id) {
        try {
          const res = await getPartnerEmployeeById(id);
          if (res && res.status === 200) {
            const data = res.data.data;
            const phone = data.phoneNumber?.startsWith("+84")
              ? data.phoneNumber.replace("+84", "")
              : data.phoneNumber;

            const employeeData = {
              ...data,
              phoneNumber: phone,
              role: data.roles.map((r) => r.roleId),
            };

            setEmployee(data);

            // Initialize the form with employee data
            reset({
              ...employeeData,
              role: data.roles.map((r) => r.roleId),
              avatar: data.avatar || "",
            });

            // Set profile image preview if there's an avatar
            if (data.avatar) {
              setProfileImagePreview(data.avatar);
            }

            setFormInitialized(true);
          }
        } catch (error) {
          console.error("Error fetching employee:", error);
        }
      } else {
        setFormInitialized(true);
      }
    };

    fetchEmployee();
  }, [id, reset]);

  // Fetch default group
  useEffect(() => {
    const fetchDefaultGroup = async () => {
      try {
        const params: GetGroupFileRequest = {
          ShopId: shopId,
          Skip: 0,
          Limit: 1,
        };
        const response = await getGroups(params);
        if (response?.data?.data?.length > 0) {
          setDefaultGroupId(response.data.data[0].groupFileId);
        }
      } catch (error) {
        console.error("Error fetching default group:", error);
      }
    };
    fetchDefaultGroup();
  }, []);

  // Fetch roles
  useEffect(() => {
    const getAllRoles = async () => {
      try {
        const res = await listRole();
        if (res && res.status === 200) {
          setRoles(res.data.data);
        }
      } catch (error) {
        console.error("Error fetching roles:", error);
      }
    };
    getAllRoles();
  }, []);

  const handleBackClick = () => {
    router.push(paths.settings.auth);
  };

  const handleAddRoleClick = () => {
    router.push(paths.settings.addRole);
  };

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!isValidImageFile(file)) {
      snackbar.error(
        "Định dạng file không hợp lệ. Hãy chọn file định dạng PNG, JPG, JPEG hoặc JFIF."
      );
    }

    if (file && file.size > FILE_SIZE_2MB) {
      snackbar.error(
        `Kích thước ảnh không được vượt quá ${(FILE_SIZE_2MB / 1024 / 1024).toFixed(1)}MB.`
      );

      return;
    }
    if (event.target.files && event.target.files[0]) {
      const selectedFile = event.target.files[0];
      setFile(selectedFile);
      setValue("profileImage", selectedFile);

      const reader = new FileReader();
      reader.onload = () => {
        setProfileImagePreview(reader.result as string);
        setValue("avatar", reader.result as string);
      };
      reader.readAsDataURL(selectedFile);
    }
  };

  const handleClearImage = () => {
    if (inputFileRef.current) {
      inputFileRef.current.value = "";
    }
    setProfileImagePreview(null);
    setFile(null);

    setValue("profileImage", null);
    setValue("avatar", "");
  };

  const handleClearStore = () => {
    setValue("store", "");
  };

  const handleFileUpload = async (file: File | null) => {
    if (file) {
      if (file.size > FILE_SIZE_2MB) {
        snackbar.error(
          `Kích thước ảnh không được vượt quá ${(FILE_SIZE_2MB / 1024 / 1024).toFixed(1)}MB.`
        );
        return null;
      }
      const allowedTypes = ["image/png", "image/jpeg", "image/jpg", "image/gif"];
      if (!allowedTypes.includes(file.type)) {
        snackbar.warning("Định dạng ảnh không hợp lệ. Chỉ chấp nhận PNG, JPG, JPEG, GIF.");
        setProfileImagePreview(null);
        return null;
      }
      try {
        const data: CreateFileGroupRequest = {
          FileUpload: file,
          GroupFileId: defaultGroupId,
          ShopId: shopId,
          RefType: RefType.User,
        };

        const response = await uploadFile(data);
        return response.data.link;
      } catch (error) {
        snackbar.warning("Có lỗi xảy ra khi tải lên ảnh. Vui lòng thử lại.");
        return null;
      }
    }
    return null;
  };

  const onSubmit = async (data: EmployeeFormData) => {
    // Remove validation checks for profile image
    const imgUrl = file ? await handleFileUpload(file) : null;

    if (id) {
      const body: PartnerEmployeeBodyCreateApi = {
        fullname: data.fullname,
        roleIds: Array.isArray(data.role) ? data.role : [data.role],
        phoneNumber: "+84" + data.phoneNumber,
        username: data.username,
        avatar: imgUrl || (profileImagePreview && employee?.avatar) || "",
        email: data.email,
        status: data.status,
      };

      const updateBody: PartnerEmployeeBodyUpdateApi = {
        employeeId: id,
        body,
      };
      console.log({ updateBody });
      try {
        const res = await updatePartnerEmployee(updateBody);
        if (res && res.status === 200) {
          router.push(paths.settings.auth);
        }
      } catch (error) {
        console.error("Error updating employee:", error);
        alert("Có lỗi xảy ra khi cập nhật nhân viên. Vui lòng thử lại.");
      }
    } else {
      const body: PartnerEmployeeBodyCreateApi = {
        password: data.password,
        fullname: data.fullname,
        roleIds: Array.isArray(data.role) ? data.role : [data.role],
        phoneNumber: "+84" + data.phoneNumber,
        username: data.username,
        avatar: imgUrl || (profileImagePreview && employee?.avatar) || "",
        email: data.email,
        status: data.status,
      };

      try {
        const res = await createPartnerEmployee(body);
        if (res && res.status === 200) {
          router.push(paths.settings.auth);
        }
      } catch (error) {
        console.error("Error creating employee:", error);
        alert("Có lỗi xảy ra khi tạo nhân viên mới. Vui lòng thử lại.");
      }
    }
  };

  if (!formInitialized) {
    return <Box sx={{ p: 3 }}>Loading...</Box>;
  }

  const onClickOpenDialog = () => {
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setNewPassword("");
    setConfirmPassword("");
    setNewPasswordError("");
    setConfirmPasswordError("");
    setShowNewPassword(false);
    setShowConfirmPassword(false);
  };

  const validatePassword = (password: string): { isValid: boolean; error: string } => {
    if (!password) {
      return { isValid: false, error: "Vui lòng nhập mật khẩu" };
    }

    if (password.length < 8) {
      return { isValid: false, error: "Mật khẩu phải có ít nhất 8 ký tự" };
    }

    if (!/[A-Z]/.test(password)) {
      return { isValid: false, error: "Mật khẩu phải có ít nhất 1 chữ hoa" };
    }

    if (!/[a-z]/.test(password)) {
      return { isValid: false, error: "Mật khẩu phải có ít nhất 1 chữ thường" };
    }

    if (!/[0-9]/.test(password)) {
      return { isValid: false, error: "Mật khẩu phải có ít nhất 1 chữ số" };
    }

    if (!/[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/.test(password)) {
      return { isValid: false, error: "Mật khẩu phải có ít nhất 1 ký tự đặc biệt" };
    }

    return { isValid: true, error: "" };
  };

  const validatePasswords = () => {
    const newPasswordValidation = validatePassword(newPassword);
    setNewPasswordError(newPasswordValidation.error);

    if (!confirmPassword) {
      setConfirmPasswordError("Vui lòng xác nhận mật khẩu");
      return false;
    }

    if (newPassword !== confirmPassword) {
      setConfirmPasswordError("Mật khẩu không khớp");
      return false;
    }

    setConfirmPasswordError("");
    return newPasswordValidation.isValid;
  };

  const handleNewPasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setNewPassword(value);
    if (value) {
      const validation = validatePassword(value);
      setNewPasswordError(validation.error);
    } else {
      setNewPasswordError("");
    }

    if (confirmPassword && value !== confirmPassword) {
      setConfirmPasswordError("Mật khẩu không khớp");
    } else if (confirmPassword) {
      setConfirmPasswordError("");
    }
  };

  const handleConfirmPasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setConfirmPassword(value);

    if (!value) {
      setConfirmPasswordError("");
      return;
    }

    if (newPassword !== value) {
      setConfirmPasswordError("Mật khẩu không khớp");
    } else {
      setConfirmPasswordError("");
    }
  };

  const handleChangePassword = async () => {
    if (!validatePasswords()) return;
    const data: EmployeeChangePassword = {
      employeeId: id,
      body: {
        password: newPassword,
      },
    };

    const response = await changePasswordEmployee(data);
    if (response?.data) {
      handleCloseDialog();
      snackbar.success("Đổi mật khẩu thành công");
      setNewPassword("");
      setConfirmPassword("");
    }
  };

  return (
    <SettingLayout>
      <Box sx={{ p: 2 }}>
        <Grid container alignItems="center" spacing={2} sx={{ mb: 4 }}>
          <Grid>
            <IconButton onClick={handleBackClick}>
              <ArrowBackIcon />
            </IconButton>
          </Grid>
          <Grid>
            <Typography variant="h5">{id ? "Chỉnh sửa nhân viên" : "Thêm nhân viên"}</Typography>
          </Grid>
        </Grid>
        <form onSubmit={handleSubmit(onSubmit)}>
          <Paper sx={{ p: 3, mx: "auto", border: "1px solid #e0e0e0" }}>
            <Grid container spacing={3}>
              {/* Profile Image */}
              <Grid item xs={12} sm={3}>
                <Typography
                  component="label"
                  variant="body2"
                  sx={{
                    display: "flex",
                    fontSize: 16,
                    fontWeight: 500,
                    alignItems: "center",
                    mb: 1,
                    gap: 0.5,
                  }}
                >
                  Ảnh đại diện
                </Typography>
                <Box sx={{ position: "relative" }}>
                  <Box
                    component="label"
                    sx={{
                      width: "100%",
                      height: 300,
                      border: "1px dashed #ccc",
                      borderRadius: 2,
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      flexDirection: "column",
                      cursor: "pointer",
                      mb: 1,
                      backgroundSize: "cover",
                      backgroundPosition: "center",
                      objectFit: "contain",
                      backgroundImage: profileImagePreview ? `url(${profileImagePreview})` : "none",
                    }}
                  >
                    {!profileImagePreview && (
                      <>
                        <AddIcon color="disabled" />
                        <Typography
                          variant="body2"
                          sx={{
                            textTransform: "none",
                            fontSize: 18,
                            fontWeight: 500,
                            color: "#979898",
                            marginTop: 2,
                          }}
                        >
                          Tải lên
                        </Typography>
                      </>
                    )}
                    <input
                      ref={inputFileRef}
                      type="file"
                      hidden
                      size={FILE_SIZE_2MB}
                      accept="image/*"
                      onChange={handleImageUpload}
                    />
                  </Box>
                  {profileImagePreview && (
                    <Box
                      sx={{
                        padding: 0.5,
                        borderBottomLeftRadius: 10,
                        borderTopRightRadius: 3,
                        backgroundColor: "rgba(67, 67, 67, 0.5)",
                        position: "absolute",
                        top: 0,
                        right: 0,
                        zIndex: 2,
                        "&:hover": {
                          backgroundColor: "rgba(54, 53, 53, 0.5)",
                        },
                        cursor: "pointer",
                      }}
                      onClick={handleClearImage}
                    >
                      <DeleteOutline sx={{ color: "white" }} />
                    </Box>
                  )}
                </Box>
              </Grid>

              {/* Form Fields */}
              <Grid item xs={12} sm={9}>
                <Grid container spacing={2}>
                  {/* Status */}
                  <Grid item xs={12}>
                    <Typography
                      component="label"
                      variant="body2"
                      sx={{
                        display: "flex",
                        fontSize: 16,
                        fontWeight: 500,
                        alignItems: "center",
                        gap: 0.5,
                      }}
                    >
                      Trạng thái
                      <Typography
                        sx={{
                          color: "error.main",
                          marginRight: 0.5,
                          fontSize: 16,
                          fontWeight: 500,
                        }}
                      >
                        *
                      </Typography>
                    </Typography>
                    <Controller
                      name="status"
                      control={control}
                      defaultValue="Actived"
                      render={({ field }) => (
                        <Box sx={{ display: "flex", alignItems: "center", mt: 1 }}>
                          <Switch
                            checked={field.value === "Actived"}
                            onChange={(e) =>
                              field.onChange(e.target.checked ? "Actived" : "InActived")
                            }
                            color="primary"
                          />
                          <Typography sx={{ ml: 1 }}>
                            {field.value === "Actived" ? "Đang hoạt động" : "Không hoạt động"}
                          </Typography>
                        </Box>
                      )}
                    />
                    {errors.status && (
                      <FormHelperText error>{errors.status.message}</FormHelperText>
                    )}
                  </Grid>

                  {/* Role */}
                  <Grid item xs={12}>
                    <Typography
                      component="label"
                      variant="body2"
                      sx={{ display: "flex", fontSize: 16, fontWeight: 500, gap: 0.5 }}
                    >
                      Vai trò
                      <Typography
                        sx={{
                          color: "error.main",
                          marginRight: 0.5,
                          fontSize: 16,
                          fontWeight: 500,
                        }}
                      >
                        *
                      </Typography>
                    </Typography>
                    <Box sx={{ display: "flex", alignItems: "start", mt: 0.5 }}>
                      <FormControl fullWidth error={!!errors.role}>
                        <Controller
                          name="role"
                          control={control}
                          rules={{ required: "Vai trò là bắt buộc" }}
                          render={({ field }) => (
                            <Select
                              {...field}
                              displayEmpty
                              size="small"
                              multiple
                              IconComponent={KeyboardArrowDownIcon}
                              value={
                                Array.isArray(field.value)
                                  ? field.value
                                  : field.value
                                  ? [field.value]
                                  : []
                              }
                              renderValue={(selected) => {
                                if (
                                  !selected ||
                                  (Array.isArray(selected) && selected.length === 0)
                                ) {
                                  return (
                                    <Typography color="text.secondary">Chọn Vai trò...</Typography>
                                  );
                                }
                                if (Array.isArray(selected)) {
                                  const selectedNames = selected
                                    .map(
                                      (id) => roles.find((r) => r.role.roleId === id)?.role.roleName
                                    )
                                    .filter(Boolean)
                                    .join(", ");
                                  return selectedNames;
                                }
                                return selected;
                              }}
                            >
                              {roles &&
                                roles.length > 0 &&
                                roles.map((role) => (
                                  <MenuItem key={role.role.roleId} value={role.role.roleId}>
                                    <Checkbox checked={field.value?.includes(role.role.roleId)} />
                                    <ListItemText primary={role.role.roleName} />
                                  </MenuItem>
                                ))}
                            </Select>
                          )}
                        />
                        {errors.role && <FormHelperText>{errors.role.message}</FormHelperText>}
                      </FormControl>
                      <IconButton
                        sx={{
                          ml: 1,
                          bgcolor: "#fff",
                          color: "#000",
                          border: "1px solid #979797",
                          borderRadius: "4px",
                          "&:hover": {
                            bgcolor: " rgb(251, 250, 250)",
                          },
                        }}
                        onClick={handleAddRoleClick}
                      >
                        <Add />
                      </IconButton>
                    </Box>
                  </Grid>

                  {/* Name and Phone */}
                  <Grid item xs={12} sm={6}>
                    <Typography
                      component="label"
                      variant="body2"
                      sx={{ display: "flex", fontSize: 16, fontWeight: 500, gap: 0.5 }}
                    >
                      Họ tên
                      <Typography
                        sx={{
                          color: "error.main",
                          marginRight: 0.5,
                          fontSize: 16,
                          fontWeight: 500,
                        }}
                      >
                        *
                      </Typography>
                    </Typography>
                    <Controller
                      name="fullname"
                      control={control}
                      rules={{
                        required: "Họ tên là bắt buộc",
                        minLength: { value: 2, message: "Họ tên phải có ít nhất 2 ký tự" },
                        maxLength: { value: 255, message: "Họ tên không được vượt quá 255 ký tự" },
                      }}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          fullWidth
                          placeholder="Vui lòng nhập Họ tên"
                          error={!!errors.fullname}
                          helperText={errors.fullname?.message}
                          size="small"
                          sx={{ mt: 0.5 }}
                        />
                      )}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Typography
                      component="label"
                      variant="body2"
                      sx={{ display: "flex", fontSize: 16, fontWeight: 500, gap: 0.5 }}
                    >
                      Số điện thoại
                      <Typography
                        sx={{
                          color: "error.main",
                          marginRight: 0.5,
                          fontSize: 16,
                          fontWeight: 500,
                        }}
                      >
                        *
                      </Typography>
                    </Typography>
                    <Controller
                      name="phoneNumber"
                      control={control}
                      rules={{
                        required: "Số điện thoại là bắt buộc",
                        pattern: {
                          value: /^[0-9]{9}$/,
                          message: "Định dạng số điện thoại này không được nhận diện",
                        },
                      }}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          fullWidth
                          placeholder="Vui lòng nhập Số điện thoại"
                          error={!!errors.phoneNumber}
                          helperText={errors.phoneNumber?.message}
                          size="small"
                          InputProps={{
                            startAdornment: (
                              <InputAdornment position="start">
                                <Box sx={{ display: "flex", alignItems: "center" }}>
                                  <VietnamFlag />
                                  <Typography
                                    variant="body2"
                                    sx={{ fontSize: 16, fontWeight: 500, marginLeft: 0.5 }}
                                  >
                                    + 84
                                  </Typography>
                                </Box>
                              </InputAdornment>
                            ),
                          }}
                          sx={{ mt: 0.5 }}
                        />
                      )}
                    />
                  </Grid>

                  {/* Username and Password/Email */}
                  <Grid item xs={12} sm={6}>
                    <Typography
                      component="label"
                      variant="body2"
                      sx={{ display: "flex", fontSize: 16, fontWeight: 500, gap: 0.5 }}
                    >
                      Email
                    </Typography>
                    <Controller
                      name="email"
                      control={control}
                      rules={{
                        pattern: {
                          value: /^[\w-.]+@([\w-]+\.)+[\w-]{2,}$/,
                          message: "Email không hợp lệ",
                        },
                        maxLength: { value: 255, message: "Email không được vượt quá 255 ký tự" },
                      }}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          fullWidth
                          error={!!errors.email}
                          helperText={errors.email?.message}
                          size="small"
                          sx={{ mt: 0.5 }}
                        />
                      )}
                    />
                  </Grid>
                  {!id && (
                    <Grid item xs={12} sm={6}>
                      <Typography
                        component="label"
                        variant="body2"
                        sx={{ display: "flex", fontSize: 16, fontWeight: 500, gap: 0.5 }}
                      >
                        Mật khẩu
                        <Typography
                          sx={{
                            color: "error.main",
                            marginRight: 0.5,
                            fontSize: 16,
                            fontWeight: 500,
                          }}
                        >
                          *
                        </Typography>
                      </Typography>
                      <Controller
                        name="password"
                        control={control}
                        rules={{
                          required: "Mật khẩu là bắt buộc",
                          pattern: {
                            value: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[^A-Za-z0-9]).{8,}$/,
                            message:
                              "Mật khẩu phải có ít nhất 8 ký tự, bao gồm chữ hoa, chữ thường, số và ký tự đặc biệt",
                          },
                        }}
                        render={({ field }) => (
                          <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                            <TextField
                              {...field}
                              fullWidth
                              type={showPassword ? "text" : "password"}
                              error={!!errors.password}
                              helperText={errors.password?.message}
                              size="small"
                              sx={{ mt: 0.5 }}
                              InputProps={{
                                endAdornment: (
                                  <InputAdornment position="end">
                                    <IconButton
                                      onClick={() => setShowPassword((prev) => !prev)}
                                      edge="end"
                                    >
                                      {showPassword ? <VisibilityOff /> : <Visibility />}
                                    </IconButton>
                                  </InputAdornment>
                                ),
                                autoComplete: "new-password",
                              }}
                            />
                            <IconButton
                              onClick={() => {
                                const newPassword = generateRandomPassword();
                                field.onChange(newPassword);
                              }}
                              title="Tạo mật khẩu ngẫu nhiên"
                            >
                              <Refresh />
                            </IconButton>
                          </Box>
                        )}
                      />
                    </Grid>
                  )}
                </Grid>
              </Grid>
            </Grid>
          </Paper>
          <TitleDialog
            title="Đặt lại mật khẩu"
            open={openDialog}
            handleClose={handleCloseDialog}
            submitBtnTitle="Lưu"
            handleSubmit={handleChangePassword}
            showActionDialog={true}
          >
            <Box sx={{ display: "flex", flexDirection: "column", gap: 0.5, mt: 0.5 }}>
              <Typography sx={{ fontSize: 15, fontWeight: 500, display: "flex" }}>
                Mật khẩu <Typography sx={{ color: "red", marginLeft: 0.5 }}>*</Typography>
              </Typography>
              <TextField
                value={newPassword}
                variant="outlined"
                type={showNewPassword ? "text" : "password"}
                fullWidth
                onChange={handleNewPasswordChange}
                placeholder="Nhập mật khẩu mới"
                required
                error={!!newPasswordError}
                helperText={newPasswordError}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        onClick={() => setShowNewPassword(!showNewPassword)}
                        edge="end"
                        aria-label="toggle password visibility"
                      >
                        {showNewPassword ? <EyeOff /> : <Eye />}
                      </IconButton>
                    </InputAdornment>
                  ),
                  sx: {
                    "& input::-ms-reveal, & input::-ms-clear": {
                      display: "none",
                    },
                    height: "45px",
                  },
                }}
              />

              <Typography sx={{ marginTop: 2.5, display: "flex", fontSize: 15, fontWeight: 500 }}>
                Nhập lại mật khẩu <Typography sx={{ color: "red", marginLeft: 0.5 }}>*</Typography>
              </Typography>
              <TextField
                value={confirmPassword}
                variant="outlined"
                type={showConfirmPassword ? "text" : "password"}
                fullWidth
                onChange={handleConfirmPasswordChange}
                placeholder="Nhập lại mật khẩu mới"
                required
                error={!!confirmPasswordError}
                helperText={confirmPasswordError}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                        edge="end"
                        aria-label="toggle password visibility"
                      >
                        {showConfirmPassword ? <EyeOff /> : <Eye />}
                      </IconButton>
                    </InputAdornment>
                  ),
                  sx: {
                    "& input::-ms-reveal, & input::-ms-clear": {
                      display: "none",
                    },
                    height: "45px",
                  },
                }}
              />

              <Typography variant="caption" sx={{ color: "text.secondary" }}>
                Mật khẩu phải có ít nhất 8 ký tự, bao gồm chữ hoa, chữ thường, số và ký tự đặc biệt.
              </Typography>
            </Box>
          </TitleDialog>
          <Box sx={{ display: "flex", justifyContent: "flex-end", gap: 2, mt: 2 }}>
            <Button variant="outlined" onClick={handleBackClick}>
              Huỷ bỏ
            </Button>
            {id && (
              <Button
                variant="outlined"
                onClick={onClickOpenDialog}
                sx={{ color: "primary.main", "&:hover": { textDecoration: "underline" } }}
              >
                Đặt lại mật khẩu
              </Button>
            )}
            <Button variant="contained" type="submit">
              Lưu
            </Button>
          </Box>
        </form>
      </Box>
    </SettingLayout>
  );
}
