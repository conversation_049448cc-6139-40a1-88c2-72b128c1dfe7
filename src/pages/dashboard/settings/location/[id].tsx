import React, { useEffect, useState } from "react";
import {
  Box,
  Typography,
  IconButton,
  Container,
  Paper,
  Stack,
  TextField,
  Button,
  Select,
  MenuItem,
} from "@mui/material";
import * as Yup from "yup";

import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import SettingLayout from "@/src/components/settings/settings-page/SettingLayout";
import Link from "next/link";
import { paths } from "@/src/paths";
import Grid from "@mui/system/Grid";
import { provinceService } from "@/src/api/services/province/province.service";
import { branchService } from "@/src/api/services/branch/branch.service";
import { useFormik } from "formik";
import { Branch } from ".";
import { useRouter } from "next/router";
import { useStoreId } from "@/src/hooks/use-store-id";
interface Province {
  provinceID: string;
  provinceName: string;
}

interface District {
  districtID: string;
  districtName: string;
  provinceID: string;
}
interface Ward {
  wardID: string;
  wardName: string;
  districtID: string;
}
export interface FormValues {
  address: string;
  branchId: string;
  branchName: string;
  created: string;
  districtId: string;
  districtName: string;
  houseNumber: string;
  image: null;
  location: null;
  phoneNumber: string;
  provinceId: string;
  provinceName: string;
  updated: string;
  wardId: string;
  wardName: string;
  submit?: null;
}

const validationSchema = Yup.object().shape({
  branchName: Yup.string()
    .required("Tên điểm bán là bắt buộc")
    .max(255, "Tên điểm bán không được vượt quá 255 ký tự"),
  phoneNumber: Yup.string()
    .required("Số điện thoại là bắt buộc")
    .matches(/^[0-9]+$/, "Số điện thoại không hợp lệ")
    .min(10, "Số điện thoại phải có ít nhất 10 số")
    .max(11, "Số điện thoại không được quá 11 số"),
  address: Yup.string()
    .required("Địa chỉ là bắt buộc")
    .max(255, "Địa chỉ không được vượt quá 255 ký tự"),
  provinceId: Yup.string().required("Vui lòng chọn Tỉnh/Thành phố"),
  districtId: Yup.string().required("Vui lòng chọn Quận/Huyện"),
  wardId: Yup.string().required("Vui lòng chọn Phường/Xã"),
});

export default function UpdateLocation() {
  const [branchName, setBranchName] = useState<string>("");
  const [phoneNumber, setPhoneNumber] = useState<string>("");
  const [address, setAddress] = useState<string>("");
  // const [provinceId, setProvinceId] = useState<string>("")
  // const [districtId, setDistrictId] = useState<string>("")
  // const [wardId, setWardId] = useState<string>("")
  const [isEdit, setIsEdit] = useState(false);
  const storeId = useStoreId();

  const router = useRouter();

  useEffect(() => {
    const categoryId = router.query.id;
    if (categoryId) {
      setIsEdit(true);
    }
  }, []);

  const fetchBranchDetail = async () => {
    try {
      if (!router.query.id) return;

      const detail = (await branchService.getBranchDetail(router.query.id as string)) as any;
      if (!detail?.data) return;

      const formData: FormValues = {
        address: detail.data.address || "",
        branchId: detail.data.branchId || "",
        branchName: detail.data.branchName || "",
        created: detail.data.created || "",
        districtId: detail.data.districtId || "",
        districtName: detail.data.districtName || "",
        houseNumber: detail.data.houseNumber || "",
        image: detail.data.image || "",
        location: detail.data.location || "",
        phoneNumber: detail.data.phoneNumber || "",
        provinceId: detail.data.provinceId || "",
        provinceName: detail.data.provinceName || "",
        updated: detail.data.updated || "",
        wardId: detail.data.wardId || "",
        wardName: detail.data.wardName || "",

        submit: null,
      };

      formik.setValues(formData);
      // setIsPublished(detail.data.publish === 'Publish');

      if (detail.data.categoryIcon) {
        // setPreviewUrls([detail.data.categoryIcon]);
        // setImagePreview(detail.data.categoryIcon);
      }
    } catch (error) {
      // logger.error('Error fetching category detail:', error);
      // snackbar.error(t(tokens.common.errors.unexpected));
      router.back();
    }
  };

  useEffect(() => {
    if (router.query.id) {
      setIsEdit(true);
      fetchBranchDetail();
    }
  }, [router.query.id]);

  useEffect(() => {
    if (isEdit) {
    }
  }, [router.query.id]);
  const [selectedProvince, setSelectedProvince] = useState<{ id: string; name: string }>({
    id: "",
    name: "",
  });
  const [selectedDistrict, setSelectedDistrict] = useState<{ id: string; name: string }>({
    id: null,
    name: "",
  });
  const [selectedWard, setSelectedWard] = useState<{ id: string; name: string }>({
    id: "",
    name: "",
  });

  const [provinces, setProvinces] = useState<Province[]>([]);
  const [districts, setDistricts] = useState<District[]>([]);
  const [wards, setWards] = useState<Ward[]>([]);

  const formik = useFormik<FormValues>({
    initialValues: {
      address: "",
      branchId: "",
      branchName: "",
      created: "",
      districtId: "",
      districtName: "",
      houseNumber: "",
      image: null,
      location: null,
      phoneNumber: "",
      provinceId: "",
      provinceName: "",
      updated: "",
      wardId: "",
      wardName: "",
      submit: null,
    },
    validationSchema,
    validateOnChange: true,
    validateOnBlur: true,
    onSubmit: handleSubmit,
  });

  const fetchProvince = async () => {
    const pr = await provinceService.getProvinces();
    if (pr && pr.status === 200) {
      setProvinces(pr?.data);
    }
  };

  useEffect(() => {
    fetchProvince();
  }, []);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;

    if (name === "branchName") {
      setBranchName(value);
    } else if (name === "phoneNumber") {
      setPhoneNumber(value);
    } else if (name === "address") {
      setAddress(value);
    }
  };
  const handleChangeProvince = async (e: any) => {
    const selectedId = e.target?.value;
    const selectedProvinceObj = provinces.find((p) => p.provinceID === selectedId);

    if (selectedProvinceObj) {
      setSelectedProvince({
        id: selectedProvinceObj.provinceID,
        name: selectedProvinceObj.provinceName,
      });
    }
    const res = await provinceService.getDistricts(e.target.value);
    if (res && res?.status === 200) {
      setDistricts(res?.data);
    }
  };
  const handleChangeDistrict = async (e: any) => {
    const selectedId = e.target?.value;
    const selectedDistrictObj = districts.find((p) => p.districtID === selectedId);

    if (selectedDistrictObj) {
      setSelectedDistrict({
        id: selectedDistrictObj.districtID,
        name: selectedDistrictObj.districtName,
      });
    }
    const res = await provinceService.getWards(e.target.value);
    if (res && res?.status === 200) {
      setWards(res?.data);
    }
  };
  const handleChangeWard = (e: any) => {
    const selectedId = e.target?.value;
    const selectedWardObj = wards.find((p) => p.wardID === selectedId);

    if (selectedWardObj) {
      setSelectedWard({
        id: selectedWardObj.wardID,
        name: selectedWardObj.wardName,
      });
    }
  };

  const fetchDistrict = async () => {
    const res = await provinceService.getDistricts(formik?.values?.provinceId);
    setDistricts(res?.data);
  };
  const fetchWard = async () => {
    const res = await provinceService.getWards(formik?.values?.districtId);

    setWards(res?.data);
  };
  useEffect(() => {
    fetchDistrict();
  }, [formik.values.provinceId]);
  useEffect(() => {
    fetchWard();
  }, [formik.values.districtId]);

  async function handleSubmit(values: FormValues) {
    try {
      const data = {
        shopId: storeId,
        branchName: values.branchName,
        branchId: values.branchId,
        phoneNumber: values.phoneNumber,
        address: values.address,
        provinceId: selectedProvince?.id || values?.provinceId,
        provinceName: selectedProvince?.name || values.provinceName,
        districtId: selectedDistrict?.id?.toString() || values.districtId,
        districtName: selectedDistrict?.name || values.districtName,
        wardId: selectedWard?.id?.toString() || values.wardId,
        wardName: selectedWard?.name || values.wardName,
      };

      const res = await branchService.updateBranch(data);
      if (res && res?.status === 200) {
        router.push(paths.settings.location.index);
      }
    } catch (error) {
      console.error("Error updating branch:", error);
      // Add error handling here
    }
  }
  return (
    <SettingLayout>
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Stack direction="row" alignItems="center" spacing={2} mb={4}>
          <Link href={paths.settings.location.index} passHref>
            <IconButton sx={{ p: 0 }}>
              <ArrowBackIcon />
            </IconButton>
          </Link>
          <Typography variant="h5">Địa điểm</Typography>
        </Stack>

        <Paper sx={{ p: 2, mb: 2 }}>
          <Grid container spacing={2}>
            <Grid size={{ xs: 12, md: 4 }}>
              <Typography variant="subtitle1" fontWeight="medium" mb={1}>
                Thông tin cơ bản
              </Typography>
              <Typography variant="body2" color="text.secondary" mb={1}>
                Đặt tên ngắn cho địa điểm này để dễ nhận dạng. Tên này sẽ xuất hiện trong thông tin
                như đơn hàng và mặt hàng.
              </Typography>
              <Box
                sx={{
                  display: "flex",
                  gap: 1,
                  p: 1,
                  cursor: "pointer",
                  "&:hover": {
                    backgroundColor: "#f0f0f0",
                  },
                }}
                component="label"
              ></Box>
            </Grid>
            <Grid
              size={{ xs: 12, md: 8 }}
              sx={{ backgroundColor: "white", borderRadius: 2, p: 2, boxShadow: 3 }}
            >
              <Stack spacing={1}>
                <Grid sx={{ borderRadius: 2, p: 2, mt: 1 }}>
                  <Typography variant="body2" color="black" mb={1} display="flex">
                    Tên điểm bán <Typography sx={{ color: "red", ml: 0.5 }}>*</Typography>
                  </Typography>

                  <TextField
                    variant="outlined"
                    fullWidth
                    size="small"
                    name="branchName"
                    value={formik.values.branchName}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={formik.touched.branchName && Boolean(formik.errors.branchName)}
                    helperText={formik.touched.branchName && formik.errors.branchName}
                  />
                  <Typography variant="body2" color="black" mb={1} mt={1} display="flex">
                    Số điện thoại <Typography sx={{ color: "red", ml: 0.5 }}>*</Typography>
                  </Typography>
                  <TextField
                    variant="outlined"
                    fullWidth
                    size="small"
                    name="phoneNumber"
                    value={formik.values.phoneNumber}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={formik.touched.phoneNumber && Boolean(formik.errors.phoneNumber)}
                    helperText={formik.touched.phoneNumber && formik.errors.phoneNumber}
                  />
                </Grid>
              </Stack>
            </Grid>
          </Grid>
        </Paper>

        <Paper sx={{ p: 2, boxShadow: 3 }}>
          <Grid container spacing={2}>
            <Grid size={{ xs: 12, md: 4 }}>
              <Typography variant="subtitle1" fontWeight="medium" mb={1}>
                Địa chỉ
              </Typography>
              <Typography variant="body2" color="text.secondary" mb={1}>
                Thiết lập địa chỉ cụ thể và vị trí bản đồ.
              </Typography>
            </Grid>
            <Grid
              size={{ xs: 12, md: 8 }}
              sx={{ backgroundColor: "white", borderRadius: 2, p: 2, boxShadow: 3 }}
            >
              <Stack spacing={1}>
                <Grid sx={{ borderRadius: 2, p: 2 }}>
                  <Typography variant="body2" color="black" mb={1} display="flex">
                    Địa chỉ <Typography sx={{ color: "red", ml: 0.5 }}>*</Typography>
                  </Typography>

                  <TextField
                    variant="outlined"
                    fullWidth
                    size="small"
                    name="address"
                    value={formik.values.address}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={formik.touched.address && Boolean(formik.errors.address)}
                    helperText={formik.touched.address && formik.errors.address}
                  />

                  <Typography variant="body2" color="black" mb={1} mt={1} display="flex">
                    Tỉnh/Thành phố <Typography sx={{ color: "red", ml: 0.5 }}>*</Typography>
                  </Typography>
                  <Select
                    fullWidth
                    displayEmpty
                    size="small"
                    name="provinceId"
                    value={selectedProvince.id ? selectedProvince.id : formik.values.provinceId}
                    onChange={(e) => {
                      handleChangeProvince(e);
                      formik.setFieldValue("provinceId", e.target.value);
                    }}
                    onBlur={formik.handleBlur}
                    error={formik.touched.provinceId && Boolean(formik.errors.provinceId)}
                  >
                    <MenuItem value="" disabled>
                      Tỉnh/Thành phố
                    </MenuItem>
                    {provinces &&
                      provinces.length > 0 &&
                      provinces.map((province) => (
                        <MenuItem key={province.provinceID} value={province.provinceID}>
                          {province.provinceName}
                        </MenuItem>
                      ))}
                  </Select>
                  {formik.touched.provinceId && formik.errors.provinceId && (
                    <Typography color="error" variant="caption">
                      {formik.errors.provinceId as string}
                    </Typography>
                  )}
                  <Grid container spacing={1}>
                    <Grid size={{ xs: 6 }}>
                      <Typography variant="body2" color="black" mb={1} mt={1} display="flex">
                        Quận/Huyện <Typography sx={{ color: "red", ml: 0.5 }}>*</Typography>
                      </Typography>
                      <Select
                        fullWidth
                        displayEmpty
                        size="small"
                        name="districtId"
                        value={selectedDistrict.id ? selectedDistrict.id : formik.values.districtId}
                        onChange={(e) => {
                          handleChangeDistrict(e);
                          formik.setFieldValue("districtId", e.target.value);
                        }}
                        onBlur={formik.handleBlur}
                        error={formik.touched.districtId && Boolean(formik.errors.districtId)}
                      >
                        <MenuItem value="" disabled>
                          Quận/Huyện
                        </MenuItem>
                        {districts &&
                          districts.length > 0 &&
                          districts.map((district) => (
                            <MenuItem key={district.districtID} value={district.districtID}>
                              {district.districtName}
                            </MenuItem>
                          ))}
                      </Select>
                      {formik.touched.districtId && formik.errors.districtId && (
                        <Typography color="error" variant="caption">
                          {formik.errors.districtId as string}
                        </Typography>
                      )}
                    </Grid>
                    <Grid size={{ xs: 6 }}>
                      <Typography variant="body2" color="black" mb={1} mt={1} display="flex">
                        Phường/Xã <Typography sx={{ color: "red", ml: 0.5 }}>*</Typography>
                      </Typography>
                      <Select
                        fullWidth
                        displayEmpty
                        size="small"
                        name="wardId"
                        value={selectedWard.id ? selectedWard.id : formik.values.wardId}
                        onChange={(e) => {
                          handleChangeWard(e);
                          formik.setFieldValue("wardId", e.target.value);
                        }}
                        onBlur={formik.handleBlur}
                        error={formik.touched.wardId && Boolean(formik.errors.wardId)}
                      >
                        <MenuItem value="" disabled>
                          Phường/Xã
                        </MenuItem>
                        {wards &&
                          wards.length > 0 &&
                          wards.map((ward) => (
                            <MenuItem key={ward.wardID} value={ward.wardID}>
                              {ward.wardName}
                            </MenuItem>
                          ))}
                      </Select>
                      {formik.touched.wardId && formik.errors.wardId && (
                        <Typography color="error" variant="caption">
                          {formik.errors.wardId as string}
                        </Typography>
                      )}
                    </Grid>
                  </Grid>
                </Grid>
              </Stack>
            </Grid>
          </Grid>
        </Paper>

        <Stack direction="row" justifyContent="flex-end" spacing={1} mt={2}>
          <Button
            variant="outlined"
            sx={{
              textTransform: "none",
              px: 2,
            }}
            onClick={() => router.push(paths.settings.location.index)}
          >
            Hủy bỏ
          </Button>
          <Button
            variant="contained"
            sx={{
              textTransform: "none",
              px: 2,
            }}
            onClick={() => formik.handleSubmit()}
          >
            Lưu
          </Button>
        </Stack>
      </Container>
    </SettingLayout>
  );
}
