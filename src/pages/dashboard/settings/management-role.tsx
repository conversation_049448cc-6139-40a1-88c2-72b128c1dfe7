import React, { useEffect, useState } from "react";
import SettingLayout from "@/src/components/settings/settings-page/SettingLayout";
import { useRouter } from "next/router";
import {
  IconButton,
  Typography,
  Box,
  Button,
  Paper,
  Divider,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
} from "@mui/material";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import PersonIcon from "@mui/icons-material/Person";
import Grid from "@mui/system/Grid";
import { paths } from "@/src/paths";
import { useRole } from "@/src/api/hooks/role/use-role";
import { RoleDto } from "@/src/components/settings/settings-page/Auth";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import TruncatedText from "@/src/components/truncated-text/truncated-text";
import { formatTruncatedText } from "@/src/utils/format";

export default function ManagementRole() {
  const { listRole, deleteRole } = useRole();
  const [roles, setRoles] = React.useState<RoleDto[]>([]);
  const router = useRouter();
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [roleToDelete, setRoleToDelete] = useState<RoleDto | null>(null);

  const handleAddRoleClick = () => {
    router.push(paths.settings.addRole);
  };

  const handleBackClick = () => {
    router.push(paths.settings.auth);
  };

  const handleRoleClick = (role: RoleDto) => {
    router.push(`${paths.settings.updateRole}?roleId=${role.role.roleId}`);
  };

  const handleDeleteClick = (event: React.MouseEvent, role: RoleDto) => {
    event.stopPropagation();
    setRoleToDelete(role);
    setOpenDeleteDialog(true);
  };

  const handleCloseDeleteDialog = () => {
    setOpenDeleteDialog(false);
    setRoleToDelete(null);
  };

  const handleConfirmDelete = async () => {
    if (roleToDelete) {
      const res = await deleteRole(roleToDelete.role.roleId);
      if (res && res.status === 200) {
        fetchRoles();
        handleCloseDeleteDialog();
      }
    }
  };

  const fetchRoles = async () => {
    const res = await listRole();
    if (res && res.status === 200) {
      setRoles(res.data.data);
    }
  };
  useEffect(() => {
    fetchRoles();
  }, []);

  return (
    <SettingLayout>
      <Box sx={{ p: 2 }}>
        <Grid container alignItems="center" spacing={2} sx={{ mb: 1 }}>
          <Grid>
            <IconButton onClick={handleBackClick}>
              <ArrowBackIcon />
            </IconButton>
          </Grid>
          <Grid>
            <Typography variant="h5">Vai trò quản lý</Typography>
          </Grid>
        </Grid>
        <Grid justifyContent="center">
          <Grid size={{ xs: 12, md: 11 }}>
            <Paper sx={{ p: 3, mb: 3, boxShadow: 3 }}>
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                  mb: 3,
                }}
              >
                <Box>
                  <Typography variant="h6" gutterBottom>
                    Chi tiết quyền vai trò
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Tạo, xem và chỉnh sửa danh tính và quyền cho trang web này.{" "}
                    <Button color="primary" sx={{ p: 0, textTransform: "none" }}>
                      Mô tả quyền
                    </Button>
                  </Typography>
                </Box>
                <Button variant="contained" color="primary" onClick={handleAddRoleClick}>
                  Thêm vai trò
                </Button>
              </Box>

              <Box sx={{ mb: 2, display: "flex", alignItems: "center" }}>
                <PersonIcon />
                <Typography sx={{ ml: 1 }}>Vai trò mặc định</Typography>
              </Box>

              <Divider sx={{ my: 2 }} />
              {Array.isArray(roles) &&
                roles.length > 0 &&
                roles.map((role) => (
                  <React.Fragment key={role.role.roleId}>
                    <Box sx={{ cursor: "pointer", mb: 2 }}>
                      <Box
                        sx={{
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "space-between",
                        }}
                      >
                        <Typography>
                          <TruncatedText
                            {...formatTruncatedText({
                              text: `${role.role.roleName}  ( ${role.userCount} )`,
                              isGranted: true,
                              openInNewTab: false,
                              actionNeed: () => handleRoleClick(role),
                            })}
                          />
                        </Typography>
                        <Box>
                          <IconButton
                            aria-label="Sửa"
                            tabIndex={0}
                            onClick={() => handleRoleClick(role)}
                            sx={{ color: "blue" }}
                            size="small"
                          >
                            <EditIcon fontSize="small" />
                          </IconButton>
                          <IconButton
                            aria-label="Xoá"
                            tabIndex={0}
                            onClick={(e) => handleDeleteClick(e, role)}
                            sx={{ color: "red" }}
                            size="small"
                          >
                            <DeleteIcon fontSize="small" />
                          </IconButton>
                        </Box>
                      </Box>
                      <Typography variant="body2" color="text.secondary">
                        {role.role.roleDescription}
                      </Typography>
                    </Box>

                    <Divider sx={{ my: 2 }} />
                  </React.Fragment>
                ))}
            </Paper>
          </Grid>
        </Grid>
      </Box>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={openDeleteDialog}
        onClose={handleCloseDeleteDialog}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogTitle id="alert-dialog-title">{"Xác nhận xoá vai trò"}</DialogTitle>
        <DialogContent>
          <DialogContentText id="alert-dialog-description">
            Bạn có chắc chắn muốn xoá vai trò "{roleToDelete?.role.roleName}" không? Hành động này
            không thể hoàn tác.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDeleteDialog} color="primary">
            Huỷ
          </Button>
          <Button onClick={handleConfirmDelete} color="error" autoFocus>
            Xoá
          </Button>
        </DialogActions>
      </Dialog>
    </SettingLayout>
  );
}
