import SettingLayout from '@/src/components/settings/settings-page/SettingLayout';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Link,
  Typography,
  Box,
  Tooltip,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Grid
} from '@mui/material';
import InfoIcon from '@mui/icons-material/Info';
import ToggleOffIcon from '@mui/icons-material/ToggleOff';
import ToggleOnIcon from '@mui/icons-material/ToggleOn';
import { styled } from '@mui/material/styles';
import { useState } from 'react';
import { useRouter } from 'next/router';

const StyledTableCell = styled(TableCell)(({ theme }) => ({
  fontSize: '16px',
  padding: '20px',
  '&.MuiTableCell-head': {
    backgroundColor: theme.palette.grey[200],
    fontWeight: 600
  }
}));

const StyledLink = styled(Link)({
  color: '#1976d2',
  textDecoration: 'none',
  '&:hover': {
    textDecoration: 'underline'
  }
});

const StyledToggleButton = styled('button')(({ theme }) => ({
  background: 'none',
  border: 'none',
  cursor: 'pointer',
  padding: 0,
  display: 'flex',
  alignItems: 'center',
  '&:focus': {
    outline: 'none'
  }
}));

export default function ChannelManagement() {
  const [toggleState, setToggleState] = useState(true);
  const [triggerEvent, setTriggerEvent] = useState('');
  const router = useRouter();

  const handleToggle = () => {
    setToggleState(!toggleState);
  };

  const handleEdit = () => {
    router.push('/dashboard/settings/notification-management-add');
  };

  const handleTriggerEventChange = (event) => {
    setTriggerEvent(event.target.value);
  };

  return (
    <SettingLayout>
      <Box sx={{ p: 3 }}>
        <Typography variant="h6" sx={{ mb: 3 }}>
          Quản lý kênh
        </Typography>

        <TableContainer component={Paper} sx={{ boxShadow: 1 }}>
          <Table>
            <TableHead>
              <TableRow>
                <StyledTableCell>Kênh</StyledTableCell>
                <StyledTableCell>Trạng thái</StyledTableCell>
                <StyledTableCell>Giới hạn gửi tin</StyledTableCell>
                <StyledTableCell>Sự kiện kích hoạt</StyledTableCell>
                <StyledTableCell>Quản lý</StyledTableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              <TableRow>
                <StyledTableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>Zalo</Box>
                </StyledTableCell>
                <StyledTableCell>
                  <StyledToggleButton onClick={handleToggle}>
                    {toggleState ? (
                      <ToggleOnIcon sx={{ fontSize: 40, color: '#1976d2' }} />
                    ) : (
                      <ToggleOffIcon sx={{ fontSize: 40, color: 'grey.500' }} />
                    )}
                  </StyledToggleButton>
                </StyledTableCell>
                <StyledTableCell sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 2 }}>
                  Theo chính sách của Zalo
                  <Tooltip
                    title="Trả phí theo giá niêm yết của Zalo 165đ/tin(UID). Trường hợp không gửi được hệ thống sử dụng ZNS giá theo mẫu tin bạn đã đăng ký. Chi tiết xem tại: Link chính sách Zalo"
                    placement="top"
                  >
                    <InfoIcon sx={{ fontSize: 16, color: 'blue', ml: 1 }} />
                  </Tooltip>
                </StyledTableCell>
                <StyledTableCell>
                  <FormControl fullWidth>
                    <Select value={triggerEvent} onChange={handleTriggerEventChange}>
                      <MenuItem value="Khi khách hàng đăng ký tài khoản mới">
                        Khi khách hàng đăng ký tài khoản mới
                      </MenuItem>
                      <MenuItem value="Khi khách hàng đặt hàng">Khi khách hàng đặt hàng</MenuItem>
                      <MenuItem value="Khi khách hàng hủy đơn hàng">Khi khách hàng hủy đơn hàng</MenuItem>
                      <MenuItem value="Khi khách hàng thanh toán">Khi khách hàng thanh toán</MenuItem>
                    </Select>
                  </FormControl>
                </StyledTableCell>
                <StyledTableCell>
                  <StyledLink href="#" onClick={handleEdit}>
                    Thêm mẫu tin
                  </StyledLink>
                </StyledTableCell>
              </TableRow>
            </TableBody>
          </Table>
        </TableContainer>
      </Box>
    </SettingLayout>
  );
}
