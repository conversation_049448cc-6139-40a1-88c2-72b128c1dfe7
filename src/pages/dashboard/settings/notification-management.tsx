import SettingLayout from '@/src/components/settings/settings-page/SettingLayout';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Link,
  Typography,
  Box,
  Tooltip,
  TextField,
  Chip
} from '@mui/material';
import InfoIcon from '@mui/icons-material/Info';
import ToggleOffIcon from '@mui/icons-material/ToggleOff';
import ToggleOnIcon from '@mui/icons-material/ToggleOn';
import { styled } from '@mui/material/styles';
import { useState } from 'react';
import { useForm, FormProvider } from 'react-hook-form';
import FormDialog from '@/src/components/dialog/FormDialog';
import { useRouter } from 'next/router';
import { paths } from '@/src/paths';

const StyledTableCell = styled(TableCell)(({ theme }) => ({
  fontSize: '16px',
  padding: '20px',
  '&.MuiTableCell-head': {
    backgroundColor: theme.palette.grey[200],
    fontWeight: 600
  }
}));

const StyledLink = styled(Link)({
  color: '#1976d2',
  textDecoration: 'none',
  cursor: 'pointer',
  '&:hover': {
    textDecoration: 'underline'
  }
});

const StyledToggleButton = styled('button')(({ theme }) => ({
  background: 'none',
  border: 'none',
  cursor: 'pointer',
  padding: 0,
  display: 'flex',
  alignItems: 'center',
  '&:focus': {
    outline: 'none'
  }
}));

export default function ChannelManagement() {
  const [toggleState, setToggleState] = useState(true);
  const [openDialog, setOpenDialog] = useState(false);
  const methods = useForm();
  const router = useRouter();
  const { title, message, params } = router.query;

  const handleToggle = () => {
    setToggleState(!toggleState);
  };

  const handleOpenDialog = () => {
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  return (
    <SettingLayout>
      <Box sx={{ padding: 3 }}>
        <Typography variant="h6" sx={{ marginBottom: 3 }}>
          {title || 'Quản lý kênh'}
        </Typography>
        <TableContainer component={Paper} sx={{ boxShadow: 1 }}>
          <Table>
            <TableHead>
              <TableRow>
                <StyledTableCell>Kênh</StyledTableCell>
                <StyledTableCell>Trạng thái</StyledTableCell>
                <StyledTableCell>Giới hạn gửi tin</StyledTableCell>
                <StyledTableCell>Sự kiện kích hoạt</StyledTableCell>
                <StyledTableCell>Quản lý</StyledTableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              <TableRow>
                <StyledTableCell>Thông báo hệ thống</StyledTableCell>
                <StyledTableCell>Bật theo mặc định</StyledTableCell>
                <StyledTableCell>Không giới hạn</StyledTableCell>
                <StyledTableCell>Khi khách hàng đăng ký tài khoản mới.</StyledTableCell>
                <StyledTableCell>
                  <StyledLink onClick={handleOpenDialog}>Sửa mẫu tin</StyledLink>
                </StyledTableCell>
              </TableRow>
              <TableRow>
                <StyledTableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>Zalo</Box>
                </StyledTableCell>
                <StyledTableCell>
                  <StyledToggleButton onClick={handleToggle}>
                    {toggleState ? (
                      <ToggleOnIcon sx={{ fontSize: 40, color: '#1976d2' }} />
                    ) : (
                      <ToggleOffIcon sx={{ fontSize: 40, color: 'grey.500' }} />
                    )}
                  </StyledToggleButton>
                </StyledTableCell>
                <StyledTableCell sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  Theo chính sách của Zalo
                  <Tooltip
                    title="Trả phí theo giá niêm yết của Zalo 165đ/tin(UID). Trường hợp không gửi được hệ thống sử dụng ZNS giá theo mẫu tin bạn đã đăng ký. Chi tiết xem tại: Link chính sách Zalo"
                    placement="top"
                  >
                    <InfoIcon sx={{ fontSize: 16, color: 'blue', marginLeft: 1 }} />
                  </Tooltip>
                </StyledTableCell>
                <StyledTableCell>Khi khách hàng đăng ký tài khoản mới.</StyledTableCell>
                <StyledTableCell>
                  <StyledLink onClick={() => router.push(paths.settings.managementAdd)}>Sửa mẫu tin</StyledLink>
                </StyledTableCell>
              </TableRow>
            </TableBody>
          </Table>
        </TableContainer>
      </Box>
      <FormProvider {...methods}>
        <FormDialog
          open={openDialog}
          onClose={handleCloseDialog}
          title=""
          onSubmit={() => { }}
          submitBtnTitle="Xác nhận"
        >
          <Box sx={{ padding: 2 }}>
            <Typography variant="h6" sx={{ marginBottom: 2 }}>
              {title}
            </Typography>
            <TextField
              multiline
              rows={4}
              fullWidth
              value={message}
              InputProps={{
                readOnly: true
              }}
              sx={{ marginBottom: 3 }}
            />
            {params && typeof params === 'string' && JSON.parse(params).length > 0 && (
              <>
                <Typography variant="h6" sx={{ marginBottom: 2 }}>
                  Tham số:
                </Typography>
                <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                  {JSON.parse(params).map((param, index) => (
                    <Chip key={index} label={param} />
                  ))}
                </Box>
              </>
            )}
          </Box>
        </FormDialog>
      </FormProvider>
    </SettingLayout>
  );
}
