import React, { useState } from 'react';
import SettingLayout from '@/src/components/settings/settings-page/SettingLayout';
import { Box, Tab, Tabs } from '@mui/material';
import NotificationSettings from '@/src/components/settings/settings-page/Notification';
import NotificationCustomerCare from '@/src/components/settings/settings-page/customerCare';
import NotificationCommunication from '@/src/components/settings/settings-page/communication';
import NotificationPartner from '@/src/components/settings/settings-page/NotificationPartner';

const TabsComponent = ({ tabValue, handleTabChange }) => (
  <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
    <Tabs
      value={tabValue}
      onChange={handleTabChange}
      variant="scrollable"
      scrollButtons="auto"
      sx={{
        '& .MuiTab-root': {
          textTransform: 'none',
          fontSize: { xs: '1rem', md: '1.125rem' },
          fontWeight: 600,
          minHeight: 48,
          px: 2
        },
        '& .Mui-selected': {
          color: 'primary.main'
        },
        '& .MuiTabs-indicator': {
          backgroundColor: 'primary.main'
        }
      }}
    >
      <Tab label="Thông báo giao dịch" />
      <Tab label="Chăm sóc khách hàng" />
      <Tab label="Hậu mãi, truyền thông" />
      <Tab label="Thông báo đối tác" />
    </Tabs>
  </Box>
);

const CustomerCare = () => <div>Chăm sóc khách hàng</div>;
const Marketing = () => <div>Hậu mãi, truyền thông</div>;
const PartnerNotification = () => <div>Thông báo đối tác</div>;

export default function SettingNotification() {
  const [tabValue, setTabValue] = useState(0);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  return (
    <SettingLayout>
      <Box sx={{ p: 3 }}>
        <TabsComponent tabValue={tabValue} handleTabChange={handleTabChange} />
        {tabValue === 0 && <NotificationSettings />}
        {tabValue === 1 && <NotificationCustomerCare />}
        {tabValue === 2 && <NotificationCommunication />}
        {tabValue === 3 && <NotificationPartner />}
      </Box>
    </SettingLayout>
  );
}
