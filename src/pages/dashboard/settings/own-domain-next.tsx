import React from 'react';
import SettingLayout from '@/src/components/settings/settings-page/SettingLayout';
import { useRouter } from 'next/router';
import { IconButton, Typography, Box, Button, Paper, Divider } from '@mui/material';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import LanguageIcon from '@mui/icons-material/Language';
import { paths } from '@/src/paths';
import Grid from '@mui/system/Grid';

const Header = ({ handleBackClick }) => (
  <Grid container spacing={2}>
    <Grid>
      <IconButton onClick={handleBackClick}>
        <ArrowBackIcon />
      </IconButton>
    </Grid>
    <Grid>
      <Typography variant="h5">Tên miền riêng của bạn</Typography>
    </Grid>
  </Grid>
);
const DomainDetails = () => (
  <Grid container direction="column" alignItems="center" spacing={3}>
    <Grid size={{ xs: 12, md: 10 }}>
      <Paper sx={{ p: 2, boxShadow: 2, borderRadius: 2, mt: 2 }}>
        <Grid container direction="column" spacing={3}>
          <Grid>
            <Grid container justifyContent="space-between" alignItems="center">
              <Typography variant="subtitle1">Tên miền</Typography>
              <Typography>chỉnh sửa</Typography>
            </Grid>
          </Grid>
          <Grid>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <LanguageIcon color="action" fontSize="large" />
              <Typography variant="h6">syncsoft.vn</Typography>
            </Box>
          </Grid>
          <Grid>
            <Divider />
          </Grid>
          <Grid>
            <Typography variant="subtitle1" gutterBottom>
              Kết nối với tên miền của bạn
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              Bạn cần đăng nhập bằng tài khoản nhà cung cấp và thay đổi cài đặt trước khi có thể kết nối với tên miền
              của mình. Xác minh kết nối để đảm bảo tên miền của bạn được thiết lập đúng.
            </Typography>
          </Grid>
        </Grid>
      </Paper>
    </Grid>
  </Grid>
);
const Footer = ({ handleNextStepClick }) => (
  <Grid container justifyContent="flex-end" size={{ xs: 12, md: 10 }}>
    <Button variant="contained" onClick={handleNextStepClick}>
      Xác minh kết nối
    </Button>
  </Grid>
);
export default function YourDomainNameNext() {
  const router = useRouter();
  const handleBackClick = () => {
    router.back();
  };
  const handleNextStepClick = () => {
    router.push(paths.settings.domainLink);
  };
  return (
    <SettingLayout>
      <Header handleBackClick={handleBackClick} />
      <DomainDetails />
      <Footer handleNextStepClick={handleNextStepClick} />
    </SettingLayout>
  );
}
