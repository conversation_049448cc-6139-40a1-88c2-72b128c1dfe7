import React from 'react';
import SettingLayout from '@/src/components/settings/settings-page/SettingLayout';
import { useRouter } from 'next/router';
import { IconButton, Typography, TextField, Button, Box } from '@mui/material';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import { paths } from '@/src/paths';
import Grid from '@mui/system/Grid';

export default function YourDomainName() {
  const router = useRouter();

  const handleBackClick = () => {
    router.back();
  };

  const handleCancelClick = () => {
    router.back();
  };

  const handleNextStepClick = () => {
    router.push(paths.settings.ownDomainNameNextStep);
  };

  return (
    <SettingLayout>
      <Grid container alignItems="center" spacing={2}>
        <Grid>
          <IconButton onClick={handleBackClick}>
            <ArrowBackIcon />
          </IconButton>
        </Grid>
        <Grid>
          <Typography variant="h5">Tên miền riêng của bạn</Typography>
        </Grid>
      </Grid>
      <Grid container justifyContent="center">
        <Grid size={{ xs: 12, md: 10 }}>
          <Box sx={{ p: 2, mt: 2, backgroundColor: 'white', boxShadow: 3, borderRadius: 2 }}>
            <Typography variant="h6" sx={{ mt: 2 }}>
              Tên miền
            </Typography>
            <TextField fullWidth margin="normal" variant="outlined" label="Nhập tên miền của bạn" />
            <Typography variant="body2" sx={{ color: 'text.secondary', ml: 1 }}>
              nhập tên miền mà bạn muốn kết nối
            </Typography>
            <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
              <Button variant="outlined" onClick={handleCancelClick} sx={{ mr: 2 }}>
                Hủy bỏ
              </Button>
              <Button variant="contained" onClick={handleNextStepClick}>
                Bước tiếp theo
              </Button>
            </Box>
          </Box>
        </Grid>
      </Grid>
    </SettingLayout>
  );
}
