import React from "react";
import DashboardLayout from "../../../../layouts/dashboard";
import Tabs from "@mui/material/Tabs";
import Tab from "@mui/material/Tab";
import Box from "@mui/material/Box";
import { Drawer, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>Subheader, Typography } from "@mui/material";
import Payment from "../../../../components/settings/settings-page/Payment";
import Grid from "@mui/material/Grid2";
import { List, ListItem, ListItemText, ListItemIcon } from "@mui/material";
import Link from "next/link";
import { useRouter } from "next/router";
import SettingLayout from "@/src/components/settings/settings-page/SettingLayout";

export default function SettingPayment() {
  return (
    <SettingLayout>
      <Payment />
    </SettingLayout>
  );
}
