import { useState, useEffect } from "react";
import { useRouter } from "next/router";
import { useTranslation } from "react-i18next";
import { useSnackbar } from "@/src/hooks/use-snackbar";
import { useStoreId } from "@/src/hooks/use-store-id";
import { paths } from "@/src/paths";
import { StorageService } from "nextjs-api-lib";
import {
  Box,
  Button,
  Checkbox,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  TextField,
  TablePagination,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Chip,
  Tooltip,
  InputAdornment,
} from "@mui/material";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import AddIcon from "@mui/icons-material/Add";
import { tokens } from "@/src/locales/tokens";
import { formatDateTimeDisplay } from "@/src/utils/date-utils";
import { useArticleCategory } from "@/src/api/hooks/dashboard/store/use-article-category";
import { EmptyState } from "@/src/components/common/empty-state";
import { LoadingState } from "@/src/components/common/loading-state";
import { rowPerPageOptionsDefault } from "@/src/types/store/enum-type";
import { useAllPermissions } from "@/src/api/hooks/auth/use-all-permissions";
import { PERMISSION_TYPE_ENUM } from "@/src/constants/constant";
import { usePathname } from "next/navigation";
import dayjs from "dayjs";
import { useDebounce } from "@/src/hooks/use-debounce";
import { ArticleCategoryDto } from "@/src/api/services/dashboard/store/article-category.service";
import TruncatedText from "@/src/components/truncated-text/truncated-text";
import { Search } from "@mui/icons-material";
import { formatTruncatedText } from "@/src/utils/format";

const ArticleCategories = () => {
  const { t } = useTranslation();
  const pathname = usePathname();
  const snackbar = useSnackbar();
  const { getArticleCategory, deleteArticleCategory, loading } = useArticleCategory();
  const [selected, setSelected] = useState<string[]>([]);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [openMultipleDeleteDialog, setOpenMultipleDeleteDialog] = useState(false);
  const [categoryToDelete, setCategoryToDelete] = useState<any>(null);
  const router = useRouter();
  const [categories, setCategories] = useState<ArticleCategoryDto[]>([]);
  const [totalCount, setTotalCount] = useState(0);
  const [searchTerm, setSearchTerm] = useState("");
  const [isInitialLoading, setIsInitialLoading] = useState(true);
  const storeId = useStoreId();
  const { permissions } = useAllPermissions();
  const debouncedSearchValue = useDebounce(searchTerm, 500);

  const isGranted = (url, permission) => {
    if (url.endsWith("/")) {
      url = url.slice(0, -1);
    }
    const matchingUrl = Object.keys(permissions).find((key) => key === url);
    if (!matchingUrl) return false;
    return permissions[matchingUrl]?.includes(permission) || false;
  };
  useEffect(() => {
    if (storeId) {
      setIsInitialLoading(true);
      fetchCategories().finally(() => {
        setIsInitialLoading(false);
      });
    }
  }, [page, rowsPerPage, debouncedSearchValue, storeId]);

  const fetchCategories = async () => {
    try {
      const response = await getArticleCategory(
        page * rowsPerPage,
        rowsPerPage,
        debouncedSearchValue
      );
      setCategories(response.data.data || []);
      setTotalCount(response.data.total || 0);
    } catch (error) {
      snackbar.error(t("common.errorOccurred"));
    }
  };

  const handleSelectAll = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.checked) {
      setSelected(categories.map((category) => category.articleCategoryId));
    } else {
      setSelected([]);
    }
  };

  const handleSelectOne = (event: React.ChangeEvent<HTMLInputElement>, id: string) => {
    if (event.target.checked) {
      setSelected([...selected, id]);
    } else {
      setSelected(selected.filter((item) => item !== id));
    }
  };

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleSearch = (event) => {
    setSearchTerm(event.target.value);
    setPage(0);
  };

  const handleAddNew = () => {
    router.push(paths.dashboard.store.createArticleCategory);
  };

  const handleEdit = (category) => {
    StorageService.set("editCategoryData", {
      categoryName: category.categoryName,
      typePublish: category.typePublish,
    });
    router.push({
      pathname: paths.dashboard.store.createArticleCategory,
      query: { id: category.articleCategoryId, edit: true },
    });
  };

  const handleDelete = (category: any) => {
    setCategoryToDelete(category);
    setOpenDeleteDialog(true);
  };

  const confirmDelete = async () => {
    try {
      await deleteArticleCategory(categoryToDelete.articleCategoryId);
      snackbar.success(t(tokens.contentManagement.articleCategory.delete.successMessage));

      if (selected.includes(categoryToDelete.articleCategoryId)) {
        setSelected(selected.filter((id) => id !== categoryToDelete.articleCategoryId));
      }

      setOpenDeleteDialog(false);
      setCategoryToDelete(null);
      fetchCategories();
    } catch (error) {
      snackbar.error(t(tokens.contentManagement.articleCategory.delete.errorMessage));
    }
  };

  const handleDeleteMultiple = () => {
    setOpenMultipleDeleteDialog(true);
  };

  const confirmDeleteMultiple = async () => {
    try {
      await Promise.all(selected.map((id) => deleteArticleCategory(id)));
      snackbar.success(`Xóa ${selected.length} danh mục thành công`);
      setSelected([]);
      setOpenMultipleDeleteDialog(false);
      fetchCategories();
    } catch (error) {
      snackbar.error("Xóa danh mục thất bại");
    }
  };

  if (!storeId) {
    return null;
  }

  return (
    <Box sx={{ p: 3 }}>
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          flexDirection: { xs: "column", sm: "row" },
          mb: 3,
        }}
      >
        <Box sx={{ display: "flex", gap: 2 }}>
          <Tooltip
            title={!isGranted(pathname, PERMISSION_TYPE_ENUM.Add) ? "Bạn không có quyền thêm" : ""}
          >
            <span>
              <Button
                sx={{ background: "#2654FE", textTransform: "none", mb: { xs: 1, sm: 0 } }}
                variant="contained"
                startIcon={<AddIcon />}
                onClick={handleAddNew}
                disabled={!isGranted(pathname, PERMISSION_TYPE_ENUM.Add)}
              >
                {t(tokens.contentManagement.articleCategory.addNew)}
              </Button>
            </span>
          </Tooltip>
          {selected.length > 0 && (
            <Tooltip
              title={
                !isGranted(pathname, PERMISSION_TYPE_ENUM.Delete) ? "Bạn không có quyền xoá" : ""
              }
            >
              <span>
                <Button
                  variant="outlined"
                  color="error"
                  startIcon={<DeleteIcon />}
                  onClick={handleDeleteMultiple}
                  disabled={!isGranted(pathname, PERMISSION_TYPE_ENUM.Delete)}
                >
                  {t(tokens.common.delete)} ({selected.length})
                </Button>
              </span>
            </Tooltip>
          )}
        </Box>

        <TextField
          placeholder={t(tokens.contentManagement.articleCategory.search)}
          value={searchTerm}
          onChange={handleSearch}
          variant="outlined"
          size="small"
          sx={{ width: "300px" }}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Search />
              </InputAdornment>
            ),
          }}
        />
      </Box>

      {(loading && page === 0) || isInitialLoading ? (
        <LoadingState />
      ) : !isInitialLoading && categories.length === 0 ? (
        <EmptyState
          title={t(tokens.contentManagement.articleCategory.emptyState.title)}
          subtitle={t(tokens.contentManagement.articleCategory.emptyState.subtitle)}
          buttonText={t(tokens.contentManagement.articleCategory.emptyState.button)}
          onAddClick={handleAddNew}
          isGranted={isGranted}
        />
      ) : (
        <>
          <Box sx={{ overflowX: "auto" }}>
            <Table sx={{ minWidth: 1000 }}>
              <TableHead>
                <TableRow>
                  <TableCell padding="checkbox" width="5%">
                    <Checkbox
                      checked={categories.length > 0 && selected.length === categories.length}
                      indeterminate={selected.length > 0 && selected.length < categories.length}
                      onChange={handleSelectAll}
                    />
                  </TableCell>
                  <TableCell width="5%" sx={{ fontWeight: 600 }}>
                    STT
                  </TableCell>
                  <TableCell sx={{ fontWeight: 600, width: 530 }}>Tên danh mục</TableCell>
                  <TableCell width="20%" sx={{ fontWeight: 600 }}>
                    Hình ảnh
                  </TableCell>
                  <TableCell width="20%" sx={{ fontWeight: 600 }}>
                    Ngày tạo
                  </TableCell>
                  <TableCell width="15%" sx={{ fontWeight: 600 }}>
                    Trạng thái
                  </TableCell>
                  <TableCell width="15%" align="center" sx={{ fontWeight: 600 }}>
                    Thao tác
                  </TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {categories.map((category, index) => (
                  <TableRow key={category.articleCategoryId}>
                    <TableCell padding="checkbox">
                      <Checkbox
                        checked={selected.includes(category.articleCategoryId)}
                        onChange={(event) => handleSelectOne(event, category.articleCategoryId)}
                      />
                    </TableCell>
                    <TableCell>{page * rowsPerPage + index + 1}</TableCell>
                    <TableCell>
                      <TruncatedText
                        {...formatTruncatedText({
                          text: category.categoryName,
                          isGranted: isGranted(pathname, PERMISSION_TYPE_ENUM.Edit),
                          openInNewTab: false,
                          actionNeed: () => handleEdit(category),
                          width: "530px",
                        })}
                      />
                    </TableCell>
                    <TableCell>
                      <img
                        src={category?.image?.link || ""}
                        alt={category.categoryName}
                        style={{
                          borderRadius: 999,
                          height: "50px",
                          width: "50px",
                          objectFit: "cover",
                        }}
                      />
                    </TableCell>
                    <TableCell>{dayjs(category.created).format("DD/MM/YYYY HH:mm:ss")}</TableCell>
                    <TableCell>
                      <Chip
                        label={category.typePublish === "Publish" ? "Xuất bản" : "Không xuất bản"}
                        color={category.typePublish === "Publish" ? "success" : "default"}
                        sx={{
                          backgroundColor:
                            category.typePublish === "Publish"
                              ? "rgba(84, 214, 44, 0.16)"
                              : "rgba(145, 158, 171, 0.16)",
                          color:
                            category.typePublish === "Publish"
                              ? "rgb(34, 154, 22)"
                              : "rgb(99, 115, 129)",
                        }}
                      />
                    </TableCell>
                    <TableCell align="center">
                      <Box sx={{ display: "flex", justifyContent: "center", gap: 1 }}>
                        <Tooltip
                          title={
                            !isGranted(pathname, PERMISSION_TYPE_ENUM.Edit)
                              ? "Bạn không có quyền sửa"
                              : "Sửa danh mục bài viết"
                          }
                        >
                          <span>
                            <IconButton
                              onClick={() => handleEdit(category)}
                              size="small"
                              sx={{ color: "primary.main" }}
                              disabled={!isGranted(pathname, PERMISSION_TYPE_ENUM.Edit)}
                            >
                              <EditIcon fontSize="small" />
                            </IconButton>
                          </span>
                        </Tooltip>
                        <Tooltip
                          title={
                            !isGranted(pathname, PERMISSION_TYPE_ENUM.Delete)
                              ? "Bạn không có quyền xoá"
                              : "Xoá danh mục bài viết"
                          }
                        >
                          <span>
                            <IconButton
                              onClick={() => handleDelete(category)}
                              size="small"
                              sx={{ color: "error.main" }}
                              disabled={!isGranted(pathname, PERMISSION_TYPE_ENUM.Delete)}
                            >
                              <DeleteIcon fontSize="small" />
                            </IconButton>
                          </span>
                        </Tooltip>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </Box>
          <TablePagination
            labelRowsPerPage="Số hàng mỗi trang"
            component="div"
            count={totalCount}
            page={page}
            onPageChange={handleChangePage}
            rowsPerPage={rowsPerPage}
            onRowsPerPageChange={handleChangeRowsPerPage}
            rowsPerPageOptions={rowPerPageOptionsDefault}
          />
        </>
      )}
      <Dialog
        open={openDeleteDialog}
        onClose={() => setOpenDeleteDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Xác nhận xóa</DialogTitle>
        <DialogContent>
          <DialogContentText>
            {`Bạn có chắc chắn muốn xóa danh mục "${categoryToDelete?.categoryName || ""}" không?`}
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => setOpenDeleteDialog(false)}
            variant="outlined"
            sx={{ minWidth: 100 }}
          >
            Hủy
          </Button>
          <Button
            onClick={confirmDelete}
            color="error"
            variant="contained"
            autoFocus
            sx={{ minWidth: 100 }}
          >
            Xóa
          </Button>
        </DialogActions>
      </Dialog>

      <Dialog
        open={openMultipleDeleteDialog}
        onClose={() => setOpenMultipleDeleteDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Xác nhận xóa nhiều danh mục</DialogTitle>
        <DialogContent>
          <DialogContentText>
            {`Bạn có chắc chắn muốn xóa ${selected.length} danh mục đã chọn không?`}
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => setOpenMultipleDeleteDialog(false)}
            variant="outlined"
            sx={{ minWidth: 100 }}
          >
            Hủy
          </Button>
          <Button
            onClick={confirmDeleteMultiple}
            color="error"
            variant="contained"
            autoFocus
            sx={{ minWidth: 100 }}
          >
            Xóa
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ArticleCategories;
