import { Box, Card, Tab, Tabs } from "@mui/material";
import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { useRouter } from "next/router";
import PopupAds from "./popup-ads";
import ArticleCategories from "./article-categories";
import Articles from "./articles";
import DashboardLayout from "../../../../layouts/dashboard";
import TitleTypography from "@/src/components/title-typography/title-typography";
import { tokens } from "@/src/locales/tokens";
import { Padding } from "@/src/styles/CommonStyle";

const TAB_STORAGE_KEY = "contentManagementTab";

const ContentManagement = () => {
  const { t } = useTranslation();
  const router = useRouter();
  const [tabIndex, setTabIndex] = useState(() => {
    const { tab } = router.query;
    if (tab) {
      const index = parseInt(tab as string, 10);
      if (!isNaN(index) && index >= 0 && index <= 2) {
        return index;
      }
    }

    const savedTab = localStorage.getItem(TAB_STORAGE_KEY);
    if (savedTab !== null) {
      return parseInt(savedTab, 10);
    }

    return 0;
  });

  useEffect(() => {
    const { tab } = router.query;
    if (tab) {
      const index = parseInt(tab as string, 10);
      if (!isNaN(index) && index >= 0 && index <= 2) {
        setTabIndex(index);
        localStorage.setItem(TAB_STORAGE_KEY, index.toString());
      }
    }
  }, [router.query]);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabIndex(newValue);
    localStorage.setItem(TAB_STORAGE_KEY, newValue.toString());
    router.push(
      {
        pathname: router.pathname,
        query: { ...router.query, tab: newValue },
      },
      undefined,
      { shallow: true }
    );
  };
  // Get title by tab index
  const getTitle = (index: number) => {
    const titles = [
      "Quản lý nội dung/Popup",
      "Quản lý nội dung/Popup/Tạo mới",
      "Quản lý nội dung/Danh mục bài viết",
    ];
    return titles[index] || "";
  };

  return (
    <DashboardLayout>
      <Box sx={{ padding: Padding, paddingTop: "20px" }}>
        <TitleTypography
          sx={{
            fontSize: "20px !important",
            lineHeight: "1",
            fontWeight: "700",
            paddingBottom: "20px",
            marginBottom: "16px",
            borderBottom: "1px solid #bdbdbd",
          }}
        >
          {getTitle(tabIndex)}
        </TitleTypography>
        <Card
          sx={{
            p: 2,
            mt: 2,
            "& .MuiTabs-indicator": {
              background: "#2654fe",
            },
          }}
        >
          <Tabs value={tabIndex} onChange={handleTabChange} sx={{ mb: 2 }}>
            {["popupAds", "articleCategories", "articles"].map((tab, index) => (
              <Tab
                sx={{
                  textTransform: "none !important",
                  color: "#000",
                  fontWeight: "500",
                  "&.Mui-selected": {
                    color: "#2654fe",
                    fontWeight: "bold",
                  },
                }}
                key={tab}
                label={t(tokens.contentManagement.tabs[tab])}
              />
            ))}
          </Tabs>
          {tabIndex === 0 && <PopupAds />}
          {tabIndex === 1 && <ArticleCategories />}
          {tabIndex === 2 && <Articles />}
        </Card>
      </Box>
    </DashboardLayout>
  );
};

export default ContentManagement;
