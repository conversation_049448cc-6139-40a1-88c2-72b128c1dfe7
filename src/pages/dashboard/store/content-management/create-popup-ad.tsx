import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import {
  Box,
  Button,
  Card,
  Checkbox,
  Stack,
  Grid,
  TextField,
  Typography,
  IconButton,
  FormControlLabel,
  CircularProgress,
} from "@mui/material";
import { useTheme } from "@mui/material/styles";
import DashboardLayout from "../../../../layouts/dashboard";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import { useRouter } from "next/router";
import { tokens } from "@/src/locales/tokens";
import TitleTypography from "@/src/components/title-typography/title-typography";
import { DateTimePicker } from "@/src/components/date-time-picker";
import CustomSwitch from "@/src/components/custom-switch";
import SvgIcon from "@mui/material/SvgIcon";
import XIcon from "@mui/icons-material/Close";
import { useFormik } from "formik";
import * as Yup from "yup";
import { useStoreId } from "@/src/hooks/use-store-id";
import { FileDropzone } from "@/src/components/file-dropzone";
import useSnackbar from "@/src/hooks/use-snackbar";
import { paths } from "@/src/paths";
import { formatDateTimeForApi } from "@/src/utils/date-utils";
import { useAdvertise } from "@/src/api/hooks/dashboard/store/use-advertise";
import Autocomplete from "@mui/material/Autocomplete";
import { useArticle } from "@/src/api/hooks/dashboard/store/use-article";
import { isValidImageFile } from "../../product/category-management/create";
import { useProductCategory } from "@/src/api/hooks/dashboard/product/use-category";
import { useUserCategory } from "@/src/api/hooks/user/use-user-category";
import { Padding } from "@/src/styles/CommonStyle";
import dayjs from "dayjs";
import { FILE_SIZE_2MB, MAX_FILE_IMAGE } from "@/src/constants/constant";
import { CommonMediaUpload } from "@/src/components/common-media-upload";
import { ExistingMediaFile, scrollToTop } from "../../product/product-management/create/create";
import { CreateFileGroupRequest, GetGroupFileRequest, RefType } from "@/src/api/types/media.types";
import { useMedia } from "@/src/api/hooks/media/use-media";
import { ImageProcessor } from "@/src/utils/image-processor";
import { FileType } from "@/src/constants/file-types";

interface FormValues {
  name: string;
  image: File | null;
  link: string;
  startDate: Date | null;
  endDate: Date | null;
  typePosition: string;
  typeDisplay: string;
  typePublish: string;
  submit: string | null;
  articleId?: string;
  categoryId?: string;
}

const CreatePopupAd: React.FC = () => {
  const { t } = useTranslation();
  const theme = useTheme();
  const router = useRouter();
  const snackbar = useSnackbar();
  const { advertiseId } = router.query;
  const [isEdit, setIsEdit] = useState(false);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [showPopup, setShowPopup] = useState<boolean>(true);
  const storeId = useStoreId();
  const { createAdvertise, updateAdvertise, getAdvertiseDetail, updateImage } = useAdvertise();
  const { getArticle } = useArticle();
  const { getUserCategory } = useUserCategory();
  const [categoryLoading, setCategoryLoading] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isPublished, setIsPublished] = useState(false);
  const [displayPosition, setDisplayPosition] = useState("NONE");
  const [displayType, setDisplayType] = useState("NONE");
  const [adDetail, setAdDetail] = useState<any | null>(null);
  const [articleOptions, setArticleOptions] = useState([]);
  const [articleSearch, setArticleSearch] = useState("");
  const [articleLoading, setArticleLoading] = useState(false);
  const [categoryOptions, setCategoryOptions] = useState([]);
  const [categorySearch, setCategorySearch] = useState("");
  const [selectedType, setSelectedType] = useState<"LINK" | "ARTICLE" | "CATEGORY" | "NONE">(
    "LINK"
  );
  const [existingFiles, setExistingFiles] = useState<ExistingMediaFile[]>([]);
  const [localFiles, setLocalFiles] = useState<File[]>([]);
  const [defaultGroupId, setDefaultGroupId] = useState<string>("");
  const { getGroups, uploadFile } = useMedia();
  const [errorMsg, setErrorMsg] = useState<string>("");

  useEffect(() => {
    const fetchDefaultGroup = async () => {
      try {
        const data: GetGroupFileRequest = {
          ShopId: storeId,
          Skip: 0,
          Limit: 10,
        };
        const response = await getGroups(data);
        if (response.data?.data?.length > 0) {
          setDefaultGroupId(response.data.data[0].groupFileId);
        }
      } catch (error) {}
    };
    fetchDefaultGroup();
  }, [storeId]);
  useEffect(() => {
    if (advertiseId) {
      setIsEdit(true);
    } else {
      setIsEdit(false);
    }
  }, [advertiseId]);

  const validationSchema = Yup.object({
    name: Yup.string()
      .required(t(tokens.contentManagement.popupAds.create.form.nameRequired))
      .max(255, t(tokens.contentManagement.popupAds.create.form.nameMaxLength)),
    // image: Yup.mixed().required(t(tokens.contentManagement.popupAds.create.form.imageRequired)),
    link: Yup.string()
      // .required(t(tokens.contentManagement.popupAds.create.form.linkRequired))
      .url(t(tokens.contentManagement.popupAds.create.form.linkInvalid))
      .max(255, t(tokens.contentManagement.popupAds.create.form.linkMaxLength)),
    startDate: Yup.date().required(t(tokens.contentManagement.popupAds.create.form.dateRequired)),
    endDate: Yup.date()
      .required(t(tokens.contentManagement.popupAds.create.form.dateRequired))
      .min(Yup.ref("startDate"), t(tokens.contentManagement.popupAds.create.form.endDateInvalid)),
  });

  const formik = useFormik<FormValues>({
    initialValues: {
      name: "",
      image: null,
      link: "",
      startDate: null,
      endDate: null,
      typePosition: "HOME",
      typeDisplay: "NONE",
      typePublish: "UnPublish",
      submit: null,
      articleId: "",
      categoryId: "",
    },
    validationSchema,
    onSubmit: handleSubmit,
  });

  useEffect(() => {
    const fetchAdvertiseDetail = async () => {
      if (isEdit && advertiseId) {
        try {
          setIsLoading(true);
          const response = await getAdvertiseDetail(advertiseId as string);
          const detail = response.data;
          let type = "LINK";
          if (detail.typeNavigate === "ARTICLE") type = "ARTICLE";
          else if (detail.typeNavigate === "CATEGORY") type = "CATEGORY";
          else if (detail.typeNavigate === "NONE") type = "NONE";
          setSelectedType(type as "LINK" | "ARTICLE" | "CATEGORY" | "NONE");

          formik.setValues({
            name: detail.name || "",
            image: detail.image || null,
            link: detail.link || "",
            startDate: detail.startDate ? new Date(detail.startDate) : null,
            endDate: detail.endDate ? new Date(detail.endDate) : null,
            typePosition: "HOME",
            typeDisplay: detail.typeDisplay || "NONE",
            typePublish: detail.typePublish || "UnPublish",
            submit: null,
            articleId: detail.articleId || "",
            categoryId: detail.categoryId || "",
          });
          setExistingFiles([detail.image]);
          setIsPublished(detail.typePublish === "Publish");
          setDisplayPosition("HOME");
          setDisplayType(detail.typeDisplay || "NONE");
          setImagePreview(detail.image?.link || null);
        } catch (error) {
          snackbar.error(t(tokens.contentManagement.popupAds.edit.error));
        } finally {
          setIsLoading(false);
        }
      }
    };

    fetchAdvertiseDetail();
  }, [isEdit, advertiseId]);

  useEffect(() => {
    if (!storeId) return;
    setArticleLoading(true);
    (async () => {
      try {
        const res = await getArticle(0, 20, articleSearch);
        if (res?.data?.data) {
          setArticleOptions(res.data.data);
        }
      } catch (error) {
      } finally {
        setArticleLoading(false);
      }
    })();
  }, [storeId, articleSearch]);

  useEffect(() => {
    if (selectedType !== "CATEGORY" || !storeId) return;
    setCategoryLoading(true);
    (async () => {
      try {
        const res = await getUserCategory(0, 99, storeId);
        setCategoryOptions(res?.data?.data || []);
      } catch (error) {
        snackbar.error("Lỗi tải danh mục");
      } finally {
        setCategoryLoading(false);
      }
    })();
  }, [selectedType, storeId]);

  async function handleSubmit(values: FormValues) {
    if (localFiles.length + existingFiles.length === 0) {
      setErrorMsg("Hãy chọn ít nhất 1 ảnh");
      scrollToTop();
      return;
    }
    let uploadedFiles = [];
    if (localFiles.length > 0) {
      for (const file of localFiles) {
        const processedFile = await ImageProcessor.processImage(file);
        const data: CreateFileGroupRequest = {
          FileUpload: processedFile,
          ShopId: storeId,
          GroupFileId: defaultGroupId,
          RefType: RefType.PopupAds,
          RefId: "",
        };
        const response = await uploadFile(data);
        uploadedFiles.push(response.data);
      }
    }
    const newImages = uploadedFiles.map((file) => ({
      type: file.type === "IMAGE" ? FileType.IMAGE : FileType.VIDEO,
      link: file.url || file.link,
      mediaFileId: file.mediaFileId,
    }));
    try {
      if (isEdit && advertiseId) {
        await updateAdvertise({
          advertiseId: advertiseId as string,
          shopId: storeId,
          name: values.name,
          desc: adDetail?.desc || "",
          // image: adDetail?.image || {
          //   type: "FILE",
          //   link: "",
          // },
          image: newImages.length > 0 ? newImages[0] : existingFiles[0],
          link: values.link,
          startDate: formatDateTimeForApi(values.startDate),
          endDate: formatDateTimeForApi(values.endDate),
          typePosition: "HOME",
          typeDisplay: displayType,
          typePublish: isPublished ? "Publish" : "UnPublish",
          status: isPublished ? "Actived" : "InActived",
          articleId: values.articleId || "",
          categoryId: values.categoryId || "",
          typeNavigate: selectedType,
        });

        // if (values.image instanceof File) {
        //   await updateImage(advertiseId as string, values.image);
        // }

        snackbar.success(t(tokens.contentManagement.popupAds.edit.success));
      } else {
        const response = await createAdvertise({
          advertiseId: "",
          shopId: storeId,
          name: values.name,
          desc: "",
          // image: {
          //   type: "FILE",
          //   link: "",
          // },
          image: newImages.length > 0 ? newImages[0] : existingFiles[0],
          link: values.link,
          startDate: formatDateTimeForApi(values.startDate),
          endDate: formatDateTimeForApi(values.endDate),
          typePosition: "HOME",
          typeDisplay: displayType,
          typePublish: isPublished ? "Publish" : "UnPublish",
          status: isPublished ? "Actived" : "InActived",
          articleId: values.articleId || "",
          categoryId: values.categoryId || "",
          typeNavigate: selectedType,
        });

        // if (response?.data?.advertiseId && values.image) {
        //   await updateImage(response.data.advertiseId, values.image);
        // }

        snackbar.success(t(tokens.contentManagement.popupAds.create.success));
      }

      router.push(paths.dashboard.store.contentManagement);
    } catch (error) {
      snackbar.error(
        isEdit
          ? t(tokens.contentManagement.popupAds.edit.error)
          : t(tokens.contentManagement.popupAds.create.error)
      );
    }
  }

  const handleCancel = () => {
    router.back();
  };

  const handleImageDrop = (files: File[]) => {
    if (files[0]) {
      if (isValidImageFile(files[0])) {
        formik.setFieldValue("image", files[0]);
        setImagePreview(URL.createObjectURL(files[0]));
        setShowPopup(true);
      } else {
        snackbar.error(
          "Định dạng file không hợp lệ. Hãy chọn file định dạng PNG, JPG, JPEG hoặc JFIF."
        );
      }
    }
  };

  const handleImageRemove = () => {
    formik.setFieldValue("image", null);
    setImagePreview(null);
    setShowPopup(false);
  };

  useEffect(() => {
    return () => {
      if (imagePreview) {
        URL.revokeObjectURL(imagePreview);
      }
    };
  }, [imagePreview]);

  const handlePublishChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setIsPublished(event.target.checked);
  };

  const handlePositionChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setDisplayPosition(event.target.checked ? "HOME" : "NONE");
  };

  const handleDisplayTypeChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setDisplayType(event.target.checked ? "OPEN_APP" : "NONE");
  };
  const handleFilesChange = (files: File[]) => {
    setExistingFiles([]);
    if (files.length > MAX_FILE_IMAGE) {
      snackbar.error(`Bạn chỉ có thể tải lên tối đa ${MAX_FILE_IMAGE} tệp hình ảnh.`);
      return;
    }
    if (files.length > 0 && !isValidImageFile(files[0])) {
      snackbar.error(
        "Định dạng file không hợp lệ. Hãy chọn file định dạng PNG, JPG, JPEG hoặc JFIF."
      );
      return;
    }
    formik.setFieldValue("image", files[0] || null);
    setShowPopup(true);
    // Cập nhật localFiles và imagePreview

    if (files.length > 0) {
      setLocalFiles(files);
      setImagePreview(URL.createObjectURL(files[0]));
    } else {
      setImagePreview(null);
    }
  };

  const handleRemove = (index: number, isExisting: boolean) => {
    if (isExisting) {
      const updated = [...existingFiles];
      updated.splice(index, 1);
      setExistingFiles(updated);
    } else {
      const localIdx = index - existingFiles.length;
      setLocalFiles((prev) => prev.filter((_, i) => i !== localIdx));
    }
  };
  return (
    <DashboardLayout>
      <Box sx={{ p: Padding }}>
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            mb: 3,
            paddingBottom: "20px",
            borderBottom: "1px solid #bdbdbd",
            marginBottom: "40px",
          }}
        >
          <IconButton onClick={handleCancel} sx={{ mr: 2 }}>
            <ArrowBackIcon />
          </IconButton>
          <TitleTypography sx={{ fontSize: "20px !important" }}>
            {"Quản lý nội dung/Popup/" +
              t(
                isEdit
                  ? tokens.contentManagement.popupAds.create.editTitle
                  : tokens.contentManagement.popupAds.create.title
              )}
          </TitleTypography>
        </Box>

        <Card sx={{ p: { xs: 2, sm: 3 } }}>
          <Stack
            direction={{ xs: "column", md: "row" }}
            spacing={{ xs: 3, md: 2 }}
            alignItems={{ xs: "center", md: "flex-start" }}
          >
            <Box
              sx={{
                flex: { xs: "none", md: 0.5 },
                width: { xs: "100%", md: "auto" },
                display: "flex",
                justifyContent: "center",
              }}
            >
              <Box
                sx={{
                  width: { xs: 200, sm: 240, md: 280 },
                  height: { xs: 400, sm: 480, md: 560 },
                  borderRadius: 2,
                  overflow: "hidden",
                  position: "relative",
                  backgroundColor: theme.palette.background.default,
                  backgroundImage: "url('/assets/phone-mockup.png')",
                  backgroundSize: "cover",
                  backgroundPosition: "center",
                }}
              >
                {showPopup && (
                  <Box
                    sx={{
                      position: "absolute",
                      top: "50%",
                      left: "50%",
                      transform: "translate(-50%, -50%)",
                      width: "75%",
                      aspectRatio: "1/1.2",
                      padding: "8px",
                    }}
                  >
                    <Box
                      sx={{
                        position: "relative",
                        width: "100%",
                        height: "100%",
                      }}
                    >
                      <Box
                        sx={{
                          width: "100%",
                          height: "100%",
                          borderRadius: "16px",
                          overflow: "hidden",
                          backgroundColor: "#fff",
                          boxShadow: "0px 4px 12px rgba(0, 0, 0, 0.1)",
                        }}
                      >
                        <Box
                          component="img"
                          src={
                            existingFiles.length === 1
                              ? existingFiles?.[0].link
                              : imagePreview || "/assets/illustrations/evotech.png"
                          }
                          sx={{
                            width: "100%",
                            height: "100%",
                            objectFit: "cover",
                          }}
                        />
                      </Box>
                      <IconButton
                        onClick={handleImageRemove}
                        sx={{
                          position: "absolute",
                          top: imagePreview ? -12 : -10,
                          right: imagePreview ? -12 : -10,
                          width: 24,
                          height: 24,
                          color: "#fff",
                          backgroundColor: "#d32f2f",
                          "&:hover": {
                            backgroundColor: "#b71c1c",
                          },
                          padding: 0,
                          minWidth: "unset",
                          boxShadow: "0px 2px 4px rgba(0, 0, 0, 0.2)",
                        }}
                      >
                        <SvgIcon fontSize="small">
                          <XIcon />
                        </SvgIcon>
                      </IconButton>
                    </Box>
                  </Box>
                )}
              </Box>
            </Box>

            <Box sx={{ flex: 1, width: "100%" }}>
              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between",
                  mb: 4,
                }}
              >
                <Typography variant="h6">
                  {t(tokens.contentManagement.popupAds.create.form.contentTitle)}
                </Typography>
                <FormControlLabel
                  control={<CustomSwitch checked={isPublished} onChange={handlePublishChange} />}
                  label={t(tokens.contentManagement.popupAds.create.form.isActive)}
                  labelPlacement="end"
                  sx={{
                    mr: 0,
                    "& .MuiFormControlLabel-label": {
                      ml: 2,
                    },
                  }}
                />
              </Box>

              <Typography sx={{ display: "flex", fontSize: "16px", fontWeight: 500, mb: 0.5 }}>
                {t(tokens.contentManagement.popupAds.create.form.adContent)}
                <Typography sx={{ color: "red", ml: 0.5 }}>*</Typography>
              </Typography>
              <TextField
                fullWidth
                name="name"
                value={formik.values.name}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={!!(formik.touched.name && formik.errors.name)}
                helperText={formik.touched.name && formik.errors.name}
                sx={{ mb: 4 }}
              />

              <Box sx={{ mb: 4 }}>
                <Typography variant="subtitle1" sx={{ mb: 1 }}>
                  {t(tokens.contentManagement.popupAds.create.form.imageTitle)}
                </Typography>
                <CommonMediaUpload
                  caption="Thêm ảnh"
                  maxFiles={1}
                  accept={{
                    "image/*": [".png", ".jpg", ".jpeg", ".jfif"],
                  }}
                  maxSize={FILE_SIZE_2MB} // 2MB
                  existingFiles={existingFiles}
                  localFiles={localFiles}
                  setLocalFiles={setLocalFiles}
                  onFilesChange={handleFilesChange}
                  onRemove={handleRemove}
                  defaultGroupId={defaultGroupId}
                  setExistingFiles={setExistingFiles}
                  errorMsg={errorMsg}
                  setErrorMsg={setErrorMsg}
                />
                {!isEdit && formik.touched.image && formik.errors.image && (
                  <Typography variant="caption" color="error" sx={{ mt: 1 }}>
                    {formik.errors.image as string}
                  </Typography>
                )}
              </Box>

              <Box sx={{ display: "flex", gap: 4, mb: 4 }}>
                <Box>
                  <Typography variant="subtitle1" sx={{ mb: 2 }}>
                    {t(tokens.contentManagement.popupAds.create.form.displayPages)}
                  </Typography>
                  <FormControlLabel
                    control={<Checkbox value={displayPosition === "HOME"} disabled checked />}
                    label={t(tokens.contentManagement.popupAds.create.form.pages.home)}
                  />
                </Box>
              </Box>

              <Grid container spacing={2} sx={{ mb: 4 }}>
                <Grid item xs={6}>
                  <Typography sx={{ display: "flex", fontSize: "16px", fontWeight: 500, mb: 0.5 }}>
                    {t(tokens.contentManagement.popupAds.create.form.startDate)}
                    <Typography sx={{ color: "red", ml: 0.5 }}>*</Typography>
                  </Typography>
                  <DateTimePicker
                    label=""
                    value={formik.values.startDate}
                    onChange={(newValue) => formik.setFieldValue("startDate", newValue)}
                    onBlur={() => formik.setFieldTouched("startDate", true)}
                    error={!!(formik.touched.startDate && formik.errors.startDate)}
                    helperText={formik.touched.startDate && formik.errors.startDate}
                    sx={{ width: "100%" }}
                  />
                </Grid>
                <Grid item xs={6}>
                  <Typography sx={{ display: "flex", fontSize: "16px", fontWeight: 500, mb: 0.5 }}>
                    {t(tokens.contentManagement.popupAds.create.form.endDate)}
                    <Typography sx={{ color: "red", ml: 0.5 }}>*</Typography>
                  </Typography>
                  <DateTimePicker
                    label=""
                    value={formik.values.endDate}
                    onChange={(newValue) => formik.setFieldValue("endDate", newValue)}
                    onBlur={() => formik.setFieldTouched("endDate", true)}
                    error={!!(formik.touched.endDate && formik.errors.endDate)}
                    helperText={formik.touched.endDate && formik.errors.endDate}
                    sx={{ width: "100%" }}
                    minDate={
                      formik.values.startDate
                        ? dayjs(formik.values.startDate).add(5, "minute").toDate()
                        : undefined
                    }
                  />
                </Grid>
              </Grid>

              <Box sx={{ mb: 4 }}>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={selectedType === "LINK"}
                      onChange={() => setSelectedType(selectedType === "LINK" ? "NONE" : "LINK")}
                    />
                  }
                  label={t(tokens.contentManagement.popupAds.create.form.link)}
                  sx={{ mb: 1 }}
                />
                <TextField
                  fullWidth
                  name="link"
                  value={formik.values.link}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={!!(formik.touched.link && formik.errors.link)}
                  helperText={
                    (formik.touched.link && formik.errors.link) ||
                    t(tokens.contentManagement.popupAds.create.form.linkArticleHint)
                  }
                  sx={{ mb: 4 }}
                  placeholder={t(tokens.contentManagement.popupAds.create.form.linkPlaceholder)}
                  disabled={selectedType !== "LINK"}
                />
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={selectedType === "ARTICLE"}
                      onChange={() =>
                        setSelectedType(selectedType === "ARTICLE" ? "NONE" : "ARTICLE")
                      }
                    />
                  }
                  label="Bài viết"
                  sx={{ mb: 1 }}
                />
                <Autocomplete
                  options={articleOptions}
                  loading={articleLoading}
                  getOptionLabel={(option) => option.title || ""}
                  value={
                    articleOptions.find((a) => a.articleId === formik.values.articleId) || null
                  }
                  onChange={(_, value) =>
                    formik.setFieldValue("articleId", value ? value.articleId : "")
                  }
                  onInputChange={(_, value, reason) => {
                    if (reason === "input" || reason === "clear" || reason === "reset") {
                      setArticleSearch(value);
                    }
                  }}
                  filterSelectedOptions={false}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      placeholder="Tìm kiếm bài viết theo tiêu đề"
                      InputProps={{
                        ...params.InputProps,
                        endAdornment: (
                          <>
                            {articleLoading ? <CircularProgress color="inherit" size={20} /> : null}
                            {params.InputProps.endAdornment}
                          </>
                        ),
                      }}
                      sx={{ mb: 4 }}
                      disabled={selectedType !== "ARTICLE"}
                    />
                  )}
                  isOptionEqualToValue={(option, value) => option.articleId === value.articleId}
                  disabled={selectedType !== "ARTICLE"}
                />
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={selectedType === "CATEGORY"}
                      onChange={() =>
                        setSelectedType(selectedType === "CATEGORY" ? "NONE" : "CATEGORY")
                      }
                    />
                  }
                  label="Danh mục"
                  sx={{ mb: 1 }}
                />
                <Autocomplete
                  options={categoryOptions}
                  loading={categoryLoading}
                  getOptionLabel={(option) => option.categoryName || ""}
                  value={
                    categoryOptions.find((c) => c.categoryId === formik.values.categoryId) || null
                  }
                  onChange={(_, value) =>
                    formik.setFieldValue("categoryId", value ? value.categoryId : "")
                  }
                  onInputChange={(_, value, reason) => {
                    if (reason === "input" || reason === "clear" || reason === "reset") {
                      setCategorySearch(value);
                    }
                  }}
                  filterSelectedOptions={false}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      placeholder="Tìm kiếm danh mục theo tên"
                      InputProps={{
                        ...params.InputProps,
                        endAdornment: (
                          <>
                            {categoryLoading ? (
                              <CircularProgress color="inherit" size={20} />
                            ) : null}
                            {params.InputProps.endAdornment}
                          </>
                        ),
                      }}
                      sx={{ mb: 4 }}
                      disabled={selectedType !== "CATEGORY"}
                    />
                  )}
                  isOptionEqualToValue={(option, value) => option.categoryId === value.categoryId}
                  disabled={selectedType !== "CATEGORY"}
                />
              </Box>

              <Stack
                direction={{ xs: "column", sm: "row" }}
                justifyContent="flex-end"
                sx={{
                  mt: 3,
                  width: "100%",
                  gap: { xs: "6px", sm: "8px" },
                }}
              >
                <Button
                  variant="contained"
                  onClick={() => formik.handleSubmit()}
                  disabled={formik.isSubmitting}
                  sx={{
                    minWidth: 120,
                    background: "#2654FE",
                    order: { xs: 1, sm: 2 },
                    width: { xs: "100%", sm: "auto" },
                  }}
                >
                  {formik.isSubmitting ? (
                    <CircularProgress size={24} />
                  ) : (
                    t(tokens.contentManagement.popupAds.create.form.saveButton)
                  )}
                </Button>
                <Button
                  onClick={handleCancel}
                  variant="outlined"
                  sx={{
                    minWidth: 120,
                    color: "#2654FE",
                    borderColor: "#2654FE",
                    order: { xs: 2, sm: 1 },
                    width: { xs: "100%", sm: "auto" },
                  }}
                >
                  {t(tokens.contentManagement.popupAds.create.form.cancelButton)}
                </Button>
              </Stack>
            </Box>
          </Stack>
        </Card>
      </Box>
    </DashboardLayout>
  );
};

export default CreatePopupAd;
