import { useState, useEffect } from "react";
import { useRouter } from "next/router";
import { useTranslation } from "react-i18next";
import { useSnackbar } from "@/src/hooks/use-snackbar";
import { useStoreId } from "@/src/hooks/use-store-id";
import { paths } from "@/src/paths";
import {
  Box,
  Button,
  Checkbox,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  TextField,
  TablePagination,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Snackbar,
  Chip,
  Tooltip,
  InputAdornment,
} from "@mui/material";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import AddIcon from "@mui/icons-material/Add";
import { tokens } from "@/src/locales/tokens";
import { formatDateDisplay, formatDateTimeDisplay } from "@/src/utils/date-utils";
import { useAdvertise } from "@/src/api/hooks/dashboard/store/use-advertise";
import { EmptyState } from "@/src/components/common/empty-state";
import { LoadingState } from "@/src/components/common/loading-state";
import ShowImage from "@/src/components/show-image";
import { useFunction } from "@/src/api/hooks/function/use-function";
import { IBodyCheckPermission } from "@/src/api/services/function/function.service";
import { rowPerPageOptionsDefault } from "@/src/types/store/enum-type";
import { useAllPermissions } from "@/src/api/hooks/auth/use-all-permissions";
import { usePathname } from "next/navigation";
import { PERMISSION_TYPE_ENUM } from "@/src/constants/constant";
import dayjs from "dayjs";
import { useDebounce } from "@/src/hooks/use-debounce";
import TruncatedText from "@/src/components/truncated-text/truncated-text";
import { Search } from "@mui/icons-material";
import { formatTruncatedText } from "@/src/utils/format";

const PopupAds = () => {
  const url = useRouter();
  const { t } = useTranslation();
  const pathname = usePathname();
  const snackbar = useSnackbar();
  const { getAdvertise, deleteAdvertise, loading } = useAdvertise();
  const [selected, setSelected] = useState<string[]>([]);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [openDialog, setOpenDialog] = useState(false);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [openMultipleDeleteDialog, setOpenMultipleDeleteDialog] = useState(false);
  const [adToDelete, setAdToDelete] = useState<any>(null);
  const router = useRouter();
  const [ads, setAds] = useState([]);
  const [totalCount, setTotalCount] = useState(0);
  const [searchTerm, setSearchTerm] = useState("");
  const [isInitialLoading, setIsInitialLoading] = useState(true);
  const storeId = useStoreId();
  const debouncedSearchValue = useDebounce(searchTerm, 500);

  const { permissions } = useAllPermissions();
  const isGranted = (url, permission) => {
    if (url.endsWith("/")) {
      url = url.slice(0, -1);
    }
    const matchingUrl = Object.keys(permissions).find((key) => key === url);
    if (!matchingUrl) return false;
    return permissions[matchingUrl]?.includes(permission) || false;
  };

  // Fetch ads when page, rowsPerPage, or search term changes
  useEffect(() => {
    if (storeId) {
      setIsInitialLoading(true);
      fetchAds().finally(() => {
        setIsInitialLoading(false);
      });
    }
  }, [page, rowsPerPage, debouncedSearchValue, storeId]);

  const fetchAds = async () => {
    try {
      const response = await getAdvertise(page * rowsPerPage, rowsPerPage, debouncedSearchValue);
      setAds(response.data.data || []);
      setTotalCount(response.data.total || 0);
    } catch (error) {
      snackbar.error(t("common.errorOccurred"));
    }
  };

  const handleSelectAll = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.checked) {
      setSelected(ads.map((ad) => ad.advertiseId));
    } else {
      setSelected([]);
    }
  };

  const handleSelectOne = (event: React.ChangeEvent<HTMLInputElement>, id: string) => {
    if (event.target.checked) {
      setSelected([...selected, id]);
    } else {
      setSelected(selected.filter((item) => item !== id));
    }
  };

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleSearch = (event) => {
    const value = event.target.value;
    setSearchTerm(value);
    setPage(0);
  };

  const handleAddNew = () => {
    router.push(paths.dashboard.store.createPopupAd);
  };

  const handleEdit = (ad) => {
    router.push({
      pathname: paths.dashboard.store.createPopupAd,
      query: { advertiseId: ad.advertiseId },
    });
  };

  const handleCopy = (ad) => {
    setSnackbarOpen(true);
  };

  const handleDelete = (ad: any) => {
    setAdToDelete(ad);
    setOpenDeleteDialog(true);
  };

  const confirmDelete = async () => {
    try {
      await deleteAdvertise(adToDelete.advertiseId);
      snackbar.success("Xóa quảng cáo thành công");

      // Remove item đã xóa khỏi danh sách selected nếu có
      if (selected.includes(adToDelete.advertiseId)) {
        setSelected(selected.filter((id) => id !== adToDelete.advertiseId));
      }

      setOpenDeleteDialog(false);
      setAdToDelete(null);
      fetchAds();
    } catch (error) {
      snackbar.error("Xóa quảng cáo thất bại");
    }
  };

  const handleDeleteMultiple = () => {
    setOpenMultipleDeleteDialog(true);
  };

  const confirmDeleteMultiple = async () => {
    try {
      // Thực hiện xóa tuần tự các items đã chọn
      await Promise.all(selected.map((id) => deleteAdvertise(id)));

      snackbar.success(`Xóa ${selected.length} quảng cáo thành công`);
      setSelected([]);
      setOpenMultipleDeleteDialog(false);
      fetchAds();
    } catch (error) {
      snackbar.error("Xóa quảng cáo thất bại");
    }
  };

  if (!storeId) {
    return null;
  }

  return (
    <Box sx={{ p: 3 }}>
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          flexDirection: { xs: "column", sm: "row" },
          mb: 3,
        }}
      >
        <Box sx={{ display: "flex", gap: 2 }}>
          <Tooltip
            title={!isGranted(pathname, PERMISSION_TYPE_ENUM.Add) ? "Bạn không có quyền thêm" : ""}
          >
            <span>
              <Button
                sx={{ background: "#2654FE", textTransform: "none", mb: { xs: 1, sm: 0 } }}
                variant="contained"
                startIcon={<AddIcon />}
                onClick={handleAddNew}
                disabled={!isGranted(pathname, PERMISSION_TYPE_ENUM.Add)}
              >
                {t(tokens.contentManagement.popupAds.addNew)}
              </Button>
            </span>
          </Tooltip>
          {selected.length > 0 && (
            <Tooltip
              title={
                !isGranted(pathname, PERMISSION_TYPE_ENUM.Delete) ? "Bạn không có quyền xoá" : ""
              }
            >
              <span>
                <Button
                  variant="outlined"
                  color="error"
                  startIcon={<DeleteIcon />}
                  onClick={handleDeleteMultiple}
                  disabled={!isGranted(pathname, PERMISSION_TYPE_ENUM.Delete)}
                >
                  {t(tokens.common.delete)} ({selected.length})
                </Button>
              </span>
            </Tooltip>
          )}
        </Box>

        <TextField
          placeholder={t(tokens.contentManagement.popupAds.search)}
          value={searchTerm}
          onChange={handleSearch}
          variant="outlined"
          size="small"
          sx={{ width: "300px" }}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Search />
              </InputAdornment>
            ),
          }}
        />
      </Box>

      {(loading && page === 0) || isInitialLoading ? (
        <LoadingState />
      ) : !isInitialLoading && ads.length === 0 ? (
        <EmptyState
          title={t(tokens.contentManagement.popupAds.emptyState.message)}
          subtitle={t(tokens.contentManagement.popupAds.emptyState.subtitle)}
          buttonText={t(tokens.contentManagement.popupAds.emptyState.button)}
          onAddClick={handleAddNew}
          isGranted={isGranted}
        />
      ) : (
        <>
          <Box sx={{ overflowX: "auto" }}>
            <Table sx={{ minWidth: 1000 }}>
              <TableHead>
                <TableRow>
                  <TableCell padding="checkbox" width="5%">
                    <Checkbox
                      checked={ads.length > 0 && selected.length === ads.length}
                      indeterminate={selected.length > 0 && selected.length < ads.length}
                      onChange={handleSelectAll}
                    />
                  </TableCell>
                  <TableCell width="5%" sx={{ fontWeight: 600 }}>
                    STT
                  </TableCell>
                  <TableCell width="300px" sx={{ fontWeight: 600 }}>
                    Tên quảng cáo
                  </TableCell>
                  <TableCell width="10%" sx={{ fontWeight: 600 }}>
                    Hình ảnh
                  </TableCell>
                  <TableCell width="30%" sx={{ fontWeight: 600 }}>
                    Thời gian chạy
                  </TableCell>
                  <TableCell width="15%" sx={{ fontWeight: 600 }}>
                    Ngày tạo
                  </TableCell>
                  <TableCell width="15%" sx={{ fontWeight: 600 }}>
                    Trạng thái
                  </TableCell>
                  <TableCell width="15%" align="center" sx={{ fontWeight: 600 }}>
                    Thao tác
                  </TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {ads.map((ad, index) => (
                  <TableRow key={ad.advertiseId}>
                    <TableCell padding="checkbox">
                      <Checkbox
                        checked={selected.includes(ad.advertiseId)}
                        onChange={(event) => handleSelectOne(event, ad.advertiseId)}
                      />
                    </TableCell>
                    <TableCell>{page * rowsPerPage + index + 1}</TableCell>
                    <TableCell>
                      <TruncatedText
                        {...formatTruncatedText({
                          text: ad.name,
                          isGranted: isGranted(pathname, PERMISSION_TYPE_ENUM.Edit),
                          openInNewTab: false,
                          actionNeed: () => handleEdit(ad),
                          width: "300px",
                        })}
                      />
                    </TableCell>
                    <TableCell>
                      <ShowImage
                        src={ad.image?.link || ""}
                        alt={ad.title}
                        size={50}
                        circular={true}
                      />
                    </TableCell>
                    <TableCell sx={{ whiteSpace: "pre-line" }}>
                      {`${dayjs(ad.startDate).format("DD/MM/YYYY HH:mm:ss")} - ${dayjs(
                        ad.endDate
                      ).format("DD/MM/YYYY HH:mm:ss")}`}
                    </TableCell>
                    <TableCell>{dayjs(ad.created).format("DD/MM/YYYY HH:mm:ss")}</TableCell>
                    <TableCell>
                      <Chip
                        label={ad.typePublish === "Publish" ? "Xuất bản" : "Không xuất bản"}
                        color={ad.typePublish === "Publish" ? "success" : "default"}
                        sx={{
                          backgroundColor:
                            ad.typePublish === "Publish"
                              ? "rgba(84, 214, 44, 0.16)"
                              : "rgba(145, 158, 171, 0.16)",
                          color:
                            ad.typePublish === "Publish" ? "rgb(34, 154, 22)" : "rgb(99, 115, 129)",
                        }}
                      />
                    </TableCell>
                    <TableCell align="center">
                      <Box sx={{ display: "flex", justifyContent: "center", gap: 1 }}>
                        <Tooltip
                          title={
                            !isGranted(pathname, PERMISSION_TYPE_ENUM.Edit)
                              ? "Bạn không có quyền sửa"
                              : "Sửa popup quảng cáo"
                          }
                        >
                          <span>
                            <IconButton
                              onClick={() => handleEdit(ad)}
                              size="small"
                              sx={{ color: "primary.main" }}
                              disabled={!isGranted(pathname, PERMISSION_TYPE_ENUM.Edit)}
                            >
                              <EditIcon fontSize="small" />
                            </IconButton>
                          </span>
                        </Tooltip>
                        <Tooltip
                          title={
                            !isGranted(pathname, PERMISSION_TYPE_ENUM.Delete)
                              ? "Bạn không có quyền xoá"
                              : "Xoá popup quảng cáo"
                          }
                        >
                          <span>
                            <IconButton
                              onClick={() => handleDelete(ad)}
                              size="small"
                              sx={{ color: "error.main" }}
                              disabled={!isGranted(pathname, PERMISSION_TYPE_ENUM.Delete)}
                            >
                              <DeleteIcon fontSize="small" />
                            </IconButton>
                          </span>
                        </Tooltip>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </Box>
          <TablePagination
            labelRowsPerPage="Số hàng mỗi trang"
            component="div"
            count={totalCount}
            page={page}
            onPageChange={handleChangePage}
            rowsPerPage={rowsPerPage}
            onRowsPerPageChange={handleChangeRowsPerPage}
            rowsPerPageOptions={rowPerPageOptionsDefault}
          />
        </>
      )}

      <Dialog
        open={openDeleteDialog}
        onClose={() => setOpenDeleteDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Xác nhận xóa</DialogTitle>
        <DialogContent>
          <DialogContentText>
            {`Bạn có chắc chắn muốn xóa quảng cáo "${adToDelete?.name || ""}" không?`}
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => setOpenDeleteDialog(false)}
            variant="outlined"
            sx={{ minWidth: 100 }}
          >
            Hủy
          </Button>
          <Button
            onClick={confirmDelete}
            color="error"
            variant="contained"
            autoFocus
            sx={{ minWidth: 100 }}
          >
            Xóa
          </Button>
        </DialogActions>
      </Dialog>

      <Dialog
        open={openMultipleDeleteDialog}
        onClose={() => setOpenMultipleDeleteDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Xác nhận xóa nhiều quảng cáo</DialogTitle>
        <DialogContent>
          <DialogContentText>
            {`Bạn có chắc chắn muốn xóa ${selected.length} quảng cáo đã chọn không?`}
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => setOpenMultipleDeleteDialog(false)}
            variant="outlined"
            sx={{ minWidth: 100 }}
          >
            Hủy
          </Button>
          <Button
            onClick={confirmDeleteMultiple}
            color="error"
            variant="contained"
            autoFocus
            sx={{ minWidth: 100 }}
          >
            Xóa
          </Button>
        </DialogActions>
      </Dialog>

      <Snackbar
        open={snackbarOpen}
        autoHideDuration={3000}
        onClose={() => setSnackbarOpen(false)}
        message={t(tokens.contentManagement.popupAds.snackbar.copySuccess)}
      />
    </Box>
  );
};

export default PopupAds;
