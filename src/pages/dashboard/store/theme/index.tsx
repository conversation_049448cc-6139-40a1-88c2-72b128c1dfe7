import React, { useState } from "react";
import {
  Box,
  List,
  ListItem,
  ListItemText,
  Paper,
  Drawer,
  IconButton,
  useMediaQuery,
  useTheme,
} from "@mui/material";
import MenuIcon from "@mui/icons-material/Menu";
import ThemeSettings from "./theme-settings";
import HomeIcon from '@mui/icons-material/Home';
import SettingsIcon from '@mui/icons-material/Settings';
import LocalOfferIcon from '@mui/icons-material/LocalOffer';
import Comingsoon from "@/src/pages/dashboard/comingsoon";

const menuItems = [
  { title: "Chung", key: "global", icon: <SettingsIcon /> },
  { title: "Trang chủ", key: "home", icon: <HomeIcon /> },
  { title: "Ưu đãi/Khuyến mãi", key: "promotion", icon: <LocalOfferIcon /> },
];

export default function ThemePage() {
  const [selectedMenu, setSelectedMenu] = useState<string>("global");
  const [openDrawer, setOpenDrawer] = useState(false);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("lg"));

  const handleMenuClick = (key: string) => {
    setSelectedMenu(key);
    if (isMobile) setOpenDrawer(false);
  };

  const SidebarContent = (
    <Box sx={{
      width: { xs: "100%", lg: 240 },
      minWidth: { xs: "auto", lg: 240 },
      maxWidth: { xs: "none", lg: 240 },
      flexShrink: 0,
      borderColor: "divider",
      backgroundColor: "#fff",
      borderRadius: { xs: 0, lg: "16px" },
      overflow: "hidden",
      height: "calc(100vh - 110px)",
      display: "flex",
      flexDirection: "column",
    }}>
      <List sx={{ 
        padding: { xs: 1, lg: 0 },
        flex: 1,
        overflow: "hidden",
      }}>
        {menuItems.map((item, index) => (
          <ListItem
            key={index}
            onClick={() => handleMenuClick(item.key)}
            sx={{
              color: selectedMenu === item.key ? "#fff" : "inherit",
              textDecoration: "none",
              cursor: "pointer",
              borderRadius: { xs: 1, lg: 0 },
              margin: { xs: "4px 0", lg: 0 },
              padding: { xs: "12px 12px", lg: "8px 8px" },
              fontSize: { xs: "14px", lg: "16px" },
              backgroundColor: selectedMenu === item.key ? "#2654fe" : "transparent",
              "&:hover": {
                backgroundColor: selectedMenu === item.key ? "#2654fe" : "#f5f6fa",
              },
            }}
          >
            <Box
              sx={{
                color: selectedMenu === item.key ? '#fff' : '#9E9E9E',
                mr: 1,
                display: 'flex',
                alignItems: 'center'
              }}
            >
              {item.icon}
            </Box>
            <ListItemText
              primary={item.title}
              sx={{
                "& .MuiListItemText-primary": {
                  fontSize: { xs: "14px", lg: "16px" },
                  fontWeight: selectedMenu === item.key ? 600 : 400,
                },
              }}
            />
          </ListItem>
        ))}
      </List>
    </Box>
  );

  const renderContent = () => {
    switch (selectedMenu) {
      case "global":
        return (
          <ThemeSettings />
        );
      case "home":
        return (
          <Comingsoon />
        );
      case "promotion":
        return (
          <Comingsoon />
        );
      default:
        return (
          <Box sx={{ 
            padding: { xs: "16px", md: "24px" },
            display: "flex",
            flexDirection: "column"
          }}>
            <div>Default view</div>
          </Box>
        );
    }
  };

  return (
    <Box
      sx={{
        padding: { xs: "8px", md: "16px" },
      }}
    >
      <Box
        sx={{
          display: "flex",
          gap: { xs: 0, lg: "10px" },
          marginTop: { xs: "8px", lg: "10px" },
          flexDirection: { xs: "column", lg: "row" },
        }}
      >
        {isMobile ? (
          <>
            <IconButton 
              onClick={() => setOpenDrawer(true)}
              sx={{
                alignSelf: "flex-start",
                mb: 2,
              }}
            >
              <MenuIcon />
            </IconButton>
            <Drawer 
              open={openDrawer} 
              onClose={() => setOpenDrawer(false)}
              sx={{
                "& .MuiDrawer-paper": {
                  width: { xs: "280px", sm: "320px" },
                  boxSizing: "border-box",
                },
              }}
            >
              {SidebarContent}
            </Drawer>
          </>
        ) : (
          SidebarContent
        )}

        <Box
          sx={{
            flexGrow: 1,
            paddingRight: { xs: 0, lg: 0 },
            paddingLeft: { xs: 0, lg: 0 },
            width: { xs: "100%", lg: "auto" },
            height: "calc(100vh - 120px)",
          }}
        >
          <Paper
            sx={{
              width: "100%",
              borderRadius: { xs: 1, md: 2 },
              display: "flex",
              flexDirection: "column",
            }}
          >
            <Box
              sx={{
                padding: { xs: 0, md: 0 },
              }}
            >
              {renderContent()}
            </Box>
          </Paper>
        </Box>
      </Box>
    </Box>
  );
}