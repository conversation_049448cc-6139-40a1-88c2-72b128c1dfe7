import React, { useState, useEffect } from "react";
import { Box, Button, Typography, Card, Stack, CircularProgress, Tooltip } from "@mui/material";
import DashboardLayout from "../../../../layouts/dashboard";
import ColorPicker from "@/src/components/color-picker";
import ThemeColorPicker from "@/src/components/theme-color-picker";
import { useShop } from "@/src/api/hooks/shop/use-shop";
import { useStoreId } from "@/src/hooks/use-store-id";
import { useSnackbar } from "@/src/hooks/use-snackbar";
import TitleTypography from "@/src/components/title-typography/title-typography";
import { JsonEditor } from "json-edit-react";
import { shopMiniAppService } from "@/src/api/services/shop-mini-app/shop-mini-app.service";
import { Padding } from "@/src/styles/CommonStyle";
import { useRouter } from "next/router";
import { useAllPermissions } from "@/src/api/hooks/auth/use-all-permissions";
import { PERMISSION_TYPE_ENUM } from "@/src/constants/constant";
import { usePathname } from "next/navigation";

// Import colors from color-picker
const colors = [
  ["#FF6B6B", "#FFD6D6"],
  ["#FFA726", "#FFCC80"],
  ["#FF8A80", "#FFE0E0"],
  ["#FF5252", "#B0BEC5"],
  ["#FFD740", "#FFECB3"],
  ["#26A69A", "#B2DFDB"],
  ["#66BB6A", "#9CCC65"],
  ["#81C784", "#C8E6C9"],
  ["#42A5F5", "#90CAF9"],
  ["#D4E157", "#FFF9C4"],
  ["#546E7A", "#ECEFF1"],
  ["#7E57C2", "#D1C4E9"],
  ["#F44336", "#FF8A65"],
];

const ThemeSettings = () => {
  const pathname = usePathname();
  const [selectedTheme, setSelectedTheme] = useState(0);
  const [customPrimary, setCustomPrimary] = useState(colors[0][0]); // Default to first primary color
  const [customSecondary, setCustomSecondary] = useState(colors[0][1]); // Default to first secondary color
  const [isLoading, setIsLoading] = useState(false);
  const storeId = useStoreId();
  const { detailShop, updateShop } = useShop();
  const snackbar = useSnackbar();
  const url = useRouter();
  const { permissions } = useAllPermissions();
  const isGranted = (url, permission) => {
    if (url.endsWith("/")) {
      url = url.slice(0, -1);
    }
    const matchingUrl = Object.keys(permissions).find((key) => key === url);
    if (!matchingUrl) return false;
    return permissions[matchingUrl]?.includes(permission) || false;
  };
  // Fetch shop theme data
  useEffect(() => {
    const fetchShopTheme = async () => {
      if (storeId) {
        try {
          setIsLoading(true);
          const detail = await detailShop(storeId);
          const { shopTheme } = detail.data;

          if (shopTheme) {
            setCustomPrimary(shopTheme.primaryColor || colors[0][0]);
            setCustomSecondary(shopTheme.secondaryColor || colors[0][1]);

            // Find and set selected theme if colors match a predefined theme
            const themeIndex = colors.findIndex(
              ([primary, secondary]) =>
                primary === shopTheme.primaryColor && secondary === shopTheme.secondaryColor
            );
            if (themeIndex !== -1) {
              setSelectedTheme(themeIndex);
            }
          }
        } catch (error) {
          console.error("Error fetching shop theme:", error);
          snackbar.error("Không thể tải thông tin giao diện");
        } finally {
          setIsLoading(false);
        }
      }
    };

    fetchShopTheme();
  }, [storeId]);

  // Handle theme selection
  const handleThemeChange = (index: number) => {
    setSelectedTheme(index);
    setCustomPrimary(colors[index][0]); // Set primary color from selected theme
    setCustomSecondary(colors[index][1]); // Set secondary color from selected theme
  };

  // Handle save changes
  const handleSave = async () => {
    try {
      setIsLoading(true);

      const detail = await detailShop(storeId);
      const currentShop = detail.data;

      await updateShop({
        ...currentShop,
        shopId: storeId,
        shopTheme: {
          primaryColor: customPrimary,
          secondaryColor: customSecondary,
          accentColor: "#FFFFFF",
        },
      });

      snackbar.success("Cập nhật giao diện thành công");
    } catch (error) {
      console.error("Error updating theme:", error);
      snackbar.error("Có lỗi xảy ra khi cập nhật giao diện");
    } finally {
      setIsLoading(false);
    }
  };

  const [jsonData, setJsonData] = useState(null);
  const [updatedJson, setUpdatedJson] = useState(null);

  const fetchShopMiniApp = async () => {
    try {
      const res = await shopMiniAppService.getShopMiniApp(storeId);

      if (res.data && res.data.template) {
        const parsedJson = res.data.template;
        setJsonData(parsedJson);
        setUpdatedJson(parsedJson);
      }
    } catch (error) {
      console.error("Error fetching shop mini app:", error);
    }
  };
  useEffect(() => {
    if (storeId) {
      fetchShopMiniApp();
    }
  }, [storeId]);
  const handleSaveJson = async () => {
    try {
      const data = {
        shopId: storeId,
        template: updatedJson,
      };
      const res = await shopMiniAppService.createShopMiniApp(data);
      console.log({ res });
      if (res?.status === 200) {
        snackbar.success("Cập nhật cấu hình thành công!");
      }
    } catch (error) {
      console.error("Error saving JSON:", error);
    }
  };
  const handleJsonChange = (newJson: any) => {
    setUpdatedJson(newJson.newData);
  };

  return (
    <DashboardLayout>
      <Box sx={{ padding: { xs: 2, md: 2 } }}>
        <Card
          sx={{
            p: 2,
            borderRadius: 2,
            boxShadow: "0 4px 20px rgba(0, 0, 0, 0.08)",
            border: "1px solid #f0f0f0",
            background: "linear-gradient(135deg, #ffffff 0%, #fafafa 100%)",
            position: 'relative',
          }}
        >
          <Stack direction={{ xs: "column", md: "row" }} spacing={4}>
            <Box sx={{ mb: 3, flex: 1 }}>
              <Typography
                variant="h6"
                sx={{
                  fontWeight: 600,
                  color: "#1a1a1a",
                  display: "flex",
                  alignItems: "center",
                  gap: 1,
                }}
              >
                🎨 Chủ đề giao diện toàn bộ mini app của bạn
              </Typography>
              <Typography
                variant="body2"
                sx={{
                  color: "#666",
                  mt: 0.5,
                }}
              >
                Tùy chỉnh màu sắc chủ đạo cho giao diện của bạn
              </Typography>
            </Box>
            <Box
              sx={{
                width: { xs: "100%", md: "auto" },
                display: "flex",
                justifyContent: "flex-end",
                alignItems: "center",
                mt: { xs: 1.5, md: 0 },
                pb: { xs: 1.5, md: 0 },
              }}
            >
              <Tooltip
                title={!isGranted(pathname, PERMISSION_TYPE_ENUM.Edit) ? "Bạn không có quyền lưu" : ""}
              >
                <span>
                  <Button
                    variant="contained"
                    onClick={handleSave}
                    disabled={!isGranted(pathname, PERMISSION_TYPE_ENUM.Edit)}
                    sx={{
                      background: "linear-gradient(135deg, #2654FE 0%, #1e40af 100%)",
                      textTransform: "none",
                      px: 2,
                      py: 0.5,
                      minWidth: 0,
                      fontSize: '0.95rem',
                      borderRadius: 1.5,
                      boxShadow: "0 2px 8px rgba(38, 84, 254, 0.18)",
                      height: 36,
                      '&:hover': {
                        background: "linear-gradient(135deg, #1e40af 0%, #1e3a8a 100%)",
                        boxShadow: "0 4px 12px rgba(38, 84, 254, 0.22)",
                      },
                    }}
                  >
                    {isLoading ? <CircularProgress size={18} color="inherit" /> : "💾 Lưu thay đổi"}
                  </Button>
                </span>
              </Tooltip>
            </Box>
          </Stack>

          <Stack direction={{ xs: "column", md: "row" }} spacing={4}>
            <Box flex={1}>
              <Box
                sx={{
                  p: 2,
                  height: { xs: 'auto', md: '14vh' },
                  borderRadius: 1.5,
                  bgcolor: "#f8f9fa",
                  border: "1px solid #e9ecef",
                }}
              >
                <Typography
                  variant="subtitle2"
                  sx={{
                    fontWeight: 500,
                    color: "#495057",
                  }}
                >
                  Chủ đề có sẵn
                </Typography>
                <ColorPicker selectedTheme={selectedTheme} onThemeChange={handleThemeChange} />
              </Box>
            </Box>
            <Box flex={1}>
              <Box
                sx={{
                  p: 2,
                  height: { xs: 'auto', md: '14vh' },
                  borderRadius: 1.5,
                  bgcolor: "#f8f9fa",
                  border: "1px solid #e9ecef",
                }}
              >
                <Typography
                  variant="subtitle2"
                  sx={{
                    fontWeight: 500,
                    color: "#495057",
                  }}
                >
                  Tùy chỉnh màu sắc
                </Typography>
                <Stack direction={{ xs: "column", md: "row" }} spacing={2}>
                  <ThemeColorPicker
                    label="Màu chính"
                    value={customPrimary}
                    onChange={setCustomPrimary}
                  />
                  <ThemeColorPicker
                    label="Màu phụ"
                    value={customSecondary}
                    onChange={setCustomSecondary}
                  />
                </Stack>
              </Box>
            </Box>
          </Stack>
        </Card>

        <Card
          sx={{
            p: 3,
            mt: 2,
            borderRadius: 2,
            boxShadow: "0 4px 20px rgba(0, 0, 0, 0.08)",
            border: "1px solid #f0f0f0",
            background: "linear-gradient(135deg, #ffffff 0%, #fafafa 100%)",
            position: 'relative',
          }}
        >
          <Stack direction={{ xs: "column", md: "row" }} spacing={4}>
            <Box sx={{ mb: 3, flex: 1 }}>
              <Typography
                variant="h6"
                sx={{
                  fontWeight: 600,
                  color: "#1a1a1a",
                  display: "flex",
                  alignItems: "center",
                  gap: 1,
                }}
              >
                ⚙️ Cấu hình JSON
              </Typography>
              <Typography
                variant="body2"
                sx={{
                  color: "#666",
                  mt: 0.5,
                }}
              >
                Chỉnh sửa cấu hình nâng cao cho ứng dụng mini của bạn. Hãy chắc chắn rằng bạn hiểu cấu trúc JSON trước khi chỉnh sửa.
              </Typography>
            </Box>
            <Box
              sx={{
                width: { xs: "100%", md: "auto" },
                display: "flex",
                justifyContent: "flex-end",
                alignItems: "center",
                mt: { xs: 1.5, md: 0 },
                pb: { xs: 1.5, md: 0 },
              }}
            >
              <Tooltip
                title={!isGranted(pathname, PERMISSION_TYPE_ENUM.Edit) ? "Bạn không có quyền lưu" : ""}
              >
                <span>
                  <Button
                    variant="contained"
                    onClick={handleSaveJson}
                    disabled={!isGranted(pathname, PERMISSION_TYPE_ENUM.Edit)}
                    sx={{
                      background: "linear-gradient(135deg, #2654FE 0%, #1e40af 100%)",
                      textTransform: "none",
                      px: 2,
                      py: 0.5,
                      minWidth: 0,
                      fontSize: '0.95rem',
                      borderRadius: 1.5,
                      boxShadow: "0 2px 8px rgba(38, 84, 254, 0.18)",
                      height: 36,
                      '&:hover': {
                        background: "linear-gradient(135deg, #1e40af 0%, #1e3a8a 100%)",
                        boxShadow: "0 4px 12px rgba(38, 84, 254, 0.22)",
                      },
                    }}
                  >
                    🔧 Lưu cấu hình
                  </Button>
                </span>
              </Tooltip>
            </Box>
          </Stack>

          <Box
            sx={{
              borderRadius: 1.5,
              bgcolor: "#f8f9fa",
              border: "1px solid #e9ecef",
              maxHeight: "calc(100vh - 460px)",
              overflow: "auto",
            }}
          >
            <JsonEditor
              className="json-editor"
              maxWidth={"100%"}
              data={jsonData}
              onUpdate={handleJsonChange}
              collapse={true}
            />
          </Box>


        </Card>
      </Box>
    </DashboardLayout>
  );
};

export default ThemeSettings;
