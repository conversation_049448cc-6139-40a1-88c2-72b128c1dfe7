import React, { useState } from "react";
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
} from "@mui/material";

interface BuildVersionDialogProps {
  open: boolean;
  onClose: () => void;
  onConfirm: (version: string) => Promise<void>;
  isBuilding: boolean;
}

const BuildVersionDialog: React.FC<BuildVersionDialogProps> = ({
  open,
  onClose,
  onConfirm,
  isBuilding,
}) => {
  const [buildVersion, setBuildVersion] = useState("");
  const [buildVersionError, setBuildVersionError] = useState("");

  const validateVersion = (version: string) => {
    const versionRegex = /^(\d{1,2})\.(\d{1,2})\.(\d{1,2})$/;
    return versionRegex.test(version);
  };

  const handleVersionChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setBuildVersion(value);

    if (value && !validateVersion(value)) {
      setBuildVersionError("Phiên bản phải có định dạng xx.xx.xx (ví dụ: 1.2.3, 11.22.33)");
    } else {
      setBuildVersionError("");
    }
  };

  const handleConfirm = async () => {
    if (!validateVersion(buildVersion)) {
      setBuildVersionError("Phiên bản phải có định dạng xx.xx.xx (ví dụ: 1.2.3, 11.22.33)");
      return;
    }

    await onConfirm(buildVersion);
  };

  const handleClose = () => {
    setBuildVersion("");
    setBuildVersionError("");
    onClose();
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
      <DialogTitle sx={{ fontWeight: 700 }}>Nhập phiên bản</DialogTitle>
      <DialogContent>
        <TextField
          label="Phiên bản"
          value={buildVersion}
          onChange={handleVersionChange}
          fullWidth
          autoFocus
          sx={{ mt: 1 }}
          error={!!buildVersionError}
          helperText={buildVersionError || "Định dạng: xx.xx.xx (ví dụ: 1.2.3, 11.22.33)"}
        />
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose} disabled={isBuilding}>
          Huỷ
        </Button>
        <Button
          variant="contained"
          onClick={handleConfirm}
          disabled={!buildVersion.trim() || !!buildVersionError || isBuilding}
        >
          {isBuilding ? "Đang build..." : "OK"}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default BuildVersionDialog;
