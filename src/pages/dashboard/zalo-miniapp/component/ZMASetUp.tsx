import React, { useState } from "react";
import { Box, Tabs, Tab } from "@mui/material";
import Setting from "./ZMASetting";
import Overview from "./ZMAOverview";
import Version from "./Version";

const ZMASetUp = () => {
  const [tabValue, setTabValue] = useState(0);

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };
  return (
    <Box sx={{ maxWidth: "100%", margin: "auto", background: "#fff", borderRadius: 4, py: 2 }}>
      <Tabs
        value={tabValue}
        onChange={handleTabChange}
        aria-label="setup tabs"
        sx={{ padding: "0 20px" }}
      >
        <Tab
          value={0}
          sx={{
            textTransform: "none",
            color: " #000 !important",
            fontSize: "20px",
            fontWeight: "700",
          }}
          label="Tổng quan"
        />
        <Tab
          value={1}
          sx={{
            textTransform: "none",
            color: " #000 !important",
            fontSize: "20px",
            fontWeight: "700",
          }}
          label="Phiên bản"
        />
        <Tab
          value={2}
          sx={{
            textTransform: "none",
            color: " #000 !important",
            fontSize: "20px",
            fontWeight: "700",
          }}
          label="Cài đặt"
        />
      </Tabs>

      {tabValue === 0 && <Overview />}
      {tabValue === 1 && <Version tabValue={tabValue} />}
      {tabValue === 2 && <Setting />}
    </Box>
  );
};

export default ZMASetUp;
