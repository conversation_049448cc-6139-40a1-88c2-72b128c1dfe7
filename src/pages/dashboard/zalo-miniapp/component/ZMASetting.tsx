import React, { useState, useEffect } from "react";
import {
  Box,
  Typo<PERSON>,
  TextField,
  Button,
  Avatar,
  Stack,
  InputAdornment,
  IconButton,
} from "@mui/material";
import { shopSettingService } from "@/src/api/services/shop-setting/shop-setting.service";
import { useShop } from "@/src/api/hooks/shop/use-shop";
import ZaloIntegrationPopup from "./Popup";
import { useZaloAuth } from "@/src/api/hooks/zalo/use-zalo-auth";
import useSnackbar from "@/src/hooks/use-snackbar";
import { useStoreId } from "@/src/hooks/use-store-id";
import { Visibility, VisibilityOff } from "@mui/icons-material";
import { useAppSelector } from "@/src/redux/hooks";
import BuildVersionDialog from "./BuildVersionDialog";

const OA = [
  {
    id: 1,
    name: "OA",
    image: "https://via.placeholder.com/40",
  },
];

const ZMASetting = () => {
  const storeId = useStoreId();

  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const [formData, setFormData] = useState({
    partnerId: "",
    shopId: "",
    ownerName: "",
    miniAppId: "",
    zaloSecretKey: "",
    zaloAppId: "",
    zaloOAName: "",
    checkoutSecretKey: "",
    userAccessToken: "",
    userRefreshToken: "",
  });
  const [isLoading, setIsLoading] = useState(false);
  const { profile } = useAppSelector((state) => state.profile);
  const { getShop } = useShop();
  const snackbar = useSnackbar();
  const { fetchZaloAuthUrl, error: authError, setError: setAuthError } = useZaloAuth();

  const [showPassword, setShowPassword] = useState({
    checkoutSecretKey: false,
    zaloSecretKey: false,
    userAccessToken: false,
    userRefreshToken: false,
  });

  const [isBuildPopupOpen, setIsBuildPopupOpen] = useState(false);
  const [isBuilding, setIsBuilding] = useState(false);

  const handleClickShowPassword = (field) => {
    setShowPassword({
      ...showPassword,
      [field]: !showPassword[field],
    });
  };

  useEffect(() => {
    const fetchPartnerIdAndShopId = async () => {
      try {
        setFormData((prevData) => ({
          ...prevData,
          partnerId: profile.partnerId,
        }));

        const shopResponse = await getShop(0, 9999);

        if (shopResponse?.data?.[0]?.shopId) {
          setFormData((prevData) => ({
            ...prevData,
            shopId: shopResponse.data[0].shopId,
          }));
        }
      } catch (error) {}
    };

    fetchPartnerIdAndShopId();
    fetchShopSettings();
  }, [storeId]);

  const fetchShopSettings = async () => {
    try {
      // const shopResponse = await getShop(0, 9999);
      // const shopId = shopResponse?.data?.[0]?.shopId;
      // if (!shopId) return;

      setIsLoading(true);
      const response = await shopSettingService.getDetailShopSetting(storeId);

      if (response?.data) {
        // setZaloAppId(response.data.zaloAppId || "");
        setFormData((prev) => ({
          ...prev,
          checkoutSecretKey: response.data.checkoutSecretKey,
          miniAppId: response.data.miniAppId,
          ownerName: response.data.ownerName,
          partnerId: response.data.partnerId,
          shopId: response.data.shopId,
          zaloAppId: response.data.zaloAppId,
          zaloOAName: response.data.zaloOAName,
          zaloSecretKey: response.data.zaloSecretKey,
          userAccessToken: response.data.userAccessToken || "",
          userRefreshToken: response.data.userRefreshToken || "",
        }));
      }
    } catch (error) {
      console.error("Error fetching shop settings:", error);
      snackbar.error("Lỗi khi tải thông tin cấu hình");
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field) => (event) => {
    setFormData({ ...formData, [field]: event.target.value });
  };

  const handleSave = async () => {
    try {
      setIsLoading(true);
      const data = {
        partnerId: formData.partnerId,
        shopId: storeId,
        ownerName: formData.ownerName,
        miniAppId: formData.miniAppId,
        zaloAppId: formData.zaloAppId,
        zaloSecretKey: formData.zaloSecretKey,
        checkoutSecretKey: formData.checkoutSecretKey,
        userAccessToken: formData.userAccessToken,
        userRefreshToken: formData.userRefreshToken,
      };
      const response = await shopSettingService.createAndUpdateShopSetting(data);

      if (response?.status === 200) {
        snackbar.success("Cập nhật thành công");
      } else {
        snackbar.error("Failed to create shop setting. Please try again.");
      }
    } catch (error) {
      snackbar.error(`${error?.detail || error?.message || "Failed to create shop setting"}`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAuthorizeZalo = async () => {
    if (!storeId) {
      snackbar.error("Không tìm thấy ID cửa hàng.");
      return;
    }

    if (!formData.zaloAppId) {
      snackbar.error("Không tìm thấy ID ứng dụng Zalo");
      return;
    }

    setAuthError(null);

    const response = await fetchZaloAuthUrl(storeId);

    if (response?.url) {
      snackbar.success("Lấy URL ủy quyền thành công, đang chuyển hướng...");
      window.open(response?.url, "_blank");
    } else if (authError) {
      snackbar.error(authError || "Lỗi khi lấy URL ủy quyền Zalo. Vui lòng thử lại.");
    } else {
      snackbar.error("Không nhận được URL ủy quyền từ API.");
    }
  };

  const handleReloadShopSettings = () => {
    fetchShopSettings();
  };

  const handleBuildMiniApp = () => {
    setIsBuildPopupOpen(true);
  };

  const handleConfirmBuild = async (buildVersion: string) => {
    if (
      !formData.zaloAppId?.trim() ||
      !formData.zaloSecretKey?.trim() ||
      !formData.userAccessToken?.trim() ||
      !formData.userRefreshToken?.trim()
    ) {
      snackbar.error(
        "Vui lòng setup đầy đủ Zalo App ID, Secret Key, User Access Token, User Refresh Token trước khi build!"
      );
      return;
    }
    setIsBuilding(true);
    try {
      const response = await shopSettingService.createGithubTag({
        version: buildVersion,
        shopId: formData.shopId,
      });

      if (response?.data?.success === true) {
        snackbar.success("Đã bắt đầu build mini app, vui lòng đợi 3-5 phút!");
        setIsBuildPopupOpen(false);
      } else {
        const errorMessage = response?.data?.error || "Có lỗi xảy ra";
        snackbar.error(`Build mini app thất bại! ${errorMessage}`);
      }
    } catch (error) {
      let errorMessage = "Có lỗi xảy ra";
      if (error?.status === 409) {
        errorMessage = "Tag đã tồn tại, vui lòng thử với version khác";
        snackbar.error(errorMessage);
      } else if (error?.status === 400) {
        snackbar.error("Thông tin mini app bạn nhập chưa đúng, vui lòng kiểm tra lại");
      } else {
        snackbar.error(`Build mini app thất bại! ${errorMessage}`);
      }
    } finally {
      setIsBuilding(false);
    }
  };

  return (
    <Box sx={{ px: 4 }}>
      <Box sx={{ paddingBottom: "35px", borderBottom: "1px solid #D8D8D8" }}>
        <Box
          sx={{
            display: "flex",
            gap: 3,
            mt: 3,
            alignItems: "stretch",
            "@media(max-width: 767px)": {
              flexDirection: "column",
            },
          }}
        >
          <Box sx={{ flex: 1 }}>
            <Typography color="#000000" fontSize={"20px"} fontWeight={"700"} mb={"15px"}>
              Thông tin thanh toán
            </Typography>
            <Box
              sx={{
                border: "1px solid #979797",
                padding: "15px",
                borderRadius: "10px",
                height: "100%",
              }}
            >
              <Typography color="#000000" fontSize={"14px"} fontWeight={"700"}>
                Checkout Secret Key
              </Typography>
              <TextField
                fullWidth
                margin="normal"
                type={showPassword.checkoutSecretKey ? "text" : "password"}
                value={formData.checkoutSecretKey}
                onChange={handleInputChange("checkoutSecretKey")}
                sx={{ border: "1px solid #979797", borderRadius: "10px" }}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        onClick={() => handleClickShowPassword("checkoutSecretKey")}
                        edge="end"
                      >
                        {showPassword.checkoutSecretKey ? <VisibilityOff /> : <Visibility />}
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
              />
            </Box>
          </Box>

          <Box sx={{ flex: 1 }}>
            <Typography color="#000000" fontSize={"20px"} fontWeight={"700"} mb={"15px"}>
              Thông tin sở hữu Zalo MiniApp
            </Typography>
            <Box
              sx={{
                border: "1px solid #979797",
                padding: "15px",
                borderRadius: "10px",
                height: "100%",
              }}
            >
              <Typography color="#000000" fontSize={"14px"} fontWeight={"700"}>
                Chủ sở hữu
              </Typography>
              <TextField
                fullWidth
                margin="normal"
                value={formData.ownerName}
                onChange={handleInputChange("ownerName")}
                sx={{ border: "1px solid #979797", borderRadius: "10px" }}
              />
              <Typography color="#000000" fontSize={"14px"} fontWeight={"700"} mb={"5px"}>
                Xác thực Zalo OA
              </Typography>
              <Typography color="#797A7C" fontSize={"14px"} fontWeight={"400"} mb={"5px"}>
                Đã xác thực
              </Typography>
              <Typography color="#000000" fontSize={"14px"} fontWeight={"700"} mb={"5px"}>
                Xác thực tài liệu ( Y tế, Dược, Giáo dục, Nhà nước)
              </Typography>
              <Typography color="#797A7C" fontSize={"14px"} fontWeight={"400"} mb={"5px"}>
                Đã xác thực
              </Typography>
              <Typography color="#000000" fontSize={"14px"} fontWeight={"700"} mb={"5px"}>
                ID Ứng dụng gốc
              </Typography>
              <TextField
                fullWidth
                margin="normal"
                value={formData.zaloAppId}
                onChange={handleInputChange("zaloAppId")}
                sx={{ border: "1px solid #979797", borderRadius: "10px", marginTop: 0 }}
              />
              <Typography color="#000000" fontSize={"14px"} fontWeight={"700"} mb={"5px"}>
                ID Ứng dụng mini app
              </Typography>
              <TextField
                fullWidth
                margin="normal"
                value={formData.miniAppId}
                onChange={handleInputChange("miniAppId")}
                sx={{ border: "1px solid #979797", borderRadius: "10px", marginTop: 0 }}
              />
              <Typography color="#000000" fontSize={"14px"} fontWeight={"700"} mb={"5px"}>
                Zalo Secret Key
              </Typography>
              <TextField
                fullWidth
                margin="normal"
                type={showPassword.zaloSecretKey ? "text" : "password"}
                value={formData.zaloSecretKey}
                onChange={handleInputChange("zaloSecretKey")}
                sx={{ border: "1px solid #979797", borderRadius: "10px", marginTop: 0 }}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        onClick={() => handleClickShowPassword("zaloSecretKey")}
                        edge="end"
                      >
                        {showPassword.zaloSecretKey ? <VisibilityOff /> : <Visibility />}
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
              />
              <Typography color="#000000" fontSize={"14px"} fontWeight={"700"} mb={"5px"}>
                User Access Token
              </Typography>
              <TextField
                fullWidth
                margin="normal"
                type={showPassword.userAccessToken ? "text" : "password"}
                value={formData.userAccessToken}
                onChange={handleInputChange("userAccessToken")}
                sx={{ border: "1px solid #979797", borderRadius: "10px", marginTop: 0 }}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        onClick={() => handleClickShowPassword("userAccessToken")}
                        edge="end"
                      >
                        {showPassword.userAccessToken ? <VisibilityOff /> : <Visibility />}
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
              />
              <Typography color="#000000" fontSize={"14px"} fontWeight={"700"} mb={"5px"}>
                User Refresh Token
              </Typography>
              <TextField
                fullWidth
                margin="normal"
                type={showPassword.userRefreshToken ? "text" : "password"}
                value={formData.userRefreshToken}
                onChange={handleInputChange("userRefreshToken")}
                sx={{ border: "1px solid #979797", borderRadius: "10px", marginTop: 0 }}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        onClick={() => handleClickShowPassword("userRefreshToken")}
                        edge="end"
                      >
                        {showPassword.userRefreshToken ? <VisibilityOff /> : <Visibility />}
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
              />
            </Box>
          </Box>
        </Box>

        <Box sx={{ display: "flex", gap: 2, mt: 10, justifyContent: "flex-end" }}>
          <Button
            sx={{
              color: "#fff",
              fontSize: "14px",
              fontWeight: "500",
              background: "#2654FE",
              textTransform: "none",
            }}
            onClick={handleBuildMiniApp}
          >
            Build Mini App
          </Button>
          <Button
            sx={{
              color: "#fff",
              fontSize: "14px",
              fontWeight: "500",
              background: "#2654FE",
              textTransform: "none",
            }}
            onClick={handleSave}
          >
            {isLoading ? "Đang lưu..." : "Lưu"}
          </Button>
        </Box>
      </Box>

      <Box sx={{ py: 2 }}>
        <Stack
          flexDirection={"row"}
          justifyContent={"space-between"}
          sx={{
            "@media(max-width: 880px)": {
              flexDirection: "column",
              gap: "20px",
            },
          }}
        >
          <Stack>
            <Typography sx={{ fontSize: "20px", fontWeight: "700" }}>Liên kết OA</Typography>
            <Typography sx={{ fontSize: "14px", fontWeight: "400" }}>
              Liên kết và ủy quyền OA để sử dụng các tính năng chăm sóc khách hàng tự động thông qua
              Zalo OA (UID, ZNS)
            </Typography>
          </Stack>

          <Stack
            direction="row"
            spacing={2}
            sx={{ display: "flex", gap: 2, mt: 1, justifyContent: "flex-end" }}
          >
            <Button
              sx={{
                color: "#fff",
                fontSize: "14px",
                fontWeight: "500",
                background: "#2654FE",
                textTransform: "none",
                width: "fit-content",
              }}
              onClick={() => setIsPopupOpen(true)}
            >
              Thiết lập
            </Button>
          </Stack>
        </Stack>
        <Box
          mt={"21px"}
          sx={{
            border: "1px solid #979797",
            padding: "15px 20px",
            borderRadius: "10px",
            width: "calc(50% - 12px)",
            minHeight: "235px",
            display: "flex",
            flexDirection: "column",
            "@media(max-width: 767px)": {
              width: "100%",
            },
          }}
        >
          {OA.map((qa) => (
            <Box key={qa.id} sx={{ display: "flex", alignItems: "center", gap: 1 }}>
              <Avatar src={qa.image} sx={{ bgcolor: "#e0e0e0", width: 40, height: 40 }} />
              <Typography sx={{ color: "#000000", fontSize: "14px", fontWeight: "14px" }}>
                {formData.zaloOAName}
              </Typography>
            </Box>
          ))}

          <Button
            sx={{
              color: "#fff",
              fontSize: "14px",
              fontWeight: "500",
              background: "#2654FE",
              textTransform: "none",
              mt: "auto",
              alignSelf: "flex-end",
            }}
            onClick={handleAuthorizeZalo}
          >
            Ủy quyền
          </Button>
        </Box>
      </Box>
      <ZaloIntegrationPopup
        open={isPopupOpen}
        onClose={() => setIsPopupOpen(false)}
        onSuccess={handleReloadShopSettings}
        initialData={{}}
      />

      <BuildVersionDialog
        open={isBuildPopupOpen}
        onClose={() => setIsBuildPopupOpen(false)}
        onConfirm={handleConfirmBuild}
        isBuilding={isBuilding}
      />
    </Box>
  );
};

export default ZMASetting;
