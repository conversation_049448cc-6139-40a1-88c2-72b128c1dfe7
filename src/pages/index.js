import { Seo } from 'src/components/seo';
import { useAuth } from 'src/hooks/use-auth';
import { usePageView } from 'src/hooks/use-page-view';
import LoginPage from 'src/pages/auth/login';
import StoresPage from 'src/pages/store/stores';

const Page = () => {
  usePageView();

  const { isAuthenticated } = useAuth();

  const renderPage = () => {
    if (isAuthenticated) {
      const getLayout = StoresPage.getLayout || ((page) => page);
      return getLayout(<StoresPage />);
    }
    return <LoginPage />;
  };

  return (
    <>
      <Seo />
      <main>
        {renderPage()}
      </main>
    </>
  );
};

export default Page;
