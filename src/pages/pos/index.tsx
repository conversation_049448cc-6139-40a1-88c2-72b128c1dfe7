import { <PERSON>, <PERSON>ton, Dialog, Grid, DialogContent, Typography, DialogActions } from "@mui/material";
import { useState, useEffect, useCallback } from "react";
import Header from "src/components/Pos/header";
import Invoice from "src/components/Pos/Invoice";
import ProductList from "src/components/Pos/ProductList";
import PaymentMethodDialog from "@/src/components/Pos/paymentbill";
import VoucherSelection from "src/components/Pos/voucher";
import BackspaceIcon from "@mui/icons-material/Backspace";
import NumericKeypad from "src/components/Pos/minus-points";
import PaymentConfirmation from "src/pages/pos/payment";
import { useCart } from "@/src/api/hooks/cart/use-cart";
import useSnackbar from "@/src/hooks/use-snackbar";
import { useOrder } from "@/src/api/hooks/order/use-order";
import { CreateOrUpdateCartData } from "@/src/api/types/cart.types";
import { StorageService } from "nextjs-api-lib";
import { useStoreId } from "@/src/hooks/use-store-id";
import _ from "lodash";
import { useUser } from "@/src/api/hooks/user/use-user";
import TitleDialog from "src/components/dialog/TitleDialog";
import { useVoucher } from "@/src/api/hooks/voucher/use-voucher";
export interface CartItem {
  itemsId: any;
  variantValueOne: any;
  variantValueTwo: any;
  variantValueThree: any;
  id: number;
  itemsName: string;
  quantity: number;
  price: number;
  priceReal: number;
  originalPrice: number;
  variant: string;
}

const buttonStyles = {
  textTransform: "none",
  bgcolor: "#D9D9D9",
  color: "black",
  height: 50,
  whiteSpace: "nowrap",
};

const specialButtonStyles = {
  textTransform: "none",
  bgcolor: "#0036FF",
  color: "white",
  height: 50,
  fontSize: "2rem",
  whiteSpace: "nowrap",
};

const deleteButtonStyles = {
  textTransform: "none",
  bgcolor: "#FD7984",
  color: "white",
  height: 50,
  whiteSpace: "nowrap",
};

const cancelButtonStyles = {
  textTransform: "none",
  bgcolor: "#7E7E7E",
  color: "white",
  height: 50,
  whiteSpace: "nowrap",
};

const payButtonStyles = {
  textTransform: "none",
  bgcolor: "#FFC23E",
  color: "white",
  height: 150,
  fontSize: "1.5rem",
};

export default function HomePage() {
  const [tab, setTab] = useState(0);
  const [openNumericKeypad, setOpenNumericKeypad] = useState(false);
  const [openPaymentDialog, setOpenPaymentDialog] = useState(false);
  const [openVoucherDialog, setOpenVoucherDialog] = useState(false);
  const [openCashDialog, setOpenCashDialog] = useState(false);
  const [openBankDialog, setOpenBankDialog] = useState(false);
  const [bankName, setBankName] = useState("MBBANK");
  const [selectedCustomer, setSelectedCustomer] = useState<any>(null);
  const [cart, setCart] = useState<any>({
    listItems: [],
    price: 0,
    cartId: "",
    shopId: "",
    cartOrigin: "Pos",
  });
  const [selectedCartItemIndex, setSelectedCartItemIndex] = useState<number | null>(null);
  const { createOrUpdateCart, deleteCart, listCart } = useCart();
  const { detailUser } = useUser();
  const snackbar = useSnackbar();
  const [selectedBranch, setSelectedBranch] = useState(null);
  const { createOrder } = useOrder();
  const [orderData, setOrderData] = useState<any>(null);
  const [carts, setCarts] = useState([]);

  const partnerId = StorageService.get("partnerId") as string | null;
  const storeId = useStoreId();
  const [searchText, setSearchText] = useState("");
  const [skip, setSkip] = useState(0);
  const [hasMore, setHasMore] = useState(true);
  const [userFound, setUserFound] = useState(false);
  useEffect(() => {
    const branch = localStorage.getItem("selectedBranch");
    if (branch) {
      setSelectedBranch(JSON.parse(branch));
    }
  }, []);

  useEffect(() => {
    if (selectedCustomer) {
      const userInfo = selectedCustomer
        ? `${selectedCustomer.fullname} (${selectedCustomer.point} điểm) \n${selectedCustomer.phoneNumber}`
        : null;
      const userFullData = selectedCustomer;

      const updatedCart = {
        ...cart,
        userId: selectedCustomer?.userId,
        cartId: cart.cartId || "",
        shopId: selectedCustomer.shopId,
      };
      setCart(updatedCart);

      createOrUpdateCart(updatedCart)
        .then((response) => {
          if (response && response.data) {
            snackbar.success("Cập nhật giỏ hàng thành công");
            setCart(response.data);
            const updateCart = { ...response.data, userInfo, userFullData };

            setCarts((prevCarts) => {
              const exists = prevCarts.some((cartData) => cartData.cartId === updateCart.cartId);

              if (exists) {
                // Cập nhật phần tử nếu tồn tại
                return prevCarts.map((cartData) =>
                  cartData.cartId === updateCart.cartId ? { ...cartData, ...updateCart } : cartData
                );
              } else {
                // Thêm mới vào đầu danh sách
                return [updateCart, ...prevCarts];
              }
            });
            setUserFound(true);
          } else {
            snackbar.error("Lỗi khi cập nhật giỏ hàng");
          }
        })
        .catch((error) => {
          console.error("Lỗi API:", error);
          snackbar.error("Lỗi khi cập nhật giỏ hàng");
        });
    }
  }, [selectedCustomer]);

  const handleAddToCart = (newCart: any) => {
    setCart(newCart);

    const exists = carts.some((cartData) => cartData.cartId === newCart.cartId);

    if (exists) {
      setCarts((prevCarts) => {
        // Cập nhật phần tử nếu tồn tại
        return prevCarts.map((cartData) =>
          cartData.cartId === newCart.cartId ? { ...cartData, ...newCart } : cartData
        );
      });
    } else {
      loadMoreData(true);
    }
  };

  const handleIncreaseQuantity = async () => {
    if (selectedCartItemIndex !== null) {
      const updatedCart = { ...cart };
      updatedCart.listItems[selectedCartItemIndex].quantity += 1;
      updatedCart.price = updatedCart.listItems.reduce(
        (total, item) => total + item.price * item.quantity,
        0
      );
      try {
        const response = await createOrUpdateCart(updatedCart);
        if (response && response.data) {
          snackbar.success("Cập nhật giỏ hàng thành công");
          const responseCart = { ...response.data, userInfo: cart.userInfo };
          setCart(responseCart);
          setCarts((prevCarts) =>
            prevCarts.map((cartData) =>
              cartData.cartId === responseCart.cartId ? { ...responseCart, ...prevCarts } : cartData
            )
          );
        } else {
          snackbar.error("Lỗi khi cập nhật giỏ hàng");
        }
      } catch (error) {
        console.error("Lỗi API:", error);
        snackbar.error("Lỗi khi cập nhật giỏ hàng");
      }
    }
  };

  const handleDecreaseQuantity = async () => {
    if (selectedCartItemIndex !== null) {
      if (cart.listItems[selectedCartItemIndex].quantity > 1) {
        const updatedCart = { ...cart };
        updatedCart.listItems[selectedCartItemIndex].quantity -= 1;
        updatedCart.price = updatedCart.listItems.reduce(
          (total, item) => total + item.price * item.quantity,
          0
        );
        try {
          const response = await createOrUpdateCart(updatedCart);
          if (response && response.data) {
            snackbar.success("Cập nhật giỏ hàng thành công");
            const responseCart = { ...response.data, userInfo: cart.userInfo };
            setCart(responseCart);
            setCarts((prevCarts) =>
              prevCarts.map((cartData) =>
                cartData.cartId === responseCart.cartId
                  ? { ...responseCart, ...prevCarts }
                  : cartData
              )
            );
          } else {
            snackbar.error("Lỗi khi cập nhật giỏ hàng");
          }
        } catch (error) {
          console.error("Lỗi API:", error);
          snackbar.error("Lỗi khi cập nhật giỏ hàng");
        }
      }
    }
  };

  const handlePaymentClick = () => {
    if (!cart.listItems.length) {
      snackbar.error("Giỏ hàng trống. Vui lòng thêm sản phẩm trước khi thanh toán.");
      return;
    }
    setOpenCashDialog(true);
  };

  const handleCloseNumericKeypad = () => {
    setOpenNumericKeypad(false);
  };

  const handleOpenNumericKeypad = () => {
    if (!cart?.userId) {
      snackbar.error("Vui lòng chọn khách hàng.");
      return;
    }
    setOpenNumericKeypad(true);
  };

  const handleClosePaymentDialog = () => {
    setOpenPaymentDialog(false);
  };

  const [paymentSelected, setPaymentSelected] = useState(null);
  const handlePaymentSubmit = async (method: string, paymentMethods: any[]) => {
    const branch = localStorage.getItem("selectedBranch");
    if (!branch) {
      snackbar.error("Không tìm thấy điểm bán. Vui lòng chọn điểm bán.");
      return;
    }

    const selectedBranch = JSON.parse(branch);
    const selectedPaymentMethod = paymentMethods.find((pm) => pm.paymentId === method);

    const updatedCart = {
      ...cart,
      shopId: selectedBranch.shopId,
      branchId: selectedBranch.branchId,
      typePay: selectedPaymentMethod?.typePay,
    };

    try {
      // cập nhật lại phương thức thanh toán khi người dùng chọn
      const cartResponse = await createOrUpdateCart(updatedCart);
      if (cartResponse && cartResponse.data) {
        // tạo đơn hàng sau khi đã cập nhật pttt
        const orderResponse = await createOrder(cartResponse.data);
        if (orderResponse?.data) {
          snackbar.success("Tạo đơn hàng thành công");
          const clonedOrderData = JSON.parse(JSON.stringify(orderResponse.data));
          setOrderData(clonedOrderData);
          setCart({ listItems: [], price: 0 });
          setSearchText("");
          loadMoreData(true);

          setPaymentSelected(selectedPaymentMethod);
          if (selectedPaymentMethod?.paymentPhoto) {
            setBankName(selectedPaymentMethod.name);
            setOpenBankDialog(true);
          } else {
            setOpenCashDialog(true);
          }

          setOpenPaymentDialog(false);
        } else {
          snackbar.error("Lỗi khi tạo đơn hàng");
        }
      } else {
        snackbar.error("Lỗi khi cập nhật giỏ hàng");
      }
    } catch (error) {
      console.error("Lỗi API:", error);
      snackbar.error("Lỗi khi tạo đơn hàng");
    }
  };

  const handleCloseCashDialog = () => {
    setOpenCashDialog(false);
  };

  const handleCloseBankDialog = () => {
    setOpenBankDialog(false);
  };

  const handlePromotionClick = () => {
    if (!partnerId) {
      snackbar.error("Thiếu thông tin đối tác.");
      return;
    }
    if (!cart.shopId) {
      snackbar.error("Thiếu thông tin cửa hàng.");
      return;
    }
    if (!cart.userId) {
      snackbar.error("Thiếu thông tin người dùng.");
      return;
    }
    if (!cart.listItems.length) {
      snackbar.error("Danh sách sản phẩm trống.");
      return;
    }
    setOpenVoucherDialog(true);
  };
  const handleVoucherApplied = (updatedCart) => {
    setCart(updatedCart);
    setCarts((prevCarts) =>
      prevCarts.map((cartData) => (cartData.cartId === updatedCart.cartId ? updatedCart : cartData))
    );
  };
  const handleCloseVoucherDialog = () => {
    setOpenVoucherDialog(false);
  };

  const handleDeleteCartItem = async () => {
    if (selectedCartItemIndex !== null) {
      const updatedCart = {
        ...cart,
        listItems: cart.listItems.filter((_, index) => index !== selectedCartItemIndex),
      };
      if (updatedCart.listItems.length === 0) {
        updatedCart.voucherPromotion = [];
      }

      try {
        const response = await createOrUpdateCart(updatedCart);
        if (response && response.data) {
          snackbar.success("Xóa sản phẩm khỏi giỏ hàng thành công");
          const cartResponse = { ...response.data, userInfo: updatedCart.userInfo };
          setCart(cartResponse);
          setCarts((prevCarts) =>
            prevCarts.map((cartData) =>
              cartData.cartId === cartResponse.cartId ? { ...cartResponse, ...prevCarts } : cartData
            )
          );
          setSelectedCartItemIndex(null);
        } else {
          snackbar.error("Lỗi khi xóa sản phẩm khỏi giỏ hàng");
        }
      } catch (error) {
        console.error("Lỗi API:", error);
        snackbar.error("Lỗi khi xóa sản phẩm khỏi giỏ hàng");
      }
    }
  };

  const loadMoreData = (reset = false) => {
    const limit = 40;
    setSkip((prevSkip) => {
      const newSkip = reset ? 0 : prevSkip + limit;
      listCart({
        skip: newSkip,
        limit,
        shopId: storeId,
        origins: ["Pos"],
        branchId: selectedBranch.branchId,
      }).then(async (response) => {
        const cartList = response?.data?.data;

        // Gọi API lấy user song song nhưng chỉ khi có userId
        const updatedCarts = await Promise.all(
          cartList?.map(async (cart) => {
            if (storeId && cart?.userId) {
              try {
                const userResponse = await detailUser(storeId, cart.userId);
                return {
                  ...cart,
                  userInfo: `${userResponse.data.fullname} (${userResponse.data.point} điểm) \n${userResponse.data.phoneNumber}`,
                  userFullData: userResponse.data,
                }; // Thêm userName vào cart
              } catch (error) {
                console.error("Lỗi khi lấy thông tin user:", error);
                return { ...cart }; // Gán tên mặc định nếu lỗi
              }
            }
            return { ...cart }; // Nếu không có userId
          })
        );

        // Loại bỏ cart trùng lặp dựa trên cartId
        setCarts((prevList) => {
          const mergedCarts = reset ? updatedCarts : [...prevList, ...updatedCarts];

          // Loại bỏ trùng lặp bằng cách dùng Map
          const uniqueCarts = Array.from(
            new Map(mergedCarts.map((cart) => [cart.cartId, cart])).values()
          );

          if (reset && uniqueCarts.length > 0) {
            const firstCart = uniqueCarts[0];

            if (
              firstCart &&
              typeof firstCart === "object" &&
              firstCart !== null &&
              "userInfo" in firstCart &&
              typeof firstCart.userInfo === "string"
            ) {
              setSearchText(firstCart.userInfo);
              setUserFound(true);
            }
            setCart(firstCart);
          }

          return uniqueCarts;
        });
        setHasMore(newSkip + limit < response.data.total);
      });

      return newSkip; // Cập nhật state dựa trên giá trị cũ
    });
  };

  const handleClickAddNew = async () => {
    const cartData: CreateOrUpdateCartData = {
      cartNo: "",
      cartId: "",
      transactionId: "",
      partnerId: partnerId,
      userId: "",
      addressId: "",
      shopId: storeId,
      branchId: selectedBranch?.branchId || "",
      listItems: [],
      voucherPromotion: [],
      voucherTransport: [],
      price: 0,
      exchangePoints: 0,
      pointPrice: 0,
      voucherPromotionPrice: 0,
      voucherTransportPrice: 0,
      transportPrice: 0,
      transportService: "LCOD",
      statusDelivery: "InShop",
      typePay: "COD",
      cartOrigin: "Pos",
      created: new Date().toISOString(),
      updated: new Date().toISOString(),
    };

    try {
      const response = await createOrUpdateCart(cartData);

      if (response && response.data) {
        // setCarts((prevCarts) => [response.data, ...prevCarts]);
        setCart(response.data);
        snackbar.success("Thêm giỏ hàng thành công");
        loadMoreData(true);
        setSearchText("");
      } else {
        // snackbar.error('Lỗi khi thêm sản phẩm vào giỏ hàng');
        console.error("Response không hợp lệ:", response);
      }
    } catch (error) {
      console.error("Lỗi API:", error);
      // snackbar.error('Lỗi khi thêm sản phẩm vào giỏ hàng')
    }
  };
  const handleCancelOrder = async () => {
    if (cart.cartId) {
      try {
        await deleteCart(cart.cartId);
        snackbar.success("Hủy đơn hàng thành công");

        setCarts((prevCarts) => prevCarts.filter((c) => c.cartId !== cart.cartId));
        setCart({ listItems: [], price: 0, cartId: "", shopId: "" });
        setSelectedCustomer(null); // Clear the selected customer
        loadMoreData(true);
        setSelectedCustomer(null);
        setSearchText("");
      } catch (error) {
        console.error("Lỗi API:", error);
        snackbar.error("Lỗi khi hủy đơn hàng");
      }
    }
  };
  const [openPopup, setOpenPopup] = useState(false); // Add this state

  const handleOpenPopup = () => {
    setOpenPopup(true);
  };
  const clearCart = async () => {
    if (cart.cartId) {
      try {
        setCarts((prevCarts) => prevCarts.filter((c) => c.cartId !== cart.cartId));
        setCart({ listItems: [], price: 0, cartId: "", shopId: "" });
        setSelectedCustomer(null);
        loadMoreData(true);
        setSelectedCustomer(null);
        setSearchText("");
      } catch (error) {
        console.error("Lỗi API:", error);
        snackbar.error("Lỗi khi xóa giỏ hàng");
      }
    }
  };
  const handleClosePopup = () => {
    setOpenPopup(false);
  };

  const handleUsePoint = async (point: number) => {
    const updatedCart = { ...cart, exchangePoints: point };
    const response = await createOrUpdateCart(updatedCart);
    if (response && response.data) {
      handleCloseNumericKeypad();
      snackbar.success("Cập nhật giỏ hàng thành công");
      const responseCart = { ...response.data, userInfo: cart.userInfo };
      setCart(responseCart);
      setCarts((prevCarts) =>
        prevCarts.map((cartData) =>
          cartData.cartId === responseCart.cartId ? { ...responseCart, ...prevCarts } : cartData
        )
      );
    } else {
      snackbar.error("Lỗi khi cập nhật giỏ hàng");
    }
  };

  return (
    <Box sx={{ height: "100vh", bgcolor: "#f5f5f5", overflow: "hidden" }}>
      <Header />
      <Box sx={{ display: "flex", height: "calc(100vh - 64px)", overflow: "hidden" }}>
        <Grid container>
          <Grid item xs={12} md={4.5} sx={{ pr: { md: 1, xs: 0 } }}>
            <Invoice
              selectedCustomer={selectedCustomer}
              setSelectedCustomer={setSelectedCustomer}
              cart={cart}
              setSelectedCartItemIndex={setSelectedCartItemIndex}
              cartTotalPrice={cart.price}
              selectedCartItemIndex={selectedCartItemIndex}
              setCart={(cart) => {
                setCart(cart);
              }}
              carts={carts}
              handleClickAddNew={handleClickAddNew}
              setCarts={setCarts}
              searchText={searchText}
              setSearchText={setSearchText}
              skip={skip}
              hasMore={hasMore}
              loadMoreData={loadMoreData}
              userFound={userFound}
              setUserFound={setUserFound}
            />
          </Grid>
          <Grid item xs={12} md={1.4} sx={{ overflow: "auto" }}>
            <Box
              sx={{
                display: "flex",
                flexDirection: "column",
                gap: 1,
                mt: 1,
                background: "white",
                p: 1,
              }}
            >
              <Button variant="contained" sx={buttonStyles} onClick={handlePromotionClick}>
                Khuyến mại
              </Button>
              <Button variant="contained" sx={buttonStyles} onClick={handleOpenNumericKeypad}>
                Trừ điểm
              </Button>
              <Button variant="contained" sx={buttonStyles} onClick={handleOpenPopup}>
                Chức năng khác
              </Button>
              <Button variant="contained" sx={specialButtonStyles} onClick={handleIncreaseQuantity}>
                +
              </Button>
              <Button variant="contained" sx={specialButtonStyles} onClick={handleDecreaseQuantity}>
                -
              </Button>
              <Button
                variant="contained"
                sx={deleteButtonStyles}
                startIcon={<BackspaceIcon sx={{ ml: 1 }} />}
                onClick={handleDeleteCartItem}
              ></Button>
              <Button variant="contained" sx={cancelButtonStyles} onClick={handleCancelOrder}>
                Hủy đơn hàng
              </Button>
              <Button variant="contained" sx={payButtonStyles} onClick={handlePaymentClick}>
                Thanh toán
              </Button>
            </Box>
          </Grid>

          <Grid item xs={12} md={6} sx={{ mt: 1, ml: 1 }}>
            <ProductList
              cart={cart}
              selectedCustomer={selectedCustomer}
              onAddToCart={handleAddToCart}
            />
          </Grid>
        </Grid>
      </Box>
      <TitleDialog
        open={openPopup}
        handleClose={handleClosePopup}
        title="Chức năng đang phát triển"
        closeBtnTitle="Đóng"
        showActionDialog={false}
      >
        <DialogActions sx={{ justifyContent: "flex-end", p: 0 }}>
          <Button onClick={handleClosePopup} variant="contained" color="primary">
            Đóng
          </Button>
        </DialogActions>
      </TitleDialog>

      {/* <Dialog open={openNumericKeypad} onClose={handleCloseNumericKeypad} maxWidth="xs" fullWidth>
        <NumericKeypad onClose={handleCloseNumericKeypad} />
      </Dialog>

      <PaymentMethodDialog
        open={openPaymentDialog}
        onClose={handleClosePaymentDialog}
        onSubmit={handlePaymentSubmit}
        cart={cart}
      /> */}

      <Dialog open={openVoucherDialog} onClose={handleCloseVoucherDialog} maxWidth="md" fullWidth>
        <VoucherSelection
          onClose={handleCloseVoucherDialog}
          open={openVoucherDialog}
          partnerId={partnerId}
          shopId={cart.shopId}
          userId={cart?.userId || ""}
          listItems={cart.listItems}
          cartId={cart.cartId}
          cart={cart}
          onVoucherApplied={handleVoucherApplied}
          initialSelectedVoucher={cart.voucherPromotion || []}
        />
      </Dialog>

      <TitleDialog
        open={openNumericKeypad}
        handleClose={handleCloseNumericKeypad}
        title="Trừ điểm"
        closeBtnTitle="Đóng"
        showActionDialog={false}
      >
        <NumericKeypad onClose={handleCloseNumericKeypad} cart={cart} onUsePoint={handleUsePoint} />
      </TitleDialog>

      <Dialog
        open={openCashDialog}
        onClose={handleCloseCashDialog}
        maxWidth="lg"
        fullWidth
        sx={{
          "& .MuiDialog-paper": {
            borderRadius: 0,
            border: "none",
            width: "80%",
            maxWidth: "none",
          },
        }}
      >
        <DialogContent>
          <PaymentConfirmation
            onClose={handleCloseCashDialog}
            orderData={orderData}
            cart={cart}
            clearCart={clearCart}
          />
        </DialogContent>
      </Dialog>
    </Box>
  );
}
