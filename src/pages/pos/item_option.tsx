import { useItemOption } from '@/src/api/hooks/item-option/use-item-option';
import { styled, ToggleButton, ToggleButtonGroup, Typography } from '@mui/material';
import { on } from 'events';
import { useEffect, useState } from 'react';

export interface ItemOptionGroup {
  id: string;
  name: string;
  isMultiSelect: boolean;
  itemOptionGroupId: string;
  require: boolean;
  shopId: string;
  itemOptions: ItemOption[];
}

export interface ItemOption {
  id: string;
  name: string;
  price: number;
  itemOptionGroupId: string;
  itemOption: string;
  shopId: string;
  isSelected?: boolean;
}

const StyledToggleButtonGroup = styled(ToggleButtonGroup)(({ theme }) => ({
  display: 'grid',
  gridTemplateColumns: 'repeat(3, 1fr)',
  gap: '8px',
  justifyItems: 'center',
  '& .MuiToggleButton-root': {
    width: '100%',
    margin: 0,
  },
}));

const StyledToggleButton = styled(ToggleButton)(({ theme }) => ({
  backgroundColor: '#E8E8E8',
  border: 'none',
  borderRadius: '4px !important',
  margin: 0,
  flex: 1,
  whiteSpace: 'nowrap',
  overflow: 'hidden',
  textOverflow: 'ellipsis',
  '&.Mui-selected': {
    backgroundColor: '#BECCFF !important',
    color: '#000 !important',
  },
  '&:hover': {
    backgroundColor: '#E0E0E0',
  },
}));


interface ItemOptionsComponentProps {
  itemOptionIds: string[];
  onOptionsChange: (options: ItemOptionGroup[]) => void;
}

const ItemOptionsComponent = ({ itemOptionIds, onOptionsChange }: ItemOptionsComponentProps) => {
  const { listItemOptionByIds } = useItemOption();
  const [itemOptions, setItemOptions] = useState<ItemOptionGroup[]>([]);
  const [choseItemOption, setChoseItemOption] = useState<ItemOptionGroup[]>([]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const res = await listItemOptionByIds(itemOptionIds);

        setItemOptions(res.data.data);
        onOptionsChange(res.data.data);
      } catch (error) {
        console.error('Error fetching item options:', error);
      }
    };

    if (itemOptionIds && itemOptionIds.length > 0) {
      fetchData();
    }
  }, []);

  const handleToggleChange = (groupId, value) => {
    setItemOptions((prevOptions) =>
      prevOptions.map((group) => {
        if (group.id === groupId) {
          const updatedGroup = {
            ...group,
            itemOptions: group.itemOptions.map((opt) => {
              if (group.isMultiSelect) {
                return { ...opt, isSelected: value.includes(opt.id) };
              } else {
                return { ...opt, isSelected: opt.id === value };
              }
            }),
          };

          // Call onOptionsChange with updated selections
          onOptionsChange(prevOptions.map((g) => (g.id === groupId ? updatedGroup : g)));

          return updatedGroup;
        }
        return group;
      })
    );
  };

  return (
    <>
      {itemOptions.length == 0 ? (
        <Typography fontWeight="bold" mb={1}>
          Không có tùy chọn thêm
        </Typography>
      ) : (
        <>
          <Typography fontWeight="bold" mt={2}>
            Tùy chọn thêm
          </Typography>
          {itemOptions.map((group) => (
            <div key={group.id} style={{ width: '100%' }}>
              <Typography sx={{ mb: 1, color: '#000', fontSize: '14px' }}>
                {group.name} {group.require && <span style={{ color: 'red' }}>*</span>}
              </Typography>
              <StyledToggleButtonGroup
                value={group.itemOptions.filter((opt) => opt.isSelected).map((opt) => opt.id)}
                exclusive={!group.isMultiSelect}
                onChange={(event, value) => handleToggleChange(group.id, value)}
                sx={{ mb: 3 }}
              >
                {group.itemOptions.map((opt) => (
                  <StyledToggleButton key={opt.id} value={opt.id} selected={opt.isSelected}>
                    {opt.name} - {opt.price}đ
                  </StyledToggleButton>
                ))}
              </StyledToggleButtonGroup>
            </div>
          ))}
        </>
      )}
    </>
  );
};

export default ItemOptionsComponent;
