import { Email, Inventory, Sell } from "@mui/icons-material";
import BuyGroup from "./pages/dashboard/marketing/buy-group";
import PromotingsAds from "./pages/dashboard/marketing/promotings-ads";
import User from "./pages/dashboard/report/user";

export const paths = {
  index: "/",
  auth: {
    login: "/auth/login",
    forgotPassword: "/auth/forgot-password",
    resetPassword: "/auth/reset-password",
    forgotPassWordEmail: "/auth/forgot-password-email",
  },
  store: {
    index: "/store",
    create: "/store/create",
    settings: "/store/settings",
  },
  dashboard: {
    index: "/dashboard",
    listLocation: "/dashboard/pos/list-location",
    account: {
      index: "/dashboard/account",
      settings: "/dashboard/account/settings",
      balanceHistory: "/dashboard/account/balance-history",
    },
    store: {
      contentManagement: "/dashboard/store/content-management",
      popupAds: "/dashboard/store/content-management/popup-ads",
      createPopupAd: "/dashboard/store/content-management/create-popup-ad",
      themeSettings: "/dashboard/store/theme",
      articleCategories: "/dashboard/store/content-management/article-categories",
      createArticleCategory: "/dashboard/store/content-management/create-article-category",
      articles: "/dashboard/store/content-management/articles",
      createArticle: "/dashboard/store/content-management/create-article",
      generalSettings: "/dashboard/store/general-settings",
    },
    product: {
      index: "/product",
      categoryManagement: "/dashboard/product/category-management",
      createProductCategory: "/dashboard/product/category-management/create",
      supplier: "/dashboard/product/supplier-management",
      brand: "/dashboard/product/brand",
      evaluate: "/dashboard/product/evaluate",
      productManagement: "/dashboard/product/product-management",
      createProduct: "/dashboard/product/product-management/create",
      updateProduct: "/dashboard/product/product-management/detail",
    },
    academy: {
      index: "/dashboard/academy",
      courseDetails: "/dashboard/academy/courses/:courseId",
    },
    account: {
      index: "/dashboard/account",
      settings: "/dashboard/account/settings",
    },
    analytics: "/dashboard/analytics",
    blank: "/dashboard/blank",
    blog: {
      index: "/dashboard/blog",
      postDetails: "/dashboard/blog/:postId",
      postCreate: "/dashboard/blog/create",
    },
    calendar: "/dashboard/calendar",
    chat: "/dashboard/chat",
    crypto: "/dashboard/crypto",
    ecommerce: "/dashboard/ecommerce",
    fileManager: "/dashboard/file-manager",
    invoices: {
      index: "/dashboard/invoices",
      details: "/dashboard/invoices/:orderId",
    },
    jobs: {
      index: "/dashboard/jobs",
      create: "/dashboard/jobs/create",
      companies: {
        details: "/dashboard/jobs/companies/:companyId",
      },
    },
    kanban: "/dashboard/kanban",
    logistics: {
      index: "/dashboard/logistics",
      fleet: "/dashboard/logistics/fleet",
    },
    mail: "/dashboard/mail",
  },
  orders: {
    draft: {
      detail: "/dashboard/orders/draft/detail",
      create: "/dashboard/orders/draft/create",
      listdraftorder: "/dashboard/orders/draft/list-draft-order",
    },
    list: "/dashboard/orders/list",
    detail: "/dashboard/orders/detail",
  },
  app: {
    gamification: "/dashboard/app/gamification",
    marketing: "/dashboard/app/affiliate-marketing",
  },
  sale: {
    tiktok: "/dashboard/sale/tiktok",
  },
  report: {
    Inventory: "/dashboard/report/inventory",
    Sell: "/dashboard/report/sell",
    User: "/dashboard/report/user",
  },
  customers: {
    list: "/dashboard/customers/list",
    customerClassification: "/dashboard/customers/customer-classification",
    new: "/dashboard/customers/new",
    detail: "/dashboard/customers/detail",
    membershipPolicy: "/dashboard/customers/membership-policy",
    setMembershipPolicy: "/dashboard/customers/set-membership-policy",
    pointCustomer: "/dashboard/marketing/membership-points",
    membershipPoints: "/dashboard/marketing/membership-points",
    paidMembership: "/dashboard/customers/paid-membership",
  },
  itemGroups: {
    list: "/dashboard/item-groups/list",
    new: "/dashboard/item-groups/new",
    update: "/dashboard/item-groups/:id",
  },
  priceList: {
    list: "/dashboard/price-list/list",
  },
  marketing: {
    vouchers: {
      newPromotion: "/dashboard/marketing/vouchers/voucher_promotions/new",
      list: "/dashboard/marketing/vouchers/list",
      promotionDetail: "/dashboard/marketing/vouchers/voucher_promotions/detail",
      newTransport: "/dashboard/marketing/vouchers/voucher_transports/new",
      transportDetail: "/dashboard/marketing/vouchers/voucher_transports/detail",
      singleCodeList: "/dashboard/marketing/vouchers/single-code/list",
      pointPromotionsList: "/dashboard/marketing/vouchers/list?tab=point-promotion",
      newPointPromotion: "/dashboard/marketing/vouchers/point-promotions/new",
      pointPromotionDetail: "/dashboard/marketing/vouchers/point-promotions/detail",
      editPointPromotion: "/dashboard/marketing/vouchers/point-promotions/detail",
      pointVoucherCodes: "/dashboard/marketing/vouchers/point-promotions/voucher-codes",
      newCustomVoucher: "/dashboard/marketing/vouchers/custom/new",
      customVoucherDetail: "/dashboard/marketing/vouchers/custom/detail",
    },
    affiliate: {
      index: "/dashboard/marketing/affiliate-marketing",
      BuyGroup: "/dashboard/marketing/buy-group",
      Email: "/dashboard/marketing/email-marketing",
      PromotingsAds: "/dashboard/marketing/promotings-ads",
    },
    landingPage: "/marketing/landing-page",
    gamification: "/marketing/gamification",
    pushNotifications: "/marketing/push-notifications",
  },
  reports: {
    statistics: "/dashboard/zalo-miniapp",
  },
  pos: {
    home: "/pos",
  },
  marketing_automation: {
    home: "/dashboard/marketing-automation",
  },
  settings: {
    payment: {
      index: "/dashboard/settings/payment",
      update: "/dashboard/settings/payment/:id",
    },
    shipping: "/dashboard/settings/shipping",
    general: "/settings/general",
    location: {
      index: "/dashboard/settings/location",
      add: "/dashboard/settings/location/add",
      update: "/dashboard/settings/location/:id",
    },
    invoice: "/dashboard/settings/invoice",
    clause: {
      index: "/dashboard/settings/clause",
      add: "/dashboard/settings/clause/add",
      update: "/dashboard/settings/clause/:id",
    },
    tax: "/dashboard/settings/tax",
    domain: "/dashboard/settings/domain-name",
    ownDomainName: "/dashboard/settings/own-domain",
    ownDomainNameNextStep: "/dashboard/settings/own-domain-next",
    domainLink: "/dashboard/settings/domain-link",
    media: "/dashboard/settings/media",
    overview: "/dashboard/settings/overview",
    auth: "/dashboard/settings/auth",
    managementRole: "/dashboard/settings/management-role",
    employeeRole: "/dashboard/settings/employee-role",
    addRole: "/dashboard/settings/add-role",
    updateRole: "/dashboard/settings/update-role",
    service: "/dashboard/settings/service-package-management",
    bill: "/dashboard/settings/bill",
    billHistory: "/dashboard/settings/bill-history-detail",
    notification: "/dashboard/settings/notification",
    management: "/dashboard/settings/notification-management",
    managementAdd: "/dashboard/settings/notification-management-add",
    integration: "/dashboard/settings/integration",
  },
  package: {
    index: "/dashboard/package",
  },
  components: {
    index: "/components",
    dataDisplay: {
      detailLists: "/components/data-display/detail-lists",
      tables: "/components/data-display/tables",
      quickStats: "/components/data-display/quick-stats",
    },
    lists: {
      groupedLists: "/components/lists/grouped-lists",
      gridLists: "/components/lists/grid-lists",
    },
    forms: "/components/forms",
    modals: "/components/modals",
    charts: "/components/charts",
    buttons: "/components/buttons",
    typography: "/components/typography",
    colors: "/components/colors",
    inputs: "/components/inputs",
  },
  notAuthorized: "/401",
  notFound: "/404",
  serverError: "/500",
  checkout: "/checkout",
};
