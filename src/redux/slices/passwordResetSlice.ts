import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import { authService } from "../../api/services/auth/auth.service";
import { ErrorHandlerService } from "../../api/services/error-handler.service";

interface PasswordResetState {
  loading: boolean;
  success: boolean;
  error: string | null;
}

const initialState: PasswordResetState = {
  loading: false,
  success: false,
  error: null,
};

export const forgotPassword = createAsyncThunk(
  "passwordReset/forgot",
  async (email: string, { rejectWithValue }) => {
    try {
      const response = await authService.forgotPassword({ email });
      return response.data;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err);
      return rejectWithValue(errorResponse.detail);
    }
  }
);

export const resetPassword = createAsyncThunk(
  "passwordReset/reset",
  async ({ token, newPassword }: { token: string; newPassword: string }, { rejectWithValue }) => {
    try {
      const response = await authService.resetPassword(token, newPassword);
      return response.data;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err);
      return rejectWithValue(errorResponse.detail);
    }
  }
);

const passwordResetSlice = createSlice({
  name: "passwordReset",
  initialState,
  reducers: {
    resetState: (state) => {
      state.loading = false;
      state.success = false;
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Forgot Password
      .addCase(forgotPassword.pending, (state) => {
        state.loading = true;
        state.success = false;
        state.error = null;
      })
      .addCase(forgotPassword.fulfilled, (state) => {
        state.loading = false;
        state.success = true;
      })
      .addCase(forgotPassword.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Reset Password
      .addCase(resetPassword.pending, (state) => {
        state.loading = true;
        state.success = false;
        state.error = null;
      })
      .addCase(resetPassword.fulfilled, (state) => {
        state.loading = false;
        state.success = true;
      })
      .addCase(resetPassword.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const { resetState } = passwordResetSlice.actions;
export default passwordResetSlice.reducer;
