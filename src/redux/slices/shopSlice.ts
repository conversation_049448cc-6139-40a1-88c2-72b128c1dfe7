import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import { shopService } from "../../api/services/shop/shop.service";
import { ErrorHandlerService } from "../../api/services/error-handler.service";

interface ShopState {
  shops: any[];
  currentShop: any | null;
  loading: boolean;
  error: string | null;
}

const initialState: ShopState = {
  shops: [],
  currentShop: null,
  loading: false,
  error: null,
};

export const getShop = createAsyncThunk(
  "shop/getShop",
  async (
    { skip, limit, search }: { skip: number; limit: number; search?: string },
    { rejectWithValue }
  ) => {
    try {
      const response = await shopService.getShop(skip, limit, search || null);
      return response.data;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      return rejectWithValue(errorResponse.detail);
    }
  }
);

export const createShop = createAsyncThunk(
  "shop/createShop",
  async (data: any, { rejectWithValue }) => {
    try {
      const response = await shopService.createShop(data);
      return response.data;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      return rejectWithValue(errorResponse.detail);
    }
  }
);

export const updateLogo = createAsyncThunk(
  "shop/updateLogo",
  async ({ shopId, logoFile }: { shopId: string; logoFile: File }, { rejectWithValue }) => {
    try {
      const response = await shopService.updateLogoShop(shopId, logoFile);
      return response.data;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      return rejectWithValue(errorResponse.detail);
    }
  }
);

export const updateShop = createAsyncThunk(
  "shop/updateShop",
  async (data: any, { rejectWithValue }) => {
    try {
      const response = await shopService.updateShop(data);
      return response.data;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      return rejectWithValue(errorResponse.detail);
    }
  }
);

export const detailShop = createAsyncThunk(
  "shop/detailShop",
  async (shopId: string, { rejectWithValue }) => {
    try {
      const response = await shopService.detailShop(shopId);
      return response.data;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      return rejectWithValue(errorResponse.detail);
    }
  }
);

export const deleteShop = createAsyncThunk(
  "shop/deleteShop",
  async (shopId: string, { rejectWithValue }) => {
    try {
      const response = await shopService.deleteShop(shopId);
      return response.data;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      return rejectWithValue(errorResponse.detail);
    }
  }
);

export const updateShopDelivery = createAsyncThunk(
  "shop/updateShopDelivery",
  async (data: any, { rejectWithValue }) => {
    try {
      const response = await shopService.updateShopDelivery(data);
      return response.data;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      return rejectWithValue(errorResponse.detail);
    }
  }
);

const shopSlice = createSlice({
  name: "shop",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      // getShop
      .addCase(getShop.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getShop.fulfilled, (state, action: PayloadAction<any>) => {
        state.loading = false;
        state.shops = action.payload || [];
      })
      .addCase(getShop.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // createShop
      .addCase(createShop.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createShop.fulfilled, (state, action: PayloadAction<any>) => {
        state.loading = false;
        // Add new shop to the shops array if appropriate
        if (action.payload.shop) {
          state.shops = [...state.shops, action.payload.shop];
        }
      })
      .addCase(createShop.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // updateLogo
      .addCase(updateLogo.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateLogo.fulfilled, (state, action: PayloadAction<any>) => {
        state.loading = false;
        // Update the current shop if it matches
        if (state.currentShop && state.currentShop.id === action.payload.shop.id) {
          state.currentShop = { ...state.currentShop, ...action.payload.shop };
        }
      })
      .addCase(updateLogo.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // updateShop
      .addCase(updateShop.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateShop.fulfilled, (state, action: PayloadAction<any>) => {
        state.loading = false;
        state.currentShop = action.payload || null;
      })
      .addCase(updateShop.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // detailShop
      .addCase(detailShop.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(detailShop.fulfilled, (state, action: PayloadAction<any>) => {
        state.loading = false;
        state.currentShop = action.payload || null;
      })
      .addCase(detailShop.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // deleteShop
      .addCase(deleteShop.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteShop.fulfilled, (state, action: PayloadAction<any>) => {
        state.loading = false;
        // Remove deleted shop from shops array
        if (action.payload.shopId) {
          state.shops = state.shops.filter((shop) => shop.id !== action.payload.shopId);
        }
        // Reset currentShop if it was deleted
        if (state.currentShop && state.currentShop.id === action.payload.shopId) {
          state.currentShop = null;
        }
      })
      .addCase(deleteShop.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // updateShopDelivery
      .addCase(updateShopDelivery.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateShopDelivery.fulfilled, (state, action: PayloadAction<any>) => {
        state.loading = false;
        // Update the current shop if it matches
        if (state.currentShop && state.currentShop.id === action.payload.shop.id) {
          state.currentShop = { ...state.currentShop, ...action.payload.shop };
        }
        // Update shop in shops array
        state.shops = state.shops.map((shop) =>
          shop.id === action.payload.shop.id ? { ...shop, ...action.payload.shop } : shop
        );
      })
      .addCase(updateShopDelivery.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export default shopSlice.reducer;
