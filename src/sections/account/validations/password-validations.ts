import * as Yup from "yup";
import { tokens } from "@/src/locales/tokens";
const newPasswordValidationRules = Yup.string()
  .required("Mật khẩu mới là bắt buộc")
  .min(8, "Mật khẩu phải có ít nhất 8 ký tự")
  .matches(/[a-z]/, "Mật khẩu phải có ít nhất 1 chữ thường")
  .matches(/[A-Z]/, "Mật khẩu phải có ít nhất 1 chữ hoa")
  .matches(/[0-9]/, "Mật khẩu phải có ít nhất 1 chữ số")
  .matches(/[^a-zA-Z0-9]/, "Mật khẩu phải có ít nhất 1 ký tự đặc biệt");

const confirmPasswordValidationRules = Yup.string()
  .oneOf([Yup.ref("newPassword")], "<PERSON>ật khẩu xác nhận không khớp")
  .required("Xác nhận mật khẩu là bắt buộc");

export const passwordChangeSchema = Yup.object({
  oldPassword: Yup.string().required("Mật khẩu cũ là bắt buộc"),
  newPassword: Yup.string()
    .required("Mật khẩu mới là bắt buộc")
    .min(8, "Mật khẩu phải có ít nhất 8 ký tự")
    .matches(/[a-z]/, "Mật khẩu phải có ít nhất 1 chữ thường")
    .matches(/[A-Z]/, "Mật khẩu phải có ít nhất 1 chữ hoa")
    .matches(/[0-9]/, "Mật khẩu phải có ít nhất 1 chữ số")
    .matches(/[^a-zA-Z0-9]/, "Mật khẩu phải có ít nhất 1 ký tự đặc biệt"),
  confirmPassword: Yup.string()
    .oneOf([Yup.ref("newPassword")], "Mật khẩu xác nhận không khớp")
    .required("Xác nhận mật khẩu là bắt buộc"),
});

export const passwordChangeInitialValues = {
  oldPassword: "",
  newPassword: "",
  confirmPassword: "",
  submit: null,
};

export const resetPasswordSchema = Yup.object({
  newPassword: newPasswordValidationRules,
  confirmPassword: confirmPasswordValidationRules,
});

export const resetPasswordInitialValues = {
  newPassword: "",
  confirmPassword: "",
  submit: null,
};

export interface PasswordFormValues {
  oldPassword: string;
  newPassword: string;
  confirmPassword: string;
  submit: null;
}
