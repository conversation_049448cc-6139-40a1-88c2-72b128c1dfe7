import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import { tagsData } from '../_mock/tag-data';
import { userService } from '../api/services/user/user.service';

interface initListTagState {
  tags: TagState[];
  loading: boolean;
}
interface TagState {
  id: any;
  name: string | null;
}

const initialState: initListTagState = {
  tags: [],
  loading: false
};

interface listTagState {
  queryData?: string;
  bodyData?: {
    search: string;
    shopId: string;
  };
}

//fetch tag
export const fetchTags = createAsyncThunk(
  'tag/fetchTag',
  async ({ queryData = '?skip=0&limit=20', bodyData = { search: '', shopId: '' } }: listTagState) => {
    const response = await userService.listTag(queryData, bodyData);
    if (response.data) {
      return response.data.data;
    } else {
      return [];
    }
  }
);

//Thêm tag mới
export const addToTags = createAsyncThunk('tag/addToTags', async (tag: string) => {
  const response = { id: Date.now(), name: tag };
  return response;
});

const listTagSlice = createSlice({
  name: 'tag',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchTags.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchTags.fulfilled, (state, action) => {
        state.loading = false;
        state.tags = action.payload;
      })
      .addCase(fetchTags.rejected, (state) => {
        state.loading = false;
      })
      .addCase(addToTags.pending, (state) => {
        state.loading = true;
      })
      .addCase(addToTags.fulfilled, (state, action) => {
        state.loading = false;
        state.tags.push(action?.payload);
      })
      .addCase(addToTags.rejected, (state) => {
        state.loading = false;
      });
  }
});

export default listTagSlice.reducer;
