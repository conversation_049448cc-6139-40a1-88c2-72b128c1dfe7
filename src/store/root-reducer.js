import { combineReducers } from "@reduxjs/toolkit";

// import { reducer as calendarReducer } from 'src/slices/calendar';
// import { reducer as chatReducer } from 'src/slices/chat';
// import { reducer as kanbanReducer } from 'src/slices/kanban';
// import { reducer as mailReducer } from 'src/slices/mail';
import listTagReducer from "@/src/slices/listTagSlice";
import addressReducer from "@/src/slices/addressSlice";
import profileReducer from "@/src/redux/slices/profileSlice";
import shopReducer from "@/src/redux/slices/shopSlice";

export const rootReducer = combineReducers({
  // calendar: calendarReducer,
  // chat: chatReducer,
  // kanban: kanbanReducer,
  // mail: mailReducer
  listTag: listTagReducer,
  address: addressReducer,
  profile: profileReducer,
  shop: shopReducer,
});
