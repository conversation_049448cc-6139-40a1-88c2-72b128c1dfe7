import { SxProps, Theme } from "@mui/material";

export interface ActionButtonStyleConfig {
  variant?: "outlined" | "contained" | "text";
  size?: "small" | "medium" | "large";
  color?: "primary" | "secondary" | "success" | "error" | "warning" | "info";
  fullWidth?: boolean;
  customSx?: SxProps<Theme>;
}

export const actionButtonBaseStyles: SxProps<Theme> = {
  borderRadius: "8px",
  fontWeight: 600,
  lineHeight: 1.5,
  minWidth: "unset",
  whiteSpace: "nowrap",
  textTransform: "none",
};

export const actionButtonSizes = {
  small: {
    fontSize: "12px",
    padding: "4px 8px",
    "& .MuiButton-startIcon": {
      marginRight: "4px",
      "& > *:nth-of-type(1)": {
        fontSize: "16px",
      },
    },
  },
  medium: {
    fontSize: { xs: "12px", sm: "14px" },
    padding: { xs: "6px 12px", sm: "8px 18px" },
    "& .MuiButton-startIcon": {
      marginRight: { xs: "6px", sm: "8px" },
      "& > *:nth-of-type(1)": {
        fontSize: { xs: "18px", sm: "20px" },
      },
    },
  },
  large: {
    fontSize: "16px",
    padding: "12px 24px",
    "& .MuiButton-startIcon": {
      marginRight: "12px",
      "& > *:nth-of-type(1)": {
        fontSize: "24px",
      },
    },
  },
};

export const actionButtonVariants = {
  outlined: {
    color: "#2654FE",
    borderColor: "#2654FE",
    "&:hover": {
      borderColor: "#1A3FCC",
      backgroundColor: "rgba(38, 84, 254, 0.04)",
    },
  },
  contained: {
    background: "#2654FE",
    color: "white",
    "&:hover": {
      background: "#1A3FCC",
    },
    "&:disabled": {
      background: "#E0E0E0",
      color: "#9E9E9E",
    },
  },
  text: {
    color: "#2654FE",
    "&:hover": {
      backgroundColor: "rgba(38, 84, 254, 0.04)",
    },
  },
};

export const actionButtonColors = {
  primary: "#2654FE",
  secondary: "#757575",
  success: "#4CAF50",
  error: "#F44336",
  warning: "#FF9800",
  info: "#2196F3",
};

export const generateActionButtonStyles = (config: ActionButtonStyleConfig): SxProps<Theme> => {
  const {
    variant = "outlined",
    size = "medium",
    color = "primary",
    fullWidth = false,
    customSx = {},
  } = config;

  const baseStyles = { ...actionButtonBaseStyles };
  const sizeStyles = actionButtonSizes[size];
  const variantStyles = actionButtonVariants[variant];

  let colorStyles = {};
  if (color !== "primary") {
    const colorValue = actionButtonColors[color];
    if (variant === "outlined") {
      colorStyles = {
        color: colorValue,
        borderColor: colorValue,
        "&:hover": {
          borderColor: colorValue,
          backgroundColor: `${colorValue}0A`,
        },
      };
    } else if (variant === "contained") {
      colorStyles = {
        background: colorValue,
        "&:hover": {
          background: colorValue,
          filter: "brightness(0.9)",
        },
      };
    } else if (variant === "text") {
      colorStyles = {
        color: colorValue,
        "&:hover": {
          backgroundColor: `${colorValue}0A`,
        },
      };
    }
  }

  const widthStyles = fullWidth ? { width: "100%" } : {};

  return {
    ...baseStyles,
    ...sizeStyles,
    ...variantStyles,
    ...colorStyles,
    ...widthStyles,
    ...customSx,
  };
};
