export interface IPayment {
  typePay: string;
  paymentId: string;
  name: string;
  detail: string;
  isActive: boolean;
  shopId: string;
  platform: string;
  // --- Extended fields for Sepay/bank info ---
  bankAccountNumber?: string;
  customerBankName?: string;
  bankShortCode?: string;
  identificationNumber?: string;
  phoneNumber?: string;
  alias?: string;
  paymentPhoto?: string;
  position?: number;
}

export interface IBankInfo {
  id: number;
  name: string;
  code: string;
  bin: string;
  shortName: string;
  logo: string;
  transferSupported: number;
  lookupSupported: number;
  short_name: string;
  support: number;
  isTransfer: number;
  swift_code: string;
}
