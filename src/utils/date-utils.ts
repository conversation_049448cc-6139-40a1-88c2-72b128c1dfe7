import dayjs from "dayjs";

// Format date for API requests (YYYY-MM-DD)
export const formatDateForApi = (date: Date | string | null): string => {
  if (!date) return "";

  const dateObject = date instanceof Date ? date : new Date(date);

  if (isNaN(dateObject.getTime())) {
    console.error("Invalid date:", date);
    return "";
  }

  const day = String(dateObject.getDate()).padStart(2, "0");
  const month = String(dateObject.getMonth() + 1).padStart(2, "0");
  const year = dateObject.getFullYear();
  return `${year}-${month}-${day}`;
};

// Format datetime for API requests (YYYY-MM-DDTHH:mm:ss)
export const formatDateTimeForApi = (date: Date | string | null): string => {
  if (!date) return "";

  const dateObject = date instanceof Date ? date : new Date(date);

  if (isNaN(dateObject.getTime())) {
    console.error("Invalid date:", date);
    return "";
  }

  const hours = String(dateObject.getHours()).padStart(2, "0");
  const minutes = String(dateObject.getMinutes()).padStart(2, "0");
  const day = String(dateObject.getDate()).padStart(2, "0");
  const month = String(dateObject.getMonth() + 1).padStart(2, "0");
  const year = dateObject.getFullYear();
  return `${year}-${month}-${day}T${hours}:${minutes}:00`;
};

// Format date for display (DD/MM/YYYY)
export const formatDateDisplay = (date: Date | string | null): string => {
  if (!date) return "";

  const dateObject = date instanceof Date ? date : new Date(date);

  if (isNaN(dateObject.getTime())) {
    console.error("Invalid date:", date);
    return "";
  }

  const day = String(dateObject.getDate()).padStart(2, "0");
  const month = String(dateObject.getMonth() + 1).padStart(2, "0");
  const year = dateObject.getFullYear();
  return `${day}/${month}/${year}`;
};

// Format datetime for display (HH:mm, DD/MM/YYYY)
export const formatDateTimeDisplay = (date: Date | string | null): string => {
  if (!date) return "";
  const dateObject = date instanceof Date ? date : new Date(date);

  if (isNaN(dateObject.getTime())) {
    console.error("Invalid date:", date);
    return "";
  }
  return dayjs(dateObject).add(7, "h").format("HH:mm DD/MM/YYYY");
};
