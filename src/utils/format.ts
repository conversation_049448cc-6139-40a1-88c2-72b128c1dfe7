interface TruncatedTextProps {
  text: string;
  link?: string;
  isGranted?: boolean;
  openInNewTab?: boolean;
  actionNeed?: () => void;
  width?: string;
  fontSize?: number;
}

/**
 * Regular expression for validating Vietnamese phone numbers
 * Matches formats:
 * - Starting with +84, 84, 0
 * - Followed by 9 digits where first digit is 3,5,7,8,9
 * - Total length should be 10 digits (excluding prefix)
 */
export const PHONE_REGEX = /^(\+84|84|0)?([3|5|7|8|9])([0-9]{8})$/;

/**
 * Formats a phone number to the standard format +84xxxxxxxxx
 * 
 * @param phone - The phone number to format
 * @returns The formatted phone number
 * 
 * @example
 * formatPhoneNumber('0912345678') // returns '+84912345678'
 * formatPhoneNumber('84912345678') // returns '+84912345678'
 * formatPhoneNumber('+84912345678') // returns '+84912345678'
 * formatPhoneNumber('912345678') // returns '+84912345678'
 */
export const formatPhoneNumber = (phoneNumber: string): string => {
  const phoneWithoutPrefix = phoneNumber.replace('+84', '');
  const cleanPhone = phoneWithoutPrefix.startsWith('0') ? phoneWithoutPrefix.slice(1) : phoneWithoutPrefix;
  return `+84${cleanPhone}`;
};

/**
 * Validates if a phone number matches Vietnamese format
 * 
 * @param phone - The phone number to validate
 * @returns boolean - True if valid, false otherwise
 * 
 * @example
 * isValidPhoneNumber('+84912345678') // returns true
 * isValidPhoneNumber('0912345678') // returns true
 * isValidPhoneNumber('1234567') // returns false
 */
export const isValidPhoneNumber = (phone: string): boolean => {
  const formatted = formatPhoneNumber(phone);
  return PHONE_REGEX.test(formatted.replace('+84', ''));
};

export const formatDisplayPhoneNumber = (phoneNumber: string): string => {
  if (phoneNumber == undefined || phoneNumber == "") return "";
  // Remove +84 prefix if exists and add 0 prefix
  const cleanPhone = phoneNumber.replace('+84', '');
  return cleanPhone.startsWith('0') ? cleanPhone : `0${cleanPhone}`;
}; 

export const formatTruncatedText = ({
  text,
  link = "",
  isGranted = false,
  openInNewTab = false,
  actionNeed,
  width = '500px',
  fontSize = 14,
}: TruncatedTextProps) => {
  const styleProps = isGranted
    ? {
        fontSize: fontSize,
        fontWeight: "500",
        color: "#2654FE",
        width: width,
      }
    : {};

  return {
    text,
    isLink: isGranted,
    link: isGranted ? link : "",
    openInNewTab: openInNewTab,
    typographyProps: {
      sx: styleProps,
    },
    actionNeed: isGranted ? actionNeed || (() => {}) : () => {},
  };
};