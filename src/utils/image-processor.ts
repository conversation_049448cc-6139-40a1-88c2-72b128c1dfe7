import { ImageType } from "nextjs-api-lib";
import { logger } from "@/src/utils/logger";

interface ImageProcessOptions {
  maxSizeMB?: number;
  maxWidth?: number;
  maxHeight?: number;
  quality?: number;
  maintainAspectRatio?: boolean;
  allowedTypes?: ImageType[];
}

export class ImageProcessor {
  private static readonly DEFAULT_OPTIONS: ImageProcessOptions = {
    maxSizeMB: 1,
    maxWidth: 1920,
    maxHeight: 1080,
    quality: 0.8,
    maintainAspectRatio: true,
    allowedTypes: [ImageType.JPEG, ImageType.PNG, ImageType.WEBP],
  };

  static async processImage(file: File, options?: Partial<ImageProcessOptions>): Promise<File> {
    try {
      const finalOptions = { ...this.DEFAULT_OPTIONS, ...options };

      // Calculate dimensions
      const img = await this.createImageElement(file);
      const { width, height } = this.calculateDimensions(
        img.width,
        img.height,
        finalOptions.maxWidth!,
        finalOptions.maxHeight!,
        finalOptions.maintainAspectRatio!
      );

      // Handle transparent images if specified
      if (this.isTransparentImage(file)) {
        return await this.preserveTransparency(file, img.naturalWidth, img.naturalHeight);
      }

      logger.debug("Image processed successfully", {
        fileName: file.name,
        originalSize: file.size,
        compressedSize: file.size,
        dimensions: { width, height },
      });

      return file;
    } catch (error) {
      logger.error("Error processing image:", error);
      throw error;
    }
  }

  private static createImageElement(file: File): Promise<HTMLImageElement> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => resolve(img);
      img.onerror = reject;
      img.src = URL.createObjectURL(file);
    });
  }

  private static calculateDimensions(
    originalWidth: number,
    originalHeight: number,
    maxWidth: number,
    maxHeight: number,
    maintainAspectRatio: boolean
  ): { width: number; height: number } {
    if (!maintainAspectRatio) {
      return { width: maxWidth, height: maxHeight };
    }

    const aspectRatio = originalWidth / originalHeight;
    let width = originalWidth;
    let height = originalHeight;

    if (width > maxWidth) {
      width = maxWidth;
      height = width / aspectRatio;
    }

    if (height > maxHeight) {
      height = maxHeight;
      width = height * aspectRatio;
    }

    return {
      width: Math.round(width),
      height: Math.round(height),
    };
  }

  // New method to check if an image is transparent
  private static isTransparentImage(file: File): boolean {
    // Check file type for PNG or WebP which support transparency
    return file.type === "image/png" || file.type === "image/webp";
  }

  // New method to preserve transparency
  private static async preserveTransparency(
    file: File,
    width: number,
    height: number
  ): Promise<File> {
    return new Promise<File>((resolve, reject) => {
      const img = new Image();
      img.src = URL.createObjectURL(file);
      img.onload = async () => {
        const canvas = document.createElement("canvas");
        const ctx = canvas.getContext("2d");

        if (ctx) {
          // Set canvas dimensions
          canvas.width = width;
          canvas.height = height;

          // Ensure transparency is preserved
          ctx.clearRect(0, 0, width, height);
          ctx.drawImage(img, 0, 0, width, height);

          // Convert canvas to file while maintaining transparency
          canvas.toBlob(
            (blob) => {
              if (blob) {
                const processedFile = new File([blob], file.name, { type: file.type });
                resolve(processedFile);
              } else {
                reject(new Error("Failed to create blob"));
              }
            },
            file.type,
            0.9 // High quality
          );
        } else {
          reject(new Error("Could not create canvas context"));
        }
      };
      img.onerror = () => reject(new Error("Failed to load image"));
    });
  }
}
