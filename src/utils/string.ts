/**
 * Cắt ngắn chuỗi và thêm dấu ... nếu chuỗi dài hơn số ký tự cho phép
 * @param str Chuỗi cần cắt ngắn
 * @param num Số ký tự tối đa
 * @returns Chuỗi đã được cắt ngắn
 * @example
 * truncateString('Hello World', 5) // 'Hello...'
 * truncateString('Hi', 5) // 'Hi'
 */
export const truncateString = (str: string, num: number): string => {
  if (!str) return '';
  if (str.length <= num) return str;
  return str.slice(0, num) + '...';
};

/**
 * Lấy tên file từ đường dẫn URL
 * @param url Đường dẫn URL của file
 * @returns Tên file
 * @example
 * getFileName('https://example.com/path/to/image.jpg') // 'image.jpg'
 * getFileName('image.jpg') // 'image.jpg'
 */
export const getFileName = (url: string): string => {
  if (!url) return '';
  
  try {
    // Xử lý URL có dấu / hoặc \
    const fileName = url.split(/[/\\]/).pop() || '';
    
    // Decode URI để xử lý các ký tự đặc biệt trong tên file
    const decodedFileName = decodeURIComponent(fileName);
    
    // Nếu tên file quá dài thì cắt ngắn
    return truncateString(decodedFileName, 20);
  } catch (error) {
    console.error('Error getting file name:', error);
    return 'Unknown file';
  }
};

/**
 * Chuyển chuỗi thành dạng slug
 * @param str Chuỗi cần chuyển
 * @returns Chuỗi dạng slug
 * @example
 * slugify('Hello World') // 'hello-world'
 */
export const slugify = (str: string): string => {
  if (!str) return '';
  return str
    .toLowerCase()
    .trim()
    .replace(/[^\w\s-]/g, '')
    .replace(/[\s_-]+/g, '-')
    .replace(/^-+|-+$/g, '');
};

/**
 * Chuyển chuỗi thành dạng capitalize
 * @param str Chuỗi cần chuyển
 * @returns Chuỗi với ký tự đầu viết hoa
 * @example
 * capitalize('hello') // 'Hello'
 */
export const capitalize = (str: string): string => {
  if (!str) return '';
  return str.charAt(0).toUpperCase() + str.slice(1);
};

/**
 * Loại bỏ dấu tiếng Việt
 * @param str Chuỗi cần xử lý
 * @returns Chuỗi không dấu
 * @example
 * removeVietnameseTones('Xin chào') // 'Xin chao'
 */
export const removeVietnameseTones = (str: string): string => {
  if (!str) return '';
  str = str.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g, 'a');
  str = str.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g, 'e');
  str = str.replace(/ì|í|ị|ỉ|ĩ/g, 'i');
  str = str.replace(/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g, 'o');
  str = str.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g, 'u');
  str = str.replace(/ỳ|ý|ỵ|ỷ|ỹ/g, 'y');
  str = str.replace(/đ/g, 'd');
  str = str.replace(/À|Á|Ạ|Ả|Ã|Â|Ầ|Ấ|Ậ|Ẩ|Ẫ|Ă|Ằ|Ắ|Ặ|Ẳ|Ẵ/g, 'A');
  str = str.replace(/È|É|Ẹ|Ẻ|Ẽ|Ê|Ề|Ế|Ệ|Ể|Ễ/g, 'E');
  str = str.replace(/Ì|Í|Ị|Ỉ|Ĩ/g, 'I');
  str = str.replace(/Ò|Ó|Ọ|Ỏ|Õ|Ô|Ồ|Ố|Ộ|Ổ|Ỗ|Ơ|Ờ|Ớ|Ợ|Ở|Ỡ/g, 'O');
  str = str.replace(/Ù|Ú|Ụ|Ủ|Ũ|Ư|Ừ|Ứ|Ự|Ử|Ữ/g, 'U');
  str = str.replace(/Ỳ|Ý|Ỵ|Ỷ|Ỹ/g, 'Y');
  str = str.replace(/Đ/g, 'D');
  return str;
}; 