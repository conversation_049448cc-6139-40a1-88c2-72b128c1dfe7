import { tokens } from "@/src/locales/tokens";
import { useTranslation } from "react-i18next";
import * as Yup from "yup";
// Định nghĩa schema Yup dùng chung cho các component
export const emailValidationSchema = (t) =>
  Yup.string()
    .email(t(tokens.auth.emailInvalid))
    .notRequired()
    .max(255, "Email không được vượt quá 255 ký tự");

export const nameValidationSchema = (t) =>
  Yup.string()
    .trim()
    .required(t(tokens.settings.firstNameRequired))
    .max(255, "Tên không được vượt quá 255 ký tự");

// Tạo một hàm để trả về schema với `t`
export const createPhoneValidationSchema = (t) =>
  Yup.string()
    .required(t(tokens.settings.phoneRequired))
    .matches(/^(\+84|0)(3|5|7|8|9)\d{8}$/, t(tokens.settings.phoneInvalid));

export const shippingPhoneValidationSchema = (t) =>
  Yup.string()
    // .required(t(tokens.settings.phoneRequired))
    .matches(/^(\+84|0)(3|5|7|8|9)\d{8}$/, t(tokens.settings.phoneInvalid));

export const addressValidationSchema = (t) =>
  Yup.string().required(t("shippingAddress.address.required"));

// Tạo schema chính với các tham số dịch
export const createBasicCustomerInfoSchema = (t) =>
  Yup.object().shape({
    email: emailValidationSchema(t),
    phone: createPhoneValidationSchema(t),
    name: nameValidationSchema(t),
    shippingAddressPhone: Yup.string()
      .nullable()
      .trim()
      .test("conditional-validation", "", function (value) {
        const {
          shippingAddressFullname,
          shippingAddressAddress,
          shippingAddressProvince,
          shippingAddressDistrict,
          shippingAddressWard,
        } = this.parent;

        const isOtherFieldFilled =
          shippingAddressFullname ||
          shippingAddressAddress ||
          shippingAddressProvince ||
          shippingAddressDistrict ||
          shippingAddressWard;

        // Nếu có field khác được điền
        if (isOtherFieldFilled) {
          // Kiểm tra xem có giá trị không
          if (!value) {
            return this.createError({
              message: "Số điện thoại là bắt buộc",
            });
          }
          // Kiểm tra định dạng
          if (!/^(\+84|0)[3-9]\d{8}$/.test(value)) {
            return this.createError({
              message: "Số điện thoại không đúng định dạng",
            });
          }
        }
        // Nếu có giá trị (không bắt buộc), kiểm tra định dạng
        else if (value && !/^(\+84|0)[3-9]\d{8}$/.test(value)) {
          return this.createError({
            message: "Số điện thoại không đúng định dạng",
          });
        }

        return true;
      }),
    tags: Yup.array().of(Yup.string()).notRequired(),

    // Các field liên quan đến địa chỉ giao hàng
    shippingAddressFullname: Yup.string()
      .nullable()
      .test("conditional-required", "Họ tên là bắt buộc", function (value) {
        const {
          shippingAddressPhone,
          shippingAddressAddress,
          shippingAddressProvince,
          shippingAddressDistrict,
          shippingAddressWard,
        } = this.parent;

        const hasAnyValue = !!(
          shippingAddressPhone ||
          shippingAddressAddress ||
          shippingAddressProvince ||
          shippingAddressDistrict ||
          shippingAddressWard
        );

        if (!hasAnyValue) {
          return true;
        }

        return value !== null && value !== undefined && value !== "";
      }),
    shippingAddressAddress: Yup.string()
      .nullable()
      .test("conditional-required", "Địa chỉ là bắt buộc", function (value) {
        const {
          shippingAddressPhone,
          shippingAddressFullname,
          shippingAddressProvince,
          shippingAddressDistrict,
          shippingAddressWard,
        } = this.parent;

        const hasAnyValue = !!(
          shippingAddressPhone ||
          shippingAddressFullname ||
          shippingAddressProvince ||
          shippingAddressDistrict ||
          shippingAddressWard
        );

        if (!hasAnyValue) {
          return true;
        }

        return value !== null && value !== undefined && value !== "";
      }),
    shippingAddressProvince: Yup.string()
      .nullable()
      .test("conditional-required", "Tỉnh/Thành phố là bắt buộc", function (value) {
        const {
          shippingAddressPhone,
          shippingAddressFullname,
          shippingAddressAddress,
          shippingAddressDistrict,
          shippingAddressWard,
        } = this.parent;

        const hasAnyValue = !!(
          shippingAddressPhone ||
          shippingAddressFullname ||
          shippingAddressAddress ||
          shippingAddressDistrict ||
          shippingAddressWard
        );

        if (!hasAnyValue) {
          return true;
        }

        return value !== null && value !== undefined && value !== "";
      }),
    shippingAddressDistrict: Yup.string()
      .nullable()
      .test("conditional-required", "Quận/Huyện là bắt buộc", function (value) {
        const {
          shippingAddressPhone,
          shippingAddressFullname,
          shippingAddressAddress,
          shippingAddressProvince,
          shippingAddressWard,
        } = this.parent;

        const hasAnyValue = !!(
          shippingAddressPhone ||
          shippingAddressFullname ||
          shippingAddressAddress ||
          shippingAddressProvince ||
          shippingAddressWard
        );

        if (!hasAnyValue) {
          return true;
        }

        return value !== null && value !== undefined && value !== "";
      }),
    shippingAddressWard: Yup.string()
      .nullable()
      .test("conditional-required", "Phường/Xã là bắt buộc", function (value) {
        const {
          shippingAddressPhone,
          shippingAddressFullname,
          shippingAddressAddress,
          shippingAddressProvince,
          shippingAddressDistrict,
        } = this.parent;

        const hasAnyValue = !!(
          shippingAddressPhone ||
          shippingAddressFullname ||
          shippingAddressAddress ||
          shippingAddressProvince ||
          shippingAddressDistrict
        );

        if (!hasAnyValue) {
          return true;
        }

        return value !== null && value !== undefined && value !== "";
      }),
  });

export const editCustomerInfoSchema = (t) =>
  Yup.object().shape({
    email: emailValidationSchema(t),
    phone: createPhoneValidationSchema(t),
    name: nameValidationSchema(t),
  });

export const shippingAddressSchema = (t) =>
  Yup.object().shape({
    shippingAddressFullname: Yup.string()
      .required("Họ tên là bắt buộc")
      .max(255, "Họ tên không được vượt quá 255 ký tự"),
    shippingAddressPhone: Yup.string()
      .required("Số điện thoại là bắt buộc")
      .matches(/^(\+84|0)[3-9]\d{8}$/, "Số điện thoại không đúng định dạng"),
    shippingAddressAddress: Yup.string()
      .required("Địa chỉ là bắt buộc")
      .max(255, "Địa chỉ không được vượt quá 255 ký tự"),
    shippingAddressProvince: Yup.string().required("Tỉnh/Thành phố là bắt buộc"),
    shippingAddressDistrict: Yup.string().required("Quận/Huyện là bắt buộc"),
    shippingAddressWard: Yup.string().required("Phường/Xã là bắt buộc"),
  });

export const paymentNameValidationSchema = Yup.string().required("Tên phương thức là bắt buộc");

export const addPaymentMethodSchema = Yup.object().shape({
  paymentMethodName: paymentNameValidationSchema,
  paymentMethodDetail: Yup.string(),
  paymentMethodInstruction: Yup.string(),
  image: Yup.mixed(),
});
